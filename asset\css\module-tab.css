﻿/*
------------
tab
------------
*/

.tab-panel {

    height: 24px;
    padding: 4px 120px 0 4px;
    overflow: hidden;
}

.tab-panel.embeded {
    padding-top: 2px;
}

.tab-unit {

    display: inline-block;
    width: 100px;
    height: 24px;
    line-height: 22px;
    border-radius: 4px 4px 0 0;
    text-align: center;
    margin-right: 2px;
    cursor: default;
    user-select: none;
}

.tab-panel.embeded .tab-unit {

    width: 70px;
    border-radius: 2px 2px 0 0;
}

.tab-unit .tab-inner {
    padding: 0 10px;
}

.tab-unit .tab-close {

    display: block;
    float: right;
    margin-right: 5px;
    line-height: 26px;
    cursor: pointer;
}

.tab-operation {

    display: block;
    float: right;
    margin-right: -110px;
    line-height: 20px;
    text-align: center;
}

.tab-operation > * {
    padding-left: 5px;
}