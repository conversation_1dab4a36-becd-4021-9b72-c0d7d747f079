.smart-table {

    position: relative;
    box-sizing: border-box;
    border-left-width: 1px;
    border-left-style: solid;
    border-top-width: 1px;
    border-top-style: solid;
    border-radius: 3px;
    overflow: auto;
}

.smart-table table {

    table-layout: fixed;
    border-collapse: collapse;
}

.smart-table table th {
    text-align: left;
}

.smart-table table th,
.smart-table table td {

    box-sizing: border-box;
    padding-left: 10px;
    padding-right: 10px;
    white-space: nowrap;
    overflow: hidden;
    border-right-width: 1px;
    border-right-style: solid;
    border-bottom-width: 1px;
    border-bottom-style: solid;
}

.smart-table .table-center {
    width: 100%;
}

.smart-table-header {
    box-sizing: border-box;
}

.smart-table-header.fixed-header {

    position: absolute;
    top: 0;
    z-index: 2;
    width: 100%;
}

.smart-table .table-fixed-left {

    position: absolute;
    left: 0;
    z-index: 1;
}

.smart-table .table-fixed-right {

    position: absolute;
    right: 0;
    z-index: 1;
}

.smart-table-header .header-cell {
    line-height: 24px;
}

.smart-table table th.filterable-column .header-text {
    padding-left: 18px;
}

.smart-table-header .header-cell .filtering-icon {

    position: absolute;
    display: inline-block;
    width: 12px;
    height: 12px;
    line-height: 24px;
    font-size: 12px;
    opacity: 0.4;
}

.smart-table-header .header-cell .filtering-icon.is-filtering {

    font-weight: bold;
    opacity: 0.6;
    color: red;
}

.smart-table-header .header-cell:hover .filtering-icon {
    opacity: 1;
}

.smart-table-header .sorting-icon {

    display: inline-block;
    position: relative;
    right: -4px;
    margin-left: -2px;
    width: 12px;
    height: 24px;
}

.smart-table-header .sorting-icon .top,
.smart-table-header .sorting-icon .bottom {

    position: relative;
    font-size: 16px;
}

.smart-table-header .sorting-icon .top {
    top: -2px;
}

.smart-table-header .sorting-icon .bottom {

    left: -16px;
    top: 4px;
}

.smart-table-body {
    box-sizing: border-box;
}

.smart-table-footer {

    position: absolute;
    bottom: 0;
    width: 100%;
    box-sizing: border-box;
}

.smart-table-header-inner,
.smart-table-body-inner,
.smart-table-footer-inner {

    position: relative;
    box-sizing: border-box;
}

.smart-table table th.column-check > * {
    margin-left: 5px;
}

.smart-table table th.column-check input,
.smart-table table td.column-check input {

    position: relative;
    top: 3px;
    margin: 0;
    width: 14px;
    height: 14px;
}
/* 
.smart-table table th.column-check input::after,
.smart-table table td.column-check input::after {

    content: " ";
    color: #fff;
}

.smart-table table th.column-check input:checked::after,
.smart-table table td.column-check input:checked::after {
    content: "✓";
} */

.smart-table-body-inner table td button,
.smart-table-body-inner table td .button {

    border-width: 1px;
    border-style: solid;
    border-radius: 2px;
    padding: 2px 4px;
    line-height: 1;
    opacity: 0.7;
}

.smart-table-body-inner table td .icon-button {

    opacity: 0.7;
    cursor: pointer;
}

.smart-table-body-inner table td button:hover,
.smart-table-body-inner table td .button:hover,
.smart-table-body-inner table td .icon-button:hover {
    opacity: 1;
}

.smart-table-body-inner table td > * {
    margin-left: 5px;
}

.smart-table .drag-info {

    display: none;
    position: fixed;
    z-index: 1;
    line-height: 20px;
    padding: 0 10px;
    border-width: 1px;
    border-style: solid;
}

.smart-table .drag-triangle {

    display: none;
    position: fixed;
    z-index: 2;
    margin-top: -10px;
}

.smart-table .drag-triangle .arrow {

    position: relative;
    width: 10px;
    height: 10px;
}

.smart-table .drag-triangle .arrow:before,
.smart-table .drag-triangle .arrow:after {
    
    content: '';
    border-color: transparent;
    border-style: solid;
    position: absolute;
}

.smart-table .drag-triangle .arrow-up:before {

    border: none;
    height: 50%;
    width: 30%;
    top: 50%;
    left: 35%;
    background-color: red;
}

.smart-table .drag-triangle .arrow-up:after {

    left: 0;
    top: -50%;
    border-width: 5px 5px;
    border-bottom-color: red;
}

.smart-table .drag-triangle .arrow-down:before {

    border: none;
    background-color: red;
    height: 50%;
    width: 30%;
    top: 0;
    left: 35%;
}

.smart-table .drag-triangle .arrow-down:after {

    left: 0;
    top: 50%;
    border-width: 5px 5px;
    border-top-color: red;
}

.smart-table .cell-tooltip {

    position: fixed;
    z-index: 999;
    display: block;
    padding: 5px;
    border-width: 2px;
    border-style: solid;
    border-radius: 5px;
}

.smart-table .config-panel {

    position: fixed;
    right: 5px;
    top: 40px;
    z-index: 99;
    border-width: 2px;
    border-style: solid;
    border-radius: 5px;
    overflow: hidden;
}

.smart-table .config-panel .content-container {

    box-sizing: border-box;
    overflow: auto;
    max-height: 300px;
    padding: 10px 5px;
}

.smart-table .config-panel .panel-boolbar {

    height: 30px;
    line-height: 30px;
    padding-right: 10px;
}

.smart-table .config-panel .panel-boolbar .tab-item {

    display: inline-block;
    height: 100%;
    padding-left: 10px;
    padding-right: 10px;
    cursor: pointer;
}

.smart-table .config-panel .btn {

    display: block;
    float: right;
    margin-left: 5px;
    margin-top: 3px;
    border-width: 1px;
    border-style: solid;
    border-radius: 3px;
    cursor: pointer;
}

.smart-table .config-panel .panel-boolbar .btn {

    padding: 3px 6px 5px 6px;
    line-height: 16px;
}

.smart-table .config-panel .content-container .btn {

    margin-top: -3px;
    padding: 2px 5px 3px 5px;
    line-height: 16px;
}

.smart-table .config-panel .column-list-panel {
    width: 240px;
}

.smart-table .config-panel .checkbox-item {

    display: block;
    overflow: hidden;
}

.smart-table .config-panel .checkbox-item > input {

    width: 16px;
    height: 16px;
}

.smart-table .config-panel .checkbox-item > span {
    
    position: relative;
    top: -3px;
    padding-left: 5px;
    font-size: 14px;
}

.smart-table-filter-panel {

    display: none;
    position: fixed;
    z-index: 2999;
    margin: 0;
    padding: 5px;
    border-radius: 3px;
}

.smart-table-filter-panel .filter-toolbar {

    padding-bottom: 5px;
    border-bottom-width: 1px;
    border-bottom-style: solid;
}

.smart-table-filter-panel ul {

    margin: 0;
    padding: 0;
    max-height: 300px;
    overflow-y: auto;
}

.smart-table-filter-panel .filter-item {
    line-height: 20px;
}

.smart-table-filter-panel .filter-item.filter-all *,
.smart-table-filter-panel .filter-item.filter-empty * {
    color: red;
}

.smart-table-action {

    border-width: 1px;
    border-style: solid;
    border-radius: 3px;
    padding: 3px 5px;
    line-height: 1;
    opacity: 0.8;
}

.smart-table-action:hover {
    opacity: 1;
}

.smart-table-action .lock-button {

    position: relative;
    top: -2px;
}

.smart-table-action ul {
    display: none;
}

.smart-table-menu-panel {

    display: none;
    border-radius: 3px;
    position: fixed;
    margin: 0;
    padding: 0;
    z-index: 99;
}

.smart-table-menu-panel li {
    
    display: block;
    border-bottom-width: 1px;
    border-bottom-style: solid;
    padding-left: 5px;
    padding-right: 5px;
    text-align: left;
    cursor: pointer;

    overflow: hidden;
    word-wrap: normal;
    white-space: nowrap !important;
    text-overflow: ellipsis;
}

.smart-table-menu-panel li:last-child {
    border-bottom: none;
}

.smart-table-menu-panel li a {

    display: inline-block;
    width: 100%;
    line-height: 24px;
}

.smart-table-menu-panel.horizontal {
    height: 24px;
}

.smart-table-menu-panel.horizontal li {

    display: inline-block;
    border-right-width: 1px;
    border-right-style: solid;
    border-bottom: none;
}

.smart-table-menu-panel.horizontal li:last-child {
    
    border-bottom: none;
    border-right: none;
}