body {
    background-color: #1f242d;
}

.el-dialog__wrapper {
    overflow: auto;
}

*,
.el-form-item__label {
    color: white;
}

input.el-input__inner,
.el-date-editor.el-input__inner,
.el-date-editor.el-input__inner > input.el-range-input,
.el-textarea__inner {
    background-color: #0c1016;
    color: white;
}

.el-range-editor--mini.el-input__inner {
    height: 24px;
    width: 230px;
    padding: 2px 10px;
}

.el-textarea__inner {
    border: none;
}

input.el-input__inner:not([disabled]):hover,
input.el-input__inner:focus,
.el-date-editor.el-input__inner:focus,
.el-date-editor.el-input__inner > input.el-range-input:focus,
.el-textarea__inner:not([disabled]):hover,
.el-textarea__inner:focus {
    opacity: 1;
}

.el-input.is-disabled .el-input__inner {
    background-color: #384E70FF;
}

.el-input-group__append,
.el-input-group__prepend {
    background-color: #585d63;
    color: #e3e7ea;
    border: 1px solid #313233;
    padding: 0 10px;
}

.el-date-editor .el-range-separator {
    color: white;
}

/*
	rewrite element control [select] dropdown list style
*/

.el-button:hover {
    opacity: 0.95;
}

.el-button--default {
    border: 1px solid #0e1218;
    background-color: #38475a;
    box-shadow: 0 0 2px 0 #1f2631 inset;
    color: #8cb5ed;
}

.el-button--default:hover {
    background-color: #38475a;
}

.el-button--default {
    background: #1f2631;
    box-shadow: 0 1px 1px 0 #65748c inset;
}

.el-button--primary {
    border: 1px solid #0e1218;
    background-image: linear-gradient(-180deg, #3e8aff 0%, #135fd5 100%);
    box-shadow: 0 2px 4px 0 rgba(15, 61, 132, 0.1), 0 1px 1px 0 #468cfb inset;
}

.el-button--default:active,
.el-button--default.is-active,
.el-button--default:focus,
.el-button--default.is-focused {

    background-color: initial !important;
    background-image: initial !important;
}

.el-select-dropdown * {
    color: white;
}

.el-select-dropdown__item.hover,
.el-select-dropdown__item:hover {
    background-color: #23354D;
}

.el-switch__core {
    background: #928383;
}

.el-switch.is-checked .el-switch__core {
    border-color: #13ce66;
    background-color: #13ce66;
}

/*
    rewrite element ui control default style
*/

.el-tabs--border-card {
    border: none;
    background: transparent;
}

.el-tabs--border-card > .el-tabs__header {
    background-color: transparent;
    border-bottom: none;
}

.el-tabs--border-card > .el-tabs__header .el-tabs__item {
    margin: 0;
    border: 1px solid transparent;
    border-top-left-radius: 5px;
    border-top-right-radius: 5px;
    background-color: transparent;
    color: white !important;
    opacity: 0.7;
}

.el-tabs--border-card > .el-tabs__header .el-tabs__item:not(.is-active):hover {
    border: 1px solid #3a465a;
    background-color: #1f242d;
    opacity: 0.9;
}

.el-tabs--border-card > .el-tabs__header .el-tabs__item.is-active {
    border-bottom: 1px solid transparent;
    border-top-color: #3a465a;
    border-left-color: #3a465a;
    border-right-color: #3a465a;
    background-color: #1f242d;
    opacity: 1;
}

.el-tabs--border-card.tabs-header-styled > .el-tabs__header > .el-tabs__nav-wrap > .el-tabs__nav-scroll {
    background-color: #2f3949;
    box-shadow: 0 0 2px 0 #45546c inset;
    border-top-left-radius: 4px;
    border-top-right-radius: 4px;
}

.el-tabs--border-card > .el-tabs__content {
    border: 1px solid #3a465a;
}

.el-table--border,
.el-table--group {
    border-left: none;
    border-top: 1px solid #65748c;
}

.el-table--border::after,
.el-table--group::after,
.el-table::before,
.el-table__fixed-right::before,
.el-table__fixed::before {
    background-color: initial;
}

.el-table {
    background-color: #1f242d;
}

.el-table .el-table__body tr {
    background-color: #1f242d;
}

.el-table .el-table__header-wrapper {
    background-color: #262f3c;
}
.el-table .el-table__body-wrapper {
    background-color: #1f242d;
}

.el-table .el-table__footer-wrapper {
    background-color: #262f3c;
}

.el-table .el-table__header th,
.el-table .el-table__body tr.el-table__row--striped td,
.smart-table > .smart-table-header tr,
.smart-table > .smart-table-body table tr:nth-child(even) {
    background: #262f3c;
}

.smart-table > .smart-table-body table tr:nth-child(odd) {
    background: #1f242d;
}

.el-table .el-table__header th.is-leaf,
.el-table .el-table__body tr td,
.el-table .el-table__footer tr td,
.smart-table > .smart-table-body table tr:nth-child(even) {
    box-shadow: 0 1px 0 #2f3948 inset;
}

.el-table .el-table__header th > .cell,
.el-table .el-table__footer td > .cell,
.smart-table > .smart-table-header th,
.smart-table > .smart-table-header th .header-text {
    color: #8cb5ed;
}

.smart-table-footer {
    border-top: 1px solid #2f3948;
}

.smart-table > .smart-table-footer table tr {
    background: #1a212b;
}

.el-table .el-table__header th > .cell a,
.el-table .el-table__footer td > .cell a {
    color: #8cb5ed;
}

.smart-table table tr.checked-row {
    background-color: #786f66 !important;
}

.el-table .el-table__body tr.current-row > td,
.smart-table table tr.selected-row {
    background-color: #67940c !important;
}

.el-table .el-table__body tr.hover-row > td,
.smart-table table tr.hover-row {
    background-color: #374c6b !important;
}

.el-table .el-table__body tr:hover,
.el-table .el-table__body tr:hover > td {
    background-color: #374c6b !important;
}

.el-table .el-table__empty-block {
    background-color: #1f242d;
}

.el-table .el-table__empty-block .el-table__empty-text {
    color: white;
}

.el-table .el-table__footer td {
    border-top: none;
    background-color: #262f3c;
}

.el-pagination button:disabled {
    background-color: #3e8aff;
    box-shadow: 0 2px 4px 0 rgba(15, 61, 132, 0.1), 0 0 2px 0 #65a1ff inset;
}

.el-pagination__total {
    color: white;
}

.el-container > .el-header {
    background-color: #262f3c;
    border: 1px solid #3c4657;
}

.el-container > .el-main {
    border-color: #3c4657;
}

.el-message-box,
.el-dialog,
.el-popover,
.el-picker-panel,
.el-autocomplete-suggestion__wrap {
    background-color: #38475a;
    border: 2px solid #696c64;
    box-shadow: 0 0 20px 0 rgba(24, 30, 40, 0.5), 0 0 2px 0 #38475a inset;
}

.el-message-box,
.el-dialog {
    border-radius: 5px;
}

.el-autocomplete-suggestion__wrap > ul > li.highlighted *,
.el-autocomplete-suggestion__wrap > ul > li:hover * {
    color: black;
}

.el-dialog__header {
    border: 1px solid #586d87;
    background: #465e79;
}

.el-dialog__title,
.el-message-box__title {
    font-size: 12px;
    color: white;
}

.el-dialog__headerbtn .el-dialog__close,
.el-dialog__headerbtn .el-dialog__close:hover,
.el-message-box__headerbtn .el-message-box__close,
.el-message-box__headerbtn .el-message-box__close:hover {
    font-size: 14px;
    color: white;
}

.el-dialog__footer {
    border-top: 1px solid #1f2631;
}

.el-notification .el-notification__content * {
    color: black;
}

.el-loading-mask {
    background-color: #666;
    opacity: 0.8;
}

.el-loading-mask > .el-loading-spinner > svg > circle {
    stroke: white;
}

.el-loading-mask > .el-loading-spinner > .el-loading-text {
    color: white;
}

.el-date-table td.in-range div,
.el-date-table td.in-range div:hover,
.el-date-table.is-week-mode .el-date-table__row.current div,
.el-date-table.is-week-mode .el-date-table__row:hover div {
    background-color: #030406;
}

.el-tooltip__popper.is-dark {
    background: #fff;
    border: 1px solid #303133;
    color: black;
}

.el-tooltip__popper[x-placement^='bottom'] .popper__arrow::after {
    border-bottom-color: #fff;
}

.el-transfer-panel {
    background-color: #2c3544;
}

.el-transfer__button.is-disabled,
.el-transfer__button.is-disabled:hover {
    background-color: #9ca09e;
}

/*
	specialize system level skins
*/

.el-table .s-col-oper .icon-button {
    background: transparent;
    color: white;
}

.el-table .s-col-oper .icon-button:hover {
    background: white;
    color: black;
}

.s-flag {
    color: #fff;
}

html::-webkit-scrollbar,
.el-table > .el-table__body-wrapper::-webkit-scrollbar,
.s-scroll-bar::-webkit-scrollbar {
    background-color: #262f3c;
}

html::-webkit-scrollbar-track,
.el-table > .el-table__body-wrapper::-webkit-scrollbar-track,
.s-scroll-bar::-webkit-scrollbar-track {
    border: 1px solid #2c3544;
    background-color: #181e28;
    border-radius: 8px;
}

html::-webkit-scrollbar-thumb,
.el-table > .el-table__body-wrapper::-webkit-scrollbar-thumb,
.s-scroll-bar::-webkit-scrollbar-thumb {
    box-shadow: 0 0 2px #8cb5ed inset;
    background-color: #65a1ff;
    border-radius: 8px;
    visibility: hidden;
}

html::-webkit-scrollbar-corner,
.el-table > .el-table__body-wrapper::-webkit-scrollbar-corner,
.s-scroll-bar::-webkit-scrollbar-corner {
    background-color: #262f3c;
}

html:hover::-webkit-scrollbar-thumb,
.el-table > .el-table__body-wrapper:hover::-webkit-scrollbar-thumb,
.s-scroll-bar:hover::-webkit-scrollbar-thumb {
    visibility: visible;
}

.el-switch.is-checked .el-switch__core {
    background-image: linear-gradient(-180deg, #3e8aff 0%, #135fd5 100%);
    border: 1px solid;
    border-color: #1f2631;
}

.el-popper[x-placement^='left'] .popper__arrow {
    border-left-color: #38475a;
}

.el-popper[x-placement^='left'] .popper__arrow::after {
    border-left-color: #38475a;
}

.el-tree {
    background-color: #38475a;
}

.el-select__tags-text {
    color: #454545;
}

.el-progress-bar__innerText {
    color: #38475a;
}

.el-date-table td.disabled div {
    background-color: #1f2631 !important;
    color: #f5f5f5;
}

.lighted-box .el-dialog__header,
.lighted-box .el-message-box__header {

    background-color: #38475a;
    border-bottom: 1px solid #696c64;
}