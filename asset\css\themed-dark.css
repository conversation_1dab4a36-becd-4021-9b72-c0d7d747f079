
body {
    color: white;
}

/*
=======================
scroll bar
=======================
*/

::-webkit-scrollbar {
    background-color: #1A212B;
}

::-webkit-scrollbar-track {

    border-color: #2c3544;
    background-color: #181e28;
}

::-webkit-scrollbar-thumb {    

    background-color: #65A1FF;
    box-shadow: inset 0 0 2px 0 #8CB5ED;
}

::-webkit-scrollbar-corner {
    background-color: #1A212B;
}

/*
=======================
splitter
=======================
*/

.xsplitter .splitter-bar {
    background-color: #666;
}

.xsplitter .splitter-bar::before {
    color: #ccc;
}

.xsplitter .splitter-bar:hover {
    background-color: #c2c2c2;
}

/*
=======================
tab
=======================
*/

.tab-panel {
    background-color: #283A56;
}

.tab-panel.embeded {
    background-color: #23354D;
}

.tab-unit {
    background-color: #384E70;
}

.tab-panel.embeded .tab-unit {
    background-color: unset;
}

.tab-unit:hover {
    background-color: #325489;
}

.tab-unit.selected {
    background-image: linear-gradient(-180deg, #3E8AFF 0%, #135FD5 100%);
}

.tab-panel.embeded .tab-unit.selected {

    background-image: none;
    background-color: #1A212B;
}

/*
=======================
channels
=======================
*/

.frag-channels .channel-item.selected {
    background-color: #245BB0;
}

/*
=======================
kinds of components
=======================
*/

.win-header,
.layout-header {
    background-image: linear-gradient(180deg, #476082 0%, #263955 100%);
}

.win-footer {
    background-color: #283A56;
}

.themed-box {
    background-color: #1A212B;
}

.themed-header {
    background-color: #23354D;
}

.themed-color,
.themed-hover {
    color: #999;
}

.themed-hover:hover,
.themed-hover-color:hover,
.themed-selected,
.themed-bright {

    opacity: 1;
    color: #FFF;
}

.themed-bg {
    background-color: #1A212B;
}

.themed-bg-harder {
    background-color: #324F80;
}

.themed-border {
    border: 1px solid #0C1016;
}

.themed-top-border {
    border-top: 1px solid #0C1016;
}

.themed-right-border {
    border-right: 1px solid #0C1016;
}

.themed-bottom-border {
    border-bottom: 1px solid #0C1016;
}

.themed-left-border {
    border-left: 1px solid #0C1016;
}

.themed-selected-member {

    background-color: #283A56;
    opacity: 1;
    border-radius: 1px;
}

.themed-highlighted {

    background-color: #2C79F2;
    opacity: 1;
}

.themed-plain-table th,
.themed-plain-table td {
    border-color: #283A56;
}

.xt-table-row-input {

    background-color: #283A56;
    color: #FFF;
}

.xt-table-row-input:active,
.xt-table-row-input:focus {

    outline: none;
    background-color: #0C1016;
    border: 1px solid #65A1FF;
}

/*
=======================
smart table
=======================
*/

.smart-table {

    border-left-color: transparent;
    border-top-color: transparent;
}

.smart-table table th,
.smart-table table td {

    border-right-color: #0C1016;
    border-bottom-color: #0C1016;
}

.smart-table-header tr {

    background-color: #1E2836;
    color: #8cb5ed;
}

.smart-table-header .sorting-icon .top,
.smart-table-header .sorting-icon .bottom {
    color: #777;
}

.smart-table-header .sorting-icon .top.ascending,
.smart-table-header .sorting-icon .bottom.descending {
    color: #FFF;
}

.smart-table-body table tr:nth-child(even) {
    background-color: #1E2836;
}

.smart-table-body table tr:nth-child(odd) {
    background-color: #1A212B;
}

/* .smart-table-body table tr.checked-row {
    background-color: #2C79F2 !important;
} */

.smart-table-body table tr.selected-row {
    background-color: #2C79F2 !important;
}

.smart-table-body table tr.hover-row {
    background-color: #324F80;
}

.smart-table-body table td button,
.smart-table-body table td .button {

    background-color: #2C79F2;
    border-color: #2C79F2;
    color: #FFF;
}

.smart-table-body table td button.info,
.smart-table-body table td .button.info {

    background-color: #909399;
    border-color: #909399;
}

.smart-table-body table td button.success,
.smart-table-body table td .button.success {

    background-color: #67c23a;
    border-color: #67c23a;
}

.smart-table-body table td button.warning,
.smart-table-body table td .button.warning {

    background-color: #e6a23c;
    border-color: #e6a23c;
}

.smart-table-body table td button.danger,
.smart-table-body table td .button.danger {

    background-color: #EF3939;
    border-color: #EF3939;
}

.smart-table-footer table tr {

    border-top: 1px solid #0C1016;
    background-color: #1E2836;
}

.smart-table .table-fixed-left.scroll-moving {
    box-shadow: #191919 3px 0 15px 3px;
}

.smart-table .table-fixed-right.scroll-moving {
    box-shadow: #191919 -3px 0 15px 3px;
}

.smart-table .smart-table-header.scroll-moving {
    box-shadow: #191919 0 3px 15px 3px;
}

.smart-table .drag-info {

    border-color: #eee;
    background-color: #000;
}

.smart-table .cell-tooltip {
    border-color: black;
}

.smart-table .cell-tooltip * {
    color: black;
}

.smart-table .config-panel {
    background-color: #423838;
}

.smart-table .config-panel .panel-boolbar {
    background-color: black;
}

.smart-table .config-panel .panel-boolbar .tab-item {
    background-color: #8c95a2;
}

.smart-table .config-panel .btn {

    border-color: #0e1218;
    background-image: linear-gradient(-180deg, #3e8aff 0%, #135fd5 100%);
}

.smart-table .config-panel .tab-item.focused {
    background-color: #423838;
}

.smart-table-filter-panel * {
    color:#111;
}

.smart-table-filter-panel .filter-toolbar {
    border-bottom-color: #111;
}

.smart-table-menu-panel li {
    border-bottom-color: #111;
}

.smart-table-menu-panel li:hover {
    background-color: #ccc;
}

.smart-table-menu-panel li a {
    color:#111;
}

.smart-table-menu-panel.horizontal li {
    border-right-color: #111;
}

.smart-table-action {

    border-color: #0e1218;
    background-image: linear-gradient(-180deg, #99aac3 0%, #525861 100%);
}

.smart-table-themed-bg {
    background-color: #FFF;
}

/*
=======================
element ui component
=======================
*/

.el-button--default {

    background-color: #F5F5F5;
    border: 1px solid #65A1FF;
    color: #2C79F2;
}

.el-button--primary,
.el-button--primary:focus {

    background-image: unset !important;
    background-color: #2C79F2 !important;
    border-color: #2C79F2 !important;
    color: #FFF;
}

.el-button--success,
.el-button--success:focus {

    background-color: #1BBE48;
    border-color: #1BBE48;
    color: #FFF;
}

.el-button--success:hover {

    background-color: #85CE61;
    border-color: #85CE61;
    color: #FFF;
}

.el-button--danger,
.el-button--danger:focus {

    background-color: #EF3939;
    border-color: #EF3939;
    color: #FFF;
}

.el-button--danger:hover {

    background-color: #F78989;
    border-color: #F78989;
    color: #FFF;
}

.el-button.purple-btn,
.el-button.purple-btn:focus {

    background-color: #4D62FF;
    border-color: #4D62FF;
    color: #FFF;
}

.el-input__inner {

    color: #999;
    background-color: #0C1016;
    box-shadow: inset 0 1px 2px 0 #0D1117;
}

.el-input.is-disabled .el-input__inner {

    color: #999;
    background-color: #384E70;
    box-shadow: inset 0 1px 2px 0 #384E70;
}

.el-input-number__decrease,
.el-input-number__increase {

    background-color: #283A56;
    color: white;
    opacity: 0.8;
}

.el-input-number__decrease:not(.is-disabled):hover,
.el-input-number__increase:not(.is-disabled):hover {

    color: white;
    opacity: 1;
}

.el-input-number__decrease {
    border-right-color: transparent;
}

.el-input-number__increase {
    border-left-color: transparent;
}

.el-date-editor .el-range-input {

    color: #FFF;
    background-color: unset;
}

.el-date-editor .el-range-separator {
    color: #FFF;
}

.el-date-editor.is-disabled,
.el-range-editor.is-disabled {
    background-color: #0C1016;
}

.el-radio,
.el-radio__input.is-checked+.el-radio__label {
    color: #FFF;
}

.el-radio__inner {

    border-color: #FFF;
    background-color: #283A56;
}

.el-radio__input.is-checked .el-radio__inner {

    border-color: #FFF;
    background-color: #FFF;
}

.el-radio__inner::after {
    background-color: #2C79F2;
}

.el-radio-button__inner {

    background-color: #1A212B;
    border-color: #324F80;
    color: #999;
}

.el-radio-button__inner:hover {
    color: #FFF;
}

.el-radio-button:first-child .el-radio-button__inner {
    border-left-color: #324F80;
}

.el-radio-button__orig-radio:checked+.el-radio-button__inner {

    background-color: #245BB0;
    border-color: #245BB0;
    opacity: 1;
    -webkit-box-shadow: unset;
    box-shadow: unset;
}

.el-checkbox {
    color: #999;
}

.el-checkbox:hover,
.el-checkbox__input.is-checked+.el-checkbox__label {
    color: #FFF;
}

.el-checkbox__inner {

    background-color: #FFF;
    /* background-color: #999; */
    border-color: #999;
}

.el-checkbox__inner::after {
    border-color: #000;
}

.el-checkbox__inner:hover,
.el-checkbox__input.is-checked .el-checkbox__inner,
.el-checkbox__input.is-indeterminate .el-checkbox__inner {

    background-color: #FFF;
    border-color: #FFF;
}

.el-checkbox__input.is-disabled .el-checkbox__inner {
    background-color: #7E8592;
}

.el-select-dropdown {

    background-color: #324F80;
    box-shadow: 0 2px 6px 0 rgba(0, 0, 0, 0.50);
}

.el-select-dropdown__item,
.el-select-dropdown__item.selected {
    color: #FFF;
}

.el-select-dropdown__item.hover,
.el-select-dropdown__item:hover {
    background-color: #23354D;
}

.el-popper[x-placement^=bottom] .popper__arrow,
.el-popper[x-placement^=bottom] .popper__arrow::after {
    border-bottom-color: #324F80;
}

.el-popper[x-placement^=top] .popper__arrow,
.el-popper[x-placement^=top] .popper__arrow::after {
    border-top-color: #324F80;
}

.el-pagination {
    color: #999;
}

.el-pager li.active,
.el-pager li:hover,
.el-pagination .btn-prev:hover,
.el-pagination .btn-next:hover {

    color: #FFF;
    opacity: 1;
}

.el-pager li.active {

    background-color: #324F80;
    border-radius: 1px;
}

.el-pagination .btn-next,
.el-pagination .btn-prev,
.el-pager li.btn-quicknext,
.el-pager li.btn-quickprev {

    color: #FFF;
    opacity: 0.7;
}

.el-pager li.btn-quicknext:hover,
.el-pager li.btn-quickprev:hover {

    opacity: 1;
    color: #FFF;
}

.el-pagination__total {
    color: #FFF;
}

.el-dialog {
    background-color: #1E2836;
}

.lighted-box .el-dialog {
    
    background-color: #F5F5F5;
    box-shadow: 0 0 20px 0 rgba(0, 0, 0, 0.50);
}

.el-dialog__header {
    background-color: #3F64A4;
}

.lighted-box .el-dialog__header,
.lighted-box .el-message-box__header {

    background-color: #F5F5F5;
    border-bottom: 1px solid #E5E5E5;
}

.el-dialog__title {
    color: #FFF;
}

.lighted-box .el-dialog__title {
    color: #606060;
}

.el-dialog__headerbtn .el-dialog__close,
.el-dialog__headerbtn .el-dialog__close:hover {
    color: #FFF;
}

.el-dialog__body {

    /* background-color: #324F80; */
    background-color: #1A212B;
    /* border: 1px solid #0C1016; */
    color: #FFF;
}

.lighted-box .el-dialog__body {

    background-color: #F5F5F5;
    color: #161C25;
}

.lighted-box .el-dialog__body .el-input__inner,
.lighted-box .el-message-box__content .el-input__inner {

    background-color: #FFFFFF;
    border: 1px solid #E5E5E5;
    box-shadow: none;
    color: #161C25;
}

.lighted-box .el-dialog__footer .el-button,
.lighted-box .el-message-box__btns .el-button {
    height: 20px;
}

.el-popover {

    background-color: #324F80;
    box-shadow: 0 4px 20px 0 rgba(0, 0 , 0, 0.50);
    color: #FFF;
}

.el-popover__title {

    background-color: #3F64A4;
    color: #FFF;
}

.el-tooltip__popper.is-dark {
    background-color: #0C1016;
}

/* .el-tooltip__popper .popper__arrow,
.el-tooltip__popper .popper__arrow::after {
    border-color: #0C1016;
} */

.el-popper[x-placement^=bottom] .popper__arrow,
.el-popper[x-placement^=bottom] .popper__arrow::after {
    border-bottom-color: #3F64A4;
}

.el-autocomplete-suggestion {

    background-color: #324F80;
    box-shadow: 0 2px 6px 0 rgba(0,0,0,0.50);
}

.el-autocomplete-suggestion li {
    color: #FFF;
}

.el-autocomplete-suggestion li:hover,
.el-autocomplete-suggestion li.highlighted {
    background-color: #23354D;
}

.el-switch__core {

    width: 32px !important;
    border-color: #65748C;
    background-color: #65748C;
}

.el-switch.is-checked .el-switch__core {

    border-color: #2C79F2;
    background-color: #2C79F2;
}

.el-transfer-panel .el-transfer-panel__header {
	
	background-color: #1A212B !important;
	color: white;
}

.el-transfer-panel .el-transfer-panel__header .el-checkbox .el-checkbox__label {
	color: white;
}