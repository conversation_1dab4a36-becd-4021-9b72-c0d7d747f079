const IView = require('./iview').IView;

class AppInstance extends IView {

    constructor(view_name, is_standalone_window, title) {

        super(view_name, is_standalone_window, title);
        this.webview = null;
        this.view_root = null;
    }

    loadBuild(view_root) {
        this.view_root = view_root;
        let $webview = document.createElement('webview');
        view_root.appendChild($webview);
        this.webview = $webview;
        this.onWindowResize();
        let url = this.viewName.slice(1);
        if (url.includes('tabsEntrust')) {
            this.gotoATGO();
        } else {
            $webview.src = url;
        }
    }

    setWebviewHeight() {
        this.webview.style.height = `${this.thisWindow.getSize()[1] - 60 - 30}px`;
    }

    onWindowResize() {

        this.setWebviewHeight();
        this.thisWindow.on('resize', () => {
            this.setWebviewHeight();
        });
    }

    gotoATGO() {
        let $loading = document.createElement('div');
        this.view_root.style.position = 'relative';
        this.view_root.appendChild($loading);
        let style = {
            height: `${this.thisWindow.getSize()[1] - 60 - 30}px`,
            position: 'absolute',
            top: 0,
            left: 0,
            width: '100 %',
            color: '#fff',
        };
        Object.keys(style).forEach(key => {
            $loading.style[key] = style[key];
        });
        $loading.innerText = '正在加载...';
        this.webview.style.height = 0;
        this.webview.src = this.viewName.slice(1);
        this.webview.preload = 'preload.js';
        this.webview.addEventListener('ipc-message', (event) => {

            if (event.channel == 'signed') {
                $loading.style.display = 'none';
                this.setWebviewHeight();
            } else {
                $loading.innerText = event.channel;
            }
        });
    }
}

module.exports = { AppInstance };
