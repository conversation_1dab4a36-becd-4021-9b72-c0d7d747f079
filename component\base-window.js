
const IView = require('./iview').IView;

class BaseWindow extends IView {

    /**
     * Creates an instance of BaseWindow.
     * @param {String} view_name
     * @param {Boolean} is_standalone_window
     * @param {*} title
     */
    constructor(view_name, is_standalone_window, title) {

        super(view_name, is_standalone_window, title);
        this.winStates = { showRestore: false, showMaximize: true, showClose: true };
        this.isUserSigninMode = (this.app.GeneralSettings || {}).signin != 'account';
        this._listen2CoreEvents();
    }

    _listen2CoreEvents() {

        var thisWindow = this.thisWindow;

        /**
         * listen to window size maximizing
         */
        thisWindow.on('maximize', () => {

            this.winStates.showRestore = true;
            this.winStates.showMaximize = false;
        });

        /**
         * listen to window size restoring
         */
        thisWindow.on('unmaximize', () => {

            this.winStates.showRestore = false;
            this.winStates.showMaximize = true;
        });

        /**
         * listen to window theming change
         */
        thisWindow.on('application-theme-change', (themeName) => {
            this.resetTheme(themeName);
        });
        
        /**
         * listen to global view routing map event
         */
        thisWindow.once(this.systemEvent.globalViewRouting, (routings) => {
            
            window['routings'] = routings;
            this.handleViewRouting();
        });

        /**
         * listen to window's focus/blur event
         */
        thisWindow.on(this.systemEvent.highlightWindow, (highlighted) => {
            
            var class_focused = 'focused-window';
            if (highlighted === true) {
                document.body.classList.add(class_focused);
            }
            else {
                document.body.classList.remove(class_focused);
            }
        });
    }

    /**
     * reset ui theme
     * @param {String} themeName 
     */
    resetTheme(themeName) {

        if (this._$theme === undefined) {
            this._$theme = document.getElementById('link-theme-stylesheet');
        }

        if (!(this._$theme instanceof HTMLLinkElement)) {
            return;
        }

        if (themeName == 'white') {
            this._$theme.href = '../asset/css/themed-white.css';
        }
        else if (themeName == 'blue') {
            this._$theme.href = '../asset/css/themed-blue.css';
        }
        else {
            this._$theme.href = '../asset/css/themed-dark.css';
        }
    }

    /**
     * handler for the event > [View Routing Map is set] 
     */
    handleViewRouting() {
        //
    }

    /**
     * brocast that this window is ready for rendering <to all subscribers>
     */
    brocastReady() {
        this.thisWindow.emit(this.systemEvent.renderWindowPrepared, this.thisWindow.id);
    }

    openDevTools() {
        this.thisWindow.webContents.toggleDevToolnt = document.querySelector('.win-top-content');s();
    }

    minimize() {
        this.thisWindow.minimize();
    }
    
    maximize() {
        this.thisWindow.maximize();
    }
    
    unmaximize() {
        this.thisWindow.unmaximize();
    }

    close() {
        this.thisWindow.close();
    }
}

module.exports = { BaseWindow };
