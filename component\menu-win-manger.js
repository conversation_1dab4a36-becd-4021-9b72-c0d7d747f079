const electron = require('electron');
const { BrowserWindow } = require('@electron/remote');
const { IView } = require('./iview');
const { Menu } = require('../model/menu');

class WindowInfo {

    /**
     * @param {Number} windowId 
     * @param {Number} menuId 
     */
    constructor(windowId, menuId) {

        this.windowId = windowId;
        this.menuId = menuId;
    }
}

class MenuWinManager {

    /**
     * @param {IView} hostView 
     */
    constructor(hostView) {

        this.hostView = hostView;
        this.winMap = {};
        this.locationMap = {};
    }

    get renderProcess() {
        return this.hostView.renderProcess;
    }

    get systemEvent() {
        return this.hostView.systemEvent;
    }

    /**
     * read/write a window's location & size info
     * @param {*} menuId 
     * @returns {{ x, y, width, height }} 
     */
    _locate(menuId, x, y, width, height) {
        
        if (typeof x == 'number') {
            this.locationMap[menuId] = { x, y, width, height };
        }
        else {
            return this.locationMap[menuId];
        }
    }

    /**
     * @returns {WindowInfo}
     */
    _seek(menuId) {
        return this.winMap[menuId];
    }

    /**
     * @param {WindowInfo} winInfo 
     */
    _tie(menuId, winInfo) {
        this.winMap[menuId] = winInfo;
    }

    _untie(menuId) {
        delete this.winMap[menuId];
    }

    _log(message) {
        console.log(message);
    }
    
    /**
     * 指定需要通过主窗口转交给当前菜单窗口的IPC信号
     * @param {String} ename 事件名称
     * @param {electron.BrowserWindow} menuWin 菜单窗口
     */
    _transmit(ename, menuWin) {

        const deliever = (event, ...args) => {

            menuWin.webContents.send(ename, ...args);
            this._log('to deliever message to menu window', ename, menuWin.id, args);
        };

        menuWin.on('closed', () => {

            this.renderProcess.removeListener(ename, deliever);
            this._log('menu window is closed', ename);
        });

        this.renderProcess.on(ename, deliever);
    }

    /**
     * @param {Menu} menu 
     * @param {Array<Number|String>} commands 
     */
    open(menu, commands = []) {
        
        const { menuId, menuName, viewLocation } = menu;
        const matched = this._seek(menuId);
        this._log(`to open menu window/${menuId}/${menuName}/${viewLocation}`);

        if (matched) {

            let winRef = BrowserWindow.fromId(matched.windowId);
            if (winRef && !winRef.isDestroyed()) {

                if (winRef.isMinimized()) {
                    winRef.restore();
                }
                
                winRef.focus();
                return;
            }
        }

        this.renderProcess.once(this.systemEvent.huntWinTabViewFromRender, (event, windowId) => {

            const winRef = BrowserWindow.fromId(windowId);
            const winfo = new WindowInfo(windowId, menuId);
            this._tie(menuId, winfo);

            winRef.on('closed', () => {

                this._untie(menuId);
                this._log(`menu window is closed/${menuId}/${menuName}/${viewLocation}`);
            });

            winRef.webContents.on('is-ready', () => {
                this._log(`menu window is ready/${menuId}/${menuName}/${viewLocation}`);
            });

            const moved = () => {
                
                let { x, y, width, height } = winRef.getBounds();
                this._locate(menuId, x, y, width, height);
            };

            winRef.on('moved', moved);
            winRef.on('close', () => { winRef.removeListener('moved', moved) });

            if (Array.isArray(commands) && commands.length > 0) {

                commands.forEach(ename => {
                    this._transmit(ename.toString(), winRef);
                });
            }
        });

        const woptions = {
            
            minWidth: 1000,
            minHeight: 600,
            width: 1200,
            height: 800,
            minimizable: true,
            maximizable: true,
            highlight: true,
        };

        const location = this._locate(menuId);
        if (location) {

            let { x, y, width, height } = location;
            woptions.x = x;
            woptions.y = y;
            woptions.width = width;
            woptions.height = height;
        }

        this.renderProcess.send(this.systemEvent.huntWinTabViewFromRender, viewLocation, menuName, woptions);
    }

    close(menuId) {
        
        const matched = this._seek(menuId);
        if (matched == undefined) {
            return;
        }

        this._untie(menuId);
        const winRef = BrowserWindow.fromId(matched.windowId);
        if (winRef && !winRef.isDestroyed()) {
            winRef.close();
        }
    }
}

module.exports = { MenuWinManager };