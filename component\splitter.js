
class SiblingRectangle {

    /**
     * 
     * @param { Number } previous_width 
     * @param { Number } next_width 
     * @param { Number } previous_height 
     * @param { Number } next_height 
     */
    constructor(previous_width, next_width, previous_height, next_height) {

        var total_width = previous_width + next_width;
        var total_height = previous_height + next_height;

        this.bywidth = { previous: 0, next: 0 };
        this.byheight = { previous: 0, next: 0 };

        if (total_width > 0) {

            this.bywidth.previous = previous_width / total_width;
            this.bywidth.next = next_width / total_width;
        }

        if (total_height > 0) {

            this.byheight.previous = previous_height / total_height;
            this.byheight.next = next_height / total_height;
        }
    }

    /**
     * clone a new instance by a given data structure
     * @param {*} struc 
     */
    static clone(struc) {

        var ins = new SiblingRectangle(0, 0, 0, 0);
        var keys = Object.keys(ins);
        keys.forEach(prop_name => { ins[prop_name] = struc[prop_name]; });
        return ins;
    }
}

function MakeDefaultOptions() {

    return {

        /**
         * 1. which direction does the splitter work through (values horizontal/vertical can be accepted)
         * 2. [optional]
         * 3. defaults to "horizontal"
         */
        layout: 'vertical',
    
        /**
         * [optional] previous sibling's minimum height (working for layout = vertical)
         */
        previousMinHeight: 0,
    
        /**
         * [optional] next sibling's minimum height (working for layout = vertical)
         */
        nextMinHeight: 0,
    
        /**
         * [optional] previous sibling's minimum width (working for layout = horizontal)
         */
        previousMinWidth: 0,
    
        /**
         * [optional] next sibling's minimum width (working for layout = horizontal)
         */
        nextMinWidth: 0,
    };
}

function MakeDefaultProfile() {

    return {
        bywidth: { previous: 0.5, next: 0.5 },
        byheight: { previous: 0.5, next: 0.5 },
    };
}

class Splitter {

    get isHorizontal() {
        
        if (this._isHorizontal === undefined) {
            this._isHorizontal = this.options.layout == this._layouts.horizontal;
        }

        return this._isHorizontal;
    }

    get isVertical() {

        if (this._isVertical === undefined) {
            this._isVertical = this.options.layout != this._layouts.horizontal;
        }

        return this._isVertical;
    }

    get rememberRequired() {
        return this.name !== null;
    }

    /**
     * @param { String } name name of the splitter (when provided the split result can be recovered by name)
     * @param { HTMLElement } $ele [required] dom element | dom element selector, which will be equipped as a splitter
     * @param { Function } callback [required] the callback function to be called when a splitter is dragged moved 
     * @param user_options 
     */
    constructor(name, $ele, callback, user_options = MakeDefaultOptions()) {

        if (!($ele instanceof HTMLElement)) {
            console.error('[$ele] is not an html element', $ele);
            return;
        }

        var $container = $ele.parentElement;
        if (!$container) {
            console.error('[$ele] has no parent node', $ele);
            return;
        }

        var $previous = $ele.previousSibling;
        var $next = $ele.nextSibling;

        if (!$previous || !$next) {
            console.error('[$ele] has no previous sibling or next sibling', $ele);
            return;
        }

        if (typeof callback !== 'function') {
            console.error('[callback] is not a function');
            return;
        }

        this.name = typeof name == 'string' && name.trim().length > 0 ? `splitter-${ name.trim() }` : null;
        this.$container = $container;
        this.$previous = $previous;
        this.$previous.style.boxSizing = 'border-box';
        this.splitterBarWH = 2;
        this.$splitter = $ele;
        this.$next = $next;
        this.$next.style.boxSizing = 'border-box';
        this.callback = typeof callback == 'function' ? callback : function () {};

        var latest_options = MakeDefaultOptions();
        var keys = Object.keys(latest_options);
        keys.forEach(prop_name => {
            if (user_options[prop_name] !== undefined) {
                latest_options[prop_name] = user_options[prop_name];
            }
        });

        this._layouts = { horizontal: 'horizontal', vertical: 'vertical' };
        if (latest_options.layout !== this._layouts.horizontal) {
            latest_options.layout = this._layouts.vertical;
        }

        this.states = { isSplitting: false };
        this.options = latest_options;
        this.handleResizeProxy = this.handleResize.bind(this);
        this.handleMouseUpProxy = this.handleMouseUp.bind(this);
        this.handleMouseMoveProxy = this.handleMouseMove.bind(this);
        this.handleSelectProxy = this.handleSelect.bind(this);
        this.customizeSplitter();
        this.bindSplitterEvents();
    }

    customizeSplitter() {

        var $container = this.$container;
        var $splitter = this.$splitter;

        $container.classList.add('xsplitter');
        $splitter.classList.add('splitter-bar');

        if (this.isVertical) {

            $splitter.style.height = this.splitterBarWH + 'px';
            $splitter.style.width = '100%';
            $container.classList.add('vertical');
        }
        else {

            $splitter.style.width = this.splitterBarWH + 'px';
            $splitter.style.height = '100%';
            $container.classList.add('horizontal');
        }
    }

    bindSplitterEvents() {

        if (this._hasBoundEvents) {
            return;
        }

        this._hasBoundEvents = true;
        this.$splitter.onmousedown = this.handleMouseDown.bind(this);
        window.addEventListener('resize', this.handleResizeProxy);
    }

    clearSelection() {

        if (document.body.createTextRange) {

            let range = document.body.createTextRange();
            range.collapse();
            range.select();
        } 
        else if (window.getSelection) {

            if (window.getSelection().empty) {
                window.getSelection().empty();
            }
            else if (window.getSelection().removeAllRanges) {
                window.getSelection().removeAllRanges();
            }
        }
        else if (document.selection) {
          document.selection.empty();
        }
    }

    handleResize() {
        this.sync();
    }

    simulateResize() {
        this.sync();
    }

    handleMouseUp() {

        this.states.isSplitting = false;
        this.unbindEvents();
    }

    handleMouseDown() {

        this.states.isSplitting = true;
        this.clearSelection();
        this.bindEvents();
    }

    handleMouseMove() {

        if (this.states.isSplitting) {
            this.clearSelection();
        }

        this.coordinate(event.pageX, event.pageY);
        this.save();
    }

    handleSelect() {
        event.preventDefault();
    }

    /**
     * to sync previous panel's height(width) & next panel's height(width) manually
     */
    sync() {

        var rects = this.$splitter.getClientRects();
        if (rects.length == 0) {
            return;
        }

        var splt_rect = rects[0];
        this.coordinate(splt_rect.x, splt_rect.y);
    }

    /**
     * adjust previous panel's height(width) & next panel's height(width) according to current splitter bar's position (expressed in [x, y])
     * @param { Number } splitter_bar_x 
     * @param { Number } splitter_bar_y 
     */
    coordinate(splitter_bar_x, splitter_bar_y) {
        
        var rects = this.$container.getClientRects();
        if (rects.length == 0) {
            return;
        }

        var container_rect = rects[0];
        var container_width = container_rect.width;
        var container_height = container_rect.height;
        var container_x = container_rect.x;
        var container_y = container_rect.y;

        if (this.isVertical) {

            let previous_height = splitter_bar_y - container_y;
            let next_height = container_height - previous_height - this.splitterBarWH;

            /**
             * 当前后之一的高度低于最低值，优先保证哪一个拥有最小高度
             */

            if (previous_height < this.options.previousMinHeight) {
                
                previous_height = this.options.previousMinHeight;
                next_height = Math.max(container_height - previous_height - this.splitterBarWH, this.options.nextMinHeight);
            }
            else if (next_height < this.options.nextMinHeight) {

                next_height = this.options.nextMinHeight;
                previous_height = Math.max(container_height - next_height - this.splitterBarWH, this.options.previousMinHeight);
            }
            
            this.$previous.style.height = previous_height + 'px';
            this.$next.style.height = next_height + 'px';
            this.callback(previous_height, next_height, this._layouts.vertical);
        }
        else if (this.isHorizontal) {

            let previous_width = splitter_bar_x - container_x;
            let next_width = container_width - previous_width - this.splitterBarWH;

            /**
             * 当前后之一的宽度低于最低值，优先保证哪一个拥有最小宽度
             */

            if (previous_width < this.options.previousMinWidth) {
                
                previous_width = this.options.previousMinWidth;
                next_width = Math.max(container_width - previous_width - this.splitterBarWH, this.options.nextMinWidth);
            }
            else if (next_width < this.options.nextMinWidth) {

                next_width = this.options.nextMinWidth;
                previous_width = Math.max(container_width - next_width - this.splitterBarWH, this.options.previousMinWidth);
            }

            this.$previous.style.width = previous_width + 'px';
            this.$next.style.width = next_width + 'px';
            this.callback(previous_width, next_width, this._layouts.horizontal);
        }
    }

    bindEvents() {

        document.addEventListener('mouseup', this.handleMouseUpProxy);
        document.addEventListener('mousemove', this.handleMouseMoveProxy);
        document.addEventListener('onselect', this.handleSelectProxy);
    }

    unbindEvents() {

        document.removeEventListener('mouseup', this.handleMouseUpProxy);
        document.removeEventListener('mousemove', this.handleMouseMoveProxy);
        document.removeEventListener('onselect', this.handleSelectProxy);
    }

    /**
     * save current splitting status
     */
    save() {

        if (!this.rememberRequired) {
            return;
        }
        
        var previous_width = this.$previous.offsetWidth;
        var previous_height = this.$previous.offsetHeight;
        var next_width = this.$next.offsetWidth;
        var next_height = this.$next.offsetHeight;
        localStorage[this.name] = JSON.stringify(new SiblingRectangle(previous_width, next_width, previous_height, next_height));
    }

    recover() {

        if (!this.rememberRequired) {
            console.error('splitter has no name assigned, nothing can be recovered');
            return;
        }

        var profile;
        try {
            profile = JSON.parse(localStorage[this.name]);
        }
        catch(ex) {
            profile = MakeDefaultProfile();
        }

        var setting = SiblingRectangle.clone(profile);

        if (this.isVertical && !(setting.byheight.previous > 0 || setting.byheight.next > 0)) {
            console.error('previous sibling / next sibling must have a height', setting);
            return;
        }

        if (this.isHorizontal && !(setting.bywidth.previous > 0 || setting.bywidth.next > 0)) {
            console.error('previous sibling / next sibling must have a width', setting);
            return;
        }

        var rects = this.$container.getClientRects();
        if (rects.length == 0) {
            return;
        }
        
        var container_rect = rects[0];
        var container_width = container_rect.width;
        var container_height = container_rect.height;
        var container_x = container_rect.x;
        var container_y = container_rect.y;

        var previous_width = setting.bywidth.previous * container_width;
        var previous_height = setting.byheight.previous * container_height;
        var sbar_x = container_x + previous_width;
        var sbar_y = container_y + previous_height;

        this.coordinate(sbar_x, sbar_y);
    }
}

module.exports = { Splitter };