
const electron = require('electron');
const interaction = require('../libs/interaction').interaction;
const systemEvent = require('../config/system-event').systemEvent;
const { Tab, TabOption } = require('./tab');
const helper = require('../libs/helper').helper;
const AppInstance = require('./app-instance').AppInstance;
const Routing = require('../model/routing').Routing;

const defaultTabOptions = {

    /**
     * if allow to close a tab (default = true)
     */
    allowCloseTab: true,

    /**
     * if hide the navigation bar, whne there is only one tab opened (default = true)
     */
    hideTab4OnlyOne: true,

    /**
     * if tab items are displayed in an embeded style (default = false)
     */
    embeded: false,

    /**
     * if a tab can be lazy loaded (default = true)
     */
    lazyLoad: true,

    /**
     * if a tab list toollit should be shown (default = true)
     */
    showToolkit: true,

    /**
     * navigation bar
     */
    $navi: document.createElement('div'),

    /**
     * container for hosting tab view content
     */
    $content: document.createElement('div'),

    /**
     * <function(tab): void> <optional> tab created callback
     */
    tabCreated: null,

    /**
     * <function(tab): void> <optional> tab focused callback
     */
    tabFocused: null,

    /**
     * <function(tab): void> <optional> tab unfocused callback
     */
    tabUnfocused: null,

    /**
     * <function(tab): void> <optional> tab closed callback
     */
    tabClosed: null,
};

defaultTabOptions.$navi = null;
defaultTabOptions.$content = null;

/**
 * component tab list
 */
class TabList {

    get routings() {

        if (this._routings instanceof Array) {
            return this._routings;
        }

        var routings = window['routings'];
        this._routings = [new Routing()];
        this._routings.pop();

        if (routings instanceof Array) {
            routings.forEach(rt_struc => {
                this._routings.push(Routing.duplicate(rt_struc));
            });
        }

        return this._routings;
    }

    constructor(options = defaultTabOptions) {

        if (!(options.$navi instanceof HTMLElement)) {
            throw new Error('[$navi] is not an html element');
        } 
        else if (!(options.$content instanceof HTMLElement)) {
            throw new Error('[$content] is not an html element');
        }

        /**
         * navigation bar for hosting all tabs
         */
        this.$navi = options.$navi;
        this.$navi.classList.add('tab-panel');
        this.$navi.classList.add('themed-color');

        if (options.embeded === true) {
            this.$navi.classList.add('embeded');
        }

        /**
         * tab content container
         */
        this.$content = options.$content;
        this.$content.classList.add('tabcontent-panel');

        this.allowCloseTab = options.allowCloseTab !== false;
        this.hideTab4OnlyOne = options.hideTab4OnlyOne !== false;
        this.lazyLoad = options.lazyLoad !== false;

        this.handlers = {

            created: options.tabCreated || function(tab) {},
            focused: options.tabFocused || function(tab) {},
            unfocused: options.tabUnfocused || function(tab) {},
            closed: options.tabClosed || function(tab) {},
        };

        this.id = `tab-list-${helper.makeToken()}`;
        this.tabs = [new Tab()].splice(1);

        /**
         * all opened tabs map (key: view name, value: Tab instance)
         */
        this.tabMap = {};
        this.focusedTab = this.tabs[0];
        this.classes = { selectedTab: 'selected', themedSelect: 'themed-selected' };
        if (typeof options.showToolkit == 'string') {
            this._buildNaviBar(options.showToolkit);
        }
        else if (options.showToolkit !== false) {
            this._buildNaviBar('refresh,export,config');
        }

        this._listen2CoreEvents();
    }

    /**
     * check if a tab is focused
     * @param {Tab} tab
     */
    isFocused(tab) {
        return tab instanceof Tab && tab === this.focusedTab;
    }

    /**
     * focus on a tab
     * @param {Tab} tab
     */
    setFocus(tab) {

        if (this.focusedTab === tab) {
            return;
        }

        var current = this.focusedTab;
        if (current) {

            current.$tabRoot.classList.remove(this.classes.selectedTab);
            current.$tabRoot.classList.remove(this.classes.themedSelect);
            current.hide();

            if (!current.isDisposed) {

                if (current.viewEngine.listenerCount(systemEvent.tabInactivated) > 0) {
                    current.viewEngine.trigger(systemEvent.tabInactivated, current);
                }

                try { this.handlers.unfocused(current); } catch(ex) { console.error(ex); }
            }
        }

        // set current tab as focused
        this._resetFocusedTab(tab);
        tab.$tabRoot.classList.add(this.classes.selectedTab);
        tab.$tabRoot.classList.add(this.classes.themedSelect);
        // show up content of the currently focused tab
        tab.show();

        if (tab.hasDrawn) {
            try { this.handlers.focused(tab); } catch(ex) { console.error(ex); }
        }

        if (tab.viewEngine.listenerCount(systemEvent.tabActivated) > 0) {
            tab.viewEngine.trigger(systemEvent.tabActivated, tab);
        }
    }

    /**
     * open a new tab (or switch to be focused if existing)
     * @param {Boolean} singleton <required> if allow to open up to 2 tabs with the same view name
     * @param {String} view_name <required> view name (standard forat as: @view/f1/f2/tmpl)
     * @param {String} title <required> title of the view
     * @param {*} view_option <optional> option for the tabbed view
     */
    openTab(singleton, view_name, title, view_option) {

        if (singleton === true) {

            var matched = this.tabMap[view_name];
            if (matched) {
                this.setFocus(matched);
                return;
            }
        }

        if (helper.isNone(view_option)) {
            view_option = { mark: 'view option is not present' };
        }

        var routing = this.routings.find(x => x.name == view_name);
        var tab_option = new TabOption(title);

        if (routing instanceof Routing) {
            this._addNewTab(false, routing, tab_option, view_option);
        } 
        else if (typeof view_name == 'string' && (view_name.startsWith('http://') || view_name.startsWith('https://'))) {
            
            let http_routing = new Routing(view_name, view_name);
            this._addNewTab(true, http_routing, tab_option, view_option);
        } 
        else {
            interaction.showAlert(`未检测到视图【${title}】模版，无法显示界面`);
        }
    }

    /**
     * @param {Tab} tab
     */
    closeView(tab) {

        if (this.tabs.length == 1) {
            interaction.showWarning('仅剩一个视图，不能关闭');
            return;
        }

        if (tab === this.focusedTab) {

            let matched_idx = this.tabs.findIndex(tab => tab === this.focusedTab);
            if (matched_idx < 0) {
                console.error('focused tab does not have a match in the view list', tab);
                return;
            }

            let next_one = null;
            // the last tab
            if (matched_idx == this.tabs.length - 1) {
                next_one = this.tabs[matched_idx - 1];
            } 
            else {
                next_one = this.tabs[matched_idx + 1];
            }

            this._destroyTab(tab);
            try { this.handlers.closed(tab); } catch(ex) { console.error(ex); }
            this.setFocus(next_one);
        } 
        else {
            this._destroyTab(tab);
        }
    }

    /**
     * @param {Tab} tab
     */
    show(tab) {
        tab.$tabRoot.style.removeProperty('display');
    }

    /**
     * @param {Tab} tab
     */
    hide(tab) {
        tab.$tabRoot.style.setProperty('display', 'none');
    }

    /**
     * fire an event on the focused tab
     * @param {String} event_name
     * @param  {...any} args
     */
    fireEventOnFocusedTab(event_name, ...args) {

        var is_focused = true;
        this.focusedTab.viewEngine.trigger(event_name, ...args, is_focused);
    }

    /**
     * fire an event on a given tab
     * @param {Tab} tab
     * @param {String} event_name
     * @param  {...any} args
     */
    fireEventOnTab(tab, event_name, ...args) {

        let is_focused = tab === this.focusedTab;
        tab.viewEngine.trigger(event_name, ...args, is_focused);
    }

    /**
     * fire an event on all tabs
     * @param {String} event_name
     * @param  {...any} args
     */
    fireEventOnAllTabs(event_name, ...args) {

        this.tabs.forEach(tab => {
            this.fireEventOnTab(tab, event_name, ...args);
        });
    }

    // standard operations

    refresh() {
        this.focusedTab.viewEngine.refresh();
    }

    export() {
        this.focusedTab.viewEngine.exportSome();
    }

    config() {
        this.focusedTab.viewEngine.config();
    }

    clone() {
        // this.currentTab.viewEngine.clone();
        interaction.showError('视图复制功能暂未完整支持');
    }

    _hanleViewCloneAnswer(tab_view_id, routing, view_options) {
        // interaction.showSuccess('视图已克隆为独立窗口');
    }

    /**
     * @param {Tab} tab
     */
    _resetFocusedTab(tab) {
        this.focusedTab = tab;
    }

    _isFirstTab() {
        return this.tabs.length <= 1;
    }

    /**
     * @param {Boolean} is_web_app <required>
     * @param {Routing} routing <required>
     * @param {TabOption} tab_option <optional>
     * @param {*} view_option <optional>
     */
    _addNewTab(is_web_app, routing, tab_option, view_option) {

        var tab = new Tab(routing, tab_option, view_option);
        var SomeModule;

        if (is_web_app) {
            SomeModule = AppInstance;
        } 
        else {
            try {
                SomeModule = require(tab.routing.script);
            } 
            catch (ex) {
                console.error(ex);
                interaction.showError(`指定TAB视图/${tab.options.title}，加载不成功，错误代码[01]`);
                return;
            }
        }

        var module_ins;
        try {
            module_ins = new SomeModule(tab.routing.name, false, tab.options.title);
        } 
        catch (ex) {
            
            console.error(ex);
            interaction.showError(`指定TAB视图/${tab.options.title}，加载不成功，错误代码[02]`);
            return;
        }

        // if (!(module_ins instanceof IView)) {
        //     console.error('expected module does not inherit from [IView]', SomeModule);
        //     interaction.showError(`指定TAB视图/${tab.options.title}，加载不成功，错误代码[03]`);
        //     return;
        // }

        tab.setContainer(this);
        tab.setLib(module_ins);
        this.tabs.push(tab);
        this.tabMap[routing.name] = tab;

        this._handleTabCountChange();

        // append tab onto navigator bar
        this.$navi.appendChild(tab.$tabRoot);
        // construct the tab & bind events
        this._constructTab(tab);
        // insert tab content root into content area
        this.$content.appendChild(tab.$tabContent);

        if (this._isFirstTab() || !this.lazyLoad) {

            // set as the currently focused on
            this.setFocus(tab);
            // load & build the view
            tab.draw();
        }
    }

    /**
     * @param {Tab} tab
     */
    _constructTab(tab) {

        var $inner = document.createElement('span');
        $inner.classList.add('tab-inner');
        $inner.innerText = tab.options.title;
        tab.$tabRoot.appendChild($inner);

        tab.$tabRoot.onclick = () => {

            this.setFocus(tab);
            !tab.hasDrawn && tab.draw();
        };

        if (this.allowCloseTab) {

            let $close = document.createElement('a');
            $close.className = 'tab-close el-icon-close';
            $close.title = '关闭';
            $close.onclick = e => {

                e.stopPropagation();
                this.closeView(tab);
            };

            $inner.appendChild($close);
        }
    }

    _handleTabCountChange() {

        if (this.hideTab4OnlyOne && this.tabs.length <= 1) {

            this._showToolkit(false);
            this.$navi.style.display = 'none';
            // this.$content.style.height = '100%';
        } 
        else {

            this._showToolkit(true);
            this.$navi.style.display = 'block';
            // this.$content.style.height = '100%';
        }
    }

    /**
     * @param {Tab} tab
     */
    _destroyTab(tab) {

        delete this.tabMap[tab.routing.name];
        this.tabs.remove(x => x === tab);
        this._handleTabCountChange();
        this.$navi.removeChild(tab.$tabRoot);
        tab.close();
    }

    /**
     * @param {String} settings 
     */
    _buildNaviBar(settings) {

        var members = settings.split(',').map(x => x.trim());
        var hasRefresh = members.some(x => x == 'refresh');
        var hasExport = members.some(x => x == 'export');
        var hasConfig = members.some(x => x == 'config');

        this.$navi.innerHTML = `<span class="tab-operation button-group s-unselectable">
                                        ${ hasRefresh ? '<a class="tab-oper-refresh themed-hover-color iconfont icon-shuaxin" title="刷新"></a>' : '' }
                                        ${ hasExport ? '<a class="tab-oper-export themed-hover-color iconfont icon-daochu1" title="导出数据"></a>' : '' }
                                        ${ hasConfig ? '<a class="tab-oper-config themed-hover-color iconfont icon-shezhi11" title="配置"></a>' : '' }
                                    </span>`;

        // <a class="tab-oper-clone themed-hover-color iconfont icon-fuzhi" title="克隆视图"></a>

        var $toolkit = this.$navi.querySelector('.tab-operation');
        
        if (hasRefresh) {
            $toolkit.querySelector('.tab-oper-refresh').onclick = this.refresh.bind(this);
        }

        if (hasExport) {
            $toolkit.querySelector('.tab-oper-export').onclick = this.export.bind(this);
        }

        if (hasConfig) {
            $toolkit.querySelector('.tab-oper-config').onclick = this.config.bind(this);
        }

        // $toolkit.querySelector('.tab-oper-clone').onclick = this.clone.bind(this);

        // tool button box
        this.$toolkit = $toolkit;
    }

    _showToolkit(visible) {
        
        if (this.$toolkit) {
            this.$toolkit.style.display = visible ? 'block' : 'none';
        }
    }

    _listen2CoreEvents() {
        
        /**
         * clone a tab view into a seperated window
         */
        electron.ipcRenderer.on(systemEvent.huntWinTabViewFromRender, (event, tab_view_id, routing, view_options) => {
            this._hanleViewCloneAnswer(tab_view_id, routing, view_options);
        });
    }
}

module.exports = { TabList };
