<!DOCTYPE html>
<html>

<head>
	<title>Dashboard</title>
	<meta http-equiv="pragma" content="no-cache" />
	<meta http-equiv="cache-control" content="no-cache" />
	<meta http-equiv="expires" content="0" />
	<meta charset="utf-8" />
	<link rel="stylesheet" href="../asset/css/3rd/iconfont/iconfont.css" />
	<link rel="stylesheet" href="../asset/css/3rd/element-ui.css" />
	<link rel="stylesheet" href="../asset/css/3rd/vxe/vxe-table.css" />
	<link rel="stylesheet" href="../asset/css/smart-table.css" />
	<link rel="stylesheet" href="../asset/css/common.css" id="link-common-stylesheet" />
	<link rel="stylesheet" href="../asset/css/module-elem-ui.css" />
	<link rel="stylesheet" href="../asset/css/module-splitter.css" />
	<link rel="stylesheet" href="../asset/css/module-business.css" />
	<link rel="stylesheet" href="../asset/css/module-tab.css" />
	<link rel="stylesheet" href="../asset/css/module-toolbar.css" />
	<link rel="stylesheet" href="../asset/css/themed-dark.css" id="link-theme-stylesheet" />
	<link rel="stylesheet" href="win-dashboard.css" />
</head>

<body class="win-body with-menu-on themed-bg s-border-box">

	<div class="win-header s-border-box">

		<div class="win-buttons">
			<template>
				<span class="s-unselectable">
					<!-- <a @click="lockWindow" class="s-opacity-7 s-opacity-hover iconfont icon-suoding" title="锁屏"></a> -->
					<el-dropdown size="mini" @command="handleUserCommand" class="user-choice">
						<el-button type="default" size="mini">
							{{ userName }} <i class="el-icon-arrow-down el-icon--right"></i>
						</el-button>
						<el-dropdown-menu slot="dropdown">
							<el-dropdown-item command="logout">注销</el-dropdown-item>
							<el-dropdown-item v-if="isUserSigninMode" command="modify-password">修改密码</el-dropdown-item>
						</el-dropdown-menu>
					</el-dropdown>
					<a @click="minimize" class="s-opacity-7 s-opacity-hover iconfont icon-zuixiaohua" title="最小化"></a>
					<a @click="maximize" v-show="winStates.showMaximize" class="s-opacity-7 s-opacity-hover iconfont icon-zuidahua" title="最大化"></a>
					<a @click="unmaximize" v-show="winStates.showRestore" class="s-opacity-7 s-opacity-hover iconfont icon-huanyuan" title="还原"></a>
					<a @click="close" class="s-opacity-7 s-opacity-hover iconfont icon-guanbi" title="关闭"></a>
				</span>
			</template>
		</div>

		<div class="win-title s-dragable s-unselectable">
			<span class="title-label">君弘高御</span>
		</div>

	</div>

	<div v-show="states.expanded" class="win-side s-border-box">

		<template>

			<div class="win-side-inner s-full-height themed-box">

				<div class="collapser themed-bottom-border s-cp s-opacity-8 s-opacity-hover" @click="toggleShowMenu">
					<a class="s-cp">收起菜单</a>
					<i class="iconfont icon-shouqi"></i>
				</div>

				<div class="menu-root s-border-box s-unselectable">

					<template v-for="(menu, menu_idx) in menus">

						<div class="menu-item menu-level-1 s-opacity-5 s-opacity-hover"
							 :class="states.selectedLevel1 && states.selectedLevel1.menuId === menu.menuId ? 'selected' : ''"
							 @click="handleLevel1MenuClick(menu)">
	
							<a class="menu-inner">
								<i class="menu-icon" :class="menu.menuIcon"></i>
								<span class="menu-name">{{ menu.menuName }}</span>
								<a v-if="menu.children.length > 0" class="menu-toggler">
									<i v-if="menu.expanded" class="iconfont icon-xia"></i>
									<i v-else class="iconfont icon-you"></i>
								</a>
							</a>

						</div>

						<div v-if="menu.children.length > 0" v-show="menu.expanded" class="sub-menu-box themed-bg">

							<div v-for="(sub_menu, sub_menu_idx) in menu.children"
								 @click="handleLevel2MenuClick(sub_menu)"
								 class="menu-item menu-level-2 s-opacity-5 s-opacity-hover"
								 :class="states.selectedLevel2 && states.selectedLevel2.menuId === sub_menu.menuId ? 'themed-highlighted' : ''">

								<a class="menu-inner">
									<i class="menu-icon"></i>
									<span class="menu-name">{{ sub_menu.menuName }}</span>
								</a>

							</div>

						</div>
					</template>
				</div>
			</div>
		</template>
	</div>

	<div class="win-footer">
		<template>
			<div class="about-server s-pull-left">{{ about.serverName }}</div>
			<div class="about-user s-pull-left">{{ about.userName }} / {{ about.fullName }}</div>
			<div class="about-trading-day s-pull-left" v-if="!!about.tradingDay">交易日{{ about.tradingDay }}</div>
			<div class="about-version s-pull-left" v-if="!!about.version">{{ about.version }}</div>
			<div class="about-logout s-pull-left s-color-red s-cp" @click="logout">注销</div>
			<div class="network-status s-pull-right" :class="network.type">
				<span class="content">
					<i :class="network.icon"></i>
					{{ network.content }}
				</span>
				<span class="pingpong" :title="network.pingpongTitle">{{ network.pingpong }}</span>
			</div>
		</template>
	</div>

	<div class="win-content s-border-box s-scroll-bar">
		<!-- menu toggle show crumb -->
		<a class="menu-expander themed-box s-opacity-8 s-opacity-hover" title="展开菜单">
			<i class="iconfont icon-zhankai"></i>
		</a>
		<div class="tab-placeholder">
			<div class="win-top-tabs"></div>
		</div>
		<div class="win-top-content s-border-box"></div>
	</div>

	<div class="window-locker" :class="displaying ? 'visible' : ''">
		<template>
			<div class="shadow">
				<div class="content">
					<div class="unlock-form">
						<label class="title">软件已锁定，请解锁</label>
						<el-input type="password" placeholder="请输入登录密码" v-model.trim="passcode" @keyup.enter.native="unlockScreen"></el-input>
						<el-button type="primary" size="small" @click="unlockScreen">解锁</el-button>
						<el-button type="danger" size="small" @click="exit">退出程序</el-button>
					</div>
				</div>
			</div>
		</template>
	</div>

	<script type="text/javascript" src="../libs/3rd/vue.js"></script>
	<script type="text/javascript" src="../libs/3rd/element-ui.js"></script>
	<script type="text/javascript" src="../libs/3rd/xe-utils.js"></script>
	<script type="text/javascript" src="../libs/3rd/vxe-table.js"></script>
	<script type="text/javascript" src="../libs/type-extension.js"></script>
	<script type="text/javascript" src="../libs/3rd/jquery.min.js"></script>
	<script type="text/javascript" src="../libs/3rd/jquery-ui-1.13.1/external/jquery/jquery.js"></script>
	<script type="text/javascript" src="../libs/3rd/jquery-ui-1.13.1/jquery-ui.min.js"></script>
	<script type="text/javascript" src="win-dashboard-entry.js"></script>

</body>

</html>