const algorithmTypeEnum = {
  定时定量: {
    label: '定时定量',
    value: 101,
    desp: '每隔N秒，根据实时盘口选择的价格，单笔卖出M%的仓位，总卖出的仓位为Q%',
  },
  低封单量: {
    label: '低封单量',
    value: 103,
    desp: '当封单量低于V万手或金额低于M万元时，根据实时盘口选择的价格，一笔卖出Q%仓位比例的股票',
  },
  定时跟量: {
    label: '定时跟量',
    value: 107,
    desp: '每隔N秒，根据实时盘口选择的价格，单笔卖出盘口对应M%比例的数量，总卖出的仓位为Q%',
  },
  定涨定量: {
    label: '定涨定量',
    value: 108,
    desp: '当股票的涨幅达到M%时，根据实时盘口选择的价格，每上涨n%卖出Q%仓位比例的股票',
  },
};

function MakeLevels(count) {
  var levels = [];
  for (let idx = 1; idx <= count; idx++) {
    levels.push({
      label: '买' + idx,
      value: idx,
    });
  }
  return levels;
}

const fullParamsEnum = {
  封单数量: {
    default: 1,
    label: '封单数量',
    prop: 'strategyVolume',
    unit: '万手',
    type: 'number',
    min: 0,
    max: 99999999,
    times: 10000,
    desp: '封单对应的数量，单位万手；封单数量和封单金额二者选填其一',
    isBoard: true,
    rules: [
      {
        required: true,
        message: '封单数量不能为空',
        trigger: 'blur',
      },
    ],
    alter: '封单金额',
    show: true,
  },
  封单金额: {
    default: 1,
    label: '封单金额',
    prop: 'strategyAmount',
    unit: '亿',
    type: 'number',
    min: 0,
    max: 99999999,
    times: 10000,
    desp: '封单对应的金额，单位亿；封单数量和封单金额二者选填其一',
    isBoard: true,
    rules: [
      {
        required: true,
        message: '封单金额不能为空',
        trigger: 'blur',
      },
    ],
    alter: '封单数量',
    show: false,
  },
  卖出仓位: {
    default: 50,
    label: '卖出仓位',
    prop: 'positionPercent',
    unit: '%',
    type: 'number',
    min: 1,
    max: 100,
    desp: '总可卖数量的比例；百分比，取值1%-100%之间，例如：50%表示半仓',
    rules: [
      {
        required: true,
        message: '卖出仓位不能为空',
        trigger: 'blur',
      },
    ],
  },
  低封单量基准价格: {
    default: 1,
    label: '基准价格',
    prop: 'priceFollowType',
    type: 'select',
    options: MakeLevels(5),
    desp: '实盘盘口对应的档位，作为卖出的委托价格；可选择买一到买五',
    rules: [
      {
        required: true,
        message: '基准价格不能为空',
        trigger: 'change',
      },
    ],
  },
  价格偏移: {
    default: undefined,
    label: '价格偏移',
    isBoard: true,
    prop: 'priceOffset',
    type: 'number',
    desp: `在基准价格的基础上，增加或减少对应单位的数值；一个单位为0.01；
    例如：基准价格设置买一（10元），价格偏移设置-8，则对应的委托价格
    为10 + （-8）*0.01 = 9.92元，若9.92元低于笼子下限价格，则以笼子下限
    价格作为委托价`,
    rules: [
      {
        trigger: 'blur',
        validator: (rule, value, callback) => {
          if (value === null || value === undefined) {
            return callback();
          }
          const reg = /^-?\d+$/;
          if (!reg.test(value)) {
            return callback(new Error('请输入正负整数'));
          }
          return callback();
        },
      },
    ],
  },
  定涨定量基准涨幅: {
    default: 5,
    label: '基准涨幅',
    prop: 'raiseRateLimit',
    unit: '%',
    type: 'number',
    min: -20,
    max: 20,
    desp: `涨幅到达时，策略启动；百分比，取值-20%-20%之间`,
    isBoard: true,
    rules: [
      {
        required: true,
        message: '基准涨幅不能为空',
        trigger: 'blur',
      },
    ],
  },
  基准涨幅: {
    default: undefined,
    label: '基准涨幅',
    prop: 'raiseRateLimit',
    unit: '%',
    type: 'number',
    min: -20,
    max: 20,
    desp: `涨幅到达时，策略启动；百分比，取值-20%-20%之间`,
    isBoard: true,
  },
  涨幅间隔: {
    default: 0.5,
    label: '涨幅间隔',
    prop: 'raiseOffset',
    unit: '%',
    type: 'number',
    min: 0,
    max: 20,
    desp: `在基准涨幅的基础上，涨幅超过间隔，对应下单一次。百分比，取值0%-20%之间`,
    isBoard: true,
    rules: [
      {
        required: true,
        message: '涨幅间隔不能为空',
        trigger: 'blur',
      },
    ],
  },
  单笔仓位: {
    default: 5,
    label: '单笔仓位',
    prop: 'strategyRate',
    unit: '%',
    type: 'number',
    min: 0,
    max: 100,
    desp: '总可卖数量的比例；百分比，取值1%-100%之间，例如：5%表示一次卖出5%的仓位',
    rules: [
      {
        required: true,
        message: '单笔仓位不能为空',
        trigger: 'blur',
      },
    ],
    alter: '单笔数量',
    show: true,
    isBoard: true,
  },
  定涨定量单笔仓位: {
    default: 5,
    label: '单笔仓位',
    prop: 'strategyRate',
    unit: '%',
    type: 'number',
    min: 1,
    max: 100,
    desp: '总可卖数量的比例；百分比，取值1%-100%之间，例如：5%表示一次卖出5%的仓位',
    rules: [
      {
        required: true,
        message: '单笔仓位不能为空',
        trigger: 'blur',
      },
    ],
    isBoard: true,
  },
  基准价格: {
    default: 1,
    label: '基准价格',
    prop: 'priceFollowType',
    type: 'select',
    options: [{ label: '卖1', value: -1 }].concat(MakeLevels(5)),
    desp: '实盘盘口对应的档位，作为卖出的委托价格；可选择买一到买五，卖一',
    rules: [
      {
        required: true,
        message: '基准价格不能为空',
        trigger: 'change',
      },
    ],
  },
  时间间隔: {
    default: 1,
    label: '时间间隔',
    prop: 'strategyDelayTime',
    unit: '秒',
    type: 'number',
    min: 0.5,
    max: 99999999,
    times: 1000,
    isBoard: true,
    desp: '每隔多少秒卖出一次；单位为秒，最小值为0.5',
    rules: [
      {
        required: true,
        message: '时间间隔不能为空',
        trigger: 'blur',
      },
    ],
  },
  单笔数量: {
    default: 100,
    label: '单笔数量',
    prop: 'strategyVolume',
    unit: '手',
    type: 'number',
    min: 0,
    max: 99999999,
    times: 100,
    desp: '单笔卖出的数量；单位为手；单笔数量或单笔仓位二选一',
    rules: [
      {
        required: true,
        message: '单笔数量不能为空',
        trigger: 'blur',
      },
    ],
    alter: '单笔仓位',
    show: false,
    isBoard: true,
  },
  未成撤单: {
    default: undefined,
    label: '未成撤单',
    prop: 'cancelProtectedTime',
    unit: '秒',
    type: 'number',
    min: 1,
    max: 99999999,
    desp: '单笔卖单报出后，N秒内未成交，自动撤单；单位秒，最小值为1',
    isCancel: true,
    change: (paramForm) => {
      paramForm.cancelProtectedEnabled = !!paramForm.cancelProtectedTime;
    },
  },
  启用未成撤单: {
    default: false,
    label: '启用未成撤单',
    prop: 'cancelProtectedEnabled',
    isCancel: true,
    show: false,
  },
  涨幅上限: {
    default: undefined,
    label: '涨幅上限',
    isBoard: true,
    prop: 'upLimit',
    unit: '%',
    type: 'number',
    min: -20,
    max: 20,
    desp: `涨幅超过时，策略暂停；百分比，取值-20%-20%之间`,
  },
  涨幅下限: {
    default: undefined,
    label: '涨幅下限',
    isBoard: true,
    prop: 'downLimit',
    unit: '%',
    type: 'number',
    min: -20,
    max: 20,
    desp: `涨幅低于时，策略暂停；百分比，取值-20%-20%之间`,
  },
  风控跌幅: {
    default: undefined,
    label: '风控跌幅',
    isBoard: true,
    prop: 'riskRate',
    unit: '%',
    type: 'number',
    min: 1,
    max: 5,
    desp: `以第一笔卖单的成交价为基准，后续股价相对基准下跌超过M%时，策略暂停；百分比，取值1%-5%之间`,
  },
  盘口比例: {
    default: 10,
    label: '盘口比例',
    prop: 'strategyRate',
    unit: '%',
    type: 'number',
    min: 1,
    max: 30,
    desp: `根据实时盘口，按照对应的比例确认单次卖出的数量。百分比，取值1%-30%之间；
    例如：假设买一的盘口数量为1000手，买二为2000手，设置价格为买二对应1%的比例为（1000+2000）*1% = 30手；
    设置价格为买一对应1%的比例为（1000）*1% = 10手`,
    isBoard: true,
    rules: [
      {
        required: true,
        message: '盘口比例不能为空',
        trigger: 'blur',
      },
    ],
  },
};

const fullParams = {
  [algorithmTypeEnum.定时定量.value]: [
    fullParamsEnum.时间间隔,
    fullParamsEnum.单笔仓位,
    fullParamsEnum.单笔数量,
    fullParamsEnum.卖出仓位,
    fullParamsEnum.基准价格,
    fullParamsEnum.价格偏移,
    fullParamsEnum.未成撤单,
    fullParamsEnum.启用未成撤单,
    fullParamsEnum.基准涨幅,
    fullParamsEnum.涨幅上限,
    fullParamsEnum.涨幅下限,
    fullParamsEnum.风控跌幅,
  ],
  [algorithmTypeEnum.低封单量.value]: [fullParamsEnum.封单数量, fullParamsEnum.封单金额, fullParamsEnum.卖出仓位, fullParamsEnum.低封单量基准价格, fullParamsEnum.价格偏移],
  [algorithmTypeEnum.定时跟量.value]: [
    fullParamsEnum.时间间隔,
    fullParamsEnum.盘口比例,
    fullParamsEnum.卖出仓位,
    fullParamsEnum.基准价格,
    fullParamsEnum.价格偏移,
    fullParamsEnum.未成撤单,
    fullParamsEnum.启用未成撤单,
    fullParamsEnum.基准涨幅,
    fullParamsEnum.涨幅上限,
    fullParamsEnum.涨幅下限,
    fullParamsEnum.风控跌幅,
  ],
  [algorithmTypeEnum.定涨定量.value]: [
    fullParamsEnum.定涨定量基准涨幅,
    fullParamsEnum.涨幅间隔,
    fullParamsEnum.定涨定量单笔仓位,
    fullParamsEnum.卖出仓位,
    fullParamsEnum.基准价格,
    fullParamsEnum.价格偏移,
  ],
};

module.exports = {
  algorithmTypeEnum,
  fullParamsEnum,
  fullParams,
};
