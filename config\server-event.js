﻿/**
 * server event definition
 */

const serverEvent = {
    /*
     * 服务器通信有关的事件
     */

    heartBeat: 1,
    pong: 8,
    serverError: 11014,
    loginTradingServerAnswered: 20000,
    loginQuoteServerAnswered: 20000,
    logoutTradingServerAnswered: 11100,

    /*
     * 与tick有关的事件
     */

    // 订阅回执
    subscribeTickReceived: 20006,
    // 退订回执
    unsubscribeTickReceived: 20007,
    // tick变化推送（持续性）
    tickPriceChanged: 30004,

    /*
     * 与交易有关的事件
     */

    // 下单回执
    orderReceived: 20001,
    // 撤单回执
    cancelOrderReceived: 20002,
    // 订单变化推送（持续性）
    orderChanged: 30001,
    // 成交变化推送（持续性）
    exchangeChanged: 30002,
    // 持仓变化推送（持续性）
    positionChanged: 30003,

    /*
     * 券池管理有关
     */

    tradableStockChanged: 30015,

    /*
     * 其他事件
     */

    accountStatusChanged: 30005,
    riskAlertReceived: 30006,
    forcedKickOut: 30010,
    tokenGenerated: 20008,
    KlineChanged: 30011,

    /* 指令审核 */
    notifyInstruction: 30012,
    serverJudgeResult: 20016,

    /** 今日订单推送 */
    todayOrderPush: 20030,
    /** 今日成交推送 */
    todayTradeRecordPush: 20031,
    /** 今日持仓推送 */
    todayPositionPush: 20032,

    /** 账号权益数据推送 */
    accountEquityPush: 20033,
    /** 账号持仓数据推送 */
    accountPositionPush: 20034,
    /** 批量订单处理结果推送 */
    motherOrderResult: 20035,
    /** 批量订单数据推送 */
    motherOrderPush: 20022,
};

module.exports = { serverEvent };
