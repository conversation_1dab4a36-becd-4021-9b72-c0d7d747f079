/**
 * trading related enumerations
 */

const systemTrdEnum = {

    tradingDirection: {

        buy: { code: 1, mean: '买入' },
        sell: { code: -1, mean: '卖出' },
    },

    positionEffect: {

        start: { code: -1, mean: 'start' },
        open: { code: 0, mean: '开仓' },
        close: { code: 1, mean: '平仓' },
        force: { code: 2, mean: '强平' },
        closeToday: { code: 3, mean: '平今' },
        closeYesterday: { code: 4, mean: '平昨' },
        forceDecrease: { code: 5, mean: '强减' },
        localClose: { code: 6, mean: '本地强平' },
        end: { code: 7, mean: 'end' },
    },

    creditTrading: {

        buy: { code: 1, direction: 1, mean: '普通买入' },
        creditBuy: { code: 2, direction: 1, mean: '融资买入' },
        creditBuyClose: { code: 3, direction: 1, mean: '买券还券' },

        sell: { code: 1, direction: -1, mean: '普通卖出' },
        creditSell: { code: 2, direction: -1, mean: '融券卖出' },
        creditSellClose: { code: 3, direction: -1, mean: '卖券还款' },
    },

    futureTradingDirection: {

        buyOpen: { code: 1, mean: '买开' },
        buyClose: { code: 2, mean: '买平' },
        buyCloseToday: { code: 3, mean: '买平今' },
        sellOpen: { code: 4, mean: '卖开' },
        sellClose: { code: 5, mean: '卖平' },
        sellCloseToday: { code: 6, mean: '卖平今' },
    },

    businessFlag: {

        normal: { code: 0, mean: '普通买卖' },
        gage: { code: 1, mean: '担保品买卖' },
        credit: { code: 2, mean: '融资融券' },
        close: { code: 3, mean: '还款还券' },
        closeWithSecurity: { code: 4, mean: '现券还券' },
        closeWithCapital: { code: 5, mean: '直接还款' },
    },

    market: {

        shse: { code: 'SHSE', mean: '上交所' },
        szse: { code: 'SZSE', mean: '深交所' },
        cffex: { code: 'CFFEX', mean: '中金所' },
        shfe: { code: 'SHFE', mean: '上期所' },
        czce: { code: 'CZCE', mean: '郑商所' },
        dce: { code: 'DCE', mean: '大商所' },
        ine: { code: 'INE', mean: '能源所' },
    },

    hedgeFlag: {

        Start: { code: 0, mean: 'Start' },
        Speculate: { code: 1, mean: '投机' },
        Profit: { code: 2, mean: '套利' },
        ForceTrade: { code: 3, mean: '强平' },
        End: { code: 4, mean: 'End' },
    },

    tickType: {

        simpleTick: 4,
        simpleTickExp: 'simple',
        tick: 5,
        tickExp: 'tick',
        transaction: 60,
        transactionExp: 'transaction',
        orderQueue: 70,
        orderQueueExp: 'queue',
        ztymd: 71,
        ztymdExp: 'preplaced',
        front: 72,
        frontExp: 'front',
        frontCancel: 73,
        frontCancelExp: 'front-cancel',
        kline: 20,
        klineExp: 'kline-has-no-relevant-tick-type',
    },

    basketMethod: {

        volume: { code: 1, mean: '按数量篮子', label: '数量', unit: '篮', isEtfOk: true },
        weight: { code: 2, mean: '按权重篮子', label: '金额', unit: '元', isEtfOk: false },
        weight2Account: { code: 3, mean: '按权重篮子与账号比例', label: '金额', unit: '元', isEtfOk: false },
        weight2Asset: { code: 4, mean: '按权重篮子与资产比例', label: '比例', unit: '%', isEtfOk: false },
    },

    bidingStages: [

        { code: 11, mean: '最新价' },
        { code: 12, mean: '涨停价' },
        { code: 13, mean: '跌停价' },

        { code: 5, mean: '对方五档' },
        { code: 4, mean: '对方四档' },
        { code: 3, mean: '对方三档' },
        { code: 2, mean: '对方二档' },
        { code: 1, mean: '对方一档' },

        { code: 6, mean: '本方一档' },
        { code: 7, mean: '本方二档' },
        { code: 8, mean: '本方三档' },
        { code: 9, mean: '本方四档' },
        { code: 10, mean: '本方五档' },
    ],

    pricingType: {

        fixedPrice: { code: 1, mean: '限价单' },
        marketPrice: { code: 2, mean: '市价单' },
        simulated: { code: 3, mean: '模拟单' },
    },

    positionShare: [

        { code: 1, mean: '系统默认数量' },
        { code: 2, mean: '指定数量' }
    ],

    shortcutTypes: [

        { code: 1, mean: '股票' }, 
        { code: 2, mean: '期货/期权' }
    ],
};

module.exports = { systemTrdEnum };