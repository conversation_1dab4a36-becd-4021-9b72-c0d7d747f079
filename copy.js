const fs = require('fs');
const stat = fs.stat;
const excludes = ['.git', '.vscode', '.babelrc', '.eslintrc.json', 'logs', 'TEMP', 'PB', 'mock-data', 'user-data', 'view-src', 'copy.js', 'minify.js', 'gulpfile.js'];
const exists = function(src, dst, callback) {
    fs.exists(dst, function(exists) {
        exists
            ? callback(src, dst)
            : fs.mkdir(dst, function() {
                  callback(src, dst);
              });
        1;
    });
};
const sleep = async function(delay_ms) {
    return new Promise((resolve, reject) => {
        setTimeout(() => {
            resolve();
        }, delay_ms);
    });
};

const copy = function(src, dst) {
    fs.readdir(src, async function(err, paths) {
        if (err) {
            throw err;
        }
        for (let path of paths) {
            if (excludes.every(item => !path.includes(item))) {
                let _src = src + '/' + path;
                let _dst = dst + '/' + path;
                stat(_src, function(err, st) {
                    if (err) {
                        throw err;
                    }
                    if (st.isFile()) {
                        let readable = fs.createReadStream(_src);
                        let writable = fs.createWriteStream(_dst);
                        readable.pipe(writable);
                    } else if (st.isDirectory()) {
                        exists(_src, _dst, copy);
                    }
                });
            }
            await sleep(30);
        }
    });
};

console.log('copying files...');
exists(__dirname, './TEMP', copy);
console.log('copying files done!');
