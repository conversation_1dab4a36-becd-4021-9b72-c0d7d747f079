/**
 * localized http request assistant
 */

const electron = require('electron');
const axios = require('axios').default;
const queryString = require('querystring');
const { isRenderProcess } = require('../config/environment');
const app = isRenderProcess ? require('@electron/remote').app : electron.app;
const serverInfo = app.contextData.serverInfo;
const resetfulBaseUrl = serverInfo.restfulServer;
const quoteRestfulServer = serverInfo.quoteRestfulServer;
const indayServer = serverInfo.indayServer;
const contextUserId = app.contextData.userInfo.userId;
const SystemSetting = require('../config/system-setting').systemSetting;
const interaction = require('./interaction').interaction;
const { getHttpLogger } = require('../libs/logging');
const httpLogger = getHttpLogger();

const moduleStates = {
    lastErrorTime: null,
};

function getLatestToken() {
    return app.contextData.userInfo.token;
}

function describeData(data) {

    if (data === null) {
        return 'null';
    }
    
    if (data === undefined) {
        return 'undefined';
    }

    const threshold = 30;
    const type = typeof data;

    if (Array.isArray(data)) {

        return data.length <= threshold ? data: {

            all: data.length,
            previews: data.slice(0, threshold),
        };
    }
    
    if (type == 'string' || type == 'number' || type == 'boolean'|| type == 'bigint') {
        return data;
    }
    
    if (type == 'object') {

        /**
         * 内含列表：data = { list: [], ... }
         */

        const { list } = data;

        if (Array.isArray(list)) {
            
            if (list.length <= threshold) {
                return data;
            }
            else {

                /**
                 * 限制内含的列表大小
                 */

                const duplicated = Object.assign({}, data);
                duplicated.list = {

                    all: list.length,
                    previews: list.slice(0, threshold),
                };

                return duplicated;
            }
        }
        
        const limit_keys = 100;
        const keys = Object.keys(data);
        
        if (keys.length <= limit_keys) {
            return data;
        }
        else {

            const partial = {};
            keys.slice(0, limit_keys).forEach(key => { partial[key] = data[key]; });
            return { partial, keys_count: keys.length };
        }
    }

    return data;
}

const http = axios.create({

    baseURL: resetfulBaseUrl,
    timeout: SystemSetting.httpTimeout,
    withCredentials: false,
    headers: {
        'X-Requested-With': 'XMLHttpRequest',
        Accept: 'application/json',
        'Content-Type': 'application/json',
    },
    transformRequest: [
        (data, headers) => {
            if (headers['Content-Type'] === 'application/json') {
                return JSON.stringify(data);
            } else if (headers['Content-Type'] === 'multipart/form-data') {
                return data;
            }

            return queryString.stringify(data);
        },
    ],
});

http.interceptors.request.use(

    function (config) {
        
        if (!config.params) {
            config.params = {};
        }

        const { url, method, headers, params, data } = config;
        params['appplt'] = 'web';
        params['token'] = getLatestToken();
        params['user_id'] = contextUserId;
        params['_'] = new Date().getTime();

        if (params._token) {

            headers['Authorization'] = `token ${params._token}`;
            delete params._token;
            delete params.token;
            delete params.user_id;
            delete params.appplt;
        }
        
        let cloned_params = Object.assign({}, params);
        delete cloned_params._;
        delete cloned_params._token;
        delete cloned_params.user_id;
        delete cloned_params.appplt;
        delete cloned_params.token;
        httpLogger.info(`[REQUEST]/[method=${method}]/[url=${url}] > ${JSON.stringify({ params, body: data })}`);

        return config;
    },

    function (error) {

        httpLogger.error(`[REQUEST_ERROR]`, error.message);
        Promise.reject(error);
    },
);

http.interceptors.response.use(

    function (response) {
        
        const { config, headers, status, statusText, data } = response;
        const { method, url, params } = config;
        const content_length = headers['content-length'];
        const { err, msg, errorCode, errorMsg, data: bizData } = data;
        const start_ts = (params || {})._;
        const end_ts = Date.now();
        const cost = typeof start_ts == 'number' && start_ts > 0 ? end_ts - start_ts : null;
        const dataDesc = describeData(bizData);
        const log_resp = { err, msg, errorCode, errorMsg, dataDesc };
        const metas = [method, status, statusText, cost, content_length].join(',');
        httpLogger.info(`[RESPONSE]/[metas=${metas}]/[url=${url}] > ${JSON.stringify(log_resp)}`);
        return response;
    },

    function (error) {
        
        var status = (error.request || error.response).status;
        var error_msg = null;

        if (error.message == 'Network Error') {
            error_msg = '网络连接错误';
        }

        if (status >= 500) {
            error_msg = `服务器未正确响应请求，错误码/${status}`;
        } 
        else if (status >= 400) {
            error_msg = `数据请求产生错误，错误码/${status}`;
        }

        var ts = new Date().getTime();
        if (error_msg !== null && (moduleStates.lastErrorTime === null || ts - moduleStates.lastErrorTime > 5000)) {

            moduleStates.lastErrorTime = ts;
            interaction.showHttpError(error_msg);
        }

        if (error.response) {
            
            let { config, response } = error;
            let { method, url } = config;            
            let { status, data } = response;
            httpLogger.error(`[RESPONSE_ERROR]/[method=${method}]/[status=${status}]/[url=${url}]`);
        }
        else {
            httpLogger.error(`[NETWORK_ERROR]`, error.message);
        }

        return Promise.reject({ httpCode: error.request.status, message: (error.response || {}).statusText });
    },
);

http.exAddr = {

    indayServer: indayServer,
    quoteRestfulServer: quoteRestfulServer,
};

module.exports = { http };
