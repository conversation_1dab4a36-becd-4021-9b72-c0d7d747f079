﻿/**
 * this module provides different ways of UI interaction
 */

const helper = require('./helper').helper;

function bubbleMsg(options, extra_options) {

    var api = (window.Vue || window.Object).prototype.$message;

    if (!helper.isJson(extra_options)) {
        extra_options = {};
    }

    //options.offset = typeof extra_options.offset == 'number' ? extra_options.offset : 36;
    options.position = extra_options.position || 'bottom-right';
    options.customClass = `customized-notify-message ${extra_options.customClass || ''}`;
    options.duration = typeof extra_options.duration == 'number' ? extra_options.duration : options.duration;
    options.showClose = typeof extra_options.showClose == 'boolean' ? extra_options.showClose : false;
    options.dangerouslyUseHTMLString = typeof extra_options.dangerouslyUseHTMLString == 'boolean' ? extra_options.dangerouslyUseHTMLString : false;
    options.center = typeof extra_options.center == 'boolean' ? extra_options.center : true;

    api(options);
}

function showAlert(args) {

    if (args.length == 0) {
        return;
    }

    var arg1 = args[0];
    var arg2 = args[1];
    var merged_options = {};

    if (typeof arg1 == 'string') {

        merged_options.message = arg1;
        merged_options.callback = typeof arg2 == 'function' ? arg2 : null;
    } 
    else if (helper.isJson(arg1)) {

        merged_options.type = arg1.type;
        merged_options.title = arg1.title;
        merged_options.message = arg1.message;
        merged_options.customClass = arg1.customClass || 'customized-alert';
        merged_options.callback = typeof arg2 == 'function' ? arg2 : typeof arg1.callback == 'function' ? arg1.callback : null;
    } 
    else {
        return;
    }

    var msgbox_api = (window.Vue || window.Object).prototype.$msgbox;

    msgbox_api({

        showClose: false,
        customClass: merged_options.customClass,
        type: merged_options.type || 'info',
        closeOnPressEscape: false,
        closeOnClickModal: false,
        confirmButtonText: '好的',
        title: merged_options.title || '系统提示',
        message: merged_options.message,
        dangerouslyUseHTMLString: true,
        callback: function(action) {

            if (typeof merged_options.callback === 'function' && action === 'confirm') {
                merged_options.callback();
            }
        },
    });
}

function setWindowAsBlockedByConfirm(isBlock) {
    window._isWindowBlockedByConfirm = !!isBlock;
}

function delaySetUnblockStatus() {
    setTimeout(() => { setWindowAsBlockedByConfirm(false); }, 100);
}

function showConfirm(args) {

    if (args.length == 0 || !helper.isJson(args[0])) {

        setWindowAsBlockedByConfirm(false);
        return;
    }

    setWindowAsBlockedByConfirm(true);

    var arg = args[0];
    var do_nothing = function() {};
    var options = {

        title: arg.title,
        message: arg.message,
        confirm_callback: typeof arg.confirmed == 'function' ? arg.confirmed : do_nothing,
        cancel_callback: typeof arg.canceled == 'function' ? arg.canceled : do_nothing,
        confirmButtonText: arg.confirmButtonText,
        customClass: arg.customClass,
    };

    var api = (window.Vue || window.Object).prototype.$confirm;

    if (typeof api == 'function') {

        api(options.message, options.title || '操作确认', {

            dangerouslyUseHTMLString: true,
            customClass: options.customClass + ' lighted-box',
            confirmButtonText: options.confirmButtonText || '确定',
            cancelButtonText: '取消',
            type: options.type || 'warning',
            showClose: options.showClose == undefined ? true : !!options.showClose,
            closeOnPressEscape: options.closeOnPressEscape == undefined ? true : !!options.closeOnPressEscape,
            closeOnClickModal: options.closeOnClickModal == undefined ? false : !!options.closeOnClickModal,
			cancelButtonClass: null,
        })

        .then(function() {

            options.confirm_callback(true);
            delaySetUnblockStatus();
        })
        .catch(function() {

            options.cancel_callback();
            delaySetUnblockStatus();
        });
    }
    else {

        if (window.confirm(options.message)) {

            options.confirm_callback(true);
            delaySetUnblockStatus();
        }
        else {

            options.cancel_callback();
            delaySetUnblockStatus();
        }
    }
}

const interaction = {

    showMessage: function(message, extra_options) {
        bubbleMsg({ type: 'info', title: '提示', message: message, duration: 1000 }, extra_options);
    },

    showInfo: function(message, extra_options) {
        bubbleMsg({ type: 'info', title: '提示', message: message, duration: 1000 }, extra_options);
    },

    showSuccess: function(message, extra_options) {
        bubbleMsg({ type: 'success', title: '成功提示', message: message, duration: 1500 }, extra_options);
    },

    showWarning: function(message, extra_options) {
        bubbleMsg({ type: 'warning', title: '警告', message: message, duration: 2000 }, helper.extend({ showClose: true }, extra_options));
    },

    showError: function(message, extra_options) {
        bubbleMsg({ type: 'error', title: '操作错误', message: message, duration: 2500 }, helper.extend({ showClose: true }, extra_options));
    },

    showHttpError: function(message, extra_options) {
        bubbleMsg({ type: 'error', title: '远程数据错误', message: message, duration: 3000 }, helper.extend({ showClose: true }, extra_options));
    },

    notify: function(options) {
        (window.Vue || window.Object).prototype.$notify(options);
    },

    // inter-page alert
    showAlert: function() {
        showAlert(arguments);
    },

    //inter-page loading (will mask up the full page screen)
    showLoading: function(options) {
        
        var api = (window.Vue || window.Object).prototype.$loading;
        return api(options);
    },

    // inter-page confirm
    showConfirm: function() {
        showConfirm(arguments);
    },

    /**
     * @param {String} message <required> 输入框提示文字
     * @param {{ 
     * title: String, 
     * inputPlaceholder: String,
     * confirmButtonText: String, 
     * cancelButtonText: String, 
     * customClass: String,
     * inputPattern: RegExp, 
     * inputErrorMessage: String,
     * inputValidator: Function,
     * }} options 
     * @param {Function} callback 
     */
    propmt: function (message, options, callback) {

        if (!helper.isJson(options)) {
            options = {};
        }

        var api = (window.Vue || window.Object).prototype.$prompt;
        options.customClass = `${options.customClass || ''} lighted-box`;
        if (typeof options.confirmButtonText != 'string') { options.confirmButtonText = '确定'; }
        if (typeof options.cancelButtonText != 'string') { options.cancelButtonText = '取消'; }
        if (typeof options.inputErrorMessage != 'string') { options.inputErrorMessage = '输入错误'; }
        api(message || '请输入内容', options.title || '数据录入', options)
        .then(value => { callback(value); });
    }
};

module.exports = { interaction };
