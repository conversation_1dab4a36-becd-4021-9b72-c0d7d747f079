const path = require('path');
const fs = require('fs');

function captureStockCode() {

    const fpath = path.join(process.cwd(), 'plugin/ths/loader.dll');
    
    if (!fs.existsSync(fpath)) {
        return '';
    }

    const { execSync } = require('child_process');
    const buffer = execSync(`${fpath}`);
    return buffer.length > 0 ? buffer.toString().trim() : '';
}

module.exports = { captureStockCode };