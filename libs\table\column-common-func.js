const { SmartTable } = require('./smart-table');
const { systemEnum } = require('../../config/system-enum');
const { systemTrdEnum } = require('../../config/system-enum.trading');
const { helperUi } = require('../helper-ui');
const { BizHelper } = require('../helper-biz');

function formatDateOrTime(val, format_str) {

    if(val instanceof Date) {
        return val.format(format_str);
    }
    else if(typeof val == 'number') {
        return new Date(val).format(format_str);
    }
    else if(typeof val == 'string') {
        return val.length <= 8 ? val : new Date(val).format(format_str);
    }
    else if(val === undefined || val === null) {
        return '';
    }
    else {
        return val.toString();
    }
}

const ColumnCommonFunc = {

    // for [formatter] 
    
    number2Str: function (row_data, field_value, field_name) {
        return typeof field_value == 'number' ? field_value.toFixed(2) : field_value;
    },

    thousands: function (row_data, field_value, field_name) {
        return typeof field_value == 'number' ? field_value.thousands() : field_value;
    },

    thousandsDecimal: function (row_data, field_value, field_name) {
        return typeof field_value == 'number' ? field_value.thousandsDecimal() : field_value;
    },

    formatPrice(row_data, price, field_name) {

        if (typeof price != 'number') {
            return price;
        }

        return price.toFixed(BizHelper.getPricePrecision(row_data.instrument));
    },

    formatDateTime: function (row_data, field_value, field_name) {
        return formatDateOrTime(field_value, 'yyyy-MM-dd hh:mm:ss');
    },

    formatDate: function (row_data, field_value, field_name) {
        return formatDateOrTime(field_value, 'yyyy-MM-dd');
    },

    formatTime: function (row_data, field_value, field_name) {
        return formatDateOrTime(field_value, 'hh:mm:ss');
    },

    formatAssetType: function (row_data, field_value, field_name) {
        return helperUi.formatAssetType(field_value);
    },

    formatDirection: function (row_data, field_value, field_name) {
        return helperUi.makeDirectionHtml(field_value, row_data.assetType, row_data.positionEffect);
    },

    formatDirectionText: function (row_data, field_value, field_name) {
        return helperUi.makeDirectionText(field_value, row_data.assetType, row_data.positionEffect);
    },

    formatBusinessFlag(row_data, field_value, field_name) {
        return helperUi.makeBusinessFlagText(field_value);
    },

    formatOrderStatus: function (row_data, field_value, field_name) {

        var status_html = helperUi.makeOrderStatusHtml(field_value);
        return typeof row_data.errorMsg == 'string' ? `<span title="${row_data.errorMsg}">${status_html}</span>` : status_html;
    },

    formatOrderStatusText: function (row_data, field_value, field_name) {
        return helperUi.makeOrderStatusText(field_value);
    },

    formatYesNo: function (row_data, field_value, field_name) {
        return helperUi.makeYesNoLabelHtml(field_value);
    },

    formatYesNoText: function(row_data, field_value, field_name) {
        return helperUi.makeYesNoLabelText(field_value);
    },

    // for [filter rebinder]

    rebindDirection(records, propName) {
        return SmartTable.MakeColFilters(records, propName, systemTrdEnum.tradingDirection);
    },

    rebindOrderStatus(records, propName) {
        return SmartTable.MakeColFilters(records, propName, systemEnum.orderStatus);
    },

    rebindBusinessFlag(records, propName) {
        return SmartTable.MakeColFilters(records, propName, systemTrdEnum.businessFlag);
    },
    
    rebindAssetType(records, propName) {
        return SmartTable.MakeColFilters(records, propName, systemEnum.assetsTypes);
    },
    
    rebindYesNo(records, propName) {
        return SmartTable.MakeColFilters(records, propName, systemEnum.yesNo);
    },

    // for [class-maker]

    makeBenefitClass: function (field_value, row_data) {
        return field_value > 0 ? 's-color-red' : field_value < 0 ? 's-color-green' : '';
    },

    getCompletementCellClass: function (field_value, row_data) {
        return field_value === true ? 'cell-completed-order' : 'cell-uncompleted-order';
    },
}

module.exports = { ColumnCommonFunc };
