/*
    main process base class
*/

const electron = require('electron');
const BrowserWindow = electron.BrowserWindow;
const UserInfo = require('../model/user-info').UserInfo;
const systemEnum = require('../config/system-enum').systemEnum;
const systemTrdEnum = require('../config/system-enum.trading').systemTrdEnum;
const systemUserEnum = require('../config/system-enum.user').systemUserEnum;
const systemEvent = require('../config/system-event').systemEvent;
const systemIndayEvent = require('../config/system-event.inday').systemIndayEvent;
const serverEvent = require('../config/server-event').serverEvent;
const serverFunction = require('../config/server-function-code').serverFunction;
const localSetting = require('../config/system-setting.local').LocalSetting;
const Server = require('./module-comm/server').Server;
const logging = require('../libs/logging');
const dataKey = require('./app-data-key').dataKey;
const helper = require('../libs/helper').helper;

class MainModule {

    /**
     * major windows
     */

    get loginWindow() {

        var win_id = this.getContextDataItem(this.dataKey.loginWindowId);
        return BrowserWindow.fromId(parseInt(win_id));
    }

    get centralWindow() {

        var win_id = this.getContextDataItem(this.dataKey.centralWindowId);
        if (typeof win_id != 'number' && typeof win_id != 'string') {
            return null;
        }

        return BrowserWindow.fromId(parseInt(win_id));
    }

    /**
     * major user data & flags
     */

    get userInfo() {

        var usr_info = this.getContextDataItem(this.dataKey.userInfo);
        if (!(usr_info instanceof UserInfo)) {
            throw new Error('user info is still not initialized');
        }
        return usr_info;
    }

    // server<connection information> list

    get isDisconnectedByLogout() {
        return this.getContextDataItem(this.dataKey.disconnectedByLogout) === true;
    }

    get isDisconnectedTradingServerAccidently() {
        return this.getContextDataItem(this.dataKey.disconnectedTradingServerAccidently) === true;
    }

    get isDisconnectedQuoteServerAccidently() {
        return this.getContextDataItem(this.dataKey.disconnectedQuoteServerAccidently) === true;
    }

    get workingDir() {
        return __dirname;
    }

    constructor(module_name) {

        this.electron = electron;
        this.app = electron.app;
        this.mainProcess = electron.ipcMain;
        this.moduleName = module_name;

        // strongly recommended not to retrieve data from this context object directly
        // introduced to provide an entrance on main class or other main module classes
        this._appData = this.app.contextData;

        this.helper = helper;
        this.logging = logging;
        this.loggerConsole = logging.getConsoleLogger();
        this.loggerSys = logging.getSystemLogger();
        this.loggerTrading = logging.getTradingLogger();

        this.systemEnum = systemEnum;
        this.systemTrdEnum = systemTrdEnum;
        this.systemUserEnum = systemUserEnum;
        this.systemIndayEvent = systemIndayEvent;
        this.systemEvent = systemEvent;
        this.serverEvent = serverEvent;
        this.serverFunction = serverFunction;
        this.dataKey = dataKey;
        this.localSetting = localSetting;
    }

    getTradingServerInfo() {
        var trading_server = this.getContextDataItem(this.dataKey.serverInfo).tradingServer;
        return { ip: trading_server.ip, port: trading_server.port };
    }

    getQuoteServerInfo() {
        var quote_server = this.getContextDataItem(this.dataKey.serverInfo).quoteServer;
        return { ip: quote_server.ip, port: quote_server.port };
    }

    getLoginUserInput() {
        return this.getContextDataItem(this.dataKey.logInInput);
    }

    /**
     * get/set data item in the namespace: electron.app
     */

    getAppDataItem(key) {
        return typeof key == 'string' || typeof key == 'number' ? this.app[key] : undefined;
    }

    /**
     * get/set data item in the namespace: electron.app.contextData
     */

    getContextDataItem(key) {
        return typeof key == 'string' || typeof key == 'number' ? this._appData[key] : undefined;
    }

    setContextDataItem(key, value) {

        if (typeof key != 'string' && typeof key != 'number') {
            console.error(`app context data key [${key}] is not an valid key`);
            return false;
        }
        this._appData[key] = value;
        return true;
    }
}

class ServerEnvMainModule extends MainModule {

    get quoteServer() {

        // if (!this._quoteServer) {
        //     this._quoteServer = this.app.serverManager.quoteServer;
        // }
        // if (!(this._quoteServer instanceof Server)) {
        //     throw new Error('quote server is not allocated');
        // }
        // return this._quoteServer;

        return this.tradingServer;
    }

    get tradingServer() {

        if (!this._tradingServer) {
            this._tradingServer = this.app.serverManager.tradingServer;
        }
        if (!(this._tradingServer instanceof Server)) {
            throw new Error('trading server is not allocated');
        }
        return this._tradingServer;
    }

    constructor(module_name) {
        super(module_name);
    }
}

module.exports = { MainModule, ServerEnvMainModule };
