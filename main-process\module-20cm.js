﻿const ServerEnvMainModule = require('./main-module').ServerEnvMainModule;
const { Cm20FunctionCodes } = require('../config/20cm');

class HitBoardModule extends ServerEnvMainModule {

    constructor(module_name) {
        super(module_name);
    }

    handleChange(message) {
        
        let body = message.body;
        try {
            let data = JSON.parse(body);
            this.centralWindow.webContents.send(message.fc.toString(), data, message.reqId);
        }
        catch(ex) {

            /**
             * 将产生异常的数据包信息，写入日志文件
             */
            this.loggerSys.fatal(`20cm data change, json parse exception/${JSON.stringify(message)}/${body.toString()}`);
        }
    }

    listen2Messages() {

        var { reply, notify, auto } = Cm20FunctionCodes;
        [reply, notify, auto].forEach(map => {

            for (let key in map) {
                this.tradingServer.listen2Event(map[key] + '', this.handleChange.bind(this));
            }
        });
    }

    listen2Events() {

        this.mainProcess.on(this.systemEvent.sysLoadingCompleted, event => {

            if (this.isLoaded) {
                return;
            }

            this.isLoaded = true;
            this.listen2Messages();
        });
    }

    run() {

        this.loggerSys.info('load module 20cm > begin');
        this.listen2Events();
        this.loggerSys.info('load module 20cm > end');
    }
}

module.exports = { HitBoardModule };
