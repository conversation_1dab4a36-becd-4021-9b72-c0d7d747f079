﻿const ServerEnvMainModule = require('./main-module').ServerEnvMainModule;

class AuditOrderModule extends ServerEnvMainModule {

    constructor(module_name) {

        super(module_name);
        this.ordersPool = [];
        
        /**
         * 存放推送过来，待审核的订单
         */
        this.map = {};
    }

    getIdentitfier(order) {
        return order.id;
    }

    /**
     * @param {*} instruction_status
     * @param {Array<Number>} orders 要提交审核的订单ID
     * @param {Boolean} is_all 是否处理当前所有待审核的订单
     */
    submitAudit(instruction_status, orders, is_all) {

        if (is_all) {
            for (let ord_id in this.map) {
                if (orders.indexOf(ord_id) < 0) {
                    orders.push(ord_id);
                }
            }
        }

        // 将该批次审核的订单指令，从map中剔除
        orders.forEach(ord_id => {
            delete this.map[ord_id];
        });

        if (orders.length == 0) {
            return;
        }

        // 组织发送数据
        let body = { instructionIds: orders, instructionStatus: instruction_status };
        let message = { fc: this.serverFunction.auditOrder, reqId: 0, dataType: 1, body: body };
        this.tradingServer.send(message);
    }

    handleInstruction(message_package) {

        let new_order = JSON.parse(message_package.body.toString());
        this.map[this.getIdentitfier(new_order)] = new_order;

        /**
         * 当前审核视图暂位于主窗口之内，故消息发往主窗口
         */
        this.centralWindow.webContents.send(this.systemEvent.notifyInstruction, new_order);
    }

    handleFeedback(message_package) {

        /**
         * feedback仅包含 error codee & error message, 并不能标识为某一个订单的处理结果
         * 故，对该通知不作处理
         */
        // let audit_feedback = JSON.parse(message_package.body);
    }

    listen2Events() {
        
        // 监听来自界面的订单审核指令
        this.mainProcess.on(this.systemEvent.auditOrder, (event, { status, orders, isAll }) => {

            if (this._listened2ServerData == undefined) {

                this._listened2ServerData = true;
                // 监听来自服务器的待审核订单通知
                this.tradingServer.listen2Event(this.serverEvent.notifyInstruction, this.handleInstruction.bind(this));
                // 监听来自服务器的订单审核反馈
                this.tradingServer.listen2Event(this.serverEvent.serverJudgeResult, this.handleFeedback.bind(this));
            }

            let msg_content = `audit order > ${ status } / ${ JSON.stringify(orders) } / ${ isAll }`;
            this.loggerConsole.debug(msg_content);
            this.loggerSys.debug(msg_content);
            this.submitAudit(status, orders, isAll);
        });
    }

    run() {

        this.loggerSys.info('load module order auditing > begin');
        this.listen2Events();
        this.loggerSys.info('load module order auditing > end');
    }
}

module.exports = { AuditOrderModule };
