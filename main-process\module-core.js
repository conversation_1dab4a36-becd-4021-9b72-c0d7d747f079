﻿/*
    deal with core system messages
*/

const MainModule = require('./main-module').MainModule;

class CoreModule extends MainModule {

    constructor(module_name) {
        super(module_name);
    }

    exitApp(event) {

        this.loggerSys.info('before exiting app');
        this.loggerSys.info('----------------------');
        this.app.quit();
    }

    handleAppError(event, title, message) {
        this.electron.dialog.showErrorBox(title, message);
    }

    handleAppException(error) {

        // this.electron.dialog.showErrorBox('MAIN PROCESS ERROR', '系统错误：主程序发生异常，错误信息可查看当前路径/logs里日志文件。');
        this.loggerConsole.error(error.message);
        this.loggerConsole.error(error.stack);
        this.loggerSys.fatal('unexpected exception happened: ' + error.message + '\n' + error.stack);
    }

    listen2Commands() {

        this.app.on('window-all-closed', this.exitApp.bind(this));
        this.mainProcess.on(this.systemEvent.exitApp, this.exitApp.bind(this));
        this.mainProcess.on(this.systemEvent.showErrorDialog, this.handleAppError.bind(this));
        process.on('uncaughtException', this.handleAppException.bind(this));
    }

    run() {

        this.loggerSys.info('the current client version id = ' + ((this.app.contextData || {}).appVersion || '[none]'));
        this.loggerSys.info('load module core > begin');
        this.listen2Commands();
        this.loggerSys.info('load module core > end');
    }
}

module.exports = { CoreModule };