﻿const ServerEnvMainModule = require('./main-module').ServerEnvMainModule;

class PipeServerModule extends ServerEnvMainModule {

    constructor(module_name) {
        super(module_name);
    }

    startServer() {

        const net = require('net');
        const fs = require('fs');
        const pipe_file = process.platform === 'win32' ? '\\\\.\\pipe\\pipe_xtrade_surfing' : '/tmp/unix.sock';
        const server = net.createServer(conn => {

            conn.on('close', () => { console.log('pipe server: disconnected'); });
            conn.on('error', (ex) => { console.error(ex); });
            conn.on('data', (data) => {
                this.centralWindow && this.centralWindow.webContents.send('pipe-stock-quote', data);
            });
        });

        try {
            fs.unlinkSync(pipe_file);
        }
        catch (ex) {
            // console.error(ex);
        }

        server.listen(pipe_file);
    }

    check2StartServer() {

        var got_locked = this.app.requestSingleInstanceLock();
        if (got_locked) {
            this.startServer();
        }
        else {
            this.loggerSys.warn('module pipe: not the first application instance, not allowed to start pipe message server');
        }
    }

    run() {

        this.loggerSys.info('load module pipe > begin');
        this.check2StartServer();
        this.loggerSys.info('load module pipe > end');
    }
}

module.exports = { PipeServerModule };
