﻿const { ServerEnvMainModule } = require('./main-module');
const systemEvent = require('../config/system-event').systemEvent;

class TradeServerModule extends ServerEnvMainModule {

    constructor(module_name) {
        super(module_name);
    }

    listen2AccountEquityPush() {

        this.tradingServer.listen2Event(this.serverEvent.accountEquityPush, (message_package) => {

            let equities = JSON.parse(message_package.body);
            this.centralWindow.webContents.send(this.serverEvent.accountEquityPush.toString(), equities);
        });
    }

    listen2AccountPositionPush() {

        this.tradingServer.listen2Event(this.serverEvent.accountPositionPush, (message_package) => {

            let positions = JSON.parse(message_package.body);
            this.centralWindow.webContents.send(this.serverEvent.accountPositionPush.toString(), positions, message_package.reqId);
        });
    }

    listen2MotherOrderPush() {

        this.tradingServer.listen2Event(this.serverEvent.motherOrderResult, (message_package) => {

            let mother_orders = JSON.parse(message_package.body);
            this.centralWindow.webContents.send(this.serverEvent.motherOrderResult.toString(), mother_orders);
        });

        this.tradingServer.listen2Event(this.serverEvent.motherOrderPush, (message_package) => {

            let mother_orders = JSON.parse(message_package.body);
            this.centralWindow.webContents.send(this.serverEvent.motherOrderPush.toString(), mother_orders);
        });
    }

    listen2RiskMsgPush() {

        this.tradingServer.listen2Event(this.serverEvent.riskAlertReceived, (message_package) => {

            let msg = JSON.parse(message_package.body);
            this.centralWindow.webContents.send(this.serverEvent.riskAlertReceived.toString(), msg);
        });
    }

    run() {

        this.mainProcess.on(this.systemEvent.sysLoadingCompleted, (event) => {

            if (this.isLoaded) {
                return;
            }

            this.isLoaded = true;
			
			this.listen2AccountEquityPush();
            this.listen2AccountPositionPush();
            this.listen2MotherOrderPush();
            this.listen2RiskMsgPush();
		});
    }
}

module.exports = { TradeServerModule };
