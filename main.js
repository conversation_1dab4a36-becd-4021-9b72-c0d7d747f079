/**
 * 1. $ npm install --save @electron/remote;
 * 2. @electron/remote/main must be initialized in the main process before it can be used from the renderer;
 * 3. require('electron').remote in the renderer process must be replaced with require('@electron/remote');
 * 4. In electron >= 14.0.0, you must use the new enable API to enable the remote module for each desired WebContents separately:
 * require("@electron/remote/main").enable(webContents).
 */
require('@electron/remote/main').initialize();
// extend basic types
require('./libs/type-extension');
// app global setting
require('./main-process/app-setting');

const { app } = require('electron');
app.commandLine.appendSwitch('ignore-certificate-errors');


const { CoreModule } = require('./main-process/module-core');
new CoreModule('main-module.core').run();

// communication with server
const { CommModule } = require('./main-process/module-comm');
new CommModule('main-module.comm').run();

// file order
const { FileOrderModule } = require('./main-process/module-file-order');
new FileOrderModule('main-module.file-order').run();

const { TradeServerModule } = require('./main-process/module-trade-server');
new TradeServerModule('main-module.trade-server').run();

const { TradbaleListModule } = require('./main-process/module-tradablelist-module');
new TradbaleListModule('main-module.tradablelist').run();

// management of windows' lifecycle
const { WinFactoryModule } = require('./main-process/module-win-factory');
const WinFactoryModuleIns = new WinFactoryModule('main-module.win-factory');
WinFactoryModuleIns.run();

// UI system management
const { UIModule } = require('./main-process/module-ui');
new UIModule('main-module.ui', WinFactoryModuleIns.routings).run();

// account service
const { AccountServiceModule } = require('./main-process/module-account-service');
new AccountServiceModule('main-module.account-service').run();

// tick service
const { TickServiceModule } = require('./main-process/module-tick-service');
new TickServiceModule('main-module.tick-service').run();

// assistance
const { ExtensionModule } = require('./main-process/module-extension');
new ExtensionModule('main-module.extension').run();

// order auditing
const { AuditOrderModule } = require('./main-process/module-audit-order');
new AuditOrderModule('main-module.audit-order').run();

// 20cm
const { HitBoardModule } = require('./main-process/module-20cm');
new HitBoardModule('main-module.hit-board').run();

// safety
const { SafetyModule } = require('./main-process/module-safety');
new SafetyModule('main-module.safety').run();

// pipe extension
const { PipeServerModule } = require('./main-process/module-pipe-server');
new PipeServerModule('main-module.pipe-server').run();

process.env.ELECTRON_DISABLE_SECURITY_WARNINGS = true;