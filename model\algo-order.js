
/**
 * 算法单
 */
class AlgoOrder {

    /**
     * @param {*} struc 
     */
    constructor(struc) {

        this.accountId = struc.accountId;
        this.afterAction = struc.afterAction;
        this.algoParam = JSON.stringify(struc.algoParam);
        this.algorithmMappingId = struc.algorithmMappingId;
        this.algorithmName = struc.algorithmName;
        this.algorithmStatus = struc.algorithmStatus;
        this.algorithmType = struc.algorithmType;
        this.direction = struc.direction;
        this.effectiveTime = struc.effectiveTime;
        this.expireTime = struc.expireTime;
        this.fundName = struc.fundName;
        this.accountName = struc.accountName;
        this.id = struc.id;
        this.identityId = struc.identityId;
        this.instrument = struc.instrument;
        this.instrumentName = struc.instrumentName;
        this.limitAction = struc.limitAction;
        this.orderedVolume = struc.orderedVolume;
        this.supplierName = struc.supplierName;
        this.tradePrice = struc.tradePrice;
        this.tradedVolume = struc.tradedVolume;
        this.userId = struc.userId;
        this.volume = struc.volume;
        this.taskName = struc.taskName;

        /**
         * algorithmStatus 值如下：
         * 0：进行中
         * 1：已完成
         * 2：已撤销
         */

        this.isCompleted = this.algorithmStatus != 0;
    }
}

module.exports = { AlgoOrder };