
/**
 * 篮子交易，任务结构
 */
class BasketTask {

    /**
     * @param {Boolean} isChild 
     * @param {*} struc 
     */
    constructor(isChild, struc) {

        this.isChild = isChild;
        this.taskId = struc.id;
        this.basketId = struc.basketId;
        this.basketName = struc.basketName;
        this.createTime = struc.createTime;

        /** 跟盘价类型 */
        this.priceFollowType = struc.priceFollowType;
        /** 完成率（0~1） */
        this.completionRate = struc.completionRate;
        /** 下单方式 */
        this.executeType = struc.executeType;
        /** 任务创建者 */
        this.executeUser = struc.executeUser;
        /** 预期执行量（篮、金额、百分比） */
        this.executeVolume = struc.executeVolume;
        /** 目标总金额 */
        this.targetMoney = struc.targetMoney;
        /** 目标总数量 */
        this.targetVolume = struc.targetVolume;
        /** 已撤总金额 */
        this.cancelMoney = struc.cancelMoney;
        /** 已撤总数量 */
        this.cancelVolume = struc.cancelVolume;
        /** 交易方向 */
        this.direction = struc.taskType;
        /** 已成交总金额 */
        this.tradedMoney = struc.tradedMoney;
        /** 已成交总数量 */
        this.tradedVolume = struc.tradedVolume;


        /** 合约代码（子任务专属） */
        this.instrument = struc.instrument;
        /** 合约名称（子任务专属） */
        this.instrumentName = struc.instrumentName;


        /**
         * 扩展字段
         */
        this._enrich(struc);
    }

    _enrich(struc) {

        // var status = struc.taskStatus;
        // var statuses = { created: 1, waiting: 2, progressing: 3, completed: 4 };
        
        this.leftVolume = this.targetVolume - this.tradedVolume - this.cancelVolume;
        this.leftMoney = this.targetMoney - this.tradedMoney - this.cancelMoney;
        this.isCompleted = this.leftVolume == 0;
        this.taskDetail = struc.taskDetail;
    }
}

module.exports = { BasketTask };