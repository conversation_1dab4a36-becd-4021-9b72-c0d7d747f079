
class Exchange {

    constructor(struc) {

        this.accountId = struc.accountId;
        this.accountName = struc.accountName;
        this.assetType = struc.assetType;
        this.commission = struc.commission;
        this.direction = struc.direction;
        this.exchangeOrderId = struc.exchangeOrderId;
        this.fundId = struc.fundId;
        this.fundName = struc.fundName;
        this.id = struc.id;
        this.instrument = struc.instrument;
        this.instrumentName = struc.instrumentName;
        this.orderId = struc.orderId;
        this.positionEffect = struc.positionEffect;
        this.strategyId = struc.strategyId;
        this.strategyName = struc.strategyName;
        this.today = struc.today;
        this.tradeId = struc.tradeId;
        this.tradeTime = struc.tradeTime;
        this.tradedPrice = struc.tradedPrice;
        this.tradingDay = struc.tradingDay;
        this.userId = struc.userId;
        this.userName = struc.userName;
        this.volume = struc.volume;

        this.enrich(struc);
    }

    enrich(struc) {
        this.adjustFlag = struc.adjustFlag !== undefined && struc.adjustFlag !== null && struc.adjustFlag !== 0;
    }
}

module.exports = { Exchange };