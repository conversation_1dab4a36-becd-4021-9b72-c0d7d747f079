/**
 * 某个已买待成交的涨停下单合约，当前的前序总量和封单量数据
 */
class BoughtOrderFrontOrder {

    constructor(orderId, { frontOrder, limitBuy, limitSize, totalSell }) {
        
        /** 已买下单订单ID */
        this.orderId = orderId;
        /** 前序总量（手） */
        this.frontOrder = frontOrder;
        /** 涨停封单量（手） */
        this.limitBuy = limitBuy;
        /** 封单笔数（手） */
        this.limitSize = limitSize;
        /** 总卖量 */
        this.totalSell = totalSell;
    }

    /**
     * 将后端返回的字典，转换成数组
     * @param dataMap key：订单ID，value：包含前序总量等字段的json结构
     * @returns 
     */
    static convert(dataMap) {

        if (typeof dataMap != 'object' || !dataMap) {
            return [];
        }
        
        return Object.entries(dataMap).map(item => new BoughtOrderFrontOrder(item[0], item[1]));
    }
}

module.exports = { BoughtOrderFrontOrder };