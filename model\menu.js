
const RouteMaps = require('../config/routemaps');

const PermissionDataStructure = {

    /** 操作名称 */
    permissionName: null,
    /** 归属菜单ID */
    menuId: 1,
    /** 该操作是否为HTTP方式，false/非http方式，如果为http方式则为get/post/put/delete */
    method: false,
    /** 数据传输方式为TCP方式时，上行功能代码 */
    functionCode: 1,
    /** http方式调用，则为目标URL，否则忽略 */
    url: null,
};

class Permission {

    constructor(struc = PermissionDataStructure) {

        /** 操作名称 */
        this.permissionName = struc.permissionName;
        /** 归属菜单ID */
        this.menuId = struc.menuId;
        /** 该操作采用的数据提交方式 TCP | GET | POST | PUT | DELETE */
        this.method = struc.method;
        /** 数据传输方式为TCP方式时，上行功能代码 */
        this.functionCode = struc.functionCode;
        /** http方式调用，则为目标URL，否则忽略 */
        this.url = struc.url;
    }
}

const MenuDataStructure = {

    /** 菜单ID */
    id: 0,
    /** 菜单名称 */
    menuName: null,
    /** 菜单图标 */
    menuIcon: null,
    /** 视图名称（视图加载路径） */
    viewLocation: null,
    /** 是否为APP外链 */
    isApp: false,
};

class Menu {

    constructor(struc = MenuDataStructure, permissions, parentId) {
        
        /** 菜单ID */
        this.menuId = struc.id;
        /** 菜单名称 */
        this.menuName = struc.menuName;
        /** 菜单图标（当前的menu icon配置并非图标，废弃该数据） */
        this.menuIcon = struc.menuIcon = undefined;
        /** 是否为APP外链 */
        this.isApp = !!struc.isApp;
        /**
         * 视图名称（视图加载路径）-- 服务端数据并不包含该信息，由本地配置进行扩充
         * 对于外链APP类型的菜单入口，则直接使用该入口信息指定的URL地址
        */
        this.viewLocation = this.isApp ? struc.viewLocation : (struc.viewLocation = null);
        /** 是否为子菜单 */
        this.isChild = typeof parentId == 'number';

        if (this.isChild) {
            this.parentId = parentId;
        }

        /**
         * @param {Array} permissions 
         * @returns {Array<Permission>}
         */
        function boxize(permissions) {
            return permissions.map(item => new Permission(item));
        }

        /**
         * @returns {Array<Menu>}
         */
        function allocate() {
            return [];
        }

        /** 操作权限（黑名单） */
        this.blackPermits = permissions instanceof Array ? boxize(permissions) : [];
        /** 包含的子菜单（当该菜单为父菜单时，适用） */
        this.children = allocate();
        /** 是否父级菜单处于展开状态（当该菜单为父菜单时，适用） */
        this.expanded = false;
        /** 映射视图和图标信息 */
        this.extend();
    }

    /**
     * 为菜单扩充本地视图映射和图标信息
     */
    extend() {

        /**
         * @returns {{name, path, icon, tag }}
         */
        function extractConfig(menu_id) {
            return RouteMaps[menu_id];
        }

        let matched = extractConfig(this.menuId);
        if (matched) {
            
            this.viewLocation = matched.path;

            if (matched.tag) {

                /** 菜单项携带的TAG信息 */
                this.menuTag = matched.tag;
            }
        }

        if (!this.menuIcon && matched) {
            this.menuIcon = matched.icon || 'iconfont icon-chanpin';
        }
    }

    /**
     * duplicate an instance
     * @param {Menu} base can be instance of [Menu] or not
     * @returns {Menu}
     */
    static Duplicate(base) {

        var copied = new Menu({});

        for (let key in copied) {
            delete copied[key];
        }

        for (let key in base) {

            if (key == 'blackPermits') {

                let records = base[key];
                copied[key] = records instanceof Array ? records.map(item => new Permission(item)) : [];
            }
            else {
                copied[key] = base[key];
            }
        }

        return copied;
    }

    /**
     * 将平行的原始菜单数组，转换成具有树形结构的菜单树
     * @param {Array} sys_menus 系统全局菜单
     * @param {Array} user_menus 属于当前用户的菜单结构
     * @returns {Array<Menu>}
     */
    static ConstructTree(sys_menus, user_menus) {

        /**

        System Menu Data Structure

        {

            active: true,
            id: 300,
            menuIcon: '超管',
            menuName: '账号管理',
            userType: 1,
            parentMenuId: null,
        }

        User Menu Data Structure

        {
            
            menuIcon: '',
            menuId: 1501,
            menuName: '产品',
            permissions: [
                {
                    defaultPermission: false,
                    functionCode: 1,
                    id: 1,
                    menuId: 1501,
                    method: 'TCP',
                    permissionName: '交易下单',
                    url: null,
                    userType: 10,
                }
            ],
        }

        */

        if (sys_menus.length == 0 || user_menus.length == 0) {
            return [];
        }

        /**
         * key: menu id / value: menu json object
         */
        var map1 = {};
        for (let item of sys_menus) {
            map1[item.id] = item;
        }

        var top_menus = [];
        var map2 = {};
        let cur_idx = 0;

        while (user_menus.length > 0) {

            if (cur_idx >= user_menus.length) {
                cur_idx = 0;
            }

            let menuObj = user_menus[cur_idx];
            let menuConfig = map1[menuObj.menuId];

            if (menuConfig === undefined) {

                user_menus.splice(cur_idx, 1);
                console.error('user menu has no definition', menuObj);
                continue;
            }
            
            let parentId = menuConfig.parentMenuId;
            let isTop = typeof parentId != 'number';

            if (isTop) {

                let top_menu = new Menu(menuConfig, menuObj.permissions);
                top_menus.push(top_menu);
                user_menus.splice(cur_idx, 1);

                /**
                 * 顶级菜单加入字典表
                 */
                map2[top_menu.menuId] = top_menu;
            }
            else {

                let parent_menu = map2[parentId];
                if (parent_menu instanceof Menu) {

                    let sub_menu = new Menu(menuConfig, menuObj.permissions, parentId);
                    parent_menu.children.push(sub_menu);
                    user_menus.splice(cur_idx, 1);
                    
                    /**
                     * 下级菜单加入字典表
                     */
                    map2[sub_menu.menuId] = sub_menu;
                }
                else {
                    cur_idx++;
                }
            }
        }

        /**
         * 将菜单按照规则排序
         * @param {Array<Menu>} menus 
         */
        function Sort(menus) {

            menus.sort((a, b) => a.menuId - b.menuId);
            menus.forEach(the_menu => {
                the_menu.children.length > 0 && Sort(the_menu.children);
            });
        }

        Sort(top_menus);
        return top_menus;
    }

    /**
     * 构造外链应用菜单
     * @param {Array<{id, appName, appIcon, url}>} records 
     */
    static ConstructAppMenus(records) {

        return records.map(item => new Menu({

            id: 'APP-' + item.id,
            menuName: item.appName,
            viewLocation: item.url,
            menuIcon: item.appIcon,
            isApp: true,
        }));
    }
}

module.exports = { Menu, Permission };