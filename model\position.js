

const SystemEnum = require('../config/system-enum').systemEnum;
const AssetsType = SystemEnum.assetsType;
const BizHelper = require('../libs/helper-biz').BizHelper;

/**
 * 持仓
 */
class Position {

    constructor(struc) {

        this.id = struc.id;
        this.accountId = struc.accountId;
        this.accountName = struc.accountName;
        this.fundId = struc.fundId;
        this.fundName = struc.fundName;
        this.strategyId = struc.strategyId;
        this.strategyName = struc.strategyName;
        this.tradingDay = struc.tradingDay;
        this.assetType = struc.assetType;
        this.instrument = struc.instrument;
        this.instrumentName = struc.instrumentName;
        this.direction = struc.direction;
        this.avgPrice = struc.avgPrice;
        this.marketValue = struc.marketValue;
        this.positionCost = struc.positionCost;
        this.todayPosition = struc.todayPosition;
        this.yesterdayPosition = struc.yesterdayPosition;
        this.closeProfit = struc.closeProfit;
        this.frozenTodayVolume = struc.frozenTodayVolume;
        this.frozenVolume = struc.frozenVolume;
        this.usedCommission = struc.usedCommission;
        this.usedMargin = struc.usedMargin;
        this.updateTime = struc.updateTime;

        this._enrich(struc);
    }

    _enrich(struc) {

        const instrument = struc.instrument;
        const is_future = struc.assetType === AssetsType.future.code;

        /** 最小价格变动，如：股票0.01，某期货合约0.5 */
        this.priceTick = BizHelper.getPriceTick(instrument);
        /** 标准价格精度（价格小数点后，小数位数），如：股票2，某期货合约0，某期权1 */
        this.pricePrecision = BizHelper.getPricePrecision(instrument);
        /** 数量乘数 */
        this.volumeMultiple = BizHelper.getVolumeMultiple(instrument);

        this.lastPrice = 0;
        this.floatProfit = struc.floatProfit;
        this.profit = this.closeProfit + this.floatProfit;
        this.totalPosition = (struc.todayPosition || 0) + (struc.yesterdayPosition || 0);
        this.closableVolume = (struc.yesterdayPosition || 0)
                            - (struc.frozenVolume || 0) 
                            - (struc.frozenTodayVolume || 0)
                            + (is_future ? struc.todayPosition || 0 : 0);
    }
}

module.exports = { Position };