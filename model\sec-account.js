/**
 * 证券账号
 */
class SecAccount {

    constructor({ id, financeAccount, userCode, branchId, orgId, counterVersion, configStr, needOrder, createTime }) {

        this.id = id;
        this.financeAccount = financeAccount;
        this.userCode = userCode;
        this.branchId = branchId;
        this.orgId = orgId;
        this.counterVersion = counterVersion;
        this.configStr = configStr;
        this.needOrder = !!needOrder;
        this.createTime = createTime;
    }
}

module.exports = { SecAccount };