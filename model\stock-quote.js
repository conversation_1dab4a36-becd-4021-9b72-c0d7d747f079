class StockQuote {

    constructor({ instrument, instrumentName, percent, speed1m, speed3m, speed5m, price }) {
        
        this.is20Cm = instrument.indexOf('.688') > 0 || instrument.indexOf('.3') > 0;
        this.instrument = instrument;
        this.instrumentName = instrumentName;
        this.percent = percent;
        this.speed1m = speed1m;
        this.speed3m = speed3m;
        this.speed5m = speed5m;
        this.price = price;
    }
}

module.exports = { StockQuote };