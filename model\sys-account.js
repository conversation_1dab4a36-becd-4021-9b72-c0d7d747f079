
/**
 * 系统级账号
 */
class SysAccount {

    /**
     * @param {*} struc 
     */
    constructor(struc) {

        this.accountId = struc.accountId;
        this.accountName = struc.accountName;
        this.assetType = struc.assetType;
        this.available = struc.available;
        this.balance = struc.balance;
        this.closeProfit = struc.closeProfit;
        this.commission = struc.commission;
        this.connectCount = struc.connectCount;
        this.connectionStatus = struc.connectionStatus;
        this.diffBalance = struc.diffBalance;
        this.isCredit = !!struc.credit;
        this.financeAccount = struc.financeAccount;
        this.frozenCommission = struc.frozenCommission;
        this.frozenMargin = struc.frozenMargin;
        this.id = struc.id;
        this.identityId = struc.identityId;
        this.identityName = struc.identityName;
        this.identityType = struc.identityType;
        this.inMoney = struc.inMoney;
        this.loanBuyBalance = struc.loanBuyBalance;
        this.loanSellBalance = struc.loanSellBalance;
        this.loanSellQuota = struc.loanSellQuota;
        this.margin = struc.margin;
        this.marketValue = struc.marketValue;
        this.maxLimitMoney = struc.maxLimitMoney;
        this.nav = struc.nav;
        this.navRealTime = struc.navRealTime;
        this.outMoney = struc.outMoney;
        this.positionProfit = struc.positionProfit;
        this.preBalance = struc.preBalance;
        this.risePercent = struc.risePercent;
        this.status = struc.status;
        this.fundId = struc.fundId;
        this.fundName = struc.fundName;
        this.fundShare = struc.fundShare;
        this.strategyId = struc.strategyId;
        this.strategyName = struc.strategyName;
        this.tradingDay = struc.tradingDay;
        this.withdrawQuota = struc.withdrawQuota;
    }
}

module.exports = { SysAccount };