
/**
 * 系统级策略
 */
class SysStrategy {

    /**
     * @param {*} struc 
     */
    constructor(struc) {

        this.available = struc.available;
        this.balance = struc.balance;
        this.closeProfit = struc.closeProfit;
        this.commission = struc.commission;
        this.connectCount = struc.connectCount;
        this.description = struc.description;
        this.diffBalance = struc.diffBalance;
        this.frozenCommission = struc.frozenCommission;
        this.frozenMargin = struc.frozenMargin;
        this.fundId = struc.fundId;
        this.fundName = struc.fundName;
        this.id = struc.id;
        this.loanBuyBalance = struc.loanBuyBalance;
        this.loanSellBalance = struc.loanSellBalance;
        this.loanSellQuota = struc.loanSellQuota;
        this.margin = struc.margin;
        this.marketValue = struc.marketValue;
        this.positionProfit = struc.positionProfit;
        this.preBalance = struc.preBalance;
        this.reportTemplates = struc.reportTemplates;
        this.risePercent = struc.risePercent;
        this.status = struc.status;
        this.strategyName = struc.strategyName;
        this.withdrawQuota = struc.withdrawQuota;
        
        this.strategyAccounts = struc.strategyAccounts;
        this.users = struc.users;
    }
}

module.exports = { SysStrategy };