class StandardTick {
    
    constructor({
        askPrice,
        askVolume,
        bidPrice,
        bidVolume,
        exchange,
        highPrice,
        instrumentID,
        lastPrice,
        lowPrice,
        lowerLimitPrice,
        openPrice,
        position,
        preClosePrice,
        settlePrice,
        strTime,
        turnover,
        updateTime,
        upperLimitPrice,
        volume
    }) {

        /** 卖1 ~ 卖10 （小 > 大） */
        this.askPrice = Array.isArray(askPrice) ? askPrice : [];
        this.askVolume = Array.isArray(askVolume) ? askVolume : [];
        /** 买1 ~ 买10 （大 > 小） */
        this.bidPrice = Array.isArray(bidPrice) ? bidPrice : [];
        this.bidVolume = Array.isArray(bidVolume) ? bidVolume : [];

        this.exchange = exchange;
        this.highPrice = highPrice;
        this.instrumentID = instrumentID;
        this.lastPrice = lastPrice;
        this.lowPrice = lowPrice;
        this.lowerLimitPrice = lowerLimitPrice;
        this.openPrice = openPrice;
        this.position = position;
        this.preClosePrice = preClosePrice;
        this.settlePrice = settlePrice;
        this.strTime = strTime;
        this.turnover = turnover;
        this.updateTime = updateTime;
        this.upperLimitPrice = upperLimitPrice;
        this.volume = volume;
    }
}

class StandardPrice {

    constructor({
        assetType,
        creditBuy,
        instrument,
        instrumentName,
        isCollateral,
        lastPrice,
        lowerLimitPrice,
        optionType,
        preClosePrice,
        priceTick,
        strikePrice,
        upperLimitPrice,
        volumeMultiple
    }) {

        this.assetType = assetType;
        this.creditBuy = creditBuy;
        this.instrument = instrument;
        this.instrumentName = instrumentName;
        this.isCollateral = isCollateral;
        this.lastPrice = lastPrice;
        this.lowerLimitPrice = lowerLimitPrice;
        this.optionType = optionType;
        this.preClosePrice = preClosePrice;
        this.priceTick = priceTick;
        this.strikePrice = strikePrice;
        this.upperLimitPrice = upperLimitPrice;
        this.volumeMultiple = volumeMultiple;
    }
}

module.exports = {
    StandardTick,
    StandardPrice,
};