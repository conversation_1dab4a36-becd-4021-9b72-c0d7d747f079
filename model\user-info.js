﻿/**
 * system user infrastructure
 */

const { systemEnum } = require('../config/system-enum');
const { systemUserEnum } = require('../config/system-enum.user');
const UserRole = systemUserEnum.userRole;

class UserInfo {

    constructor(usr_info) {

        this.userId = usr_info.userId;
        this.userName = usr_info.userName || usr_info.username;
        this.fullName = usr_info.fullName;
        this.orgId = usr_info.orgId;
        this.orgName = usr_info.orgName;
        this.roleId = usr_info.roleId;
        this.token = usr_info.token;
        this.password = usr_info.password;
        this.userType = usr_info.userType;
        
        /**
         * 该角色，是否需要登录行情服务器
         */
        this.quoteServerRequired = this.roleId != UserRole.superAdmin.code;

        if (this.roleId === UserRole.superAdmin.code) {

            /** 是否超级管理员 */
            this.isSuperAdmin = true;
            this.userTypeName = UserRole.superAdmin.mean;
        }
        else if (this.roleId === UserRole.orgAdmin.code) {

            /** 是否机构管理员 */
            this.isOrgAdmin = true;
            this.userTypeName = UserRole.orgAdmin.mean;
        }
        else if (this.roleId === UserRole.counselor.code) {

            /** 是否投顾 */
            this.isCounselor = true;
            this.userTypeName = UserRole.counselor.mean;
        }
        else if (this.roleId === UserRole.product.code) {

            /** 是否产品经理 */
            this.isProductManager = true;
            this.userTypeName = UserRole.product.code;
        }
        else if (this.roleId === UserRole.riskProtector.code) {

            /** 是否风控员 */
            this.isRiskProtector = true;
            this.userTypeName = UserRole.riskProtector.mean;
        }
        else if (this.roleId === UserRole.observing.code) {

            /** 是否观察员 */
            this.isObserver = true;
            this.userTypeName = UserRole.observing.mean;
        }
        else if (this.roleId === UserRole.tradingMan.code) {

            /** 是否交易员 */
            this.isTradingMan = true;
            this.userTypeName = UserRole.tradingMan.mean;
        }
        
        if (this.userType === systemEnum.userType.dayTrader.code) {

            /** 是否日内交易员 */
            this.isDayTrader = true;
        }

        this.lastLoginTime = usr_info.lastLoginTime;
        this.lastLoginComputerName = usr_info.lastLoginComputerName;
        this.lastLoginIp = usr_info.lastLoginIp;
        this.lastLoginMachineAddr = usr_info.lastLoginMachineAddr;
    }

    updateToken(new_token) {
        this.token = new_token;
    }

    static Clone(sample) {
        return new UserInfo(sample);
    }
}

module.exports = { UserInfo };
