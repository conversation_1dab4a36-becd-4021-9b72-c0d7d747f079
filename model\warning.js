class WarningMsg {

    constructor(struc) {

        this.id = struc.id;
        this.configurationId = struc.configurationId;
        this.identity = struc.identity;
        this.identityType = struc.identityType;
        this.identityName = struc.identityName;
        this.warningType = struc.warningType;
        this.content = struc.content;

        let ctime = struc.createTime;
        if (typeof ctime == 'number') {
            ctime = new Date(ctime).format('yyyy-MM-dd hh:mm:ss');
        }

        this.createTime = ctime;
    }
}

module.exports = { WarningMsg };