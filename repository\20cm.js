const { helper } = require('../libs/helper');
const { BinaryRepo } = require('./binary-repo');

class X20CmRepository extends BinaryRepo {

    constructor() {

        super();

        this.url = {

            manual_setting: 'strike/board/setting',
            history: 'strike/board/info',
            basic_setting: 'auto/strike/board/base/setting',
            auto_setting: 'auto/strike/board/setting',
            task: 'auto/strike/board/task',
            pool: 'tick/pool',
            pool_detail: 'tick/pool/detail',
            add_2_pool: 'tick/pool/detail/import',
        };
    }

    queryFinishedTasks(type) {

        return new Promise((resolve, reject) => {

            this.http('/strike/board/finished', { method: 'get', params: { direction: 1, type: type || 0 } }).then(
                (resp) => { resolve(resp.data); },
                (error) => { reject(error); },
            );
        });
    }

    querySetting() {

        return new Promise((resolve, reject) => {

            this.http(this.url.manual_setting, { method: 'get' }).then(
                (resp) => { resolve(resp.data); },
                (error) => { reject(error); },
            );
        });
    }

    setting(data) {

        return new Promise((resolve, reject) => {

            this.http(this.url.manual_setting, { method: 'post', data }).then(
                (resp) => { resolve(resp.data); },
                (error) => { reject(error); },
            );
        });
    }

    deleteSetting() {

        return new Promise((resolve, reject) => {

            this.http(this.url.manual_setting, { method: 'delete' }).then(
                (resp) => { resolve(resp.data); },
                (error) => { reject(error); },
            );
        });
    }

    decrypt(result) {

        var serialized = result.data;
        if (!serialized) {
            return result;
        }

        if (typeof serialized != 'string' || serialized.trim().length == 0) {
            return result;
        }

        try {
            result.data = JSON.parse(helper.aesDecrypt(serialized));
        }
        catch(ex) {
            //
        }

        return result;
    }

    queryParamb(instrument) {

        return new Promise((resolve, reject) => {

            this.http(`http://182.150.112.115:23301/v1/indicators/extra?instrument=${instrument}`, { method: 'get' }).then(
                (resp) => { resolve(this.decrypt(resp.data)); },
                (error) => { reject(error); },
            );
        });
    }

    queryBasicSetting() {

        return new Promise((resolve, reject) => {

            this.http(this.url.basic_setting, { method: 'get' }).then(
                (resp) => { resolve(resp.data); },
                (error) => { reject(error); },
            );
        });
    }

    updateBasicSetting(data) {

        return new Promise((resolve, reject) => {

            this.http(this.url.basic_setting, { method: helper.isNone(data.id) ? 'post' : 'put' , data }).then(
                (resp) => { resolve(resp.data); },
                (error) => { reject(error); },
            );
        });
    }

    queryAutoSettings() {

        return new Promise((resolve, reject) => {

            this.http(this.url.auto_setting, { method: 'get' }).then(
                (resp) => { resolve(resp.data); },
                (error) => { reject(error); },
            );
        });
    }

    updateAutoSetting(data) {

        return new Promise((resolve, reject) => {

            this.http(this.url.auto_setting, { method: helper.isNone(data.id) ? 'post' : 'put' , data }).then(
                (resp) => { resolve(resp.data); },
                (error) => { reject(error); },
            );
        });
    }

    deleteAutoSetting(setting_id) {

        return new Promise((resolve, reject) => {

            this.http(this.url.auto_setting, { method: 'delete' , params: { setting_id } }).then(
                (resp) => { resolve(resp.data); },
                (error) => { reject(error); },
            );
        });
    }

    queryPools() {

        return new Promise((resolve, reject) => {

            this.http(this.url.pool, { method: 'get' }).then(
                (resp) => { resolve(resp.data); },
                (error) => { reject(error); },
            );
        });
    }

    createPool(pool) {

        return new Promise((resolve, reject) => {

            this.http(this.url.pool, { method: 'post', data: pool }).then(
                (resp) => { resolve(resp.data); },
                (error) => { reject(error); },
            );
        });
    }

    deletePool(ticket_pool_id) {

        return new Promise((resolve, reject) => {

            this.http(this.url.pool, { method: 'delete', params: { ticket_pool_id } }).then(
                (resp) => { resolve(resp.data); },
                (error) => { reject(error); },
            );
        });
    }

    queryPoolDetail(ticket_pool_id) {
        
        return new Promise((resolve, reject) => {

            this.http(this.url.pool_detail, { method: 'get', params: { ticket_pool_id } }).then(
                (resp) => { resolve(resp.data); },
                (error) => { reject(error); },
            );
        });
    }

    add2Pool(ticket_pool_id, stocks) {

        return new Promise((resolve, reject) => {

            this.http(this.url.add_2_pool, { method: 'post', params: { ticket_pool_id }, data: stocks }).then(
                (resp) => { resolve(resp.data); },
                (error) => { reject(error); },
            );
        });
    }

    queryBindings() {

        return new Promise((resolve, reject) => {

            this.http(this.url.task, { method: 'get' }).then(
                (resp) => { resolve(resp.data); },
                (error) => { reject(error); },
            );
        });
    }

    bind2Pool(data) {
        
        return new Promise((resolve, reject) => {

            this.http(this.url.task, { method: helper.isNone(data.id) ? 'post' : 'put' , data }).then(
                (resp) => { resolve(resp.data); },
                (error) => { reject(error); },
            );
        });
    }

    deletePoolBindding(strike_board_task_id) {
        
        return new Promise((resolve, reject) => {

            this.http(this.url.task, { method: 'delete' , params: { strike_board_task_id } }).then(
                (resp) => { resolve(resp.data); },
                (error) => { reject(error); },
            );
        });
    }

    queryTaskHistory(trading_day, instrument) {

        return new Promise((resolve, reject) => {

            this.http(this.url.history, { method: 'get', params: { trading_day, instrument } }).then(
                (resp) => { resolve(resp.data); },
                (error) => { reject(error); },
            );
        });
    }
}

module.exports = { repo20Cm: new X20CmRepository() };
