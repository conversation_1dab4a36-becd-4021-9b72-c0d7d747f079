const httpRequest = require('../libs/http').http;

/**
 * 账号周边操作
 */
class AccountRoundRepository {

    /**
     * 查询全部账号检测结果
     */
    queryAll() {

        return new Promise((resolve, reject) => {
                    httpRequest.get('/account/overwrite').then(
                        (resp) => { resolve(resp.data); },
                        (error) => { reject(error); });
        });
    }

    /**
     * 查询账号检测结果详情
     * @param {Number|String} account_id 账号ID
     * @param {Number} check_type 检测类型代码
     */
    queryDetail(account_id, check_type) {

        return new Promise((resolve, reject) => {
                    httpRequest.get('/account/overwrite/result', { params: { account_id, check_type } }).then(
                        (resp) => { resolve(resp.data); },
                        (error) => { reject(error); });
        });
    }

    /**
     * 覆盖
     */
    overlap(account_id, overlap) {

        return new Promise((resolve, reject) => {
                    httpRequest.post('/account/overlap', {}, { params: { account_id, overlap } }).then(
                        (resp) => { resolve(resp.data); },
                        (error) => { reject(error); });
        });
    }

    /**
     * 成交检测
     */
    tradeCheck(accountId) {

        return new Promise((resolve, reject) => {
                    httpRequest.post('/account/check/trade', {}, { params: { accountId } }).then(
                        (resp) => { resolve(resp.data); },
                        (error) => { reject(error); });
        });
    }

    /**
     * 委托检测
     */
    orderCheck(accountId) {

        return new Promise((resolve, reject) => {
                    httpRequest.post('/account/check/order', {}, { params: { accountId } }).then(
                        (resp) => { resolve(resp.data); },
                        (error) => { reject(error); });
        });
    }

    /**
     * 权益检测
     */
    balanceCheck(accountId) {

        return new Promise((resolve, reject) => {
                    httpRequest.post('/account/check/balance', {}, { params: { accountId } }).then(
                        (resp) => { resolve(resp.data); },
                        (error) => { reject(error); });
        });
    }
}

module.exports = {

    /** 账号周边操作 */
    repoAccountAround: new AccountRoundRepository(),
};
