const httpRequest = require('../libs/http').http;

class AccountRepository {
    getAll(condition) {
        return new Promise((resolve, reject) => {
            httpRequest
                .get('/account', {
                    params: {
                        fund_id: condition ? condition.fund_id : '',
                        strategy_id: condition ? condition.strategy_id : '',
                    },
                })
                .then(
                    (resp) => {
                        resolve(resp.data);
                    },
                    (error) => {
                        reject(error);
                    },
                );
        });
    }

    getSimpleAccountList(filter_bind, org_id) {
        return new Promise((resolve, reject) => {
            httpRequest.get('/account/simple', { params: { filterBind: filter_bind, org_id } }).then(
                (resp) => {
                    resolve(resp.data);
                },
                (error) => {
                    reject(error);
                },
            );
        });
    }

    queryUserScopeAccounts() {

        return new Promise((resolve, reject) => {
            httpRequest.get('../v4/account/simple').then(
                (resp) => { resolve(resp.data); },
                (error) => { reject(error); },
            );
        });
    }

    getUserAccountList() {
        return new Promise((resolve, reject) => {
            httpRequest.get('/account/all', {}).then(
                (resp) => {
                    resolve(resp.data);
                },
                (error) => {
                    reject(error);
                },
            );
        });
    }

    createAccount(account) {
        return new Promise((resolve, reject) => {
            httpRequest.post('/account', account, { headers: { 'Content-Type': 'application/json' } }).then(
                (resp) => {
                    resolve(resp.data);
                },
                (error) => {
                    reject(error);
                },
            );
        });
    }

    liquidationAccount(accountId) {
        return new Promise((resolve, reject) => {
            httpRequest
                .put(
                    '/account/settle',
                    {},
                    {
                        params: {
                            account_id: accountId,
                        },
                    },
                )
                .then(
                    (resp) => {
                        resolve(resp.data);
                    },
                    (err) => {
                        reject(err);
                    },
                );
        });
    }

    updateAccount(account) {
        return new Promise((resolve, reject) => {
            httpRequest.put('/account', account, { headers: { 'Content-Type': 'application/json' } }).then(
                (resp) => {
                    resolve(resp.data);
                },
                (error) => {
                    reject(error);
                },
            );
        });
    }

    initAccount(accountId) {
        return new Promise((resolve, reject) => {
            httpRequest.post('/account/init', {}, { params: { account_id: accountId } }).then(
                (resp) => {
                    resolve(resp.data);
                },
                (error) => {
                    reject(error);
                },
            );
        });
    }

    getAccountDetail(accountId) {
        return new Promise((resolve, reject) => {
            httpRequest.get('/account/detail', { params: { accountId } }).then(
                (resp) => {
                    resolve(resp.data);
                },
                (error) => {
                    reject(error);
                },
            );
        });
    }

    batchGetAccountCash(assetType) {

        return new Promise((resolve, reject) => {
            httpRequest.get('/account/batch', { params: { asset_type: assetType } }).then(
                (resp) => { resolve(resp.data); },
                (error) => { reject(error); },
            );
        });
    }

    batchGetAccountDetail(accountIds) {
        return new Promise((resolve, reject) => {
            httpRequest.post('/account/batch', accountIds).then(
                (resp) => {
                    resolve(resp.data);
                },
                (error) => {
                    reject(error);
                },
            );
        });
    }

    getAccountFee(accountId) {
        return new Promise((resolve, reject) => {
            httpRequest.get('/account/fee', { params: { account_id: accountId } }).then(
                (resp) => {
                    resolve(resp.data);
                },
                (err) => {
                    reject(err);
                },
            );
        });
    }

    saveAccountFee(commission) {
        return new Promise((resolve, reject) => {
            httpRequest.post('/account/fee', commission).then(
                (resp) => {
                    resolve(resp.data);
                },
                (error) => {
                    reject(error);
                },
            );
        });
    }

    updateAccountFee(commission) {
        return new Promise((resolve, reject) => {
            httpRequest.put('/account/fee', commission).then(
                (resp) => {
                    resolve(resp.data);
                },
                (error) => {
                    reject(error);
                },
            );
        });
    }

    updatePreBalance(params) {
        return new Promise((resolve, reject) => {
            httpRequest
                .post('/account/prebalance', undefined, {
                    params,
                })
                .then(
                    (resp) => {
                        resolve(resp.data);
                    },
                    (error) => {
                        reject(error);
                    },
                );
        });
    }

    bindTerminal(account_id, terminalIds) {
        terminalIds = terminalIds || [];
        return new Promise((resolve, reject) => {
            httpRequest.post(`/account/bind-terminal?account_id=${account_id}`, terminalIds, { headers: { 'Content-Type': 'application/json' } }).then(
                (resp) => {
                    resolve(resp.data);
                },
                (error) => {
                    reject(error);
                },
            );
        });
    }

    overlapFinance({ accountId, overlap }) {
        return new Promise((resolve, reject) => {
            httpRequest
                .post(
                    '/account/overlap/detail',
                    {},
                    {
                        params: {
                            account_id: accountId,
                            overlap: overlap,
                        },
                    },
                )
                .then(
                    (resp) => {
                        resolve(resp.data);
                    },
                    (err) => {
                        reject(err);
                    },
                );
        });
    }

    availableCheck({ accountId, availableCheck }) {
        return new Promise((resolve, reject) => {
            httpRequest
                .put(
                    '/account/availablecheck',
                    {},
                    {
                        params: {
                            account_id: accountId,
                            available_check: availableCheck,
                        },
                    },
                )
                .then(
                    (resp) => {
                        resolve(resp.data);
                    },
                    (err) => {
                        reject(err);
                    },
                );
        });
    }

    bindFund(account_id, fund_id) {
        return new Promise((resolve, reject) => {
            httpRequest.post('/account/fund-fund', { account_id, fund_id }).then(
                (resp) => {
                    resolve(resp.data);
                },
                (error) => {
                    reject(error);
                },
            );
        });
    }

    removeAccount(accountId) {
        return new Promise((resolve, reject) => {
            httpRequest.delete('/account', { params: { account_id: accountId } }).then(
                (resp) => {
                    resolve(resp.data);
                },
                (error) => {
                    reject(error);
                },
            );
        });
    }

    shareAccount2User(identity_id, user_ids) {
        return new Promise((resolve, reject) => {
            httpRequest.post('../v4/identity/share', user_ids, { params: { identity_id, share_type: 1 } }).then(
                resp => { resolve(resp.data); },
                error => { reject(error); },
            );
        });
    }

    disconnect(accountId) {
        return new Promise((resolve, reject) => {
            httpRequest.put('/account/logout', {}, { params: { account_id: accountId } }).then(
                (resp) => {
                    resolve(resp.data);
                },
                (error) => {
                    reject(error);
                },
            );
        });
    }

    connect(accountId) {
        return new Promise((resolve, reject) => {
            httpRequest
                .get('/account/connection_test', {
                    params: {
                        account_id: accountId,
                    },
                })
                .then(
                    (resp) => {
                        resolve(resp.data);
                    },
                    (error) => {
                        reject(error);
                    },
                );
        });
    }

    getAccountAll(condition) {
        
        return new Promise((resolve, reject) => {
            // httpRequest.get('/account', {
            httpRequest.get('../v3/account', {
                params: {
                    user_id: condition ? condition.userId : '', 
                    token: condition ? condition.token : '',
                },
            }).then(
                resp => { resolve(resp.data); },
                error => { reject(error); },
            );
        });
    }

    getAccountDetailInfo(condition) {
        return new Promise((resolve, reject) => {
            httpRequest.get('../v4/account/detail', { params: { 
                identity_id: condition.identity_id,
                user_id: condition.userId,
            } }).then(
                (resp) => {
                    resolve(resp.data);
                },
                (error) => {
                    reject(error);
                },
            );
        });
    }
}

module.exports = { repoAccount: new AccountRepository() };
