const http = require('../libs/http').http;

class AlgorithmRepository {
    
    constructor() {
        this.basePath = '/algorithm';
    }

    // get uploadUrl() {
    //     return `${this.url}/order/upload`;
    // }

    // login(data) {
    //     return new Promise((resolve, reject) => {
    //         http(`${this.url}/user/login`, {
    //             method: 'post',
    //             data,
    //         }).then(
    //             (resp) => {
    //                 resolve(resp.data);
    //             },
    //             (error) => {
    //                 reject(error);
    //             },
    //         );
    //     });
    // }

    // getAccounts(token) {
    //     return new Promise((resolve, reject) => {
    //         http(`${this.url}/user/findCustomer`, {
    //             params: {
    //                 _token: token,
    //             },
    //         }).then(
    //             (resp) => {
    //                 resolve(resp.data);
    //             },
    //             (error) => {
    //                 reject(error);
    //             },
    //         );
    //     });
    // }

    // getStrategies(clientID, token) {
    //     return new Promise((resolve, reject) => {
    //         http(`${this.url}/user/findStrategy/${clientID}`, {
    //             params: {
    //                 _token: token,
    //             },
    //         }).then(
    //             (resp) => {
    //                 resolve(resp.data);
    //             },
    //             (error) => {
    //                 reject(error);
    //             },
    //         );
    //     });
    // }

    queryAlgoes() {
        
        return new Promise((resolve, reject) => {
            http(`${this.basePath}/mapping`).then(
                (resp) => { resolve(resp.data); },
                (error) => { reject(error); },
            );
        });
    }

    /**
     * @param {Array} data
     * @param {Boolean} isCreditFirst
     */
    order(data, isCreditFirst) {
        
        return new Promise((resolve, reject) => {
            http(`${this.basePath}/order`, { method: 'post', params: { algorithm_class: 1, credit_flag: isCreditFirst ? 1 : 0 }, data }).then(
                (resp) => { resolve(resp.data); },
                (error) => { reject(error); },
            );
        });
    }

    // clear(token) {
    //     return new Promise((resolve, reject) => {
    //         http(`${this.url}/order/clearAlgoOrder`, {
    //             method: 'post',
    //             params: {
    //                 _token: token,
    //             },
    //         }).then(
    //             (resp) => {
    //                 resolve(resp.data);
    //             },
    //             (error) => {
    //                 reject(error);
    //             },
    //         );
    //     });
    // }

    // getSuccess({ pageNo, pageSize }, token) {
    //     return new Promise((resolve, reject) => {
    //         http(`${this.url}/order/querySuccessOrder/${pageNo}/${pageSize}`, {
    //             params: {
    //                 _token: token,
    //             },
    //         }).then(
    //             (resp) => {
    //                 resolve(resp.data);
    //             },
    //             (error) => {
    //                 reject(error);
    //             },
    //         );
    //     });
    // }

    // getError({ pageNo, pageSize }, token) {
    //     return new Promise((resolve, reject) => {
    //         http(`${this.url}/order/queryErrorOrder/${pageNo}/${pageSize}`, {
    //             params: {
    //                 _token: token,
    //             },
    //         }).then(
    //             (resp) => {
    //                 resolve(resp.data);
    //             },
    //             (error) => {
    //                 reject(error);
    //             },
    //         );
    //     });
    // }

    // orderFile(token) {
    //     return new Promise((resolve, reject) => {
    //         http(`${this.url}/order/newAlgoOrder`, {
    //             method: 'post',
    //             params: {
    //                 _token: token,
    //             },
    //         }).then(
    //             (resp) => {
    //                 resolve(resp.data);
    //             },
    //             (error) => {
    //                 reject(error);
    //             },
    //         );
    //     });
    // }

    getOrderList() {

        return new Promise((resolve, reject) => {
            
            http(`${this.basePath}/query`, { params: { algorithm_class: 1 } }).then(
                (resp) => { resolve(resp.data); },
                (error) => { reject(error); },
            );
        });
    }

    /**
     * @description:
     * @param {Array} data
     * @return:
     */

    cancelOrder(data) {

        return new Promise((resolve, reject) => {

            http(`${this.basePath}/cancel`, { method: 'post', data }).then(
                (resp) => { resolve(resp.data); },
                (error) => { reject(error); },
            );
        });
    }
}

module.exports = new AlgorithmRepository();
