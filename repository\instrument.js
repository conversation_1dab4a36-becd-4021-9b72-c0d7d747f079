
const http = require('../libs/http').http;
const systemEnum = require('../config/system-enum').systemEnum;
const assetsType = systemEnum.assetsType;
const { StandardPrice} = require('../model/tick');

class InstrumentRepository {

    getAll(asset_type) {

		return new Promise((resolve, reject) => {
			http.get('/instrument', { params: { type: asset_type }})
				.then(
					resp => { 
						let resp_data = resp.data || {};
						let instrument_list = resp_data.data || [];
						if (asset_type == assetsType.future.code || asset_type == assetsType.option.code) {
							instrument_list.forEach(item => {
								item.instrument = item.instrument.replace(item.exchangeId + '.', '');
							});
						}
						resolve(resp_data); 
					}, 
					err => { reject(err); }
				);
		});
	}

	/**
	 * @returns {{ errorCode: number, errorMsg: string, data: StandardPrice }}
	 */
	queryPrice(instrument) {

        return new Promise((resolve, reject) => {
			http.get('/instrument/price', { params: { instrument }})
				.then(
					resp => { resolve(resp.data); }, 
					err => { reject(err); }
				);
		});
    }
}

module.exports = { repoInstrument: new InstrumentRepository() };