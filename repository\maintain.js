const httpRequest = require('../libs/http').http;
class maintainRepository {

    constructor(){

    }

    getService() {
        return new Promise((resolve, reject) => {
            httpRequest.get('/cache').then(res => {
                resolve(res.data);
            }, err => {
                reject(err);
            });
        });
    }

    getCache({ serviceName, methodName, page, count, args }) {
        return new Promise((resolve, reject) => {
            httpRequest.put('/cache', args, {
                params: {
                    service_name: serviceName,
                    method_name: methodName,
                    page: page,
                    count: count
                }
            }).then(res => {
                resolve(res.data);
            }, err => {
                reject(err);
            });
        });
    }


    getRecover(serviceName) {
        return new Promise((resolve, reject) => {
            httpRequest.put('/cache/recover', {}, {
                params: {
                    service_name: serviceName
                }
            }).then(res => {
                resolve(res.data);
            }, err => {
                reject(err);
            });
        });
    }

    getTransactionData(orgId, monitorId) {
        return new Promise((resolve, reject) => {
            httpRequest.get('/organization/monitor', {
                params: {
                    org_id: orgId,
                    monitor_id: monitorId
                }
            }).then(resp => {
                resolve(resp.data);
            }, err => {
                reject(err);
            });
        });
    }

    getNodeStatus() {
        return new Promise((resolve, reject) => {
            httpRequest.get('/system/status').then(res => { resolve(res.data); }, err => { reject(err); });
        });
    }
    
    setNodeStatus(status) {
        return new Promise((resolve, reject) => {
            httpRequest.put('/system/status/set', null, { params: { status } }).then(res => { resolve(res.data); }, err => { reject(err); });
        });
    }

    qmd5s() {
        return new Promise((resolve, reject) => {
            httpRequest.get('/md5msg').then(res => { resolve(res.data); }, err => { reject(err); });
        });
    }

    cmd5s(md5, version) {
        return new Promise((resolve, reject) => {
            httpRequest.post('/md5msg', { md5, version }).then(resp => { resolve(resp.data); }, error => { reject(error); });
        });
    }

    dmd5s(md5) {
        return new Promise((resolve, reject) => {
            httpRequest.delete('/md5msg', { params: { md5 } }).then(resp => { resolve(resp.data); }, error => { reject(error); });
        });
    }
}

module.exports = { repoMaintain: new maintainRepository() };