const httpRequest = require('../libs/http').http;
class riskRepository {

    constructor() {

    }

    getRiskMessage(identity) {
        return new Promise((resolve, reject) => {
            httpRequest.get('/risk/message', {
                params: {
                    identity: identity
                }
            }).then(resp => {
                resolve(resp.data);
            }, err => {
                reject(err);
            });
        });
    }


    saveTemplate (templateModels) {
        return new Promise((resolve, reject) => {
            httpRequest.post('/risk/template', templateModels).then(resp => {
                resolve(resp.data);
            }, err => {
                reject(err);
            });
        });
    }


    getTemplateList() {
        return new Promise((resolve, reject) => {
            httpRequest.get('/risk/template').then(resp => {
                resolve(resp.data);
            }, err => {
                reject(err);
            });
        });
    }


    deleteTemplate(id) {
        return new Promise((resolve, reject) => {
            httpRequest.delete('/risk/template', {
                params: {
                    template_id: id
                }
            }).then(resp => {
                resolve(resp.data);
            }, err => {
                reject(err);
            });
        });
    }

    getRiskRuleList (identity) {
        return new Promise((resolve, reject) => {
            httpRequest.get('/risk/configuration', {
                params: {
                    identity
                }
            }).then(resp => {
                resolve(resp.data);
            }, err => {
                reject(err);
            });
        });
    }

    getRiskRuleTemplate(templateId) {
        return new Promise((resolve, reject) => {
            httpRequest.get('/risk/template/detail', {
                params: {
                    template_id: templateId
                }
            }).then(resp => {
                resolve(resp.data);
            }, err => {
                reject(err);
            });
        });
    }


    saveRiskRule (model) {
        return new Promise((resolve, reject) => {
            httpRequest.post('/risk/configuration', model).then(resp => {
                resolve(resp.data);
            }, err => {
                reject(err);
            });
        });
    }

    updateRiskRule(model) {
        return new Promise((resolve, reject) => {
            httpRequest.put('/risk/configuration', model).then(resp => {
                resolve(resp.data);
            }, err => {
                reject(err);
            });
        });
    }

    deleteRiskRule(configurationId) {
        return new Promise((resolve, reject) => {
            httpRequest.delete('/risk/configuration', {
                params: {
                    configuration_id: configurationId
                }
            }).then(resp => {
                resolve(resp.data);
            }, err => {
                reject(err);
            });
        });
    }


    getRiskKind () {
        return new Promise((resolve,reject) => {
            httpRequest.get('/risk/riskkind').then(resp => {
                resolve(resp.data);
            }, err => {
                reject(err);
            });
        });
    }


    saveLimitRule (limitations) {
        return new Promise((resolve, reject) => {
            httpRequest.post('/risk/restriction', limitations).then(resp => {
                resolve(resp.data);
            }, err => {
                reject(err);
            });
        });
    }


    updateLimitRule(limitations) {
        return new Promise((resolve, reject) => {
            httpRequest.put('/risk/restriction', limitations).then(resp => {
                resolve(resp.data);
            }, err => {
                reject(err);
            });
        });
    }

    getLimitRules (identity) {
        return new Promise((resolve, reject) => {
            httpRequest.get('/risk/restriction', {
                params: {
                    identity: identity
                }
            }).then(resp => {
                resolve(resp.data);
            }, err => {
                reject(err);
            });
        });
    }


    deleteLimitRules (identity) {
        return new Promise((resolve, reject) => {
            httpRequest.delete('/risk/restriction', {
                params: {
                    restriction_id: identity
                }
            }).then(resp => {
                resolve(resp.data);
            }, err => {
                reject(err);
            });
        });
    }

    getRiskClassification () {
        return new Promise((resolve,reject) => {
            httpRequest.get('/risk/classification').then(resp => {
                resolve(resp.data);
            }, err => {
                reject(err);
            });
        });
    }

}

module.exports = { repoRisk:  new riskRepository() };