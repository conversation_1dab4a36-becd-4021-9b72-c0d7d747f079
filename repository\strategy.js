const http = require('../libs/http').http;

class StrategyRepository {
    getAll(condition) {
        return new Promise((resolve, reject) => {
            http.get('/strategy', {
                params: {
                    fund_id: condition ? condition.fund_id : '',
                    account_id: condition ? condition.account_id : '',
                },
            }).then(
                resp => {
                    resolve(resp.data);
                },
                error => {
                    reject(error);
                },
            );
        });
    }

    getDetail(strategyIds) {
        return new Promise((resolve, reject) => {
            http.post('/strategy/detail_batch', strategyIds).then(
                resp => {
                    resolve(resp.data);
                },
                error => {
                    reject(error);
                },
            );
        });
    }

    create(strategy) {
        return new Promise((resolve, reject) => {
            http.post('/strategy', strategy).then(
                resp => {
                    resolve(resp.data);
                },
                error => {
                    reject(error);
                },
            );
        });
    }

    update(strategy) {
        return new Promise((resolve, reject) => {
            http.put('/strategy', strategy).then(
                resp => {
                    resolve(resp.data);
                },
                error => {
                    reject(error);
                },
            );
        });
    }

    delete(strategyId) {
        return new Promise((resolve, reject) => {
            http.delete('/strategy', { params: { strategy_id: strategyId } }).then(
                resp => {
                    resolve(resp.data);
                },
                error => {
                    reject(error);
                },
            );
        });
    }

    // 扩展功能

    bindAccount(strategy_id, accounts) {
        return new Promise((resolve, reject) => {
            http.post('/strategy/detail', accounts, { params: { strategy_id: strategy_id } }).then(
                resp => {
                    resolve(resp.data);
                },
                error => {
                    reject(error);
                },
            );
        });
    }

    unbindAccount(strategyId, detailId) {
        return new Promise((resolve, reject) => {
            http.delete('/strategy/detail', { params: { strategy_id: strategyId, detail_id: detailId } }).then(
                resp => {
                    resolve(resp.data);
                },
                error => {
                    reject(error);
                },
            );
        });
    }

    updateBoundAccount(strategy_id, account) {
        return new Promise((resolve, reject) => {
            http.put('/strategy/detail', account, { params: { strategy_id: strategy_id } }).then(
                resp => {
                    resolve(resp.data);
                },
                error => {
                    reject(error);
                },
            );
        });
    }

    share2Users(strategy_id, user_ids, role_id, share_type) {
        return new Promise((resolve, reject) => {
            http.put('/strategy/share', user_ids, {
                params: { strategy_id: strategy_id, share_type: share_type, role_id: role_id },
            }).then(
                resp => {
                    resolve(resp.data);
                },
                error => {
                    reject(error);
                },
            );
        });
    }

    getOnlineInstance(strategyId) {
        return new Promise((resolve, reject) => {
            http.get('/strategy/trading', { params: { strategy_id: strategyId } }).then(
                resp => {
                    resolve(resp.data);
                },
                error => {
                    reject(error);
                },
            );
        });
    }

    takeDownInstance({ strategyId, instance_key }) {
        return new Promise((resolve, reject) => {
            http.put('/strategy/logout', {}, { params: { strategy_id: strategyId, key: instance_key } }).then(
                resp => {
                    resolve(resp.data);
                },
                error => {
                    reject(error);
                },
            );
        });
    }

    updateStrategyUsers(strategy_id, user_ids, role_id, share_type) {
        return new Promise((resolve, reject) => {
            http.put('/strategy/share', user_ids, {
                params: {
                    strategy_id: strategy_id,
                    share_type: share_type,
                    role_id: role_id,
                },
            }).then(
                resp => {
                    resolve(resp.data);
                },
                error => {
                    reject(error);
                },
            );
        });
    }

    /**
     * 获取查询需要的策略信息
     */
    getStrategyAll(condition) {

        return new Promise((resolve, reject) => {
            // http.get('/strategy', {
            http.get('../v4/strategy', {
                params: {
                    user_id: condition ? condition.userId : '', 
                    token: condition ? condition.token : '',
                },
            }).then(
                resp => { resolve(resp.data); },
                error => { reject(error); },
            );
        });
    }

    /**
     * 获取查询需要的策略权益信息
     */
    getStrategyPosition(condition) {

        return new Promise((resolve, reject) => {
            // http.get('/strategy', {
            http.get('../v4/strategy', {
                params: {
                    user_id: condition ? condition.userId : '', 
                    token: condition ? condition.token : '',
                },
            }).then(
                resp => { resolve(resp.data); },
                error => { reject(error); },
            );
        });
    }
    
}

module.exports = { repoStrategy: new StrategyRepository() };
