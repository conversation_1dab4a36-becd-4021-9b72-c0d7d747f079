const httpRequest = require("../libs/http").http;
const { userRole } = require("../config/system-enum");
class UserRepository {
  createUser(user) {
    return new Promise((resolve, reject) => {
      httpRequest.post("/user", user).then(
        resp => {
          resolve(resp.data);
        },
        error => {
          reject(error);
        }
      );
    });
  }

  updateUser(user) {
    return new Promise((resolve, reject) => {
      httpRequest.put("/user", user).then(
        resp => {
          resolve(resp.data);
        },
        error => {
          reject(error);
        }
      );
    });
  }

  getAll() {
    return new Promise((resolve, reject) => {
      httpRequest.get("/user").then(
        resp => {
          resolve(resp.data);
        },
        error => {
          reject(error);
        }
      );
    });
  }

  deleteUser(target_user_id) {
    return new Promise((resolve, reject) => {
      httpRequest
        .delete("/user", {
          params: {
            target_user_id: target_user_id
          }
        })
        .then(
          resp => {
            resolve(resp.data);
          },
          error => {
            reject(error);
          }
        );
    });
  }

  resetPassword(username, password) {
    
    return new Promise((resolve, reject) => {
      httpRequest
        .put(
          "/user/password",
          {},
          {
            params: {
              username: username,
              password: password,
            }
          }
        )
        .then(
          resp => {
            resolve(resp.data);
          },
          err => {
            reject(err);
          }
        );
    });
  }

  changeUserRole(user_id, role_id) {
    return new Promise((resolve, reject) => {
      resolve({ errorCode: 0 });
    });
  }

  forceOfflineInstance(userId) {
    return new Promise((resolve, reject) => {
      httpRequest
        .put(
          "/user/logout",
          {},
          {
            params: {
              op_user_id: userId
            }
          }
        )
        .then(
          resp => {
            resolve(resp.data);
          },
          err => {
            reject(err);
          }
        );
    });
  }

  getTraderList() {
    return new Promise((resolve, reject) => {
      httpRequest
        .get("/user/simple", {
          params: {
            role_id: userRole.tradingMan.code
          }
        })
        .then(
          resp => {
            resolve(resp.data);
          },
          error => {
            reject(error);
          }
        );
    });
  }

  getRiskUserList() {
    return new Promise((resolve, reject) => {
      httpRequest
        .get("/user/simple", {
          params: {
            role_id: userRole.riskProtector.code
          }
        })
        .then(
          resp => {
            resolve(resp.data);
          },
          error => {
            reject(error);
          }
        );
    });
  }

  getLogHistory(userId, logType, { pageNo, pageSize }) {
    return new Promise((resolve, reject) => {
      httpRequest
        .get("/log/action", {
          params: {
            op_user_id: userId,
            log_type: logType,
            page_no: pageNo,
            page_size: pageSize
          }
        })
        .then(
          resp => {
            resolve(resp.data);
          },
          err => {
            reject(err);
          }
        );
    });
  }
}

module.exports = { repoUser:  new UserRepository() };
