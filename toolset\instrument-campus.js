
const path = require('path');
const fileSystem = require('fs');
const systemEnum = require('../config/system-enum').systemEnum;
const LocalSetting = require('../config/system-setting.local').LocalSetting;
const ConvertPinyin = require('../libs/3rd/pinyin-converter').ConvertPinyin;
const repoInstrument = require('../repository/instrument').repoInstrument;
const { LocalFileStorage } = require('./local-file-storage');

class InstrumentCampus {

    get systemEnum() {
        return systemEnum;
    }

    constructor(success_callback, error_callback) {

        var server_info = LocalFileStorage.getItem('serverInfo');
		this.isProductionServer = server_info.serverType == 1;
        this.successCallback = success_callback;
        this.errorCallback = error_callback;
        this.stockProfilePath = LocalSetting.getMarketStockPath();
        this.futureProfilePath = LocalSetting.getMarketFuturePath();
        this.optionProfilePath = LocalSetting.getMarketOptionPath();

        this.cache = {

            future: { loaded: false, data: [] },
            stock: { loaded: false, data: [] },
            option: { loaded: false, data: [] }
        };

        this.encoding = 'utf8';
    }

    async loadMarketInstruments() {

        let loadStock = !this.cache.stock.loaded ? this._loadStocks.bind(this) : () => {};
        let loadFuture = !this.cache.future.loaded ? this._loadFutures.bind(this) : () => {};
        let loadOption = !this.isProductionServer ? (!this.cache.option.loaded ? this._loadOptions.bind(this) : () => {}) : () => {};
        
        try {
            await Promise.all([loadStock(), loadFuture(), loadOption()]);
            this._notifySucceed();
        } 
        catch (e) {}

        return this;
    }

    async _loadStocks() {

        let stockPath = this.stockProfilePath;
        if(fileSystem.existsSync(stockPath)) {
            this._checkFreshStatus(stockPath);
        }

        if (!fileSystem.existsSync(stockPath)) {
            await this._requestStocks(stockPath);
            return;
        }

        await this._readFromFile(stockPath, 'stock', '股票');
    }

    async _loadFutures() {

        let futurePath = this.futureProfilePath;
        if(fileSystem.existsSync(futurePath)) {
            this._checkFreshStatus(futurePath);
        }

        if (!fileSystem.existsSync(futurePath)) {
            await this._requestFutures(futurePath);
            return;
        }

        await this._readFromFile(futurePath, 'future', '期货');
        return this;
    }

    async _loadOptions () {

        let optionPath = this.optionProfilePath;
        if(fileSystem.existsSync(optionPath)) {
            this._checkFreshStatus(optionPath);
        }

        if (!fileSystem.existsSync(optionPath)) {
            await this._requestOptions(optionPath);
            return;
        }

        await this._readFromFile(optionPath, 'option', '期权');
        return this;
    }

    _prepareDirectory(filePath) {

        var folder_path = path.dirname(filePath);
        var hieracies = [];

        while (!fileSystem.existsSync(folder_path)) {

            hieracies.push(folder_path);
            folder_path = path.dirname(folder_path);
        }

        while (hieracies.length > 0) {
            fileSystem.mkdirSync(hieracies.pop());
        }
        return this;
    }

    _createFile(filePath) {

        fileSystem.writeFileSync(filePath, '', this.encoding);
        return this;
    }

    /**
     * 如果不是新鲜的文件，就将会删除之前的文件
     * @param file_path
     */
    _checkFreshStatus(file_path) {

        var date_fmt = 'yyyy-MM-dd';
        var last_update_day = fileSystem.statSync(file_path).mtime.format(date_fmt);
        var today = new Date().format(date_fmt);

        if (new Date(today) > new Date(last_update_day)) {
            fileSystem.unlinkSync(file_path);
        }
    }

    _calculatePricePrecision(price_tick) {

        if (typeof price_tick == 'number' || typeof price_tick == 'string') {

            let str = price_tick.toString();
            return str.indexOf('.') < 0 ? 0 : str.split('.')[1].length;
        }
        else {
            return 2;
        }
    }

    async _requestFutures(futurePath) {

        return new Promise((resolve, reject) => {

            var exceptionMsg = '（期货）合约，数据源服务调用异常';

            repoInstrument.getAll(this.systemEnum.assetsType.future.code).then((response) => {

                if (response.errorCode !== 0) {

                    this._notifyFail(`市场期货列表未能加载成功，详细信息:'${response.errorMsg}'`);
                    return;
                }

                var future_list = response.data || [];
                future_list.forEach(ins => { 

                    ins.py = ConvertPinyin(ins.instrumentName, true);
                    ins.pricePrecision = this._calculatePricePrecision(ins.priceTick);
                });
                this._prepareDirectory(futurePath);
                this._createFile(futurePath);
                // write to file
                this._write2File(future_list, this.futureProfilePath, () => {
                    // notify external caller by callback
                    this.cache.future.data = future_list;
                    this.cache.future.loaded = true;
                    resolve();
                });
            }, 
            err => { this._notifyFail(exceptionMsg); reject(exceptionMsg); }).catch(
            _ => { this._notifyFail(exceptionMsg); reject(exceptionMsg); });
        });
    }

    async _requestOptions (optionPath) {

        return new Promise((resolve, reject) => {

            var exceptionMsg = '（期权）合约，数据源服务调用异常';

            repoInstrument.getAll(this.systemEnum.assetsType.option.code).then((response) => {

                if (response.errorCode !== 0) {
                    this._notifyFail(`市场期权列表未能加载成功，详细信息:'${response.errorMsg}'`);
                    return;
                }

                // var tmp_list = response.data || [];
                // var subject_dict = tmp_list.groupBy(x => x.underlyingInstrument);
                // var option_list = [];

                // for (let sub_ins in subject_dict) {

                //     let child_ins = subject_dict[sub_ins];
                //     let date_dict = child_ins.groupBy(x => x.expireDate);
                //     let date_list = [];

                //     for(let key_date in date_dict) {
                //         date_list.push({ 
                //             expireDate: key_date,
                //             longIns: date_dict[key_date].filter(x => x.optionType == 1).orderBy(x => x.strikePrice),
                //             shortIns: date_dict[key_date].filter(x => x.optionType == 2).orderBy(x => x.strikePrice)
                //         });
                //     }

                //     option_list.push({
                //         underlyingInstrument: sub_ins, 
                //         underlyingInstrumentName: child_ins[0].instrumentName.replace('买权', '').replace('卖权', ''),
                //         dateGroups: date_list.orderBy(x => x.expireDate)
                //     });
                // }

                var option_list = response.data || [];
                option_list.forEach(ins => {

                    ins.py = ConvertPinyin(ins.instrumentName, true);
                    ins.pricePrecision = this._calculatePricePrecision(ins.priceTick);
                });

                this._prepareDirectory(optionPath);
                this._createFile(optionPath);
                // write to file
                this._write2File(option_list, this.optionProfilePath, () => {
                    // notify external caller by callback
                    this.cache.option.data = option_list;
                    this.cache.option.loaded = true;
                    resolve();
                });
            },
            err => { this._notifyFail(exceptionMsg); reject(exceptionMsg); }).catch(
            _ => { this._notifyFail(exceptionMsg); reject(exceptionMsg); });
        });
    }

    async _requestStocks(stockPath) {

        return new Promise((resolve, reject) => {

            var exceptionMsg = '（股票）合约，数据源服务调用异常';

            repoInstrument.getAll(this.systemEnum.assetsType.stock.code).then((response) => {

                if (response.errorCode !== 0) {

                    this._notifyFail(`市场股票列表未能加载成功，详细信息:'${response.errorMsg}'`);
                    return;
                }

                var stock_list = response.data || [];
                stock_list.forEach(ins => {

                    /**
                     * remark: fixed by @Steven in 0224/2021 ~ 底层合约数据可能在该字段传0，在底层数据确保无误后，该逻辑需删除
                     */
                    if (ins.priceTick === 0) {
                        ins.priceTick = 0.01;
                    }

                    ins.py = ConvertPinyin(ins.instrumentName, true);
                    ins.pricePrecision = this._calculatePricePrecision(ins.priceTick);
                });
                this._prepareDirectory(stockPath);
                this._createFile(stockPath);
                // write to file
                this._write2File(stock_list, this.stockProfilePath, () => {
                    // notify external caller by callback
                    this.cache.stock.data = stock_list;
                    this.cache.stock.loaded = true;
                    resolve();
                });
            },
            err => { this._notifyFail(exceptionMsg); reject(exceptionMsg); }).catch(
            _ => { this._notifyFail(exceptionMsg); reject(exceptionMsg); });
        });
    }

    async _readFromFile(filePath, type, type_name) {

        return new Promise((resolve, reject) => {

            fileSystem.readFile(filePath, this.encoding, (err, data) => {

                try {
                    var dataList = JSON.parse(data);

                    if (!(dataList instanceof Array)) {
                        this._recoverLocalChange(filePath);
                        this._notifyFail(`读取到[${ type_name }]列表，数据结构非预期`);
                    }
                    else {
                        this.cache[type].data = dataList;
                        this.cache[type].loaded = true;
                        resolve();
                    }
                }
                catch (ex) {

                    this._recoverLocalChange(filePath);
                    reject('读取数据文件文件发生异常');
                    this._notifyFail(`读取数据文件文件发生异常，类型：${type}，文件路径：${filePath}`);
                }
            });
        });
    }

    _recoverLocalChange(filePath) {
        try { fileSystem.unlink(filePath, () => {}); } catch (ex) {}
    }

    _write2File(dataList, filePath, completeCallback) {

        try {
            fileSystem.writeFile(filePath, JSON.stringify(dataList), this.encoding, completeCallback);
        }
        catch (ex) {
            this._notifyFail('数据写入本地文件失败!');
        }
        return this;
    }

    _notifySucceed() {

        let stockList = this.cache.stock.data || [];
        let futureList = this.cache.future.data || [];
        let optionList = this.cache.option.data || [];

        this.successCallback(stockList, futureList, optionList);
    }

    _notifyFail(reason) {
        this.errorCallback(reason);
    }
}

module.exports = { InstrumentCampus };