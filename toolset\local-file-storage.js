const path = require('path');
const fs = require('fs');
const { LocalSetting } = require('../config/system-setting.local');
const StorageFilePath = path.join(LocalSetting.userDataPath, 'local-data-dict.js');
const { helper } = require('../libs/helper');
const LocalDict = {

    data: {},
    lastmtime: 0,
};

class LocalFileStorage {

    /**
     * @param {String} key 
     */
    static getItem(key) {

        if (helper.isNone(key)) {
            return undefined;
        }
        
        if (!fs.existsSync(StorageFilePath)) {
            return undefined;
        }
        
        let finfo = fs.statSync(StorageFilePath);
        if (!finfo.isFile()) {
            return undefined;
        }

        if (finfo.mtimeMs == LocalDict.lastmtime) {
            return LocalDict.data[key];
        }

        let renewed = this._read();
        LocalDict.lastmtime = finfo.mtimeMs;
        return (LocalDict.data = renewed)[key];
    }

    /**
     * @param {String} key 
     * @param {*} value 
     */
    static setItem(key, value) {
        
        if (helper.isNone(key)) {

            console.error('data key must not be empty');
            return;
        }

        if (fs.existsSync(StorageFilePath)) {
            
            let finfo = fs.statSync(StorageFilePath);
            if (!finfo.isFile()) {

                console.error(`file reading error: [${StorageFilePath}] is not a file`);
                return;
            }
        }
        
        let latest = this._read();
        latest[key] = value;
        fs.writeFileSync(StorageFilePath, JSON.stringify(latest));
    }

    /**
     * @param {String} key 
     */
    static removeItem(key) {

        if (helper.isNone(key)) {
            return;
        }
        
        let value = LocalDict.data[key];
        if (value === undefined) {
            return;
        }

        delete LocalDict.data[key];
        fs.writeFileSync(StorageFilePath, JSON.stringify(LocalDict.data));
    }

    static _read() {

        if (!fs.existsSync(StorageFilePath)) {
            return {};
        }

        let data = {};
        let content = fs.readFileSync(StorageFilePath, { encoding: 'utf8' });
        try {
            let struc = JSON.parse(content);
            if (helper.isJson(struc)) {
                data = struc;
            }
        }
        catch(ex) {
            console.error('file reading exception', ex);
        }

        return data;
    }
}

module.exports = {
    LocalFileStorage,
};