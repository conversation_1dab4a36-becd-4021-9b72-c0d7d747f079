const electron = require('electron');
const { helper } = require('../libs/helper');

/**
 * 窗口位置及尺寸状态管理
 */
class WinPosSizeMgr {
    
    /**
     * @param {string} name 窗口名称
     * @param {electron.BrowserWindow} winRef 窗口引用
     */
    constructor(name, winRef) {

        this.storageKey = `win_info_${name}`;
        this.winRef = winRef;
    }

    /**
     * 恢复上次使用时的位置与尺寸
     */
    recover() {

        let data = this.getLast();
        if (data == null) {
            return;
        }

        try {

            if (data.isMaximized) {
                this.winRef.maximize();
            }
            else {

                this.winRef.setSize(data.width, data.height);
                this.winRef.setPosition(data.left, data.top);
            }
        }
        catch(ex) {
            console.error(ex);
        }
    }

    /**
     * 获取窗口上一次的尺寸和位置
     */
    getLast() {

        var content = localStorage.getItem(this.storageKey);
        var data = null;
        try { data = JSON.parse(content); } catch(ex) {}

        if (helper.isJson(data)) {
            
            let { width, height, isMaximized, left, top } = data;
            return { width, height, isMaximized, left, top };
        }
        else {
            return null;
        }
    }
    
    /**
     * 启动窗口尺寸和位置变化，并记录
     */
    abserve() {

        this.winRef.on('resize', this._remember.bind(this));
        this.winRef.on('move', this._remember.bind(this));
        this.winRef.on('maximize', this._remember.bind(this));
        this._remember();
    }

    _remember() {
        
        var size = this.winRef.getSize();
        var width = size[0]
        var height = size[1];
        var isMaximized = this.winRef.isMaximized();
        var pos = this.winRef.getPosition();
        var left = pos[0]
        var top = pos[1];
        localStorage.setItem(this.storageKey, JSON.stringify({ width, height, isMaximized, left, top }));
    }
}

module.exports = {
    WinPosSizeMgr,
};