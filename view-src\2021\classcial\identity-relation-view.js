const { IView } = require('../../../component/iview');
const { IdentityRelation } = require('./identity-relation');

/**
 * 联动视图选项
 */
const IRViewOption = {

    /** 是否展示产品下拉列表 */
    showProduct: true,
    /** 是否展示策略下拉列表 */
    showStrategy: true,
    /** 是否展示账号下拉列表 */
    showAccount: true,
};

class IdentityRelationView extends IView {

    get productId() {
        return this.istates.productId;
    }

    get strategyId() {
        return this.istates.strategyId;
    }

    get accountId() {
        return this.istates.accountId;
    }

    getProductName() {
        
        var matched = this._irelation.allProducts.find(x => x.id == this.productId);
        return matched ? matched.name : null;
    }

    getStrategyName() {
        
        var matched = this._irelation.allStrategies.find(x => x.id == this.strategyId);
        return matched ? matched.name : null;
    }

    getAccountName() {
        
        var matched = this._irelation.allAccounts.find(x => x.id == this.accountId);
        return matched ? matched.name : null;
    }

    constructor(view_name, title, options = IRViewOption) {

        super(view_name, false, title);
        var irelation = new IdentityRelation();
        this.helper.extend(irelation.istates, options);
        irelation.initialize(this.userInfo.userId);
        this._irelation = irelation;

        this.registerEvent('set-product-id', (pid) => {

            this.istates.productId = pid;
            this.handleProductChange();
        });

        this.registerEvent('set-strategy-id', (sid) => {

            this.istates.strategyId = sid;
            this.handleStrategyChange();
        });

        this.registerEvent('set-account-id', (aid) => {

            this.istates.accountId = aid;
            this.handleAccountChange();
        });
    }

    /**
     * 当前产品列表
     */
    get products() {
        return this._irelation.products;
    }
    
    /**
     * 当前策略列表
     */
    get strategies() {
        return this._irelation.strategies;
    }

    /**
     * 当前账号列表
     */
    get accounts() {
        return this._irelation.accounts;
    }

    /**
     * 当前级联关系选择状态
     */
    get istates() {
        return this._irelation.istates;
    }

    /**
     * 处置产品变更事件
     */
    handleProductChange() {
        this._irelation.handleProductChange();
    }

    /**
     * 处置策略变更事件
     */
    handleStrategyChange() {
        this._irelation.handleStrategyChange();
    }

    /**
     * 处置账号变更事件
     */
    handleAccountChange() {
        this._irelation.handleAccountChange();
    }

    /**
     * 提取当前组件的选择情况快照
     */
    snapshoot() {
        return new IdentitySnapshot(SnapshotInstanceState, this);
    }
}

/**
 * 快照对象创建，辅助状态标识（该标识无法从外部提供认证）
 */
const SnapshotInstanceState = new Object();

/**
 * 当前组件的选择情况，数据快照
 */
class IdentitySnapshot {

    /**
     * @param {Object} stateObj 防止外部创建实例的标识对象
     * @param {IdentityRelationView} irv 
     */
    constructor(stateObj, irv) {

        if (stateObj !== SnapshotInstanceState) {
            throw new Error('unable to create an instance from outside scope');
        }

        this.productId = irv.productId;
        this.productName = irv.getProductName();
        this.strategyId = irv.strategyId;
        this.strategyName = irv.getStrategyName();
        this.accountId = irv.accountId;
        this.accountName = irv.getAccountName();
    }
}

module.exports = { IdentityRelationView, IdentitySnapshot };