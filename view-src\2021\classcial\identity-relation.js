const { repoFund } = require('../../../repository/fund');
const { repoAccount } = require('../../../repository/account');

class Product {

    constructor(struc) {
        
        this.id = struc.fundId;
        this.name = struc.fundName;
        this.strategies = makeStrategies(struc.strategyAccountRetBeans);
        this.accounts = makeAccounts(struc.accounts);
    }
}

class Strategy {
 
    constructor(struc) {

        this.id = struc.strategyId;
        this.name = struc.strategyName;
        this.accounts = makeAccounts(struc.accounts);
    }
}

class Account {

    constructor(struc) {
        
        this.identityId = struc.identityId;
        this.id = struc.accountId;
        this.name = struc.accountName;
        this.isCredit = !!struc.credit;
    }
}

/**
 * @param {Array} records 
 * @returns {Array<Product>} 
 */
function makeProducts(records) {
    return records instanceof Array ? records.map(item => new Product(item)) : [];
}

/**
 * @param {Array} records 
 * @returns {Array<Strategy>} 
 */
function makeStrategies(records) {
    return records instanceof Array ? records.map(item => new Strategy(item)) : [];
}

/**
 * @param {Array} records 
 * @returns {Array<Account>} 
 */
function makeAccounts(records) {
    return records instanceof Array ? records.map(item => new Account(item)) : [];
}

/**
 * @returns {Array<{ label: null, value: null }>}
 */
function createPairs() {
    return [];
}

/**
 * @returns {Array<{ label: String, value: any, identityId: any, isCredit: Boolean }>}
 */
function createAccounts() {
    return [];
}

class IdentityRelation {

    constructor() {

        /** 所有产品信息 */
        this.allProducts = makeProducts();
        /** 所有策略信息*/
        this.allStrategies = makeStrategies();
        /** 所有账号信息 */
        this.allAccounts = makeAccounts();

        /** 当前的产品信息 */
        this.products = createPairs();
        /** 当前的策略信息*/
        this.strategies = createPairs();
        /** 当前的账号信息*/
        this.accounts = createAccounts();

        this.istates = {

            productId: null,
            strategyId: null,
            accountId: null,

            showProduct: true,
            showStrategy: true,
            showAccount: true,
        };
    }
    
    async initialize(userId) {

        this.userId = userId;
        var accounts = await this.requestAccounts(userId);

        var tprods = makeProducts(accounts.map(x => ({ fundId: x.fundId, fundName: x.fundName, strategyAccountRetBeans: [], accounts })));
        var tstras = makeStrategies(accounts.map(x => ({ fundId: x.fundId, fundName: x.fundName, strategyAccountRetBeans: [], accounts })));
        var tacnts = makeAccounts(accounts);
        
        this.allProducts.refill(tprods);
        this.allStrategies.refill(tstras);
        this.allAccounts.refill(tacnts);

        this.products.refill(this.convert2ProductDtos(tprods));
        this.strategies.refill(this.convert2StrategyDtos(tstras));
        this.accounts.refill(this.convert2AccountDtos(tacnts));

        if (tprods.length > 0) {
            this.istates.productId = tprods[0].id;
        }

        if (tstras.length > 0) {
            this.istates.strategyId = tstras[0].id;
        }

        if (tacnts.length > 0) {
            this.istates.accountId = tacnts[0].id;
        }
    }

    /**
     * @returns {Array<{identityId, accountId, accountName, fundId, fundName, strategyId, strategyName, credit}>}
     */
    async requestAccounts(userId) {

        var resp = await repoAccount.getAccountDetailInfo({ identity_id: '', userId });
        if (resp.errorCode == 0) {

            let records = resp.data.list;
            let accounts = records instanceof Array ? records.map(x => ({
                                identityId: x.id, 
                                accountId: x.identityId, 
                                accountName: x.identityName, 
                                fundId: x.fundId, 
                                fundName: x.fundName, 
                                strategyId: x.strategyId, 
                                strategyName: x.strategyName,
                                credit: x.credit, })) : [];
            return accounts;
        }
        else {

            this.interaction.showError('获取账号资金详情发生异常：' + resp.errorMsg);
            return [];
        }
    }

    /**
     * @param {Array<Product>} records 
     * @returns 
     */
    convert2ProductDtos(records) {
        return records.map(item => ({ value: item.id, label: item.name }));
    }

    /**
     * @param {Array<Strategy>} records 
     * @returns 
     */
    convert2StrategyDtos(records) {
        return records.map(item => ({ value: item.id, label: item.name }));
    }

    /**
     * @param {Array<Account>} records 
     * @returns 
     */
    convert2AccountDtos(records) {
        return records.map(item => ({ value: item.id, label: item.name, identityId: item.identityId, isCredit: item.isCredit }));
    }
    
    handleProductChange() {

        this.istates.strategyId = null;
        this.istates.accountId = null;
        
        /**
         * 策略，账号，填入为选中的产品下的所有的策略，账号(选中的产品下的，策略下挂的账号与产品下挂的账号的并集去重);
         */

        if (this.istates.productId) {

            let matched = this.allProducts.find(x => x.id == this.istates.productId);
            let strategies = matched.strategies;
            let accounts = matched.accounts;
            
            this.strategies.refill(this.convert2StrategyDtos(strategies));
            this.accounts.refill(this.convert2AccountDtos(accounts));
        }
        else {

            this.strategies.refill(this.convert2StrategyDtos(this.allStrategies));
            this.accounts.refill(this.convert2AccountDtos(this.allAccounts));
        }
    }
    
    handleStrategyChange() {

        this.istates.accountId = null;
        var prodId = this.istates.productId;
        var straId = this.istates.strategyId;
        var isProductInvolved = !!prodId;
        var isStrategyInvolved = !!straId;
        var intersects = makeAccounts();

        if (isProductInvolved && isStrategyInvolved) {
            
            let pacnts = this.allProducts.find(x => x.id == prodId).accounts;
            let sacnts = this.allStrategies.find(x => x.id == straId).accounts;
            intersects.refill(pacnts.filter(x => sacnts.some(y => y.id == x.id)));
        }
        else if (isProductInvolved) {

            let pacnts = this.allProducts.find(x => x.id == prodId).accounts;
            intersects.refill(pacnts);
        }
        else if (isStrategyInvolved) {

            let sacnts = this.allStrategies.find(x => x.id == straId).accounts;
            intersects.refill(sacnts);
        }
        else {
            intersects.refill(this.allAccounts);
        }

        this.accounts.refill(this.convert2AccountDtos(intersects));
    }

    handleAccountChange() {
        
        //
    }
}

module.exports = { IdentityRelation };