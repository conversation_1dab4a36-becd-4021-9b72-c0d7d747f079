const { IView } = require('../../../component/iview');
const { SmartTable } = require('../../../libs/table/smart-table');
const { ColumnCommonFunc } = require('../../../libs/table/column-common-func');

/**
 * 拼音样本字典
 */
const PinyinMap = {};

/**
 * 典型数据视图（工具栏 + 数据列表 + 分页，上中下3部分）
 */
class TypicalDataView extends IView {

    /**
     * @param {String} viewName 
     * @param {Boolean} standAlong 
     * @param {String} title 
     */
    constructor(viewName, standAlong, title) {

        super(viewName, standAlong, title);
        this.states = { keywords: null };
        var paging = this.systemSetting.tablePagination;
        this.paging = {

            pageSizes: paging.pageSizes,
            pageSize: paging.pageSize,
            layout: paging.layout,
            total: 0,
            page: 1,
        };
    }

    /**
     * 创建工具栏
     */
    createToolbar() {

        return new Vue({

            el: this.$toolbar,
            data: {
                states: this.states,
            },
            methods: this.helper.fakeVueInsMethod(this, [
                this.handleSearch,
            ]),
        });
    }

    /**
     * 执行工具栏搜索请求
     */
    handleSearch() {

        this.tableObj.setPageIndex(1, false);
        this.tableObj.setKeywords(this.states.keywords, false);
        this.tableObj.customFilter((record) => { return this.testRecords(record); });
    }

    /**
     * 检测数据行是否满足搜索条件
     * @param {Object} record 
     * @returns {Boolean}
     */
    testRecords(record) {
        return this.tableObj.matchKeywords(record);
    }

    /**
     * 测试样本内容对应的拼音是否能被关键字命中
     * @param {String} sample 目标字串样本
     * @param {String} keywords 关键字
     */
    testPy(sample, keywords) {

        if (this.helper.isNone(keywords)) {
            return true;
        }
        else if (typeof sample != 'string' || typeof keywords != 'string') {
            return false;
        }
        else if (sample.length == 0) {
            return false;
        }
        else if (keywords.length == 0) {
            return true;
        }

        var matched = PinyinMap[sample];
        if (matched === undefined) {
            matched = PinyinMap[sample] = this.helper.pinyin(sample);
        }

        return typeof matched == 'string' && matched.indexOf(keywords) >= 0;
    }

    /**
     * 提取勾选记录
     * @param {Boolean} isAll 是否忽略勾选，返回所有记录
     * @returns {Array}
     */
    extractRecords(isAll) {
        return isAll === true ? this.tableObj.extractAllRecords() : this.tableObj.extractCheckedRecords();
    }

    /**
     * 创建底边栏（通常放置分页组件）
     */
    createFooter() {
        
        return new Vue({

            el: this.$footer,
            data: {
                paging: this.paging,
            },
            methods: this.helper.fakeVueInsMethod(this, [

                this.handlePageSizeChange,
                this.handlePageChange,
            ]),
        });
    }

    handlePageSizeChange() {

        this.tableObj.setPageSize(this.paging.pageSize, false);
        this.tableObj.setPageIndex(1);
    }

    handlePageChange() {
        this.tableObj.setPageIndex(this.paging.page);
    }

    createTable(options) {

        this.helper.extend(this, ColumnCommonFunc);
        var tableObj = new SmartTable(this.$table, this.identifyRecord, this, this.helper.extend({

            tableName: 'smt-' + this.viewName,
            displayName: this.title,
            enableConfigToolkit: true,
            defaultSorting: { prop: 'id', direction: 'desc' },
            rowSelected: this.handleRowSelected.bind(this),
            rowDbClicked: this.handleRowDblClicked.bind(this),
            recordsFiltered: this.handleTableFiltered.bind(this),

        }, options));

        tableObj.setPageSize(this.paging.pageSize);

        if (this.mheight > 0) {
            tableObj.setMaxHeight(this.mheight);
        }
        
        return this.tableObj = tableObj;
    }

    /**
     * 行数据组件生成方法
     * @param {Object} record 
     */
    identifyRecord(record) {
        return record.id;
    }

    handleRowSelected(record) {
        //
    }

    handleRowDblClicked(record) {
        //
    }

    handleTableFiltered(total) {
        this.paging.total = total;
    }

    /**
     * 创建数据列自定义数据源
     * @param {Array} records 
     * @param {String} propName 
     * @param {*} translators 
     */
    makeColFilters(records, propName, translators) {
        return SmartTable.MakeColFilters(records, propName, { translators: translators });
    }

    /**
     * 请求数据
     */
    async requestRecords() {
        throw new Error('not implemented');
    }

    /**
     * 保持定时刷新
     * @param {Number} frequency 两次刷新间隔毫秒数
     */
    timelyRefresh(frequency) {

        if (this.refreshJob !== undefined) {

            console.error('refresh job has been created');
            return;
        }

        /**
         * 定时刷新任务
         */
        this.refreshJob = setInterval(async () => {
            
            if (this._isHalted === true || this._isRefreshJobRunning) {
                return;
            }

            this._isRefreshJobRunning = true;
            try {
                await this.requestRecords();
            }
            catch(ex) {
                console.error(ex);
            }
            finally {
                this._isRefreshJobRunning = false;
            }

        }, frequency);
    }

    /**
     * 设置定时刷新为停止状态
     */
    stop2Refresh() {

        /**
         * 是否当前定时刷新处于中止状态
         */
        this._isHalted = true;
    }

    /**
     * 重新启动定时刷新
     */
    resume2Refresh() {
        this._isHalted = false;
    }

    resetControls() {

        this.states.keywords = null;
        this.tableObj.removeAllColFilters();
    }

    refresh() {
        
        this.resetControls();
        this.requestRecords();
        this.interaction.showSuccess(this.title + '，刷新请求已发出');
    }

    config() {
        this.tableObj.showColumnConfigPanel();
    }

    exportSome() {

        var date = new Date().format('yyyyMMdd');
        var time = new Date().format('hhmmss');
        this.tableObj.exportAllRecords(`${this.title}-${date}-${time}`);
    }

    /**
     * 设置表格最大高度
     * @param {*} height 
     * @param {*} height 
     * @param {Boolean} isMaximized 
     */
    setTableHeight(height, width, isMaximized) {
        this.tableObj.setMaxHeight(height - this.hoffset);
    }

    /**
     * 表格滚动至最左端
     */
    scroll2Left() {
        this.tableObj.scroll2Left(0);
    }

    /**
     * 表格滚动至最顶端
     */
    scroll2Top() {
        this.tableObj.scroll2Top(0);
    }

    /**
     * 表格滚动至最左最顶
     */
    scroll2Start() {
        
        this.scroll2Left();
        this.scroll2Top();
    }

    /**
     * 处理当前视图获得焦点事件
     */
    handleActivated(tab) {

        this.simulateWinSizeChange();
        this.tableObj.fitColumnWidth();
    }

    /**
     * 处理当前视图失去焦点事件
     */
    handleInactivated(tab) {
        //
    }

    /**
     * @param {*} $container 
     * @param {*} options 
     * @param {{
     *      maxHeight: 0,
     *      heightOffset: 0,
     *      tableName: String,
     *      displayName: String,
     *      defaultSorting: { prop: String, direction: String },
     * }} tableOptions <optional>
     */
    build($container, options, tableOptions) {

        super.build($container, options);

        /**
         * 视图配置选项
         */
        this.voptions = options;

        var viewRootClass = '.typical-data-view';
        this.$toolbar = this.$container.querySelector(viewRootClass + ' > .user-toolbar');
        this.$footer = this.$container.querySelector(viewRootClass+ ' > .user-footer');
        this.$table = this.$container.querySelector(viewRootClass + ' > .data-list');

        this.toolbarApp = this.createToolbar();
        this.footerApp = this.createFooter();
        var mheight = tableOptions.maxHeight;
        var hoffset = tableOptions.heightOffset;
        /** 表格初始最大高度 */
        this.mheight = typeof mheight == 'number' && mheight >= 50 ? mheight : 0;
        /** 表格动态适配高度变化时的缩减量 */
        this.hoffset = typeof hoffset == 'number' ? hoffset : 0;
        this.createTable(tableOptions);

        /** 监听设置表格高度指令 */
        this.registerEvent('table-max-height', this.setTableHeight.bind(this));
        /** 监听表格滚动至左端 */
        this.registerEvent('table-scroll-2-left', this.scroll2Left.bind(this));
        /** 监听表格滚动至顶端 */
        this.registerEvent('table-scroll-2-top', this.scroll2Top.bind(this));
        /** 监听表格滚动至初始位置 */
        this.registerEvent('table-scroll-2-start', this.scroll2Start.bind(this));
        /** 监听该视图所在TAB被激活 */
        this.registerEvent(this.systemEvent.tabActivated, this.handleActivated.bind(this));
        /** 监听该视图所在TAB被隐藏 */
        this.registerEvent(this.systemEvent.tabInactivated, this.handleInactivated.bind(this));
    }
}

module.exports = { TypicalDataView };