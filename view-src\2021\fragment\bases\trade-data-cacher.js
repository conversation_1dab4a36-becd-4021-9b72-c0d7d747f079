const os = require('os');
const fs = require('fs');
const path = require('path');
const { helper } = require('../../../../libs/helper');
const { LocalSetting } = require('../../../../config/system-setting.local');

/** 行结束符 */
const LineSplitter = os.EOL;
/** 文件内容编码 */
const FileContentEncoding = 'utf8';
/** 文件最新写入信息描述长度 */
const VersionInfoMessageLength = 200;

/**
 * 推送数据写入到本地化文件的设置
 */
const JobSetting = {

    /** 每一次常规检测，间隔毫秒数 */
    Interval: 1000 * 40,
    /** 距上一次实际执行，最大间隔毫秒数后，无论已累积推送数量多少，均需写入一次 */
    LeastMs: 1000 * 60 * 10,
    /** 距上一次实际执行，当前已累积最小推送数据量，方可写入一次 */
    MinSize: 1000,
};

const UserHandlers = {

    /**
     * 首屏数据异步访问方法
     * @returns {{
        * errorCode,
        * errorMsg,
        * data: {
            * pageNo,
            * pageSize,
            * totalPages,
            * totalSize,
            * contents: [],
            * identityId,
        * },
    * }}
        */
    firstScreenQuerier: async function () { throw new Error('not implemented'); },

    /**
     * 增量（全量）数据异步访问方法
     * @param {*} since 本地数据最新版本标识
     * @returns {{
        * errorCode,
        * errorMsg,
        * data: Array<Array>,
    * }}
        */
    increQuerier: async function (since) { throw new Error('not implemented'); },

    /**
     * 实时数据变化订阅者
     */
    subscriber: function () { throw new Error('not implemented'); },

    /**
     * 批量数据消费
     * @param {Array} contents 数据列表
     * @param {Number} totalSize 服务器端总数据量（该参数仅在首屏数据时，通知界面）
     */
    consumer: function (contents, totalSize) { throw new Error('not implemented'); },

    /**
     * 实时推送数据消费
     * @param {*} data 推送的实时数据
     */
    realtimeConsumer: function (data) { throw new Error('not implemented'); },

    /**
     * 合并新老数据
     * @param {Array<Array>} locals
     * @param {Array<Array>} newers
     * @returns {Array<Array>}
     */
    mergeAll: function (locals, newers) { throw new Error('not implemented'); },

    /**
     * 显示加载效果
     */
    showLoading: function (options) { throw new Error('not implemented'); },

    /**
     * 显示错误HTTP信息
     */
    showError: function (options) { throw new Error('not implemented'); },
};

/**
 * 已经写入到本地化文件，的最新一笔数据版本信息
 */
class RecordVersionInfo {

    constructor(struc) {

        /** 记录ID字段 */
        this.id = struc.id;
        /** 记录最新更新时间 */
        this.updateTime = struc.updateTime;

        /** 对版本信息，字段结构缺失进行容错 */
        if (this.updateTime === undefined) {
            this.updateTime = 0;
        }
    }
}

class TradeDataCacher {

    /**
     * 本地化数据文件完整路径
     */
    get dataFilePath() {
        return this._makeDataPath();
    }

    /**
     * id字段，在一维数据中的索引位置
     */
    get RecordIdIdx() {
        return this.titles2IdxMap['id'];
    }

    /**
     * updateTime字段，在一维数据中的索引位置
     */
    get RecordUpdateTimeIdx() {
        return this.titles2IdxMap['updateTime'];
    }

    /**
     * 获取，数据消费者是否处于正常状态
     */
    get isConsumerReady() {
        return this._isConsumerReady === true;
    }

    /**
     * 切换数据消费者是否正常收发
     */
    switchConsumerState() {

        if ((this._isConsumerReady = !this._isConsumerReady)) {
            this._handleFlushRequest();
        }
    }

    /**
     * 
     * @param {Array<String>} titles 标题，标题字段列表的顺序、数量，与服务器推送数据，完全一致（如遇服务器端调整，需同步调整）
     * @param {Boolean} isByUpdateTime 本地缓存版本信息，关键识别标识，是否根据更新时间（否则根据ID字段）
     */
    constructor(titles, isByUpdateTime) {

        /**
         * 交易数据结构 ~ 标题信息
         */
        this.titles = titles;
        
        var map = {};
        titles.forEach((name, idx) => { map[name] = idx; });

        /**
         * 交易数据结构 ~ 标题字段与对应序号，查询关系字典
         */
        this.titles2IdxMap = map;

        /**
         * 本地缓存版本信息，关键识别标识是否根据更新时间
         */
        this.isByUpdateTime = isByUpdateTime;
        
        /**
         * 实时推送数据，缓冲区
         * 1. 未完成接收服务器批量推送时，实时推送的数据，存放于该缓冲区
         * 2. 已完成接收服务器批量推送时，如果该缓冲区存在数据，则一次性将内部所有数据渲染至TABLE
         */
        this.hotChanges = [];

        /**
         * 实时推送 & 需要被本地化的，增量数据，缓冲区
         */
        this.incrChanges = [];

        /**
         * 设置默认状态下，消费者是否处于正常可接收数据推送的状态
         */
        this._isConsumerReady = true;
    }

    /**
     * 启动缓存模块
     */
    registerHandlers(handlers = UserHandlers) {

        /** 数据服务异步接口 */
        this.handlers = handlers;
    }

    /**
     * 启动缓存模块
     * 1. 该方法可被重复调用，每次针对不同的标识
     * 2. 针对上下文，无变动的场景，仅调用一次即可（典型场景：用户维度的交易数据）
     * 3. 针对上下文，存在可能变动的场景，每次调用该方法，实现主体的切换（典型场景：某个账号的交易数据）
     * @param {String} identifier 缓存文件标识
     */
    startup(identifier) {

        /** 今日数据种类（用于文件名标识等用途） */
        this.identifier = identifier;
        /** 重置为，批量数据推送 & 接收，尚未就位 */
        this._setBatchDataReadyState(false);
        /** 快速获取用于首屏展示的数据 */
        this.quickRequest();
        /** 清空实时数据缓冲区 */
        this._clearBuffereds();
        /** 清空增量数据缓冲区 */
        this._clearIncrChanges();
        /** 请求当前截止当前增量（全量）数据 */
        this.turn2Request();
        /** 订阅实时数据 */
        this.handlers.subscriber();
        /** 开始周期性的推送数据写入 */
        this._startIntervalWrite();
    }

    /**
     * 快速获取首页数据
     */
    async quickRequest() {

        /**
         * 首屏数据快速获取操作，是否已完成
         */
        this.isQuickRequestCompleted = false;
        var resp = await this.handlers.firstScreenQuerier();

        if (resp.errorCode != 0) {
            this.handlers.showError(`交易数据获取错误 > ${resp.errorMsg}`);
        }
        else if (this.isDataPushReceived) {

            /**
             * 批量推送数据，已先于首屏数据完成接收处理，故此处的首屏数据，作废弃处理
             */
        }
        else {

            let bean = resp.data;
            let contents = bean.contents;
            /** 将首行标题栏抛掉 */
            let titles = contents.shift();
            this.handlers.consumer(contents, bean.totalSize);
        }

        /**
         * 1. 首屏数据获取、渲染完成后，即将加载效果隐藏
         * 2. 加载效果，可由2个环节进行关闭：
         *    A. 全量（增量）数据推送接收处理完成，进行关闭
         *    B. 首屏数据获取、渲染完成，进行关闭
         */
        this.hideLoading();
        this.isQuickRequestCompleted = true;
    }

    /**
     * 异步获取增量（全量）数据
     * @param {*} since 本地数据版本标识
     */
    async batchRequest(since) {

        let resp = await this.handlers.increQuerier(since);
        if (resp.errorCode != 0) {

            /** 隐藏数据加载效果 */
            this.hideLoading();
            /** 标识数据推送已完成接收 */
            this._setBatchDataReadyState(true);
            this.handlers.showError(`交易数据获取错误 > ${resp.errorMsg}`);
            return;
        }

        let newers = resp.data;
        this.logEvent(`received data push, length = ${newers.length}`);

        /**
         * 刨除标题元素
         * 1. 仅当存在至少1条数据时，后端才给出具体的标题信息
         * 2. 如果没有数据，则标题信息也不存在
         */
        let titles = newers.shift();

        /**
         * 1. 尚未本地化情况下，首次的批量数据，进行完整本地化
         * 2. 已发生本地化情况下，增量的数据，与本地化版本的数据，进行合并后，对合并结果再进行本地化
         */

        let locals = this._readLocalized();
        this.logEvent(`completed to load local records`);
        let mergeds = newers.length == 0 ? locals : locals.length == 0 ? newers : this.handlers.mergeAll(locals, newers);
        this.logEvent(`data merged: locals = ${locals.length}, newers = ${newers.length}, mergeds = ${mergeds.length}`);
        this.localize(mergeds);
        this.logEvent(`completed to overwrite to local`);
        this.handlers.consumer(mergeds);
        this.logEvent(`completed to render to table`);

        /** 隐藏数据加载效果 */
        this.hideLoading();
        /** 一次性将批次数据推送到消费处 */
        this._flushAll();
        /** 标识数据推送已完成接收 */
        this._setBatchDataReadyState(true);
    }

    /**
     * 向服务器动态请求批量数据
     */
    turn2Request() {

        /**
         * @param {Boolean | RecordVersionInfo} version 是否有效获得 | 实际的版本信息 | undefined
         * @param {String} reason
         */
        var callback = (version, reason) => {

            if (version === false) {

                try {

                    console.error('version info not read properly & localized file removed: ' + reason);
                    fs.unlinkSync(this.dataFilePath);
                } 
                catch (ex) {
                    console.error(ex);
                }
            }

            /**
             * 根据最新的写入情况，采取增量的方式请求数据
             */

            let since = version instanceof RecordVersionInfo ? (this.isByUpdateTime ? version.updateTime : version.id) : undefined;
            this.batchRequest(since);
        };

        this._readLatestRecordVersion(callback);
    }

    /**
     * 本地化合并后的完整数据
     * @param {Array<Array>} mergeds 要写入本地化数据文件，的完整数据集
     */
    localize(mergeds) {

        if (mergeds.length == 0) {
            return;
        }

        let lines = mergeds.map(values => JSON.stringify(values));
        let version = { id: null, updateTime: 0 };

        /**
         * 寻找最新的一笔记录，用以设置为，当前本地化的数据版本
         */

        if (mergeds.length > 0) {

            let latest = this._seekLatest(mergeds);
            version.id = latest.id;
            version.updateTime = latest.updateTime;

            if (version.updateTime === undefined) {
                version.updateTime = 0;
            }
        }

        /**
         * 本地化文件，首行放置版本信息
         */
        lines.unshift(JSON.stringify(version));
        this.logEvent(`to localize, data length = ${mergeds.length} & version = ${JSON.stringify(version)}`);

        /**
         * 将：首行版本信息 + 合并后的完整数据，写入本地化文件
         */
        fs.writeFileSync(this.dataFilePath, lines.join(LineSplitter) + LineSplitter, { encoding: FileContentEncoding });
    }

    /**
     * 本地化增量数据（适用于，本地化文件已存在）
     */
    localizeChanges() {

        let changes = this.incrChanges;
        if (changes.length == 0) {
            return;
        }

        let locals = this._readLocalized();
        let newers = this._convertObjects2Values(changes);
        let mergeds = locals.length == 0 ? newers : this.handlers.mergeAll(locals, newers);
        this.logEvent(`to localize incremental hot changes, changes.length = ${changes.length} mergeds.length = ${mergeds.length}`);
        this.localize(mergeds);
        this._clearIncrChanges();
    }

    /**
     * 处理单个实时推送数据
     */
    handleRealtimeChange(data) {

        /**
         * 无论是否，批量数据已完成接收和处理，都将实时推送放入增量队列
         * --------------------------------------------------------
         * 1. 从服务器开始推送，批量数据的第一个字节开始，到主进程完成接收 & 原始数据解压 & 跨进程传输 & 反序列化等，耗时较长
         * 2. 步骤1耗时较长，此过程中可能存在实时数据推送
         * 3. 避免存在全量 & 增量之间的遗漏偏差（也许，还存在数据重叠的可能性 ？）
         */
        this.incrChanges.push(data);

        /**
         * 根据批量数据接收情况，决定是否将短时间内的实时推送数据放入缓冲区
         */

        if (this.isDataPushReceived && this.isConsumerReady) {
            this.handlers.realtimeConsumer(data);
        } 
        else {
            this.hotChanges.push(data);
        }
    }

    logEvent(message) {
        // console.log(`${ Date.now() } > ${ this.dataType } > ${ this.context.identityId } > ${ message }`);
    }

    dispose() {

        clearInterval(this._intervalJob);
        delete this._intervalJob;
        console.log('the internal job cleared for > today order/position/exchange base class');
    }

    showLoading() {

        this.hideLoading();
        this._$loading = this.handlers.showLoading({ text: '正在加载交易数据' });
        this._closeTimer = setTimeout(() => { this.hideLoading(); }, 1000 * 20);
    }

    hideLoading() {

        if (this._$loading !== undefined) {

            this._$loading.close();
            delete this._$loading;
        }

        if (this._closeTimer !== undefined) {

            clearTimeout(this._closeTimer);
            delete this._closeTimer;
        }
    }

    /**
     * 清空实时数据缓冲区
     */
    _clearBuffereds() {
        this.hotChanges.clear();
    }

    /**
     * 清空增量数据缓冲区
     */
    _clearIncrChanges() {
        this.incrChanges.clear();
    }

    /**
     * 处理消费者重启推送请求，将所有缓冲区的数据一次性全部推送
     */
    _handleFlushRequest() {

        this.showLoading();
        setTimeout(() => {

            this._flushAll();
            this.hideLoading(); }, 0);
    }

    /**
     * 将，自数据请求开始，到批量数据返回，此期间羁押的实时推送数据，一次性推送给消费者
     */
    _flushAll() {

        /** 将羁押的实时推送，全部传送至消费处 */
        this.hotChanges.forEach(struc => {
            this.handlers.realtimeConsumer(struc);
        });

        /** 清空实时数据缓冲区 */
        this._clearBuffereds();
    }

    /**
     * 设置标识：批量数据，是否已就位
     * 1. 后续的数据，将根据该状态，决定是放入推送缓冲区，还是追加到表格组件
     * 2. 对首屏数据的处理，也将根据批量数据是否已返回，决定是否需要：A. 进行实际渲染，或 B. 丢弃首屏数据
     * @param {Boolean} flag
     */
    _setBatchDataReadyState(flag) {

        /**
         * 批量数据，是否已完成推送和接收
         */
        this.isDataPushReceived = flag;
    }

    _startIntervalWrite() {

        this.logEvent(`to start interval check`);
        this._lastJobExecuteTime = Date.now();

        if (this._intervalJob !== undefined) {

            // console.error('interval job already started');
            return;
        }

        this._intervalJob = setInterval(() => {

            let totalSize = this.incrChanges.length;
            if (totalSize == 0) {
                return;
            }

            let last = this._lastJobExecuteTime;
            let now = Date.now();
            let ellapsed = now - last;
            let shouldRun = totalSize >= JobSetting.MinSize || ellapsed >= JobSetting.LeastMs;

            if (shouldRun) {

                this.logEvent(`incremental change flushed due to size = ${totalSize} & time = ${ellapsed}, last time = ${last} & now = ${now}`);
                this._lastJobExecuteTime = now;
                this.localizeChanges();
            }

        }, JobSetting.Interval);
    }

    /**
     * 生成本地化文件地址
     */
    _makeDataPath() {

        if (this.todayStr === undefined) {
            this.todayStr = new Date().format('yyyyMMdd');
        }

        return path.join(LocalSetting.userDataPath, `${this.todayStr}-${this.identifier}`);
    }

    /**
     * 识别是否有本地化的当日数据
     */
    _hasLocalized() {
        return fs.existsSync(this.dataFilePath);
    }

    /**
     * 读取已经被本地化的数据
     * @returns {Array}
     */
    _readLocalized() {
        
        let results = [];
        if (!this._hasLocalized()) {
            return [];
        }

        try {

            let content = fs.readFileSync(this.dataFilePath, { encoding: FileContentEncoding });

            if (typeof content == 'string' && content.length > 0) {

                let lines = content.split(LineSplitter);

                /**
                 * 1. 第一行为数据版本信息，故需要将第一行数据抛掉
                 * 2. 推送数据各个字段，为固定顺序（已通过编码中 setTitle 保持一致顺序，如遇服务器端调整，需同步调整）
                 */
                lines.shift(0);
                let data_lines = lines.filter((x) => x.length > 0);
                results = data_lines.map((line_content) => JSON.parse(line_content));
            }
        } 
        catch (ex) {

            console.error(ex);
            try {

                /**
                 * 读取文件内容产生异常，则将该文件删除
                 */
                fs.unlinkSync(this.dataFilePath);
            } 
            catch (ex2) {
                console.error(ex2);
            }
        }

        return results;
    }

    /**
     * 读取本地化数据的最新版本
     * @param {Function} callback 回调函数
     */
    _readLatestRecordVersion(callback) {

        if (!this._hasLocalized()) {

            callback();
            return;
        }

        try {

            let readLine = require('readline');
            let firstLine = null;
            let lineReader = readLine.createInterface({ input: fs.createReadStream(this.dataFilePath) });

            lineReader.on('line', function (line_content) {

                firstLine = line_content;
                /** 获取第一行内容后，立即移除行读取监听 */
                lineReader.removeAllListeners('line');
                lineReader.close();
            });

            lineReader.on('close', function () {

                let has_content = typeof firstLine == 'string' && firstLine.trim().length > 0;
                if (!has_content) {

                    callback(false, 'empty file');
                    return;
                }

                try {

                    let struc = JSON.parse(firstLine.substr(0, VersionInfoMessageLength));
                    if (helper.isJson(struc) && struc.id !== undefined && struc.updateTime !== undefined) {
                        callback(new RecordVersionInfo(struc), `version(${firstLine}) fields [id] or [updateTime] is not present`);
                    } 
                    else {
                        callback(false, `version(${firstLine}) text content is invalid`);
                    }
                } 
                catch (ex) {

                    console.error(ex);
                    callback(false, 'version text content parsing error');
                }
            });
        } 
        catch (ex) {

            console.error(ex);
            callback(false, 'version extracting error');
        }
    }

    /**
     * 寻找批次当中最新的记录
     * @param {Array<Array>} contents
     */
    _seekLatest(contents) {

        let id_idx = this.RecordIdIdx;
        let ut_idx = this.RecordUpdateTimeIdx;
        let matched = contents[0];

        for (let idx = 1; idx < contents.length; idx++) {

            let values = contents[idx];
            if (matched[ut_idx] < values[ut_idx]) {
                matched = values;
            }
        }

        return { id: matched[id_idx], updateTime: matched[ut_idx] };
    }

    /**
     * 将推送数据，转换为title所确定顺序的二维数组数据
     * @param {Array} records
     */
    _convertObjects2Values(records) {

        let titles = this.titles;
        return records.map((item) => {

            let values = [];
            titles.forEach((name) => { values.push(item[name]); });
            return values;
        });
    }
}

module.exports = { TradeDataCacher };