<div class="basket-group s-full-height">
	<div class="basket-group-internal themed-box s-scroll-bar s-full-height">
		<div class="xtcontainer s-border-box s-full-height">

			<div class="grp-toolbar xtheader themed-header">
				<template>

					<span class="header-title">篮子列表</span>

					<el-select placeholder="篮子类型"
							    class="s-w-100"
								v-model="states.basketTypeId"
								@change="handleBasketTypeChange">

								<el-option v-for="(item, item_idx) in basketTypes" 
										   :key="item_idx" 
										   :label="item.typeName" 
										   :value="item.typeId"></el-option>
					</el-select>

					<el-select placeholder="请选择篮子"
								class="s-w-100"
								v-model="states.basketId"
								@change="handleBasketChange" filterable>

								<el-option v-for="(item, item_idx) in baskets"
											:key="item_idx"
											:label="item.basketName"
											:value="item.basketId">

											<div class="basket-group-item">
												<span class="group-name">{{ item.basketName }}</span>
												<span v-if="isCustomBasket" class="group-opers s-pull-right">
													<!-- <a @click.stop="hope2EditBasketName(item)" title="编辑篮子名称" class="btn iconfont icon-bianjihexiugai"></a> -->
													<a @click.stop="hope2DeleteBasket(item)" title="删除该篮子" class="btn iconfont icon-shanchu"></a>
												</span>
											</div>
								</el-option>
					</el-select>

					<el-input placeholder="搜索"
							  prefix-icon="el-icon-search"
							  class="s-w-100 s-mgl-5"
							  v-model="states.keywords"
							  @change="filterRecords" clearable></el-input>

					<div class="toolkit-box s-pull-right">

						<template v-if="isCustomBasket">
							
							<a @click="hope2AddInstrument" class="s-mgr-10 themed-hover" title="添加合约">
								<i class="iconfont icon-tianjia"></i>
							</a>

							<el-popover placement="top-start"
										title="导入合约到篮子（文件请保存为UTF8格式）"
										trigger="hover">

								<span class="import-basket-instrument-sample-pic"></span>

								<a slot="reference" @click="hope2ImportInstruments" class="s-mgr-10 themed-hover">
									<i class="iconfont icon-daoru1"></i>
								</a>

							</el-popover>
	
							<a @click="hope2SaveBasket" class="s-mgr-10 themed-hover" title="保存篮子">
								<i class="iconfont icon-baocun"></i>
							</a>

							<a @click="hope2RemoveCheckeds" class="s-mgr-10 themed-hover" title="删除勾选合约">
								<i class="iconfont icon-shanchu"></i>
							</a>

						</template>

						<a @click="hope2Refresh" class="s-mgr-10 themed-hover" title="刷新">
							<i class="iconfont icon-shuaxin"></i>
						</a>

					</div>

				</template>

			</div>

			<div class="module-edit-basket">
				<template>
					<el-dialog width="290px"
							   class="lighted-box"
							   :title="dialog.title"
							   :visible="dialog.visible"
							   :close-on-click-modal="false"
							   :show-close="false">

						<div class="dialog-body-inner">
							<label>篮子名称</label>
							<el-input v-model.trim="dialog.basketName" class="s-mgl-10" maxlength="30" style="width: 190px;"></el-input>
						</div>

						<div slot="footer">
							<el-button type="primary" @click="saveBasket">保存</el-button>
							<el-button type="primary" @click="saveBasketAs">另存为</el-button>
							<el-button type="default" @click="unsaveBasket">取消</el-button>
						</div>
					</el-dialog>
				</template>
			</div>

			<div class="table-external">
				<div class="table-component s-full-width">
					<table>
						<tr>
								
							<th type="check" width="110"></th>

							<th label="证券代码"
								prop="instrument" 
								min-width="110"
								formatter="formatInstrument"
								sorting-method="sortBySth" sortable overflowt></th>
								
							<th label="证券名称"
								prop="instrumentName" 
								min-width="110"
								sorting-method="sortBySth" sortable overflowt></th>

							<th label="数量"
								prop="amount" 
								min-width="80" 2
								align="right"
								formatter="formatAmount"
								sorting-method="sortBySth" sortable summarizable></th>

							<th label="权重"
								prop="weight" 
								min-width="80" 
								align="right" 
								formatter="formatWeight"
								sorting-method="sortBySth" sortable summarizable></th>
						</tr>
					</table>
				</div>
			</div>
		</div>
	</div>
</div>