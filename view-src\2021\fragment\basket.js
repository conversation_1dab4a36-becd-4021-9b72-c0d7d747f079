const remote = require('@electron/remote');
const IView = require('../../../component/iview').IView;
const SmartTable = require('../../../libs/table/smart-table').SmartTable;
const ColumnCommonFunc = require('../../../libs/table/column-common-func').ColumnCommonFunc;
const repoBasket = require('../../../repository/basket');
const { Basket, BasketItem } = require('../model/account');
const { BizHelper } = require('../../../libs/helper-biz');

class View extends IView {

    get isCustomBasket() {
        return this.states.basketTypeId == this.basketTypes.custom.typeId;
    }

    get isEtfBasket() {
        return this.states.basketTypeId == this.basketTypes.etf.typeId;
    }

    constructor(view_name, is_standalone_window) {

        super(view_name, is_standalone_window, '交易篮子管理');

        this.settings = {
            defaultId: **********,
        };

        this.basketTypes = {

            custom: { typeId: 1, typeName: '普通篮子' },
            etf: { typeId: 2, typeName: 'ETF篮子' },
        };

        this.allBaskets = [new Basket({})];
        this.allBaskets.pop();
        this.baskets = [new Basket({})];
        this.baskets.pop();
        this.memberDict = {};
        this.states = {

            basketTypeId: this.basketTypes.custom.typeId,
            basketId: null,
            keywords: null,
        };
    }

    pickId() {
        return this.settings.defaultId += 1;
    }

    setAsBasketId(bskId) {

        this.states.basketId = bskId;
        this.spread();
    }

    /**
     * @param {Boolean} isChanged 
     */
    setAsLocalChanged(isChanged) {

        this.isLocalChanged = isChanged;
        this.trigger('basket-local-changed', isChanged);
    }

    spread() {

        var selected = this.theBasket;
        this.trigger('basket-changed', selected ? selected.basketId: null
                                     , selected ? selected.basketName : null
                                     , selected ? selected.isEtf : false);
    }

    hasAnyUncommitedRecords() {

        var records = this.typedMembers(this.tableObj.extractAllRecords());
        return records.some(item => this.helper.isNone(item.instrument));
    }

    /**
     * @param {Array<BasketItem>} members
     */
    rewriteMembs(basketId, members) {
        return this.memberDict[basketId] = members;
    }

    /**
     * @returns {Array<BasketItem>}
     */
    seekMembs(basketId) {
        return this.memberDict[basketId] || [];
    }

    createApp() {

        new Vue({

            el: this.$container.querySelector('.xtcontainer > .grp-toolbar'),
            data: {

                basketTypes: this.helper.dict2Array(this.basketTypes),
                baskets: this.baskets,
                states: this.states,
            },
            computed: {
                isCustomBasket: () => { return this.isCustomBasket; },
            },
            methods: this.helper.fakeVueInsMethod(this, [

                this.handleBasketChange,
                this.handleBasketTypeChange,
                this.filterRecords,
                this.hope2EditBasketName,
                this.hope2DeleteBasket,
                this.hope2Refresh,
                this.hope2ImportInstruments,
                this.hope2AddInstrument,
                this.hope2SaveBasket,
                this.hope2RemoveCheckeds,
            ]),
        });
    }

    get theBasket() {
        return this.allBaskets.find(item => item.basketId == this.states.basketId);
    }

    /**
     * @param {BasketItem} record 
     */
    identifyRecord(record) {
        return record.recordId;
    }

    /**
     * @param {BasketItem} record
     */
    formatInstrument(record, value, fieldName) {

        if (this.helper.isNone(record.instrument)) {

            return `<input class="xt-table-row-input" style="width: 70px;"
                       value=""
                       maxlength="9"
                       onmouseover="this.select();"
                       event.onchange="handleInstrumentChange" />

                    <i class="iconfont icon-shanchu" event.onclick="deleteDraftRecord"></i>`;
        }
        else {
            return record.instrument;
        }
    }

    /**
     * @param {BasketItem} record 
     * @param {HTMLInputElement} $ctr 
     * @param {HTMLTableCellElement} $cell 
     * @param {Number} previousVal 
     * @param {String} fieldName 
     */
    handleInstrumentChange(record, $ctr, $cell, previousVal, fieldName) {
        
        var keywords = $ctr.value;
        var matches = this.typedMembers(BizHelper.filterInstruments(this.systemEnum.assetsType.stock.code, keywords));
        var matches2 = this.typedMembers(BizHelper.filterInstruments(this.systemEnum.assetsType.stock.code, keywords, true));
        var mright = matches.length == 1 ? matches[0] : matches2.length == 1 ? matches2[0] : undefined;

        if (!mright) {
            return this.interaction.showError(`关键字：“${keywords}” 未能精准匹配到合约`);
        }

        var instrument = mright.instrument;
        var members = this.typedMembers(this.tableObj.extractAllRecords());
        var existing = members.some(item => item.instrument == instrument);
        if (existing) {
            return this.interaction.showError(`合约：“${instrument}” 已存在`);
        }

        this.tableObj.updateRow(new BasketItem(record.recordId, {

            basketId: record.basketId,
            basketName: record.basketName,
            instrument: instrument,
            instrumentName: mright.instrumentName,
            amount: record.amount,
            weight: record.weight,
            createUser: record.createUser,
        }));

        this.setAsLocalChanged(true);
    }

    /**
     * @param {BasketItem} record 
     */
    deleteDraftRecord(record) {
        this.tableObj.deleteRow(this.identifyRecord(record));
    }

    /**
     * @param {BasketItem} record
     */
    formatAmount(record, weight, fieldName) {
        return this.formatCell(weight, 'handleAmountChange');
    }

    handleAmountChange(record, $ctr, $cell, previousAmount, fieldName) {
        this.handleValueChange(record, $ctr, $cell, previousAmount, fieldName);
    }

    /**
     * @param {BasketItem} record
     */
    formatWeight(record, weight, fieldName) {
        return this.formatCell(weight, 'handleWeightChange');
    }

    handleWeightChange(record, $ctr, $cell, previousWeight, fieldName) {
        this.handleValueChange(record, $ctr, $cell, previousWeight, fieldName);
    }

    /**
     * @param {Number} value
     * @param {String} handler
     */
    formatCell(value, handler) {

        if (this.isCustomBasket) {
            
            return `<input class="xt-table-row-input s-right" 
                       value="${typeof value  == 'number' && value >= 0 ? value : 0}"
                       maxlength="9"
                       onmouseover="this.select();"
                       event.onchange="${handler}" />`;
        }
        else {
            return typeof value  == 'number' && value >= 0 ? value : 0;
        }
    }

    /**
     * @param {BasketItem} record 
     * @param {HTMLInputElement} $ctr 
     * @param {HTMLTableCellElement} $cell 
     * @param {Number} previousVal 
     * @param {String} fieldName 
     */
    handleValueChange(record, $ctr, $cell, previousVal, fieldName) {
        
        let newValue = +$ctr.value;

        if (isNaN(newValue) || newValue < 0 || newValue > 999999) {
            $ctr.value = previousVal;
        }
        else {

            let struct = {

                recordId: record.recordId,
                [fieldName]: newValue,
            };

            this.tableObj.updateRow(struct);
        }

        this.setAsLocalChanged(true);
    }

    createTable() {

        this.helper.extend(this, ColumnCommonFunc);
        this.tableObj = new SmartTable(this.$container.querySelector('.table-component'), this.identifyRecord, this, {

            tableName: 'smt-fbskt',
            displayName: this.title,
        });

        this.tableObj.setPageSize(999999999);
        this.tableObj.setMaxHeight(245);
        this.tableObj.fitColumnWidth();
    }

    handleBasketTypeChange() {

        var isEtf = this.isEtfBasket;
        var baskets = this.allBaskets.filter(item => item.isEtf === isEtf);
        this.baskets.clear();
        this.baskets.merge(baskets);
        this.setAsBasketId(baskets.length > 0 ? baskets[0].basketId : null);

        if (this.isCustomBasket) {
            this.tableObj.showColumns(['权重']);
        }
        else {
            this.tableObj.hideColumns(['权重']);
        }

        this.handleBasketChange();
    }

    async handleBasketChange() {

        this.spread();
        this.setAsLocalChanged(false);
        this.states.keywords = null;
        this.tableObj.setKeywords(null, false);

        if (this.baskets.length == 0) {

            this.tableObj.clear();
            return;
        }
        
        var selected = this.theBasket;
        var basketId = selected.basketId;

        if (!selected.isMemberLoaded) {

            var resp = await repoBasket.getBasketDetail(basketId);
            var instruments = resp.data;
            if (resp.errorCode == 0 && instruments instanceof Array) {
                
                selected.isMemberLoaded = true;
                this.rewriteMembs(basketId, instruments.map(item => new BasketItem(this.pickId(), item)));
            }
            else {
                this.interaction.showError('获取篮子内含合约发生异常：' + resp.errorMsg);
            }
        }

        var members = this.seekMembs(basketId);
        var cloneds = members.map(item => BasketItem.Clone(item));
        this.tableObj.refill(cloneds);
    }

    filterRecords() {
        this.tableObj.setKeywords(this.states.keywords);
    }

    /**
     * @param {BasketItem} first 
     * @param {BasketItem} second 
     * @param {String} fieldName 
     * @param {String} direction 
     */
    sortBySth(first, second, fieldName, direction) {

        var isFirstNew = this.helper.isNone(first.instrument);
        var isSecondNew = this.helper.isNone(second.instrument);

        if (isFirstNew && isSecondNew) {
            return 0;
        }
        else if (isFirstNew) {
            return -1;
        }
        else if (isSecondNew) {
            return 1;
        }

        var a = first[fieldName];
        var b = second[fieldName];

        if (direction == 'ASC') {
            return a < b ? -1 : a > b ? 1 : 0;
        }
        else {
            return a < b ? 1 : a > b ? -1 : 0;
        }
    }

    /**
     * 该操作，暂时不支持（因编辑篮子时，为完整更新数据，篮子列表从表格中获取，而该入口取到的篮子列表，并非自己的，而是当前所选中的篮子）
     * @param {Basket} basket 
     */
    hope2EditBasketName(basket) {
        this.openEditDialog(basket);
    }

    hope2Refresh() {

        var dotask = () => {

            this.interaction.showSuccess('篮子内容，刷新请求已发出');
            this.requestBaskets();
        };

        if (this.tableObj.rowCount > 0 && this.isLocalChanged) {
            
            this.interaction.showConfirm({

                title: '操作确认',
                message: '篮子列表可能存在尚未保存的变更，刷新将重新加载，是否刷新？',
                confirmed: () => {
                    dotask();
                },
            });
        }
        else {
            dotask();
        }
    }

    hope2ImportInstruments() {

        const targetPath = remote.dialog.showOpenDialogSync(this.thisWindow, {
            title: '选择上传文件',
            filters: [{ name: 'xlsx文件', extensions: ['xlsx', 'xls', 'csv'] }],
        });
        
        this.handleUploadDialog(targetPath);
    }

    handleUploadDialog(paths) {

        if (!paths) {
            return;
        }

        const fs = require('fs');
        const xlsx = require('node-xlsx');
        var file = fs.readFileSync(paths[0]);
        var sheets = xlsx.parse(file);
        var records = sheets[0].data;
        
        if (!Array.isArray(records) || records.length <= 1) {

            this.interaction.showAlert('导入文件无数据行');
            return;
        }

        records.shift();
        var oks = records.filter(eles => {

            if (!Array.isArray(eles) || eles.length != 4) {
                return false;
            }

            let code = eles[0];
            let name = eles[1];
            let amount = eles[2];
            let weight = eles[3];
            return typeof code == 'string' && code.length > 0
                    && typeof name == 'string' && name.length > 0
                    && typeof amount == 'number' && Number.isInteger(amount)
                    && typeof weight == 'number' && weight >= 0;
        });

        if (oks.length == 0) {

            this.interaction.showAlert('导入文件无有效合约');
            return;
        }

        var currentBskt = this.theBasket;
        var structures = oks.map(arr => ({

            basketId: currentBskt ? currentBskt.basketId : null,
            basketName: currentBskt ? currentBskt.basketName : null,
            instrument: arr[0],
            instrumentName: arr[1],
            amount: arr[2],
            weight: arr[3],
            createUser: currentBskt ? currentBskt.createUser : null,
        }));

        var memberMap = {};
        var members = this.typedMembers(this.tableObj.extractAllRecords());
        members.forEach(item => { memberMap[item.instrument] = true; });

        structures.forEach(item => {

            if (memberMap[item.instrument] === true) {
                this.tableObj.updateRow(new BasketItem(this.pickId(), item));
            }
            else {

                memberMap[item.instrument] = true;
                this.tableObj.insertRow(new BasketItem(this.pickId(), item));
            }
        });

        this.tableObj.uncheckAll();
        this.setAsLocalChanged(true);
        this.interaction.showSuccess('已导入有效合约数 = ' + structures.length);
    }

    hope2AddInstrument() {

        if (this.isEtfBasket) {
            return this.interaction.showError('ETF篮子不允许，自行添加合约');
        }
        
        if (this.hasAnyUncommitedRecords()) {
            return this.interaction.showError('当前新记录尚未修改，请完善合约');
        }

        var bskt = this.theBasket;
        var bitem = new BasketItem(this.pickId(), {

            basketId: this.states.basketId,
            basketName: bskt ? bskt.basketName : null,
            instrument: null,
            instrumentName: null,
            amount: 0,
            weight: 0,
            createUser: this.userInfo.userId,
        });

        this.tableObj.insertRow(bitem);
        this.setAsLocalChanged(true);
    }

    hope2SaveBasket() {

        if (this.tableObj.rowCount == 0) {
            return this.interaction.showError('无法保存为空篮子');
        }
        else if (this.hasAnyUncommitedRecords()) {
            return this.interaction.showError('列表中存在，尚未指定合约，的记录');
        }

        this.openEditDialog(this.theBasket);
    }

    hope2RemoveCheckeds() {
        
        if (this.tableObj.rowCount == 0) {
            return this.interaction.showAlert('篮子内无合约');
        }

        var checkeds = this.tableObj.extractCheckedRecords();
        if (checkeds.length == 0) {
            return this.interaction.showAlert('请勾选要删除的合约');
        }

        this.interaction.showConfirm({

            title: '操作确认',
            message: '确定要删除选中合约？',
            confirmed: () => {
                
                checkeds.forEach(item => { this.tableObj.deleteRow(this.identifyRecord(item)); });
                this.tableObj.uncheckAll();
                this.setAsLocalChanged(true);
            },
        });
    }

    /**
     * @param {Basket} basket 
     */
    openEditDialog(basket) {

        var title = basket ? '保存篮子' : '创建篮子';

        if (this.dialogApp) {

            this.dialog.visible = true;
            this.dialog.title = title;

            if (basket instanceof Basket) {

                this.dialog.basketId = basket.basketId;
                this.dialog.basketName = basket.basketName;
            }
            else {

                this.dialog.basketId = null;
                this.dialog.basketName = null;
            }

            return;
        }

        this.dialog = {

            title: title,
            visible: true,
            basketId: null,
            basketName: null,
        };

        if (basket instanceof Basket) {

            this.dialog.basketId = basket.basketId;
            this.dialog.basketName = basket.basketName;
        }

        this.dialogApp = new Vue({

            el: this.$container.querySelector('.xtcontainer > .module-edit-basket'),
            data: {
                dialog: this.dialog,
            },
            methods: {

                saveBasket: () => { this.checkAndSave(); },
                saveBasketAs: () => { this.checkAndSave(true); },
                unsaveBasket: () => { this.exitDialog(); },
            }
        });
    }

    /**
     * @param {Array} records 
     * @returns {Array<BasketItem>}
     */
    typedMembers(records) {
        return records;
    }

    /**
     * 保存篮子
     * @param {Boolean} isCreation
     */
    async checkAndSave(isCreation) {

        /**
         * 无论点击 “保存” 或 “另存为”，未映射到已有篮子，均视为创建
         */
        if (this.helper.isNone(this.dialog.basketId)) {
            isCreation = true;
        }

        if (this.helper.isNone(this.dialog.basketName)) {
            return this.interaction.showError('请输入适当的篮子名称');
        }

        this.exitDialog();

        /**
         * 获取当前表格中所承载的所有合约，作为该篮子的合约列表
         */
        var members = this.typedMembers(this.tableObj.extractAllRecords());
        if (members.length == 0) {
            return this.interaction.showError('不可保存空篮子');
        }

        var cloneds = members.map(item => BasketItem.Clone(item));        
        cloneds.forEach(item => { 

            if (isCreation) {

                /**
                 * 对于创建（另存为，或真实新创建）均不可包含篮子ID
                 */
                item.basketId = null;
            }

            item.basketName = this.dialog.basketName;
            
            /**
             * 数据提交前，清理掉辅助字段
             */
            delete item.recordId;
        });

        var resp = isCreation ? await repoBasket.saveBasket(cloneds)
                              : await repoBasket.updateBasket(cloneds);

        var instruments = resp.data || [];

        if (resp.errorCode != 0 || !Array.isArray(instruments)) {
            return this.interaction.showError(`篮子保存失败：${resp.errorMsg}`);
        }

        this.interaction.showSuccess('篮子已保存');
        
        if (isCreation) {

            let first = new BasketItem(0, instruments[0]);
            let bskId = first.basketId;
            let bskName = first.basketName;
            let newbsk = new Basket({

                basketId: bskId,
                basketName: bskName,
                totalWeight: instruments.sum(item => item.weight),
                createUser: first.createUser,
            });

            let newItems = instruments.map(item => new BasketItem(this.pickId(), item));
            this.allBaskets.unshift(newbsk);
            this.baskets.unshift(newbsk);
            this.rewriteMembs(bskId, newItems);
            newbsk.isMemberLoaded = true;
            this.setAsBasketId(bskId);

            /**
             * 将界面所有数据清空，重新填入新数据
             */
            this.tableObj.refill(newItems);
        }
        else {

            let matched = this.baskets.find(item => item.basketId == this.dialog.basketId);
            let bskName = this.dialog.basketName;
            matched.basketName = bskName;
            matched.isMemberLoaded = true;
            let cloneds = members.map(item => BasketItem.Clone(item));
            cloneds.forEach(item => { item.basketName = bskName; });
            this.rewriteMembs(matched.basketId, cloneds);

            /**
             * 更新模式时，所有界面存量数据为最新，仅更新篮子名称即可（篮子ID不产生变化）
             */

            cloneds.forEach(item => {

                this.tableObj.updateRow({

                    recordId: item.recordId,
                    basketName: bskName,
                });
            });
        }

        this.setAsLocalChanged(false);
        this.spread();
    }

    exitDialog() {

        this.dialog.visible = false;
        // this.dialog.basketId = null;
        // this.dialog.basketName = null;
    }

    /**
     * @param {Basket} basket 
     */
    hope2DeleteBasket(basket) {
        
        this.interaction.showConfirm({

            title: '操作确认',
            message: `是否确认删除篮子 / ${basket.basketName}？`,
            confirmed: () => { this.deleteGroup(basket); },
        });
    }

    /**
     * @param {Basket} basket 
     */
    async deleteGroup(basket) {
        
        var baskets = this.baskets;
        var basketId = basket.basketId;
        var isSelected = basketId == this.states.basketId;
        var resp = await repoBasket.deleteBasket(basketId);

        if (resp.errorCode != 0) {

            this.interaction.showError(`篮子删除失败：${resp.errorMsg}`);
            return;
        }

        /** 从列表中将该分组删除 */
        baskets.remove(item => item === basket);
        
        /**
         * 删除项为选中项
         */
        if (isSelected) {

            /**
             * 删除处于选中状态的分组后，模拟回到全部选项
             */
            
            this.setAsBasketId(baskets.length > 0 ? baskets[0].basketId : null);
            this.handleBasketChange();
        }
    }

    async requestBaskets() {

        this.allBaskets.clear();
        this.tableObj.clear();

        var resp1 = await repoBasket.getBasket();
        var records1 = resp1.data;
        if (resp1.errorCode == 0 && records1 instanceof Array) {

            let baskets = records1.map(item => new Basket(item, false));
            this.allBaskets.merge(baskets);
        }
        else {
            this.interaction.showError('获取篮子列表发生异常：' + resp1.errorMsg);
        }

        var resp2 = await repoBasket.getEtfBasket();
        var records2 = resp2.data;
        if (resp2.errorCode == 0 && records2 instanceof Array) {

            let baskets = records2.map(item => new Basket(item, true));
            this.allBaskets.merge(baskets);
        }
        else {
            this.interaction.showError('获取ETF列表发生异常：' + resp2.errorMsg);
        }

        this.handleBasketTypeChange();
    }

    build($container) {

        super.build($container);
        this.createApp();
        this.createTable();
        this.requestBaskets();
    }
}

module.exports = View;