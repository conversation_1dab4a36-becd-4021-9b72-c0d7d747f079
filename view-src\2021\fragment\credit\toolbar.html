<div>
	
	<el-select v-model="uistates.typeId"
			   placeholder="选择类型" 
			   class="s-w-100 s-mgr-10" clearable>

		<el-option v-for="item in types" 
					:key="item.value" 
					:label="item.label" 
					:value="item.value"></el-option>
	</el-select>

	<el-select v-model="istates.productId"
			   v-show="istates.showProduct"
			   placeholder="选择产品" 
			   @change="handleProductChange" 
			   class="s-w-150 s-mgr-10" filterable clearable>

		<el-option v-for="item in products"
					:key="item.value" 
					:label="item.label" 
					:value="item.value"></el-option>
	</el-select>

	<el-select v-model="istates.strategyId"
				v-show="istates.showStrategy"
				placeholder="选择策略"
				@change="handleStrategyChange" 
				class="s-w-150 s-mgr-10" filterable clearable>

		<el-option v-for="item in strategies" 
					:key="item.value" 
					:label="item.label" 
					:value="item.value"></el-option>
	</el-select>

	<el-select v-model="istates.accountId" 
				v-show="istates.showAccount"
				placeholder="选择账号" 
				@change="handleAccountChange" 
				class="s-w-150 s-mgr-10" filterable clearable>

		<el-option v-for="item in accounts" 
					:key="item.value"
					:label="item.label"
					:value="item.value"></el-option>
	</el-select>

	<!-- <el-select v-model="uistates.subsistId"
				placeholder="选择状态"
				class="s-w-100 s-mgr-10" clearable>
		
		<el-option v-for="item in subsists" 
					:key="item.value" 
					:label="item.label"
					:value="item.value"></el-option>
	</el-select> -->

	<el-input placeholder="合约代码过滤" 
			  prefix-icon="el-icon-search" 
			  v-model="uistates.keywords" 
			  class="s-w-150 s-mgr-10" clearable></el-input>

	<el-button type="primary" icon="el-icon-search" @click="handleSearch"></el-button>
	<el-button v-if="uistates.showReturnMoneyButton" type="primary" class="s-pull-right" @click="showReturnMoneyPrompt">直接还款</el-button>
	
</div>