const { IdentityRelationView } = require('../../classcial/identity-relation-view');

class CreditToolbarView extends IdentityRelationView {

    /**
     * @param {*} view_name 
     * @param {*} title 
     * @param {Boolean} isDebted 是否为debt页面（负债？标的？）
     * @param {Boolean} isBySingleAccount 是否显示直接还款按钮
     * @param {*} options 
     */
    constructor(view_name, title, isDebted, isBySingleAccount, options) {

        super(view_name, title, options);

        this.types = [

            { value: 0, label: '融资' },
            // { value: 1, label: '融券' },
        ];

        // if (isDebted === true) {

        //     this.types.push({ value: 2, label: '标的' });
        //     this.types.push({ value: 3, label: '专项' });
        // }

        this.subsists = [

            { value: 0, label: '未了结' },
            { value: 1, label: '已了结' },
        ];

        this.uistates = {

            typeId: null,
            subsistId: null,
            keywords: null,
            showReturnMoneyButton: !isDebted && isBySingleAccount,
        };

        /**
         * 注册外部主动请求
         */
        this.registerEvent('pull-query-data', () => { this.trigger('reply-query-data', this.collect()); });
    }
    
    createApp() {

        this.toolbarApp = new Vue({

            el: this.$container.firstElementChild,
            data: {

                types: this.types,
                subsists: this.subsists,

                uistates: this.uistates,
                istates: this.istates,
                products: this.products,
                strategies:  this.strategies,
                accounts: this.accounts,
            },
            
            methods: this.helper.fakeVueInsMethod(this, [

                this.handleProductChange,
                this.handleStrategyChange,
                this.handleAccountChange,
                this.handleSearch,
                this.showReturnMoneyPrompt,
            ]),
        });
    }

    handleSearch() {
        this.trigger('do-search', this.collect());
    }

    showReturnMoneyPrompt() {
        this.trigger('show-return-money-prompt');
    }

    collect() {
        
        var snap = this.snapshoot();
        var uistates = this.uistates;

        return new ToolbarQueryData({

            productId: snap.productId,
            strategyId: snap.strategyId,
            accountId: snap.accountId,

            typeId: uistates.typeId,
            subsistId: uistates.subsistId,
            keywords: uistates.keywords,
        });
    }

    build($container) {

        super.build($container);
        this.createApp();
    }
}

class ToolbarQueryData {

    constructor(struc) {

        /** 所选产品ID */
        this.productId = struc.productId;
        /** 所选策略ID */
        this.strategyId = struc.strategyId;
        /** 所选账号ID */
        this.accountId = struc.accountId;

        /** 两融业务ID */
        this.typeId = struc.typeId;
        /** 了结状态ID */
        this.subsistId = struc.subsistId;
        /** 关键字 */
        this.keywords = struc.keywords;
    }
}

module.exports = { CreditToolbarView, ToolbarQueryData };