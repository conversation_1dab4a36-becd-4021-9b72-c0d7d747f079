const { IView } = require('../../../component/iview');
const { Position } = require('../../../model/position');
const { SmartTable } = require('../../../libs/table/smart-table');
const { ColumnCommonFunc } = require('../../../libs/table/column-common-func');
const { repoTrading } = require('../../../repository/trading');

class View extends IView {

    constructor(view_name, is_standalone_window) {

        super(view_name, is_standalone_window, '平仓操作');

        var paging = this.systemSetting.tablePagination;
        this.paging = {

            pageSizes: paging.pageSizes,
            pageSize: paging.pageSize,
            layout: paging.layout,
            total: 0,
            page: 0,
        };

        this.stages = this.systemTrdEnum.bidingStages;
        this.states = {

            title: '平仓操作',
            visible: false,
            stage: this.stages[0].code,
            offset: 0,
            keywords: null,
        };

        this.pinyinMap = {};
    }

    /**
     * @param {Array<Position>} positions 
     */
    showup(positions) {

        this.states.visible = true;
        this.vueApp.$nextTick(_=> { this.render(this.helper.deepClone(positions)); });
    }

    /**
     * @param {Array<Position>} positions 
     */
    render(positions) {
        
        if (this.tableObj === undefined) {

            var $table = this.vueApp.$el.querySelector('.data-list');
            this.tableObj = new SmartTable($table, this.identifyRecord, this, {

                tableName: 'smt-fdcp',
                displayName: this.title,
                recordsFiltered: this.handleTableFiltered.bind(this),
            });
    
            this.tableObj.setMaxHeight(390);
        }
        
        var dirs = this.systemTrdEnum.tradingDirection;
        positions.forEach(item => {

            item.closableAmount = 0;
            item.stageName = this.stages[0].mean;
            item.newDirection = item.direction == dirs.buy.code ? dirs.sell.code : dirs.buy.code;
        });

        this.positions = positions;
        this.tableObj.refill(positions);
        this.updateWidthLastTick();
    }

    async updateWidthLastTick() {

        var assts = this.systemEnum.assetsType;
        var resp1 = await repoTrading.getMarketLatestPrice(assts.stock.code);
        var resp2 = await repoTrading.getMarketLatestPrice(assts.bond.code);
        var resp3 = await repoTrading.getMarketLatestPrice(assts.future.code);

        var map1 = resp1.data || {};
        var map2 = resp2.data || {};
        var map3 = resp3.data || {};

        this.positions.forEach(item => {

            let latestPrice = map1[item.instrument] || map2[item.instrument] || map3[item.instrument] || 0;
            this.tableObj.updateRow({ id: item.id, lastPrice: latestPrice, closableAmount: item.closableVolume * latestPrice });
        });
    }

    createApp() {

        var $root = this.$container.querySelector('.dialog-close-positions');
        this.vueApp = new Vue({

            el: $root,
            data: {

                stages: this.stages,
                states: this.states,
                paging: this.paging,
            },

            methods: this.helper.fakeVueInsMethod(this, [

                this.filterRecords,
                this.handleStageChange,
                this.handleOffsetChange,
                this.handlePageChange,
                this.handlePageSizeChange,
                this.confirm,
                this.cancel,
            ]),
        });
    }

    rebindAssetType(records, propName) {
        return SmartTable.MakeColFilters(records, propName, { translators: this.systemEnum.assetsTypes });
    }

    rebindDirection(records, propName) {
        return SmartTable.MakeColFilters(records, propName, { translators: this.systemTrdEnum.tradingDirection });
    }

    /**
     * @param {Position} record 
     */
    identifyRecord(record) {
        return record.id;
    }

    testPy(sample, keywords) {

        let matched_py = this.pinyinMap[sample];
        if (matched_py === undefined) {
            matched_py = this.pinyinMap[sample] = this.helper.pinyin(sample);
        }

        return typeof keywords == 'string' && keywords.length >= 1 && matched_py.indexOf(keywords) >= 0;
    }

    filterRecords() {

        var thisObj = this;
        var keywords = this.states.keywords;

        /**
         * @param {Position} record 
         */
        function filterByPinyin(record) {

            return thisObj.testPy(record.instrumentName, keywords)
                || thisObj.testPy(record.accountName, keywords)
                || thisObj.testPy(record.strategyName, keywords);
        }

        /**
         * @param {Position} record 
         */
        function testRecords(record) {
            return thisObj.tableObj.matchKeywords(record) || filterByPinyin(record);
        }

        this.tableObj.setPageIndex(1, false);
        this.tableObj.setKeywords(keywords, false);
        this.tableObj.customFilter(testRecords);
    }

    handleStageChange() {
        
        var stage = this.stages.find(x => x.code == this.states.stage);
        this.positions.forEach(item => {
            this.tableObj.updateRow({ id: item.id, stageName: stage.mean });
        });
    }

    handleOffsetChange() {

        //
    }

    handlePageSizeChange() {

        this.tableObj.setPageIndex(1, false);
        this.tableObj.setPageSize(this.paging.pageSize);
    }

    handlePageChange() {
        this.tableObj.setPageIndex(this.paging.page);
    }

    handleTableFiltered(total) {
        this.paging.total = total;
    }

    confirm() {

        var userId = this.userInfo.userId;
        var stage = this.states.stage;
        var priceType = this.systemTrdEnum.pricingType.fixedPrice.code;
        var effect = this.systemTrdEnum.positionEffect.close.code;
        var hedge = this.systemTrdEnum.hedgeFlag.Speculate.code;

        this.positions.forEach(item => {

            this.renderProcess.send(this.systemEvent.transmitMsg2TradingServer, this.serverFunction.sendOrder, {

                strategyId: item.strategyId || item.fundId,
                accountId: item.accountId,
                userId: userId,
                price: 0,
                volume: item.closableVolume,
                instrument: item.instrument,
                priceType: priceType,
                priceFollowType: stage,
                deviation: typeof this.states.offset == 'number' ? this.states.offset : 0,
                bsFlag: item.newDirection,
                businessFlag: 0,
                positionEffect: effect,
                assetType: item.assetType,
                customId: 'close-position-' + this.helper.makeToken(),
                orderTime: null,
                hedgeFlag: hedge,
            });
        });

        this.hide();
        this.interaction.showSuccess('平仓请求已发送，数量 = ' + this.positions.length);
    }

    cancel() {
        this.hide();
    }

    hide() {
        
        this.states.keywords = null;
        this.states.stage = this.stages[0].code;
        this.states.offset = 0;
        this.states.visible = false;
    }

    build($container) {

        super.build($container);
        this.helper.extend(this, ColumnCommonFunc);
        this.createApp();
        this.registerEvent('showup', this.showup.bind(this));
    }
}

module.exports = View;