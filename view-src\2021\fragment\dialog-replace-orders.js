const { IView } = require('../../../component/iview');
const { Order } = require('../../../model/order');
const { SmartTable } = require('../../../libs/table/smart-table');
const { ColumnCommonFunc } = require('../../../libs/table/column-common-func');

class View extends IView {

    constructor(view_name, is_standalone_window) {

        super(view_name, is_standalone_window, '追单操作');

        var paging = this.systemSetting.tablePagination;
        this.paging = {

            pageSizes: paging.pageSizes,
            pageSize: paging.pageSize,
            layout: paging.layout,
            total: 0,
            page: 0,
        };

        this.stages = this.systemTrdEnum.bidingStages;
        this.states = {

            title: '追单操作',
            visible: false,
            stage: this.stages[0].code,
            offset: 0,
            keywords: null,
        };

        this.pinyinMap = {};
    }

    /**
     * @param {Array<Order>} orders 
     * @param {Boolean} showAccount 
     */
    showup(orders, showAccount) {

        this.states.visible = true;
        this.showAccount = showAccount;
        this.vueApp.$nextTick(_=> { this.render(this.helper.deepClone(orders)); });
    }

    /**
     * @param {Array<Order>} orders 
     */
    render(orders) {
        
        if (this.tableObj === undefined) {

            var $table = this.vueApp.$el.querySelector('.data-list');
            this.tableObj = new SmartTable($table, this.identifyRecord, this, {

                tableName: 'smt-fdro',
                displayName: this.title,
                recordsFiltered: this.handleTableFiltered.bind(this),
            });
    
            this.tableObj.setMaxHeight(390);
        }
        
        orders.forEach(item => {

            let volume = item.volumeOriginal - item.tradedVolume;
            item.leftVolume = volume;
            item.entrustAmount = volume * item.orderPrice;
            item.stageName = this.stages[0].mean;
        });

        this.orders = orders;
        this.tableObj.refill(orders);

        if (this.showAccount) {
            this.tableObj.showColumns(['账号', '产品', '策略']);
        }
        else {
            this.tableObj.hideColumns(['账号', '产品', '策略']);
        }
    }

    createApp() {

        var $root = this.$container.querySelector('.dialog-replace-orders');
        this.vueApp = new Vue({

            el: $root,
            data: {

                stages: this.stages,
                states: this.states,
                paging: this.paging,
            },

            methods: this.helper.fakeVueInsMethod(this, [

                this.filterRecords,
                this.handleStageChange,
                this.handleOffsetChange,
                this.handlePageChange,
                this.handlePageSizeChange,
                this.confirm,
                this.cancel,
            ]),
        });
    }

    rebindDirection(records, propName) {
        return SmartTable.MakeColFilters(records, propName, { translators: this.systemTrdEnum.tradingDirection });
    }

    rebindOrderStatus(records, propName) {
        return this.makeFilters(records, propName, this.systemEnum.orderStatus);
    }

    makeFilters(records, propName, translators) {
        return SmartTable.MakeColFilters(records, propName, { translators: translators });
    }

    /**
     * @param {Order} record 
     */
    identifyRecord(record) {
        return record.id;
    }

    testPy(sample, keywords) {

        let matched_py = this.pinyinMap[sample];
        if (matched_py === undefined) {
            matched_py = this.pinyinMap[sample] = this.helper.pinyin(sample);
        }

        return typeof keywords == 'string' && keywords.length >= 1 && matched_py.indexOf(keywords) >= 0;
    }

    filterRecords() {

        var thisObj = this;
        var keywords = this.states.keywords;

        /**
         * @param {Order} record 
         */
        function filterByPinyin(record) {

            return thisObj.testPy(record.instrumentName, keywords)
                || thisObj.testPy(record.accountName, keywords)
                || thisObj.testPy(record.strategyName, keywords);
        }

        /**
         * @param {Order} record 
         */
        function testRecords(record) {
            return thisObj.tableObj.matchKeywords(record) || filterByPinyin(record);
        }

        this.tableObj.setPageIndex(1, false);
        this.tableObj.setKeywords(keywords, false);
        this.tableObj.customFilter(testRecords);
    }

    handleStageChange() {
        
        var stage = this.stages.find(x => x.code == this.states.stage);
        this.orders.forEach(item => {
            this.tableObj.updateRow({ id: item.id, stageName: stage.mean });
        });
    }

    handleOffsetChange() {

        //
    }

    handlePageSizeChange() {

        this.tableObj.setPageIndex(1, false);
        this.tableObj.setPageSize(this.paging.pageSize);
    }

    handlePageChange() {
        this.tableObj.setPageIndex(this.paging.page);
    }

    handleTableFiltered(total) {
        this.paging.total = total;
    }

    confirm() {

        this.renderProcess.send(this.systemEvent.transmitMsg2TradingServer, this.serverFunction.replaceOrder, {

            orderIds: this.orders.map(item => item.id),
            priceFollowType: this.states.stage,
            deviation: typeof this.states.offset == 'number' ? this.states.offset : 0,
        });

        this.hide();
        this.interaction.showSuccess('追单请求已发送，数量 = ' + this.orders.length);
    }

    cancel() {
        this.hide();
    }

    hide() {
        
        this.states.keywords = null;
        this.states.stage = this.stages[0].code;
        this.states.offset = 0;
        this.states.visible = false;
    }

    build($container) {

        super.build($container);
        this.helper.extend(this, ColumnCommonFunc);
        this.createApp();
        this.registerEvent('showup', this.showup.bind(this));
    }
}

module.exports = View;