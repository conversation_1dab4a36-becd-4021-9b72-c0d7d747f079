<div class="typical-data-view">

	<div class="user-toolbar themed-box">

		<el-input placeholder="输入关键字搜索" 
				  prefix-icon="el-icon-search" 
				  class="keywords"
				  v-model="states.keywords"
				  @change="filterRecords" clearable></el-input>
	</div>

	<div class="data-list">

		<table>
			<tr>
				<!-- <th type="index"></th> -->

				<th label="账号" 
					min-width="150" 
					prop="accountName" sortable searchable filterable overflowt></th>

				<th label="产品" 
					min-width="150" 
					prop="fundName" sortable searchable filterable overflowt></th>

				<th label="策略" 
					min-width="150" 
					prop="strategyName" sortable searchable filterable overflowt></th>
				
				<th label="合约代码" 
					fixed-width="100" 
					prop="shortInstrument" sortable searchable filterable overflowt></th>

				<th label="合约名称" 
					fixed-width="80"
					prop="instrumentName" sortable searchable filterable overflowt></th>

				<th label="交易方向"
					fixed-width="70"
					prop="direction"
					watch="direction, positionEffect"
					formatter="formatDirection"
					export-formatter="formatDirectionText" sortable></th>

				<th label="委托价"
					fixed-width="60"
					prop="orderPrice"
					align="right"
					formatter="formatPrice"></th>

				<th label="委托数量" 
					fixed-width="80"
					prop="volumeOriginal"
					align="right" sortable summarizable thousands-int></th>

				<th label="委托金额" 
					fixed-width="80" 
					prop="amount"
					align="right" sortable summarizable thousands-int></th>

				<th label="备注" 
					min-width="220"
					prop="remark"
					formatter="formatRemark" overflowt sortable></th>

			</tr>
		</table>

	</div>

	<div class="user-footer themed-box">
		<el-pagination class="s-pull-right"
					   :page-sizes="paging.pageSizes"
					   :page-size.sync="paging.pageSize" 
					   :total="paging.total"
					   :current-page.sync="paging.page" 
					   :layout="paging.layout" 
					   @size-change="handlePageSizeChange"
					   @current-change="handlePageChange"></el-pagination>
	</div>

</div>