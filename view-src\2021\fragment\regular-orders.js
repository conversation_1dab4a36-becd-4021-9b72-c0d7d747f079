const { TypicalDataView } = require('../classcial/typical-data-view');
const { Order } = require('../../../model/order');
const { repoTrading } = require('../../../repository/trading');

class View extends TypicalDataView {

    constructor(view_name, is_standalone_window) {
        super(view_name, is_standalone_window, '委托记录');
    }

    /**
     * @param {Order} record 
     */
    testRecords(record) {
        
        var kw = this.states.keywords;
        return this.tableObj.matchKeywords(record)
            || this.testPy(record.instrumentName, kw)
            || this.testPy(record.accountName, kw)
            || this.testPy(record.strategyName, kw)
            || this.testPy(record.fundName, kw)
            || this.testPy(record.userName, kw);
    }

    handleContextChange(identityId) {

        if (identityId === this.identityId) {
            return;
        }

        this.identityId = identityId;
        this.tableObj.clear();

        if (this.helper.isNotNone(identityId)) {

            this.resetControls();
            this.requestRecords();
        }
    }

    async requestRecords() {

        var identityId = this.identityId;
        var resp = await repoTrading.getTodayOrders({

            traderId: null,
            fundId: this.is4Product ? identityId : null,
            strategyId: this.is4Strategy ? identityId : null,
            accountId: this.is4Account ? identityId : null,
        });

        var records = resp.data;
        var typeds = records.map(x => new Order(x));
        this.tableObj.refill(typeds);
    }

    refresh() {
        
        if (this.helper.isNotNone(this.identityId)) {
            super.refresh();
        }
    }

    build($container, options) {

        super.build($container, options, {

            heightOffset: 109,
            tableName: 'smt-fro',
            defaultSorting: { prop: 'updateTime', direction: 'desc' },
        });

        this.is4Product = !!this.voptions.is4Product;
        this.is4Strategy = !!this.voptions.is4Strategy;
        this.is4Account = !!this.voptions.is4Account;

        /** 监听上下文切换 */
        this.registerEvent('set-context-identity', this.handleContextChange.bind(this));
    }
}

module.exports = View;