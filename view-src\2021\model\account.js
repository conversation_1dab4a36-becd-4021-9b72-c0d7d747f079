class AccountDetail {

    constructor(struc) {

        this.accountId = struc.accountId;
        this.accountName = struc.accountName;
        this.assetType = struc.assetType;
        this.available = struc.available;
        this.balance = struc.balance;
        this.closeProfit = struc.closeProfit;
        this.commission = struc.commission;
        this.connectCount = struc.connectCount;
        this.connectionStatus = struc.connectionStatus;
        this.diffBalance = struc.diffBalance;
        this.isCredit = !!struc.credit;
        this.financeAccount = struc.financeAccount;
        this.frozenCommission = struc.frozenCommission;
        this.frozenMargin = struc.frozenMargin;
        this.id = struc.id;
        this.identityId = struc.identityId;
        this.identityName = struc.identityName;
        this.identityType = struc.identityType;
        this.inMoney = struc.inMoney;
        this.loanBuyBalance = struc.loanBuyBalance;
        this.loanSellBalance = struc.loanSellBalance;
        this.loanSellQuota = struc.loanSellQuota;
        this.margin = struc.margin;
        this.marketValue = struc.marketValue;
        this.maxLimitMoney = struc.maxLimitMoney;
        this.nav = struc.nav;
        this.navRealTime = struc.navRealTime;
        this.outMoney = struc.outMoney;
        this.positionProfit = struc.positionProfit;
        this.preBalance = struc.preBalance;
        this.risePercent = struc.risePercent;
        this.status = struc.status;
        this.fundId = struc.fundId;
        this.fundName = struc.fundName;
        this.fundShare = struc.fundShare;
        this.strategyId = struc.strategyId;
        this.strategyName = struc.strategyName;
        this.tradingDay = struc.tradingDay;
        this.withdrawQuota = struc.withdrawQuota;
    }
}

class WeightedAccountDetail extends AccountDetail {

    constructor(struc) {

        super(struc);
        /** 权重系数 */
        this.multiple = 0;
        /** 按规则计算得出的可交易数量 */
        this.volume = 0;
        /** 合约可用多头仓位 */
        this.longPosition = 0;
        /** 合约可用空头仓位 */
        this.shortPosition = 0;
        /** 短合约代码 */
        this.shortInstrument = null;
        /** 合约代码 */
        this.instrument = null;
        /** 合约名称 */
        this.instrumentName = null;
    }

    /**
     * @param {WeightedAccountDetail} account 
     */
    static MakeId(account) {
        return `${account.accountId}/${account.fundId}/${account.strategyId || 'strategy-is-none'}`;
    }
}

class GroupMember {

    constructor(struc) {

        /** 账号ID */
        this.accountId = struc.accountId;
        /** 账号名称 */
        this.accountName = struc.accountName;
        /** 账号所属机构ID */
        this.orgId = struc.orgId;
        /** 该账号在组内所占权重 */
        this.multiple = struc.multiple;

        /** 所属基金ID */
        this.fundId = struc.fundId;
        /** 所属基金名称 */
        this.fundName = struc.fundName;

        var strategyId = struc.strategyId;
        if (strategyId != null && strategyId != undefined) {

            /** 是否绑定了策略 */
            this.isStrategyOn = true;
            /** 绑定的策略ID */
            this.strategyId = strategyId;
            /** 绑定的策略名称 */
            this.strategyName = struc.strategyName;
        }
    }
}

class SimpleAccountItem {
    
    constructor(struc) {
 
        var funds = struc.funds;
        var fundId = funds instanceof Array && funds.length > 0 ? funds[0].fundId : null;
        this.fundId = fundId;
        this.accountId = struc.id;
        this.accountName = struc.accountName;
        this.assetType = struc.assetType;
        this.isCredit = !!struc.credit;
    }
}

class AccountGroup {

    constructor(struc) {

        /**
         * @returns {Array<GroupMember>}
         */
        function AllocateGroupMembers() {
            return [];
        }

        this.groupId = struc.groupId;
        this.groupName = struc.groupName;
        this.createUserId = struc.createUserId;
        this.createTime = struc.createTime;
        this.members = AllocateGroupMembers();
    }

    /**
     * @param {Array} records 
     * @returns {Array<AccountGroup>}
     */
    static Convert(records) {

        /**
         * @param {*} groupId 
         * @returns {AccountGroup}
         */
        function seekGroup(groupId) {
            return dict[groupId];
        }

        let dict = {};
        let groups = [];
        records.forEach(item => {

            if (dict[item.groupId] === undefined) {
                groups.push(dict[item.groupId] = new AccountGroup(item));
            }

            let matched = seekGroup(item.groupId);
            matched.members.push(new GroupMember(item));
        });

        return groups;
    }

    /**
     * @param {*} struc 
     * @returns {GroupMember}
     */
    static ConvertMember(struc) {
        return new GroupMember(struc);
    }
}

class AccountPosition {

    constructor(struc) {

        this.accountId = struc.accountId;
        this.accountName = struc.accountName;
        this.assetType = struc.assetType;
        this.avgPrice = struc.avgPrice;
        this.closeProfit = struc.closeProfit;
        this.direction = struc.direction;
        this.frozenTodayVolume = struc.frozenTodayVolume;
        this.frozenVolume = struc.frozenVolume;
        this.fundId = struc.fundId;
        this.fundName = struc.fundName;
        this.strategyId = struc.strategyId;
        this.strategyName = struc.strategyName;
        this.id = struc.id;
        this.instrument = struc.instrument;
        this.instrumentName = struc.instrumentName;
        this.marketValue = struc.marketValue;
        this.positionCost = struc.positionCost;
        this.todayPosition = struc.todayPosition;
        this.tradingDay = struc.tradingDay;
        this.updateTime = struc.updateTime;
        this.usedCommission = struc.usedCommission;
        this.usedMargin = struc.usedMargin;
        this.yesterdayPosition = struc.yesterdayPosition;
    }

    /**
     * @param {Array} records 
     * @returns {Array<AccountPosition>}
     */
    static Convert(records) {
        
        return records instanceof Array ? records.map(item => new AccountPosition(item)) : 
        typeof records == 'object' && records != null ? [new AccountPosition] : [];
    }
}

class OrderPreview {

    constructor(struc) {
        
        this.id = struc.id;
        this.accountId = struc.accountId;
        this.accountName = struc.accountName;
        this.fundId = struc.fundId;
        this.fundName = struc.fundName;
        this.strategyId = struc.strategyId;
        this.strategyName = struc.strategyName;

        var instrument = struc.instrument;
        this.shortInstrument = typeof instrument != 'string' || instrument.indexOf('.') < 0 ? instrument : instrument.split('.')[1];
        this.instrument = instrument;
        this.instrumentName = struc.instrumentName;
        this.assetType = struc.assetType;
        this.direction = struc.direction;
        this.positionEffect = struc.positionEffect;
        this.orderPrice = struc.orderPrice;
        this.volumeOriginal = struc.volumeOriginal;
        this.amount = struc.orderPrice * struc.volumeOriginal;

        this.remark = {

            /** 备注文字 */
            content: null,
            /** 备注class name */
            clsname: null,
        };

        var remark = struc.remark;
        if (typeof remark == 'object') {

            this.remark.content = remark.content;
            this.remark.clsname = remark.clsname;
        }
    }
}

class CreditOperation {

    /**
     * @param {Number|String} code 
     * @param {Number|String} business 
     * @param {Number|String} direction
     * @param {String} mean
     * @param {String} buttonType
     */
    constructor(code, business, direction, mean, buttonType) {

        this.code = code;
        this.business = business;
        this.direction = direction;
        this.mean = mean;
        this.buttonType = buttonType;
    }
}

class Basket {

    /**
     * @param {*} struc 
     * @param {Boolean} isEtf
     */
    constructor(struc, isEtf) {

        this.isEtf = isEtf;
        /** ETF篮子代码 */
        this.etfCode = struc.code;
        this.basketId = struc.basketId;
        this.basketName = struc.basketName;
        
        if (this.basketName === undefined || this.basketName === null) {
            this.basketName = this.basketId;
        }

        this.totalWeight = struc.totalWeight = 0;
        /** 该成员暂不使用，避免VUE挂载时，进行深度数据绑定 */
        this.members = (/** @returns {Array<BasketItem>} */ function() { return []; })();
        this.createUser = struc.createUser;
        this.isMemberLoaded = false;
    }
}

class BasketItem {

    constructor(recordId, struc) {

        this.recordId = recordId;
        this.basketId = struc.basketId;
        this.basketName = struc.basketName;
        this.instrument = struc.instrument;
        this.instrumentName = struc.instrumentName;
        this.amount = struc.amount;
        this.weight = struc.weight;
        this.createUser = struc.createUser;
    }

    /**
     * @param {BasketItem} sample 
     */
    static Clone(sample) {
        
        var cloned = new BasketItem(-1, {});
        for (let key in sample) {
            cloned[key] = sample[key];
        }
        return cloned;
    }
}

module.exports = {

    AccountDetail,
    WeightedAccountDetail,
    SimpleAccountItem,
    AccountGroup,
    AccountPosition,
    OrderPreview,
    CreditOperation,
    Basket,
    BasketItem,
};