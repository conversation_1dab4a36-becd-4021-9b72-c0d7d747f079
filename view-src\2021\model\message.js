class CodeMeanItem {

    /**
     * @param {Number|String} code 
     * @param {String} mean 
     */
    constructor(code, mean) {

        this.code = code;
        this.mean = mean;
    }
}

const DefaultChannelOptions = {

    /** 是否现货竞价 */
    isSpot: false,
    /** 是否期货 */
    isFuture: false,
    /** 是否融资融券 */
    isCredit: false,
    /** 是否期权 */
    isOption: false,
    /** 是否盘后定价 */
    isAfterClose: false,
    /** 是否债券 */
    isBond: false,
    /** 是否申购认购 */
    isApply: false,
    /** 是否质押出入库 */
    isPledge: false,
    /** 是否大宗 */
    isBlock: false,
    /** 是否固收 */
    isConstr: false,
    /** 是否协议回购 */
    isProtocol: false,
};

class TradeChannel extends CodeMeanItem {

    /**
     * @param {Number|String} code 频道代码
     * @param {String} mean 频道名称
     * @param {Number|String} assetType 该频道挂钩的典型资产类型（用于合约搜索，以及下单和账号相当模块的标识、过滤用途）
     * @param {Array<Number|String>} supporteds 该频道可支持交易的所有资产类型（实际交易层面的资产类型，以及交易数据分类筛选目的）
     */
    constructor(code, mean, assetType, supporteds, options = DefaultChannelOptions) {

        super(code, mean);
        /** 该频道支持交易的资产类型（可能存在非唯一或不兼容的情况，例如：现货竞价，同时支持股票或基金） */
        this.assetType = assetType;
        /** 该渠道可以支持的所有资产类型 */
        this.supporteds = supporteds || [];

        /**
         * 对渠道选项进行补齐（实例化时，仅提供各自为true的选项即可）
         */
        for (let key in DefaultChannelOptions) {
            if (typeof options[key] != 'boolean') {
                options[key] = false;
            }
        }

        /** 渠道选项 */
        this.options = options;
    }
}

class InstrumentInfo {

    /**
     * @param {{ 
     * assetType: Number, 
     * exchangeId: String, 
     * instrument: String, 
     * instrumentName: String, 
     * precision: Number, 
     * priceStep: Number, 
     * volumeStep: Number,
     * multiple: Number }} options 
     */
    constructor (options) {

        /** 资产类型代码 */
        this.assetType = options.assetType;
        /** 交易所代码 */
        this.exchangeId = options.exchangeId;
        /** 合约代码 */
        this.shortInstrument = options.instrument.indexOf('.') < 0 ? options.instrument : options.instrument.split('.')[1];
        /** 合约代码 */
        this.instrument = options.instrument;
        /** 合约名称 */
        this.instrumentName = options.instrumentName;
        /** 合约价格精度 */
        this.precision = options.precision;
        /** 合约价格变动步长 */
        this.priceStep = options.priceStep;
        /** 合约委托数量变动步长 */
        this.volumeStep = options.volumeStep;
        /** 合约乘数 */
        this.multiple = options.multiple;

        if (typeof this.multiple != 'number' || this.multiple <= 0) {
            this.multiple = 1;
        }
    }
}

const LevelMessageOptions = {

    /** 是否为买入报价 */
    isBuy: true,
    /** 报价档位价格 */
    price: 0,
    /** 是否点击该档位时，将下单面板委托方向调整为该方向（一致时忽略） */
    changeDirection: false,
    /** 是否点击该档位时，引起下单面板弹出下单确认框 */
    promptConfirm: false,
};

class LevelMessage {

    /**
     * @param {Number} direction 
     * @param {Number} price 
     * @param {Boolean} changeDirection 
     * @param {Boolean} promptConfirm 
     */

    constructor (options = LevelMessageOptions) {

        /** 报价档位是否为买入 */
        this.isBuy = options.isBuy;
        /** 报价档位是否为卖出 */
        this.isSell = !options.isBuy;
        /** 报价档位价格 */
        this.price = options.price;
        /** 辅助选项 */
        this.options = {

            /** 是否自动切换到该档位，相同的委托方向 */
            changeDirection: !!options.changeDirection,
            /** 是否需要自动弹出下单确认 */
            promptConfirm: !!options.promptConfirm,
        };
    }
}

class TickDataConvertedFromHttpService {

    constructor(arr) {

        function readAsArr(some) {
            return some instanceof Array ? some : [];
        }

        /** 行情时间（utc时间戳） */
        this.updateTime = arr[0] * 1000;
        /** 昨收价 */
        this.preclose = arr[5];
        /** 开盘价 */
        this.open = arr[1];
        /** 日内最高价 */
        this.high = arr[2];
        /** 日内最低价 */
        this.low = arr[3];
        /** 涨停价 */
        this.ceiling = arr[8];
        /** 跌停价 */
        this.floor = arr[9];
        /** 当前价 */
        this.latest = arr[4];
        /** 当前涨跌幅（未乘以100） */
        this.change = this.preclose == 0 ? 0 : (this.latest - this.preclose) / this.preclose;
        /** 结算价 */
        this.settle = arr[14] || 0;
        /** 昨日结算价 */
        this.presettle = arr[15] || 0;

        let svolumes = readAsArr(arr[10]);
        let sells = readAsArr(arr[11]);
        let bvolumes = readAsArr(arr[12]);
        let buys = readAsArr(arr[13]);

        /** 买入档位报价 */
        this.buys = buys.map((val, idx) => ({ price: val, hands: bvolumes[idx] }));
        /** 卖出档位报价 */
        this.sells = sells.map((val, idx) => ({ price: val, hands: svolumes[idx] }));
    }
}

class TickData {

    constructor(struc) {

        /**
         * @param {Number} some 
         */
        function simplify(some) {

            if (typeof some != 'number') {
                return some;
            }
            
            var absed = Math.abs(some);
            if (absed < 1000000) {
                return parseInt(some).thousands();
            }
            else if (absed >= 1000000 && absed < 100000000) {
                return parseFloat((some / 10000).toFixed(2)).thousandsDecimal() + '万';
            }
            else {
                return parseFloat((some / 100000000).toFixed(2)).thousandsDecimal() + '亿';
            }
        }

        var sells = struc.sells;
        var buys = struc.buys;

        /** 行情时间（utc时间戳） */
        this.updateTime = struc.time;
        /** 昨收价 */
        this.preclose = struc.preclose;
        /** 开盘价 */
        this.open = struc.open;
        /** 日内最高价 */
        this.high = struc.high;
        /** 日内最低价 */
        this.low = struc.low;
        /** 涨停价 */
        this.ceiling = struc.ceiling;
        /** 跌停价 */
        this.floor = struc.floor;
        /** 当前价 */
        this.latest = struc.latest;

        /**
         * 集合竞价阶段，最新价未有返回，采用买1（或卖1）价充当
         */
        if (this.latest == 0 && sells instanceof Array && sells.length > 0) {
            this.latest = sells[0][0];
        }

        /** 当前涨跌幅（未乘以100） */
        this.change = this.preclose == 0 ? 0 : (this.latest - this.preclose) / this.preclose;
        /** 结算价 */
        this.settle = 0;
        /** 昨日结算价 */
        this.presettle = 0;
        /** 成交金额 */
        this.amount = struc.amount;
        /** 成交金额（格式化后） */
        this.amountShortWords = simplify(struc.amount);
        /** 卖出档位报价 */
        this.sells = sells instanceof Array ? sells.map(arr => ({ price: arr[0], hands: arr[1] })) : [];
        /** 买入档位报价 */
        this.buys = buys instanceof Array ? buys.map(arr => ({ price: arr[0], hands: arr[1] })) : [];
    }
}

const BasketOrderOpt = {

    suspend: false,
    cash: false,
    ceiling: false,
    floor: false,
};

class BasketOrderParam {

    constructor(struc, regulation = BasketOrderOpt) {

        this.direction = struc.direction;
        this.directionName = struc.directionName;
        this.basketId = struc.basketId;
        this.basketName = struc.basketName;
        this.method = struc.method;
        this.methodName = struc.methodName;
        this.methodLabel = struc.methodLabel;
        this.methodUnit = struc.methodUnit;
        this.scale = struc.scale;
        this.stage = struc.stage;
        this.stageName = struc.stageName;
        this.offset = struc.offset;
        this.regulation = regulation;
    }
}

/**
 * 下单面板下单信息
 */
class AlgoOrderInfo {

    constructor(struc) {

        this.direction = struc.direction;
        this.directionName = struc.directionName;
        this.productId = null;
        this.productName = null;
        this.strategyId = null;
        this.strategyName = null;
        this.identityId = struc.identityId;
        this.accountId = struc.accountId;
        this.accountName = struc.accountName;
        this.algoType = struc.algoType;
        this.algoId = struc.algoId;
        this.algoName = struc.algoName;
        this.volume = struc.volume;
        this.instrument = struc.instrument;
        this.instrumentName = struc.instrumentName;
        this.startTime = null;
        this.endTime = null;
        this.algoParam = struc.algoParam || null;
        this.remark = struc.remark;
    }
}

class PriceLevel {

    /**
     * @param {Boolean} isBuy 
     * @param {String} label 
     * @param {Number} price
     * @param {Number} hands
     */
    constructor(isBuy, label, price, hands) {

        this.isBuy = isBuy;
        this.isSell = !isBuy;
        this.label = label;
        this.price = price;
        this.hands = hands;
        this.colorClass = null;
    }

    /**
     * @param {Number} price 
     * @param {Number} base 
     * @param {Number} hands 
     * @param {Number} level1Price 对应方向第一档的价格（辅助判断集合竞价时，买卖档位价格缺失的场景）
     */
    update(price, base, hands, level1Price) {

        this.price = price;
        this.hands = hands;
        let comparePrice = price > 0 ? price : level1Price;
        this.colorClass = comparePrice > base ? 's-color-red' : comparePrice < base ? 's-color-green' : '';
    }
}

class TransactionItem {

    constructor({ direction, time, price, volume }) {

        this.time = time;
        this.price = price;
        this.volume = volume;
        this.direction = direction;
    }
}

module.exports = {

    CodeMeanItem,
    TradeChannel,
    InstrumentInfo,
    LevelMessage,
    TickData,
    BasketOrderParam,
    AlgoOrderInfo,
    PriceLevel,
    TransactionItem,
};