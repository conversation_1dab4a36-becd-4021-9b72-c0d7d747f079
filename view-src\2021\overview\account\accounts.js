const { TypicalDataView } = require('../../classcial/typical-data-view');
const { SysAccount } = require('../../../../model/sys-account');
const { repoAccount } = require('../../../../repository/account');

class AccountsView extends TypicalDataView {

    constructor(view_name, is_standalone_window) {

        super(view_name, is_standalone_window, '账号列表');
        this.registerEvent('refresh', this.refresh.bind(this));
    }

    /**
     * @param {SysAccount} record 
     */
    identifyRecord(record) {
        return record.id;
    }

    /**
     * @param {SysAccount} record 
     */
    testRecords(record) {

        return this.tableObj.matchKeywords(record) 
            || this.testPy(record.accountName, this.states.keywords)
            || this.testPy(record.fundName, this.states.keywords)
            || this.testPy(record.strategyName, this.states.keywords);
    }

    async requestRecords() {

        var resp = await repoAccount.batchGetAccountCash();
        if (resp.errorCode != 0) {
            return this.interaction.showError(`账号列表加载错误：${resp.errorCode}/${resp.errorMsg}`);
        }

        var records = resp.data || [];
        var accounts = records.map(item => new SysAccount(item));
        accounts.remove(item => item instanceof SysAccount && this.helper.isNotNone(item.strategyId));
        this.tableObj.refill(accounts);
        
        if (accounts.length > 0) {
            this.tableObj.selectNextRow();
        }
        else {
            this.trigger('selected-one-account', null);
        }
    }

    /**
     * @param {SysAccount} record 
     * @param {Boolean} status 
     */
    formatAccountStatus(record, status) {
        return this.helperUi.makeYesNoLabelHtml(status, { yesLabel: '在线', noLabel: '离线' });
    }

    /**
     * @param {SysAccount} record 
     * @param {Boolean} status 
     */
    formatAccountStatusText(record, status) {
        return status ? '已连接' : '离线';
    }

    /**
     * @param {SysAccount} record
     */
    handleRowSelected(record) {
        this.trigger('selected-one-account', record);
    }

    build($container, options) {

        super.build($container, options, { tableName: 'smt-oaa', heightOffset: 82 });
        this.requestRecords();
    }
}

module.exports = { AccountsView };