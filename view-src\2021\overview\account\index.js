const { IView } = require('../../../../component/iview');
const { TabList } = require('../../../../component/tab-list');
const { Tab } = require('../../../../component/tab');
const { Splitter } = require('../../../../component/splitter');
const { AccountsView } = require('./accounts');
const { SysAccount } = require('../../../../model/sys-account');

class View extends IView {

    constructor(view_name, is_standalone_window) {
        super(view_name, is_standalone_window, '账号概览');
    }

    createAccounts() {

        var $root = this.$container.querySelector('.xsplitter > .view-accounts');
        var view = new AccountsView('@2021/overview/account/accounts');
        view.registerEvent('selected-one-account', this.handleAccountSelect.bind(this));
        view.loadBuild($root);
        this.accountView = view;
    }

    /**
     * @param {SysAccount} account 
     */
    handleAccountSelect(account) {

        /**
         * 上下文账号对象
         */
        this.account = account;
        this.tabs.fireEventOnFocusedTab('set-context-identity', account.accountId);
    }

    createRecords() {

        var $tab = this.$container.querySelector('.xsplitter > .view-records > .tabs');
        var $content = this.$container.querySelector('.xsplitter > .view-records > .contents');
        var tabs = new TabList({

            allowCloseTab: false,
            hideTab4OnlyOne: false,
            embeded: true,
            $navi: $tab,
            $content: $content,
            tabCreated: this.handleTabCreated.bind(this),
            tabFocused: this.handleTabFocused.bind(this),
        });

        tabs.openTab(true, '@2021/overview/components/nav', '累计收益率', { is4Account: true });
        tabs.openTab(true, '@2021/fragment/regular-orders', '委托', { is4Account: true });
        tabs.openTab(true, '@2021/fragment/regular-positions', '持仓', { is4Account: true });
        tabs.openTab(true, '@2021/fragment/regular-exchanges', '成交', { is4Account: true });
        this.tabs = tabs;
    }

    /**
     * @param {Tab} tab 
     */
    handleTabCreated(tab) {

        if (this.account) {
            this.tabs.fireEventOnTab(tab, 'set-context-identity', this.account.accountId);
        }

        this.simulateSplit();
    }

    /**
     * @param {Tab} tab 
     */
    handleTabFocused(tab) {
        
        if (this.account) {
            this.tabs.fireEventOnTab(tab, 'set-context-identity', this.account.accountId);
        }

        this.simulateSplit();
    }

    createSplitter() {

        var bar_name = 'overview-account';
        var $bar = this.$container.querySelector('.xsplitter > .splitter-bar');
        this.splitter = new Splitter(bar_name, $bar, this.handleSpliting.bind(this), {

            previousMinHeight: 145,
            nextMinHeight: 200,
        });

        setTimeout(() => { this.splitter.recover(); }, 1000);
    }

    simulateSplit() {
        setTimeout(() => { this.splitter.simulateResize(); }, 100);
    }

    /**
     * @param {Number} previous_height 
     * @param {Number} next_height 
     */
    handleSpliting(previous_height, next_height) {

        this.accountView.trigger('table-max-height', previous_height);
        this.tabs.fireEventOnAllTabs('table-max-height', next_height);
    }

    exportSome() {
        this.accountView.exportSome();
    }

    refresh() {
        this.productView.trigger('refresh');
    }

    build($container) {

        super.build($container);
        this.createRecords();
        this.createAccounts();
        this.createSplitter();
    }
}

module.exports = View;