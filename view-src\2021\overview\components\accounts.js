const { TypicalDataView } = require('../../classcial/typical-data-view');
const { AccountDetail } = require('../../model/account');
const { repoAccount } = require('../../../../repository/account');

class AccountsView extends TypicalDataView {

    constructor(view_name, is_standalone_window) {
        super(view_name, is_standalone_window, '账号列表');
    }

    /**
     * @param {AccountDetail} record 
     */
    testRecords(record) {
        return this.tableObj.matchKeywords(record) || this.testPy(record.identityName, this.states.keywords);
    }

    async requestRecords() {

        if (this.helper.isNone(this.identityId)) {

            console.log('no contextual identity has been set');
            return;
        }

        var resp = await repoAccount.getAccountDetailInfo({ user_id: this.userInfo.userId, identity_id: this.identityId });
        if (resp.errorCode != 0) {
            this.interaction.showError(`账号列表加载错误：${resp.errorCode}/${resp.errorMsg}`);
        }

        var accounts = resp.data.list;
        this.tableObj.refill(accounts.map(x => new AccountDetail(x)));
    }

    handleIdentityChange(identityId) {

        this.identityId = identityId;
        this.requestRecords();
    }

    build($container, options) {

        super.build($container, options, { tableName: 'smt-oca', heightOffset: 92 });
        this.registerEvent('set-context-identity', this.handleIdentityChange.bind(this));
    }
}

module.exports = { AccountsView };