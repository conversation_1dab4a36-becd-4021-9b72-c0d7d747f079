const { IView } = require('../../../../component/iview');
const { AccountsView } = require('./accounts');
const IdentityNavView = require('./nav');

class View extends IView {

    constructor(view_name, is_standalone_window) {
        super(view_name, is_standalone_window, '概览');
    }

    createAccountsView() {

        var $root = this.$tr.querySelector('td > .block-accounts');
        var view = new AccountsView('@2021/overview/components/accounts', false);
        view.loadBuild($root, this.voptions);
        this.accountView = view;
    }

    createNavView() {

        var $root = this.$tr.querySelector('td > .block-nav-chart');
        var view = new IdentityNavView('@2021/overview/components/nav', false);
        view.loadBuild($root, this.voptions);
        this.chartView = view;
    }

    handleIdentityChange(identityId) {

        if (identityId === this.identityId) {
            return;
        }

        this.identityId = identityId;
        this.accountView.trigger('set-context-identity', identityId);
        this.chartView.trigger('set-context-identity', identityId);
    }

    handleHeightChange(height) {

        this.$table.style.height = height - 100 + 'px';
        this.accountView.trigger('table-max-height', height);
        this.chartView.trigger('set-chart-height', height);
    }

    exportSome() {
        this.accountView.exportSome();
    }

    build($container, options) {

        super.build($container);
        this.voptions = options;
        this.$table = this.$container.querySelector('.identity-composition > table');
        this.$tr = this.$table.querySelector('tbody > tr');
        this.createAccountsView();
        this.createNavView();
        this.registerEvent('set-context-identity', this.handleIdentityChange.bind(this));
        this.registerEvent('table-max-height', this.handleHeightChange.bind(this));
    }
}

module.exports = View;