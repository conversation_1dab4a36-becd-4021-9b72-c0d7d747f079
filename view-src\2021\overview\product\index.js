
const { IView } = require('../../../../component/iview');
const { TabList } = require('../../../../component/tab-list');
const { Tab } = require('../../../../component/tab');
const { Splitter } = require('../../../../component/splitter');
const { ProductsView } = require('./products');
const { SysProductDetail } = require('../../../../model/sys-product');

class View extends IView {

    constructor(view_name, is_standalone_window) {
        super(view_name, is_standalone_window, '产品概览');
    }

    createProducts() {

        var $root = this.$container.querySelector('.xsplitter > .view-products');
        var view = new ProductsView('@2021/overview/product/products');
        view.registerEvent('selected-one-product', this.handleProductSelect.bind(this));
        view.loadBuild($root);
        this.productView = view;
    }

    /**
     * @param {SysProductDetail} product 
     */
    handleProductSelect(product) {

        /**
         * 上下文产品对象
         */
        this.product = product;
        this.tabs.fireEventOnFocusedTab('set-context-identity', product.fundId);
    }

    createRecords() {

        var $tab = this.$container.querySelector('.xsplitter > .view-records > .tabs');
        var $content = this.$container.querySelector('.xsplitter > .view-records > .contents');
        var tabs = new TabList({

            allowCloseTab: false,
            hideTab4OnlyOne: false,
            embeded: true,
            $navi: $tab,
            $content: $content,
            tabCreated: this.handleTabCreated.bind(this),
            tabFocused: this.handleTabFocused.bind(this),
        });

        tabs.openTab(true, '@2021/overview/components/composition', '概览', { is4Product: true });
        tabs.openTab(true, '@2021/fragment/regular-orders', '委托', { is4Product: true });
        tabs.openTab(true, '@2021/fragment/regular-positions', '持仓', { is4Product: true });
        tabs.openTab(true, '@2021/fragment/regular-exchanges', '成交', { is4Product: true });
        this.tabs = tabs;
    }

    /**
     * @param {Tab} tab 
     */
    handleTabCreated(tab) {

        if (this.product) {
            this.tabs.fireEventOnTab(tab, 'set-context-identity', this.product.fundId);
        }

        this.simulateSplit();
    }

    /**
     * @param {Tab} tab 
     */
    handleTabFocused(tab) {
        
        if (this.product) {
            this.tabs.fireEventOnTab(tab, 'set-context-identity', this.product.fundId);
        }

        this.simulateSplit();
    }

    createSplitter() {

        var bar_name = 'overview-product';
        var $bar = this.$container.querySelector('.xsplitter > .splitter-bar');
        this.splitter = new Splitter(bar_name, $bar, this.handleSpliting.bind(this), {

            previousMinHeight: 145,
            nextMinHeight: 200,
        });

        setTimeout(() => { this.splitter.recover(); }, 1000);
    }

    simulateSplit() {
        setTimeout(() => { this.splitter.simulateResize(); }, 100);
    }

    /**
     * @param {Number} previous_height 
     * @param {Number} next_height 
     */
    handleSpliting(previous_height, next_height) {

        this.productView.trigger('table-max-height', previous_height);
        this.tabs.fireEventOnAllTabs('table-max-height', next_height);
    }

    exportSome() {
        this.productView.exportSome();
    }

    refresh() {
        this.productView.trigger('refresh');
    }

    build($container) {

        super.build($container);
        this.createRecords();
        this.createProducts();
        this.createSplitter();
    }
}

module.exports = View;
