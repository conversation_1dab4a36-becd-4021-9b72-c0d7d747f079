const { TypicalDataView } = require('../../classcial/typical-data-view');
const { SysStrategy } = require('../../../../model/sys-strategy');
const { repoStrategy } = require('../../../../repository/strategy');

class StrategiesView extends TypicalDataView {

    constructor(view_name, is_standalone_window) {

        super(view_name, is_standalone_window, '策略列表');
        this.registerEvent('refresh', this.refresh.bind(this));
    }

    /**
     * @param {SysStrategy} record 
     */
    identifyRecord(record) {
        return record.id;
    }

    /**
     * @param {SysStrategy} record 
     */
    testRecords(record) {

        return this.tableObj.matchKeywords(record) 
            || this.testPy(record.fundName, this.states.keywords)
            || this.testPy(record.strategyName, this.states.keywords);
    }

    async requestRecords() {

        var resp = await repoStrategy.getAll();
        if (resp.errorCode != 0) {
            return this.interaction.showError(`策略列表加载错误：${resp.errorCode}/${resp.errorMsg}`);
        }

        var records = resp.data || [];
        records.forEach(item => {

            delete item.strategyAccounts;
            delete item.users;
        });

        var strategies = records.map(item => new SysStrategy(item));
        this.tableObj.refill(strategies);
        if (strategies.length > 0) {
            this.tableObj.selectNextRow();
        }
        else {
            this.trigger('selected-one-strategy', null);
        }
    }

    /**
     * @param {SysStrategy} record 
     * @param {Boolean} status 
     */
    formatStrategyStatus(record, status) {
        return this.helperUi.makeYesNoLabelHtml(status, { yesLabel: '运行中', noLabel: '停止' });
    }

    /**
     * @param {SysStrategy} record 
     * @param {Boolean} status 
     */
    formatStrategyStatusText(record, status) {
        return status ? '运行中' : '停止';
    }

    /**
     * @param {SysStrategy} record
     */
    handleRowSelected(record) {
        this.trigger('selected-one-strategy', record);
    }

    build($container, options) {

        super.build($container, options, { tableName: 'smt-os', heightOffset: 82 });
        this.requestRecords();
    }
}

module.exports = { StrategiesView };