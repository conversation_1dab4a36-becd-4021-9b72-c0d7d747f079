const { IdentityRelationView } = require('../classcial/identity-relation-view');

class AssetToolbarView extends IdentityRelationView {

    constructor(view_name) {

        super(view_name, false);

        this.total = 100;
        this.pageSize = 30;
    }
    
    createApp() {

        this.toolbarVueIns = new Vue({

            el: this.$container.firstElementChild,
            data: {

                selectStates: this.istates,
                date: '',
                checked: true,
                isSummary: false,
                funds: this.products,
                strategies:  this.strategies,
                accounts: this.accounts,
                pickerOptions: {

                    disabledDate(time) {
                        return time.getTime() > Date.now() - 8.64e6;
                    },
                },
            },

            methods: this.helper.fakeVueInsMethod(this, [

                this.handleSearch,
                this.handleProductChange,
                this.handleStrategyChange,
                this.handleAccountChange,
                this.dateTimechange,
                this.handleSummarizeOptionChange,
            ]),
        });
    }

    handleSearch() {
        
        let vueData = this.toolbarVueIns;
        
        if (!vueData.date) {
            vueData.checked = true;
        }

        let searchData = {

            date: vueData.date,
            checked: vueData.checked,
            isSummary: vueData.isSummary,
            fund: vueData.selectStates.productId,
            strategy: vueData.selectStates.strategyId,
            account: vueData.selectStates.accountId,
            pageNo: 1,
            pageSize: 30,
        }

        this.trigger("search-btn", searchData);
    };

    handleSummarizeOptionChange() {

        this.istates.accountId = null;
        this.toolbarVueIns.selectStates.showAccount = !this.toolbarVueIns.isSummary;
    }

    dateTimechange() {

        let vueData = this.toolbarVueIns;

        function getDateString(timeString) {

            let dateTiem = timeString;
            let year = dateTiem.getFullYear();
            let month = dateTiem.getMonth();

            if (month < 10) {
                month = `0` + (month+1);
            }

            let day = dateTiem.getDate();

            if (day < 10) {
                day = `0` + day;
            }
            return `${year}` + `${month}` + `${day}`;
        }

        let nowDate = getDateString(new Date);
        let time = vueData.date;
        
        if (!time) {
            return;
        }
        let minDate = time[0];
        let maxDate = time[1];
        
        if (minDate == nowDate && maxDate == nowDate) {

            vueData.date = '';
            vueData.checked = true;
        }
    };


    build($container) {

        super.build($container);
        this.createApp();
        this.handleSearch();
    }
}

module.exports = { AssetToolbarView };