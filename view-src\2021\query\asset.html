<div>

	<div class="user-toolbar themed-box"></div>

	<div class="data-list">
		<table>
            <tr>
                <th label="交易日" fixed-width="90" prop="tradingDay" sortable></th>
                <th label="权益" min-width="120" prop="balance" align="right" thousands-int></th>
                <th label="市值" min-width="120" prop="marketValue" align="right" thousands-int></th>
                <th label="可用资金" min-width="120" prop="available" align="right" thousands-int></th>
                <th label="昨日权益" min-width="120" prop="preBalance" align="right" thousands-int></th>
                <th label="冻结资金" min-width="80" prop="frozenMargin" align="right" thousands-int></th>

				<th label="产品" min-width="150" prop="fundName" sortable overflowt searchable></th>
                <th label="策略" min-width="150" prop="strategyName" overflowt searchable sortable></th>
                <th label="账号" min-width="150" prop="identityName" overflowt sortable searchable></th>
                
                <th label="涨跌幅" 
                    min-width="80" 
                    prop="risePercent" 
                    align="right" 
                    class-maker="makeBenefitClass" 
                    footer-class-maker="makeBenefitClass" overflowt percentage sortable></th>

                <th label="平仓盈亏" 
                    min-width="100" 
                    prop="closeProfit" 
                    align="right" 
                    class-maker="makeBenefitClass" 
                    footer-class-maker="makeBenefitClass" overflowt thousands-int sortable summarizable></th>

                <th label="浮动盈亏" 
                    min-width="100" 
                    prop="positionProfit" 
                    align="right" 
                    class-maker="makeBenefitClass" 
                    footer-class-maker="makeBenefitClass" overflowt thousands-int sortable summarizable></th>

                <th label="出入金" min-width="100" prop="ioMoney" align="right" thousands-int></th>
                <th label="手续费" min-width="80" prop="commission" align="right" overflowt sortable summarizable thousands-int></th>
                <th label="占用保证金" min-width="100" prop="margin" align="right" thousands-int></th>
                <th label="份额" min-width="100" prop="fundShare" align="right" thousands-int></th>
                <th label="净值" min-width="100" prop="nav" align="right" precision="4" sortable></th>

                <th label="收益" 
                    min-width="100" 
                    prop="dayProfit" 
                    align="right" 
                    class-maker="makeBenefitClass" 
                    footer-class-maker="makeBenefitClass" overflowt thousands-int sortable summarizable></th>
            </tr>
        </table>
	</div>

	<div class="user-footer themed-box">

		<el-pagination class="s-pull-right"
                       @size-change="handleSizeChange" 
                       @current-change="handleCurrentChange" 
                       :current-page.sync="currentPage"
                       :page-sizes="pageSizes" 
                       :page-size="pageSize"
                       :total="total"
                       layout="prev, pager, next, sizes, total">
		</el-pagination>
	</div>

</div>