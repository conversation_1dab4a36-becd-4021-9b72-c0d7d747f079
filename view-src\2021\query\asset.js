const { IView } = require('../../../component/iview');
const { AssetToolbarView } = require('./asset-toolbar');
const { SmartTable } = require('../../../libs/table/smart-table');
const { ColumnCommonFunc } = require('../../../libs/table/column-common-func');
const { repoTrading } = require('../../../repository/trading');

class View extends IView {

    constructor(view_name, is_standalone_window) {

        super(view_name, is_standalone_window, '权益查询');
        this.paging = {

            pageSizes: [30, 50, 100, 300, 500, 1000],
            pageSize: 30,
            layout: " prev, pager, next, sizes, total ",
            total: 0,
            page: 1,
        };
    }

    createApp() {

        this.vins = new Vue({

            el: this.$container.firstElementChild,
            data: {

                total: this.paging.total,
                pageSize: this.paging.pageSize,
                pageSizes: this.paging.pageSizes,
                currentPage: 1,
                oldData: {},
            },

            methods: this.helper.fakeVueInsMethod(this, [

                this.handleCurrentChange,
                this.handleSizeChange,
            ]),
        });
    }

    createToolbar() {

        var $root = this.$container.querySelector('.user-toolbar');
        var view = new AssetToolbarView('@2021/query/asset-toolbar');
        view.registerEvent('search-btn', this.handleSearch.bind(this));
        view.loadBuild($root);
        this.toolbarView = view;
    }

    async handleSearch(searchData) {

        searchData.userId = this.userInfo.userId;
        searchData.token = this.userInfo.token;
        searchData.pageNo = searchData.pageNo ? searchData.pageNo : 1;
        searchData.pageSize = searchData.pageSize ? searchData.pageSize : 30;

        this.vins.oldData = searchData;
        let resp = null;
        let isSummary = searchData.isSummary;
        
        if (searchData.checked) {
            resp = await repoTrading.getTodayBalance(searchData);
        } 
        else {
            resp = await repoTrading.getHositoryBalance(searchData);
        }

        this.vins.total = resp.data.totalSize;
        this.vins.pageSize = resp.data.pageSize;
        this.vins.currentPage = resp.data.pageNo;
        let records = resp.data.list;

        if (isSummary) {
            this.tableObj.hideColumns(['账号']);
        }
        else {
            this.tableObj.showColumns(['账号']);
        }

        this.tableObj.refill(records);
        this.tableObj.setPageSize(resp.data.pageSize);
    }

    handleSizeChange(val) {

        let tbdata = this.toolbarView.toolbarVueIns;
        this.vins.currentPage = 1;

        let searchData = {

            date: tbdata.date,
            checked: tbdata.checked,
            isSummary: tbdata.isSummary,
            fund: tbdata.selectStates.productId,
            account: tbdata.selectStates.accountId,
            strategy: tbdata.selectStates.strategyId,
            pageNo: this.vins.currentPage,
            pageSize: val,
        };

        this.vins.oldData = searchData;
        this.handleSearch(searchData);
    }

    handleCurrentChange(val) {

        let tbdata = this.toolbarView.toolbarVueIns;
        let searchData = {

            date: tbdata.date,
            checked: tbdata.checked,
            isSummary: tbdata.isSummary,
            fund: tbdata.selectStates.productId,
            account: tbdata.selectStates.accountId,
            strategy: tbdata.selectStates.strategyId,
            pageNo: val,
            pageSize: this.vins.pageSize,
        };

        for (let key in searchData) {

            if (key == 'date') {

                if (this.vins.oldData[key][0] != searchData[key][0] || this.vins.oldData[key][1] != searchData[key][1]) {

                    searchData.pageNo = 1;
                    searchData.pageSize = 30;
                    break;
                }
            } 
            else if (searchData[key] != this.vins.oldData[key]) {
                
                if (key != 'pageNo') {

                    searchData.pageNo = 1;
                    searchData.pageSize = 30;
                }
            }
        }

        this.vins.oldData = searchData;
        this.handleSearch(searchData);
    }

    entrustRecord(record) {
        return record.id;
    }

    setupTable() {

        this.helper.extend(this, ColumnCommonFunc);
        var $table = this.$container.querySelector('.data-list');

        this.tableObj = new SmartTable($table, this.entrustRecord, this, {

            tableName: 'smt-qa',
            displayName: this.title,
            enableConfigToolkit: true,
            recordsFiltered: (filtered_count) => {
                this.paging.total = filtered_count;
            }
        });

        this.tableObj.setMaxHeight(400);
    }  

    adjustTableHeight(width, height, isMaximized) {
        this.tableObj.setMaxHeight(height - (isMaximized ? 195 : 180));
    }

    handleActivated() {
        this.tableObj.fitColumnWidth();
    }
    
    listen2Events() {

        /** 监听TAB激活 */
        this.registerEvent(this.systemEvent.tabActivated, this.handleActivated.bind(this));
    
        /** 监听窗口尺寸调整 */
        this.lisen2WinSizeChange(this.adjustTableHeight.bind(this));
    }

    build($container) {

        super.build($container);
        this.createApp();
        this.createToolbar();
        this.setupTable();
        this.listen2Events();
        setTimeout(() => { this.simulateWinSizeChange(); }, 1000);
    }
}

module.exports = View;