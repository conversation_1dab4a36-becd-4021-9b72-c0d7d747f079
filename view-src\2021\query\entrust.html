<div>

	<div class="channel-container"></div>
	<div class="user-toolbar themed-box"></div>

	<div class="data-list">
		<table>
			<tr>
				<th label="代码" 
					fixed-width="100" 
					prop="instrument" overflowt sortable searchable></th>

				<th label="名称" 
					fixed-width="80" 
					prop="instrumentName" overflowt sortable searchable></th>

				<th label="产品" 
					min-width="150" 
					prop="fundName" sortable overflowt searchable></th>

				<th label="策略" 
					min-width="150" 
					prop="strategyName" overflowt searchable sortable></th>

				<th label="账号" 
					min-width="150" 
					prop="accountName" overflowt sortable searchable></th>

				<th label="方向"
					fixed-width="70" 
					prop="direction" 
					formatter="formatDirection"
					export-formatter="formatDirectionText" sortable></th>

				<th label="订单状态"
					fixed-width="100"
					prop="orderStatus"
					watch="orderStatus, errorMsg"
					formatter="formatOrderStatus"
					export-formatter="formatOrderStatusText" overflowt sortable></th>

				<th label="委托量" 
					fixed-width="80" 
					prop="volumeOriginal" 
					align="right" summarizable thousands-int></th>

				<th label="委托价" 
					fixed-width="60" 
					prop="orderPrice" 
					align="right" 
					formatter="formatPrice"></th>

				<th label="成交量" 
					fixed-width="80" 
					prop="tradedVolume" 
					align="right" summarizable thousands-int></th>

				<th label="成交价" 
					fixed-width="60" 
					prop="tradedPrice" 
					align="right" 
					formatter="formatPrice"></th>

				<th label="冻结资金" 
					fixed-width="80" 
					prop="frozenMargin" 
					align="right" thousands-int></th>

				<th label="报单编号" 
					min-width="80" 
					prop="exchangeOrderId" overflowt sortable searchable></th>

				<th label="交易日" 
					fixed-width="80" 
					prop="tradingDay" sortable></th>

				<th label="交易员" 
					min-width="90" 
					prop="userName" overflowt sortable searchable></th>

				<th label="交易方式" 
					fixed-width="100" 
					prop="businessFlag"
					formatter="formatBusinessFlag" 
					export-formatter="formatBusinessFlag" sortable></th>

				<th label="报单时间" fixed-width="100" prop="orderTime" formatter="formatTime" sortable></th>
				<th label="创建时间" fixed-width="100" prop="createTime" formatter="formatTime" sortable></th>
				<th label="成交时间" fixed-width="100" prop="tradeTime" formatter="formatTime" sortable></th>
				
				<!-- product-remove -->

				<th label="资产类型" 
					fixed-width="100"
					prop="assetType"
					formatter="formatAssetType" sortable></th>

				<th label="外来单" 
					fixed-width="90" 
					prop="foreign" 
					formatter="formatYesNo"
					export-formatter="formatYesNoText" sortable></th>

				<th label="强平"
					fixed-width="80"
					prop="forceClose"
					formatter="formatYesNo" 
					export-formatter="formatYesNoText" sortable></th>

				<th label="备注" 
					min-width="150" 
					prop="remark" overflowt sortable></th>
			</tr>
		</table>
	</div>

	<div class="user-footer themed-box">

		<el-pagination class="s-pull-right"
					   @size-change="handleSizeChange" 
					   @current-change="handleCurrentChange"
					   :current-page.sync="currentPage" 
					   :page-sizes="pageSizes" 
					   :page-size="pageSize"
					   :total="total"
					   layout="prev, pager, next, sizes, total">
		</el-pagination>
	</div>
	
</div>