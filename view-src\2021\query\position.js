const IView = require('../../../component/iview').IView;
const SmartTable = require('../../../libs/table/smart-table').SmartTable;
const ColumnCommonFunc = require('../../../libs/table/column-common-func').ColumnCommonFunc;
const PositionRecord = require('../../../model/position').Position;
const { QueryToolbarView } = require('./query-toolbar');
const ChannelView = require('../fragment/trading-channel');
const { TradeChannel } = require('../model/message');
const repoTrading = require('../../../repository/trading').repoTrading;

class View extends IView {

    constructor(view_name, is_standalone_window) {

        super(view_name, is_standalone_window, '持仓查询');
        this.paging = {
            
            pageSizes: [30, 50, 100, 300, 500, 1000],
            pageSize: 30,
            layout: " prev, pager, next, sizes, total ",
            total: 0,
            page: 1,
        };
    }

    createApp() {

        this.vins = new Vue({

            el: this.$container.firstElementChild,
            data: {

                total: this.paging.total,
                pageSize: this.paging.pageSize,
                pageSizes: this.paging.pageSizes,
                currentPage: 1,
                oldData: {},
            },

            methods: this.helper.fakeVueInsMethod(this, [

                this.handleCurrentChange,
                this.handleSizeChange,
            ]),
        });


    }

    createChannel() {

        var $root = this.$container.querySelector('.channel-container');

        this.channels = {

            compete: new TradeChannel(1, '竞价'),
            block: new TradeChannel(2, '大宗交易'),
            apply: new TradeChannel(3, '申购类'),
            repo: new TradeChannel(4, '协议回购'),
        };

        this.defaultChannel = this.channels.compete;

        this.states = {

            /** 当前的交易频道 */
            channel: this.defaultChannel,
            /** 当前的交易合约 */
            instrument: null,
            /** 当前的交易合约名称 */
            instrumentName: null,
        };

        var view = new ChannelView('@2021/fragment/trading-channel');
        view.trigger('set-as-channels', this.helper.dict2Array(this.channels));
        view.registerEvent('channel-selected', this.handleChannelChange.bind(this));
        view.loadBuild($root);
        view.trigger('set-default-channel', this.defaultChannel);
        this.channelView = view;
    }

    handleChannelChange(channel) {

        // console.log(JSON.parse(JSON.stringify(channel)));
        this.defaultChannel = channel;
    }

    createToolbar() {

        var $root = this.$container.querySelector('.user-toolbar');
        var view = new QueryToolbarView('@2021/query/query-toolbar');
        view.registerEvent('search-btn', this.handleSearch.bind(this));
        view.loadBuild($root);
        this.toolbarView = view;
    }

    async handleSearch(searchData) {

        searchData.userId = this.userInfo.userId;
        searchData.token = this.userInfo.token;
        searchData.pageNo = searchData.pageNo ? searchData.pageNo : 1;
        searchData.pageSize = searchData.pageSize ? searchData.pageSize : 30;

        this.vins.oldData = searchData;
        let resp = null;

        if (searchData.checked) {
            resp = await repoTrading.getTodayPosition(searchData);
        } else {
            
            resp = await repoTrading.getHistoryPosition(searchData);
        }
        
        let records = resp.data.list.map(item => {
            return new PositionRecord(item);
        });
        
        this.tableObj.refill(records);
        this.tableObj.setPageSize(resp.data.pageSize);

        this.vins.total = resp.data.totalSize;
        this.vins.pageSize = resp.data.pageSize;
        this.vins.currentPage = resp.data.pageNo;
    }

    handleSizeChange(val) {

        let tbdata = this.toolbarView.toolbarVueIns;
        this.vins.currentPage = 1;
        let searchData = {

            date: tbdata.date,
            checked: tbdata.checked,
            fund: tbdata.selectStates.fundId ,
            account: tbdata.selectStates.accountId ,
            strategy: tbdata.selectStates.strategyId,
            searchValue: tbdata.searchValue,
            pageNo: this.vins.currentPage,
            pageSize: val,
        };

        this.vins.oldData = searchData;
        this.handleSearch(searchData);
    }

    handleCurrentChange(val) {

        let tbdata = this.toolbarView.toolbarVueIns;
        let searchData = {

            date: tbdata.date,
            checked: tbdata.checked,
            fund: tbdata.selectStates.fundId ,
            account: tbdata.selectStates.accountId ,
            strategy: tbdata.selectStates.strategyId,
            searchValue: tbdata.searchValue,
            pageNo: val,
            pageSize: this.vins.pageSize,
        };

        for (let key in searchData) {

            if (key == 'date') {

                if (this.vins.oldData[key][0] != searchData[key][0] || this.vins.oldData[key][1] != searchData[key][1]) {

                    searchData.pageNo = 1;
                    searchData.pageSize = 30;
                    break;
                }
            } 
            else if (searchData[key] != this.vins.oldData[key]) {

                if (key != 'pageNo') {

                    searchData.pageNo = 1;
                    searchData.pageSize = 30;
                }
            }
        }

        this.vins.oldData = searchData;
        this.handleSearch(searchData);
    }

    entrustRecord(record) {
        return record.id;
    }

    setupTable() {

        this.helper.extend(this, ColumnCommonFunc);
        var $table = this.$container.querySelector('.data-list');

        this.tableObj = new SmartTable($table, this.entrustRecord, this, {

            tableName: 'smt-qp',
            displayName: this.title,
            enableConfigToolkit: true,
            recordsFiltered: (filtered_count) => {
                this.paging.total = filtered_count;
            }
        });

        this.tableObj.setMaxHeight(400);
    }

    adjustTableHeight(width, height, isMaximized) {
        this.tableObj.setMaxHeight(height - (isMaximized ? 215 : 200));
    }

    handleActivated() {
        this.tableObj.fitColumnWidth();
    }

    listen2Events() {

        /** 监听TAB激活 */
        this.registerEvent(this.systemEvent.tabActivated, this.handleActivated.bind(this));
    
        /** 监听窗口尺寸调整 */
        this.lisen2WinSizeChange(this.adjustTableHeight.bind(this));
    }

    build($container) {

        super.build($container);
        this.createApp();
        this.createToolbar();
        this.createChannel();
        this.setupTable();
        this.listen2Events();
        setTimeout(() => { this.simulateWinSizeChange(); }, 1000);

    }
}

module.exports = View;