<div>

	<el-checkbox v-model="checked">只看当日</el-checkbox>

	<el-date-picker v-model="date" 
					type="daterange" 
					value-format="yyyyMMdd" 
					:picker-options="pickerOptions"
					range-separator="至" 
					start-placeholder="开始日期" 
					end-placeholder="结束日期" 
					:disabled="checked"
					@change="dateTimechange" 
					class="s-w-150 s-mgl-10 s-mgr-10">
	</el-date-picker>

	<el-select v-model="selectStates.productId" 
				placeholder="请选择产品" 
				@change="handleProductChange" 
				class="s-w-150 s-mgr-10" filterable clearable>

		<el-option v-for="item in funds" 
					:key="item.value" 
					:label="item.label" 
					:value="item.value"></el-option>
	</el-select>

	<el-select v-model="selectStates.strategyId"
				placeholder="请选择策略"
				@change="handleStrategyChange" 
				class="s-w-150 s-mgr-10" filterable clearable>

		<el-option v-for="item in strategies"
					:key="item.value" 
					:label="item.label" 
					:value="item.value"></el-option>
	</el-select>

	<el-select v-model="selectStates.accountId" 
				placeholder="请选择账号" 
				@change="handleAccountChange" 
				class="s-w-150 s-mgr-10" filterable clearable>

		<el-option v-for="item in accounts" 
					:key="item.value"
					:label="item.label"
					:value="item.value"></el-option>
	</el-select>

	<el-input placeholder="合约代码搜索" prefix-icon="el-icon-search" v-model="searchValue" class="s-w-150 s-mgr-10"></el-input>
	<el-button type="primary" icon="el-icon-search" @click="handleSearch"></el-button>
	
</div>