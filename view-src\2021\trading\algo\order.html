<div class="typical-data-view">

	<div class="user-toolbar themed-box">

		<span>算法母单</span>

		<el-input placeholder="输入关键字搜索" 
				  prefix-icon="el-icon-search" 
				  class="keywords s-mgl-10"
				  v-model="states.keywords"
				  @change="filterOrders" clearable></el-input>

		<div class="s-pull-right">
			<el-button type="primary" @click="hope2CancelCheckeds">撤勾选</el-button>
			<el-button type="primary" @click="hope2CancelAll">撤全部</el-button>
			<el-button type="primary" @click="refresh">刷新</el-button>
			<!-- <el-button type="primary" @click="hope2Replace">追单</el-button> -->
		</div>

	</div>

	<div class="data-list">

		<table>
			<tr>
				<th type="check" min-width="40"></th>

				<th label="账号" 
					min-width="100" 
					prop="accountName" overflowt filterable sortable searchable></th>

				<th label="算法类型" 
					min-width="70" 
					prop="algorithmName" overflowt filterable sortable searchable></th>

				<th label="证券代码" 
					min-width="100" 
					prop="instrument" overflowt sortable searchable></th>

				<th label="证券名称" 
					min-width="80" 
					prop="instrumentName" overflowt sortable searchable></th>
			
				<th label="目标量" 
					min-width="60" 
					prop="volume" 
					align="right" sortable thousands-int></th>

				<th label="方向" 
					min-width="50"
					prop="direction"
					formatter="formatDirection" 
					export-formatter="formatDirectionText" 
					filter-data-provider="rebindDirection" sortable></th>

				<th label="状态"
					fixed="right"
					prop="algorithmStatus"
					fixed-width="60"
					formatter="formatStatus"
					export-formatter="formatStatus"></th>

				<th label="操作"
					fixed="right"
					prop="isCompleted"
					fixed-width="60"
					formatter="formatActions"
					exportable="false"></th>
					
			</tr>
		</table>

	</div>

	<div class="user-footer themed-box">
		<el-pagination class="s-pull-right"
					   :page-sizes="paging.pageSizes"
					   :page-size.sync="paging.pageSize" 
					   :total="paging.total"
					   :current-page.sync="paging.page" 
					   :layout="paging.layout" 
					   @size-change="handlePageSizeChange"
					   @current-change="handlePageChange"></el-pagination>
	</div>

</div>