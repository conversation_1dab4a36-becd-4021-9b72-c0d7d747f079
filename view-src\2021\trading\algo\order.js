const IView = require('../../../../component/iview').IView;
const SmartTable = require('../../../../libs/table/smart-table').SmartTable;
const { ColumnCommonFunc } = require('../../../../libs/table/column-common-func');
const { AlgoOrder } = require('../../../../model/algo-order');
const repoAlgo = require('../../../../repository/algorithm');

class View extends IView {

    constructor(view_name, is_standalone_window) {

        super(view_name, is_standalone_window, '算法订单');

        this.states = {

            /** 关键字 */
            keywords: null,
        };

        var paging = this.systemSetting.tablePagination;
        this.paging = {

            pageSizes: paging.pageSizes,
            pageSize: paging.pageSize,
            layout: paging.layout,
            total: 0,
            page: 0,
        };

        /** 本地设置 */
        this.settings = {

            /** 任务状态更新频率 */
            frequency: 1000 * 40,
        };

        /**
         * 算法母单订单状态
         */
        this.mostatus = { running: 0, done: 1, canceled: 2 };
    }

    /**
     * 当前算法单中正在进行的母单
     */
    get runnings() {

        let records = this.typeRecords(this.tableObj.extractAllRecords());
        let runnings = records.filter(x => x.algorithmStatus == this.mostatus.running);
        return runnings;
    }

    /**
     * @param {AlgoOrder} record
     */
    identifyRecord(record) {
        return record.id;
    }

    createToolbarApp() {

        new Vue({

            el: this.$container.querySelector('.user-toolbar'),
            data: {
                states: this.states,
            },
            methods: this.helper.fakeVueInsMethod(this, [

                this.filterOrders,
                this.hope2CancelCheckeds,
                this.hope2CancelAll,
                this.hope2Replace,
                this.refresh,
            ]),
        });
    }

    createFooterRowApp() {
        
        new Vue({

            el: this.$container.querySelector('.user-footer'),
            data: {
                paging: this.paging,
            },

            methods: this.helper.fakeVueInsMethod(this, [

                this.handlePageSizeChange,
                this.handlePageChange,
            ]),
        });
    }

    createTable() {

        this.helper.extend(this, ColumnCommonFunc);
        var $table = this.$container.querySelector('.data-list');
        var tableObj = new SmartTable($table, this.identifyRecord, this, {

            tableName: 'smt-tao',
            displayName: this.title,
            defaultSorting: { prop: 'id', direction: 'desc' },
            recordsFiltered: this.handleTableFiltered.bind(this),
            rowSelected: this.handleRowSelected.bind(this),
        });

        tableObj.setPageSize(this.paging.pageSize);
        tableObj.setMaxHeight(333);
        this.tableObj = tableObj;
    }

    handlePageChange() {
        this.tableObj.setPageIndex(this.paging.page);
    }

    handlePageSizeChange() {

        this.tableObj.setPageSize(this.paging.pageSize, true);
        this.tableObj.setPageIndex(1);
    }

    handleTableFiltered(total) {
        this.paging.total = total;
    }

    /**
     * @param {AlgoOrder} order 
     */
    handleRowSelected(order) {
        this.trigger('algo-order-selected', order);
    }

    rebindDirection(records, propName) {
        return SmartTable.MakeColFilters(records, propName, { translators: this.systemTrdEnum.tradingDirection });
    }

    /**
     * @param {AlgoOrder} record
     */
    formatStatus(record) {

        var status = this.mostatus;
        switch (record.algorithmStatus) {

            case status.running: return '进行中';
            case status.done: return '已完成';
            case status.canceled: return '已撤销';
            default: return record.algorithmStatus;
        }
    }

    /**
     * @param {AlgoOrder} record
     */
    formatActions(record) {
        return record.isCompleted ? '' : '<button class="danger" event.onclick="cancelSingle">撤销</button>';
    }

    filterOrders() {

        var tableObj = this.tableObj;
        var keywords = this.states.keywords;

        /**
         * @param {AlgoOrder} record 
         */
        function testRecords(record) {
            return tableObj.matchKeywords(record);
        }

        tableObj.setPageIndex(1, false);
        tableObj.setKeywords(keywords, false);
        tableObj.customFilter(testRecords);
    }

    /**
     * @param {Boolean} isAll
     * @returns {Array<AlgoOrder>}
     */
    extractCheckeds(isAll) {
        return isAll ? this.tableObj.extractAllRecords() : this.tableObj.extractCheckedRecords();
    }

    hope2CancelCheckeds() {

        if (this.tableObj.rowCount == 0) {
            return this.interaction.showMessage('当前无订单');
        }

        var checkeds = this.extractCheckeds();
        if (checkeds.length == 0) {
            return this.interaction.showMessage('请选择要撤销的订单');
        }

        var progressings = checkeds.filter(item => !item.isCompleted);
        if (progressings.length == 0) {
            return this.interaction.showError(`勾选订单 = ${checkeds.length}，可撤销订单 = 0`);
        }

        this.interaction.showConfirm({

            title: '撤单确认',
            message: `勾选订单 = ${checkeds.length}，可撤销订单 = ${progressings.length}，是否确定？`,
            confirmed: () => {
                this.cancel(progressings);
            },
        });
    }

    /**
     * 单一撤销
     * @param {AlgoOrder} order 
     */
    cancelSingle(order) {

        this.interaction.showConfirm({

            title: '撤单确认',
            message: `是否撤销该算法单？`,
            confirmed: () => {
                this.cancel([order]);
            },
        });
    }

    /**
     * 撤销
     * @param {Array<AlgoOrder>} orders 
     */
    async cancel(orders) {

        var ids = orders.map(x => x.id);
        var resp = await repoAlgo.cancelOrder(ids);
        if (resp.errorCode != 0) {
            return this.interaction.showError(`撤单请求处理错误：${resp.errorCode}/${resp.errorMsg}`);
        }

        setTimeout(() => { this.requestAlgoOrders(); }, 1000 * 2);
        this.interaction.showSuccess(`撤销请求已发出，数量 = ${orders.length}`);
    }

    hope2CancelAll() {

        if (this.tableObj.rowCount == 0) {

            this.interaction.showMessage('当前无订单');
            return;
        }

        var all = this.extractCheckeds(true);
        var progressings = all.filter(item => !item.isCompleted);
        if (progressings.length == 0) {

            this.interaction.showError(`所有订单 = ${all.length}，可撤销订单 = 0`);
            return;
        }

        this.interaction.showConfirm({

            title: '撤单确认',
            message: `所有订单 = ${all.length}，可撤销订单 = ${progressings.length}，是否确定？`,
            confirmed: () => {
                this.cancel(progressings);
            },
        });
    }

    /**
     * @param {Array<AlgoOrder>} records
     * @returns {Array<AlgoOrder>}
     */
    typeRecords(records) {
        return records;
    }

    hope2Replace() {
        
        if (this.tableObj.rowCount == 0) {
            return this.interaction.showMessage('当前无订单');
        }

        if (this.tableObj.filteredRowCount == 0) {
            return this.interaction.showMessage('筛选结果无订单');
        }

        var checkeds = this.typeRecords(this.tableObj.extractCheckedRecords());
        if (checkeds.length == 0) {
            return this.interaction.showMessage('请选择要追单的订单');
        }

        var cancellables = checkeds.filter(item => !item.isCompleted);
        if (cancellables.length == 0) {
            return this.interaction.showError(`勾选订单 = ${checkeds.length}，可（撤）追单数 = 0`);
        }

        this.replaceTask(cancellables);
    }

    /**
     * 追单
     * @param {Array<AlgoOrder>} orders
     */
    replaceTask(orders) {
        
        if (this.dialogReplacing === undefined) {
            
            var DialogReplacing = require('./replacing');
            var dialog = new DialogReplacing('@2021/trading/algo/replacing');
            dialog.loadBuild(this.$container.firstElementChild, null, _=> { dialog.trigger('showup', orders); });
            this.dialogReplacing = dialog;
        }
        else {
            this.dialogReplacing.trigger('showup', orders);
        }
    }

    refresh() {
        
        this.interaction.showSuccess('刷新请求已发出');
        this.requestAlgoOrders();
    }

    async requestAlgoOrders(isRefresh) {

        var resp = await repoAlgo.getOrderList();
        if (resp.errorCode != 0) {
            return this.interaction.showError(`算法单加载失败：${resp.errorCode}/${resp.errorMsg}`);
        }

        var records = resp.data;
        var tasks = records instanceof Array ? records.map(item => new AlgoOrder(item)) : [];
        
        if (isRefresh) {
            tasks.forEach(item => this.tableObj.putRow(item));
        }
        else {
            this.tableObj.refill(tasks);
        }
    }

    listen2Events() {
        this.registerEvent('reload-algo-orders', () => { this.requestAlgoOrders(); });
    }

    keepRefreshed() {

        this.refreshJob = setInterval(async () => {
            
            if (this._isJobRunning) {
                return;
            }

            this._isJobRunning = true;
            await this.requestAlgoOrders(true);
            this._isJobRunning = false;

        }, this.settings.frequency);
    }

    build($container) {
        
        super.build($container);
        this.createToolbarApp();
        this.createFooterRowApp();
        this.createTable();
        this.requestAlgoOrders();
        this.listen2Events();
        this.keepRefreshed();
    }
}

module.exports = View;