<div class="dialog-replace-tasks">

	<el-dialog width="780px"
			   :title="states.title"
			   :visible="states.visible"
			   :close-on-click-modal="false"
			   :show-close="false">

		<div class="themed-box s-pdt-5 s-pdb-5">

			<label class="themed-color s-pdr-5">跟盘价</label>

			<el-select placeholder="请选择跟盘价"
						v-model="states.stage"
						@change="handleStageChange"
						class="s-w-120 s-pdr-5">

				<el-option v-for="(item, item_idx) in stages"
							:key="item_idx"
							:label="item.mean"
							:value="item.code"></el-option>
			</el-select>

			<label class="themed-color s-pdr-5">偏移量</label>

			<el-input-number placeholder="偏移"
							 class="s-w-150"
							 v-model="states.offset"
							 :min="-999999999" 
							 :max="999999999"
							 :step="0.01"
							 @change="handleOffsetChange"></el-input-number>

			<div class="s-pull-right">
				<el-input placeholder="输入关键字搜索"
						  class="s-w-120"
						  prefix-icon="el-icon-search"
						  v-model="states.keywords"
						  @change="filterRecords" clearable></el-input>
			</div>

		</div>

		<div class="data-list">

			<table>
				<tr>

					<th label="篮子名称" 
						min-width="120" 
						prop="basketName" filterable sortable searchable overflowt></th>

					<th label="篮子代码" 
						min-width="100" 
						prop="basketId" filterable sortable searchable overflowt></th>

					<th label="任务ID" 
						min-width="100" 
						prop="taskId" sortable searchable overflowt></th>

					<th label="合约代码"
						min-width="80"
						prop="instrument" sortable searchable overflowt></th>
	
					<th label="合约名称"
						min-width="80"
						prop="instrumentName" sortable searchable overflowt></th>
						
					<th label="创建时间" 
						min-width="100" 
						prop="createTime"
						formatter="formatTime" sortable></th>

					<th label="方向" 
						min-width="70" 
						prop="taskType"
						formatter="formatDirection" 
						export-formatter="formatDirectionText" 
						filter-data-provider="rebindDirection" sortable></th>

					<th label="跟盘价"
						fixed-width="80"
						prop="stageName"></th>

					<th label="进度" 
						min-width="80" 
						prop="completionRate"
						align="right" percentage by100 sortable></th>

					<th label="下单方式" 
						min-width="80" 
						prop="executeType"
						formatter="formatMethod"
						align="right" sortable></th>

					<th label="下单规模" 
						min-width="80" 
						prop="executeVolume"
						formatter="formatScale"
						align="right" sortable></th>

					<th label="目标量" 
						min-width="80" 
						prop="targetVolume"
						align="right" sortable thousands-int></th>

					<th label="成交量" 
						min-width="80" 
						prop="tradedVolume" 
						align="right" sortable thousands-int></th>

					<th label="剩余量" 
						min-width="80" 
						prop="leftVolume" 
						align="right" sortable thousands-int></th>

					<th label="目标额" 
						min-width="80" 
						prop="targetMoney"
						align="right" sortable thousands-int></th>

					<th label="成交额" 
						min-width="80" 
						prop="tradedMoney" 
						align="right" sortable thousands-int></th>

					<th label="剩余额" 
						min-width="80" 
						prop="leftMoney" 
						align="right" sortable thousands-int></th>

				</tr>

			</table>

		</div>

		<div class="user-footer themed-box">
			<el-pagination class="s-pull-right"
							:page-sizes="paging.pageSizes"
							:page-size.sync="paging.pageSize"
							:total="paging.total"
							:current-page.sync="paging.page"
							:layout="paging.layout"
							@size-change="handlePageSizeChange"
							@current-change="handlePageChange"></el-pagination>
		</div>

		<span slot="footer" class="dialog-footer">
			<el-button type="primary" @click="confirm">确定</el-button>
			<el-button type="default" @click="cancel">取消</el-button>
		</span>

	</el-dialog>

</div>