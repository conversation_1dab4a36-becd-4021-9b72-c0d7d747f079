<div class="trade-form algform s-full-height">
	<div class="trade-form-inner algform-internal themed-box s-scroll-bar s-full-height">
		
		<div class="xtcontainer s-border-box s-full-height">

			<template>
				<div class="xtheader themed-header">

					<span>算法交易</span>

					<!-- <el-popover placement="top-start"
								title="导入一篮子算法单（文件请保存为UTF8格式）"
								trigger="hover">
						<span class="import-algo-orders-sample-pic"></span>
						<i slot="reference" class="s-pull-right s-mgl-10 s-mgt-5 el-icon-question"></i>

					</el-popover>

					<el-button class="s-pull-right" type="primary" @click="hope2Import">
						<i class="iconfont icon-daoru1"></i> 导入算法篮子</el-button> -->

					<template v-if="warnings.length > 0">

						<el-popover placement="top-start" title="交易风险提示" width="500px" trigger="hover">

							<div class="warning-msgs">
								<div v-for="(msg, idx) in warnings" :key="idx" class="msg-item themed-bottom-border">
									<span class="time">{{ msg.createTime }}</span>
									<el-button type="primary" size="mini" class="s-mgl-10" @click="read(msg)">已知晓</el-button>
									<br>
									<span class="content s-ellipsis">{{ msg.content }}</span>
								</div>
							</div>
	
							<el-button slot="reference" type="info" class="s-pull-right s-mgr-10">
								<i class="el-icon-info s-color-red"></i>
								交易风险提示
							</el-button>
	
						</el-popover>

					</template>

				</div>
			</template>

			<template>

				<div class="form-external s-unselectable">
	
					<form class="xtform">

						<div class="xtinput">
							<span class="xtlabel themed-color">账号</span>
							<el-select v-model="istates.accountId" @change="handleAccountChange" filterable clearable>
								<el-option v-for="(item, item_idx) in accounts"
										   :key="item_idx"
										   :value="item.value"
										   :label="item.label"></el-option>
							</el-select>
						</div>

						<div v-if="isCreditAccount()" class="xtinput">
							<span class="xtlabel themed-color">优先融资</span>
							<el-radio v-model="uistates.isCreditFirst" :label="true" style="position: relative; top: -8px;">是</el-radio>
							<el-radio v-model="uistates.isCreditFirst" :label="false" style="position: relative; top: -8px;">否</el-radio>
						</div>

						<div class="xtinput">
							<span class="xtlabel themed-color">算法类型</span>
							<el-select v-model="uistates.algoId" @change="handleAlgoChange">

								<el-option-group v-for="(group, group_idx) in algoGrps" :key="group_idx" :label="group.name">
									<template v-for="(item, item_idx) in group.algoes">
										<el-option :key="item_idx" :label="item.name" :value="item.id"></el-option>
									</template>
								</el-option-group>

							</el-select>
						</div>

						<div class="xtinput">
							<span class="xtlabel themed-color">合约</span>
							<el-autocomplete v-model="uistates.keywords"
											 :fetch-suggestions="suggest"
											 @keydown.native="handleUserInput"
											 @clear="handleClearIns"
											 @select="handleSelect" clearable>
	
								<template slot-scope="{ item: ins }">
									<span class="item-code">[{{ ins.instrument }}] </span>
									<span class="item-name">{{ ins.instrumentName }}</span>
								</template>
								
							</el-autocomplete>
						</div>

						<div class="xtinput">
							<span class="xtlabel themed-color">数量（股）</span>
							<el-input-number placeholder="0" v-model="uistates.volume" :min="0" :max="999999999" :step="uistates.step"></el-input-number>
						</div>

						<div v-if="isAlgoType1()" class="xtinput">
							<span class="xtlabel themed-color">封单量（手）</span>
							<el-input-number v-model="uistates.strategyVolume" :min="0" :max="999999999" :step="1" :controls="false"></el-input-number>
						</div>

						<div v-if="isAlgoType2()" class="xtinput">
							<span class="xtlabel themed-color">总卖单金额（万）</span>
							<el-input-number v-model="uistates.strategyRate" :min="0" :max="999999999" :step="1" :controls="false"></el-input-number>
						</div>
	
						<div class="xtinput button-row basket-button-row">
							<el-button type="danger" @click="hope2Entrust" style="width: 98%;">买入</el-button>
						</div>
	
					</form>

				</div>

			</template>
		</div>
		
		<div class="dialog-param">

			<el-dialog width="300px"
					:title="dialog.title"
					:visible="dialog.visible"
					:close-on-click-modal="false"
					:show-close="false">

				<template>

					<div class="themed-box s-pdt-5 s-pdb-5">
						<div v-for="(item, item_idx) in algoParams" :key="item_idx">
							<span class="xtlabel themed-color">{{ item.property }}</span>
							<el-input v-model="item.value" clearable></el-input>
						</div>
					</div>
	
					<span slot="footer" class="dialog-footer">
						<el-button type="primary" @click="keepParam">设置</el-button>
						<el-button type="default" @click="unkeepParam">不设置</el-button>
					</span>

				</template>

			</el-dialog>
			
		</div>
		
		<div class="dialog-import">

			<el-dialog width="990px"
					:title="dialog.title"
					:visible="dialog.visible"
					:close-on-click-modal="false"
					:show-close="false">

				<template>

					<div class="data-list">
						<table>
							<tr>

								<th label="所属账号" 
									min-width="100" 
									prop="accountName" overflowt sortable searchable></th>

								<th label="算法类型" 
									min-width="70" 
									prop="algoName" overflowt sortable searchable></th>

								<th label="证券代码" 
									min-width="100" 
									prop="instrument" overflowt sortable searchable></th>
				
								<th label="证券名称" 
									min-width="80" 
									prop="instrumentName" overflowt sortable searchable></th>
							
								<th label="方向" 
									min-width="50"
									prop="directionName" sortable></th>				
				
								<th label="数量" 
									min-width="60" 
									prop="volume" 
									align="right" sortable thousands-int></th>
				
								<th label="封单量" 
									min-width="60" 
									prop="strategyVolume" 
									align="right" sortable thousands-int></th>
				
								<th label="总卖单金额" 
									min-width="60" 
									prop="strategyRate" 
									align="right" sortable thousands-int></th>
									
							</tr>
						</table>
					</div>
	
					<span slot="footer" class="dialog-footer">
						<el-button type="primary" @click="confirm">下单</el-button>
						<el-button type="default" @click="giveup">放弃</el-button>
					</span>

				</template>

			</el-dialog>

		</div>

	</div>
</div>