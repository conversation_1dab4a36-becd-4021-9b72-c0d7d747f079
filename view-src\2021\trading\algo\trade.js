const { IdentityRelationView } = require('../../classcial/identity-relation-view');
const { CodeMeanItem, AlgoOrderInfo } = require('../../model/message');
const { WarningMsg } = require('../../../../model/warning');
const repoAlgo = require('../../../../repository/algorithm');
const { BizHelper } = require('../../../../libs/helper-biz');
const { SmartTable } = require('../../../../libs/table/smart-table');

const AlgoTypes = {

    type1: 1,
    type2: 2,
};

class AlgoGroup {

    constructor(name, members) {
        
        this.name = name;
        this.algoes = [{ id: 0, name: null, type: 0, paramObj: {} }];
        this.algoes.pop();

        if (members instanceof Array) {
            this.algoes.refill(members);
        }
    }
}

class SequencialAlgoOrder extends AlgoOrderInfo {
    
    constructor(sequence, struc) {

        super(struc);
        this.sequence = sequence;
    }
}

/**
 * @returns {Array<AlgoGroup}
 */
function allocateAlgoGrps() {
    return [];
}

class View extends IdentityRelationView {

    constructor(view_name) {

        super(view_name, false, '算法交易');
        var dirs = this.systemTrdEnum.tradingDirection;

        /**
         * 委托方向
         */
        this.directions = {

            buy: new CodeMeanItem(dirs.buy.code, dirs.buy.mean),
            sell: new CodeMeanItem(dirs.sell.code, dirs.sell.mean),
        };

        /**
         * 算法分组，打平后的算法列表
         */
        this.algoes = [{ id: 0, name: null, type: 0, paramObj: {} }];
        this.algoes.pop();
        this.algoGrps = allocateAlgoGrps();

        this.uistates = {
            
            algoId: null,
            algoType: null,
            keywords: null,
            instrument: null,
            instrumentName: null,
            strategyVolume: 0,
            strategyRate: 0,
            volume: 0,
            step: 100,
            isCreditFirst: false,
        };

        this.algoParams = [{ property: null, value: null }];
        this.algoParams.pop();
    }

    isCreditAccount() {
        
        var selected_acnt = this.accounts.find(x => x.value == this.accountId);
        return selected_acnt == undefined ? false : selected_acnt.isCredit;
    }

    isAlgoType1() {
        return this.uistates.algoType == AlgoTypes.type1;
    }

    isAlgoType2() {
        return this.uistates.algoType == AlgoTypes.type2;
    }

    createApp() {

        this.warnings = [new WarningMsg({})];
        this.warnings.pop();

        this.vueIns = new Vue({

            el: this.$container.querySelector('.trade-form-inner > .xtcontainer'),

            data: {

                accounts: this.accounts,
                algoGrps: this.algoGrps,
                uistates: this.uistates,
                istates: this.istates,
                warnings: this.warnings,
            },

            methods: this.helper.fakeVueInsMethod(this, [

                this.hope2Import,
                this.handleAccountChange,
                this.handleAlgoChange,
                this.suggest,
                this.handleUserInput, 
                this.handleSelect,
                this.handleClearIns,
                this.hope2Entrust,
                this.read,
                this.isCreditAccount,
                this.isAlgoType1,
                this.isAlgoType2,
            ]),
        });
    }

    handleAccountChange() {

        super.handleAccountChange();
        this.uistates.isCreditFirst = false;
    }

    handleAlgoChange() {

        this.algoParams.clear();
        this.uistates.algoType = null;

        var matched = this.algoes.find(x => x.id == this.uistates.algoId);
        if (matched == undefined) {
            return;
        }

        for (let property in matched.paramObj) {
            this.algoParams.push({ property, value: null });
        }

        this.uistates.algoType = matched.type;
    }

    handleSelect(selected) {

        var { instrument, instrumentName } = selected;
        var uistates = this.uistates;
        uistates.keywords = `${instrumentName}-${instrument}`;
        uistates.volume = 0;
        uistates.strategyVolume = 0;
        uistates.strategyRate = 0;
        uistates.instrument = instrument;
        uistates.instrumentName = instrumentName;
    }

    /**
     * @param {String} keywords 
     * @param {Function} callback 
     */
    suggest(keywords, callback) {

        if (typeof keywords != 'string' || keywords.trim().length < 1) {

            callback([]);
            return;
        }

        let matches = BizHelper.filterInstruments(this.systemEnum.assetsType.stock.code, keywords);
        if (matches.length == 1) {

            callback([]);
            this.handleSelect(matches[0]);
            return;
        }

        callback(matches);
    }

    handleUserInput() {

        if (event.keyCode == 8) {

            event.returnValue = false;
            this.uistates.keywords = null;
            this.handleClearIns();
        }
        else if (typeof this.uistates.keywords == 'string' && this.uistates.keywords.trim().length == 0) {
            this.handleClearIns();
        }
    }

    handleClearIns() {

        var uistates = this.uistates;
        uistates.keywords = null;
        uistates.instrument = 0;
        uistates.instrumentName = 0;
        uistates.volume = 0;
        uistates.strategyVolume = 0;
        uistates.strategyRate = 0;
    }

    createParamDialog() {

        this.dialogParam = new Vue({

            el: this.$container.querySelector('.trade-form-inner > .dialog-param'),
            data: {

                dialog: this.dialog1,
                algoParams: this.algoParams,
            },

            methods: {

                keepParam: () => {

                    this.dialog1.visible = false;
                    var isChanged = this.algoParams.some(x => typeof x.value == 'string' && x.value.length > 0);
                },

                unkeepParam: () => {

                    this.dialog1.visible = false;
                    this.algoParams.forEach(x => { x.value = null; });
                },
            }
        });
    }

    hope2Import() {

        const { dialog } = require('@electron/remote');
        const paths = dialog.showOpenDialogSync(this.thisWindow, {
            
            title: '导入算法篮子文件',
            filters: [{ name: 'xlsx文件', extensions: ['xlsx', 'xls', 'csv'] }],

        });
        this.handleUploadDialog(paths);
    }
    
    handleUploadDialog(paths) {

        if (!paths) {
            return;
        }

        const fs = require('fs');
        const xlsx = require('node-xlsx');
        var file = fs.readFileSync(paths[0]);
        var sheets = xlsx.parse(file);
        var records = sheets[0].data;
        
        if (!Array.isArray(records) || records.length <= 1) {
            return this.interaction.showAlert('导入文件无数据行（第一行，将视为标题行）');
        }

        var headers = ['产品ID', '策略ID', '账号ID', '算法ID', '证券代码', '证券名称', '方向ID', '数量', '开始时间', '结束时间', '参数', '备注'];
        var uheaders = records.shift();
        
        if (uheaders.length < headers.length) {
            return this.interaction.showAlert('内容标题列数，小于，预定义标题列数（请核对标题栏）');
        }
        
        if (uheaders.length > headers.length) {
            return this.interaction.showAlert('内容标题列数，大于，预定义标题列数（请核对标题栏）');
        }

        var intersecs = uheaders.slice(0, headers.length);
        if (intersecs.join() != headers.join()) {
            
            for (let idx = 0; idx < headers.length; idx++) {

                let text = headers[idx];
                let utext = uheaders[idx];
                if (typeof utext == 'string' && utext.trim() == text) {
                    continue;
                }

                return this.interaction.showAlert(`第${String.fromCharCode(65 + idx)}列，预期标题 = [${text}]，实际标题 = [${utext}]`);
            }
        }

        var dirs = this.directions;
        var timeReg = /^([01]\d|2[0-3])[0-5]\d[0-5]\d$/;

        /**
         * 除最后（连续的）非必要列，之前必须出现的数据列数
         */
        var mustCols = headers.length - 2;
        var oks = [];
        var errors = [];
        
        records.forEach((eles, idx) => {

            let rowNo = idx + 2;

            if (!Array.isArray(eles)) {

                errors.push({ line: rowNo, message: '未包含数据' });
                return;
            }
            else if (eles.length < mustCols) {
                
                errors.push({ line: rowNo, message: '必要性数据未充足' });
                return;
            }

            let productId = eles[0];
            let strategyId = eles[1];
            let accountId = eles[2];
            let algoId = eles[3];
            let code = eles[4];
            let name = eles[5];
            let direction = eles[6];
            let volume = eles[7];
            let tstart = eles[8];
            let tend = eles[9];
            let algoParam = eles[10];
            let remark = eles[11];

            let isOk = typeof productId == 'number' || typeof productId == 'string' && productId.trim().length > 0;
            if (!isOk) {

                errors.push({ line: rowNo, message: '产品ID缺失' });
                return;
            }

            isOk = typeof accountId == 'number' || typeof accountId == 'string' && accountId.trim().length > 0;
            if (!isOk) {

                errors.push({ line: rowNo, message: '账号ID缺失' });
                return;
            }

            isOk = typeof algoId == 'number' || typeof algoId == 'string' && algoId.trim().length > 0;
            if (!isOk) {

                errors.push({ line: rowNo, message: '算法ID缺失' });
                return;
            }
            
            isOk = typeof code == 'number' || typeof code == 'string' && code.trim().length > 0;
            if (!isOk) {

                errors.push({ line: rowNo, message: '证券代码缺失' });
                return;
            }

            let matches = BizHelper.filterInstruments(this.systemEnum.assetsType.stock.code, code, true);
            let matches2 = BizHelper.filterInstruments(this.systemEnum.assetsType.stock.code, code);

            isOk = matches.length == 1 || matches2.length == 1;
            if (!isOk) {

                errors.push({ line: rowNo, message: `证券代码[ ${code} ]无对应合约信息` });
                return;
            }

            isOk = direction == dirs.buy.code || direction == dirs.sell.code;
            if (!isOk) {

                errors.push({ line: rowNo, message: `方向[ ${direction} ]不能识别` });
                return;
            }

            isOk = typeof volume == 'number' && Number.isInteger(volume) && volume > 0;
            if (!isOk) {

                errors.push({ line: rowNo, message: `数量[ ${volume} ]无效` });
                return;
            }

            isOk = (typeof tstart == 'number' || typeof tstart == 'string') && timeReg.test(tstart.toString().trim());
            if (!isOk) {

                errors.push({ line: rowNo, message: `开始时间[ ${tstart} ]无效` });
                return;
            }

            isOk = (typeof tend == 'number' || typeof tend == 'string') && timeReg.test(tend.toString().trim());
            if (!isOk) {

                errors.push({ line: rowNo, message: `结束时间[ ${tend} ]无效` });
                return;
            }
            else if (tstart >= tend) {
                
                errors.push({ line: rowNo, message: `开始时间 ${tstart} >= 结束时间 ${tend}` });
                return;
            }
            
            let mright = matches.length == 1 ? matches : matches2;
            let stockCode = mright[0].instrument;
            let stockName = mright[0].instrumentName;

            oks.push({ productId, strategyId, accountId, algoId, stockCode, stockName, direction, volume, tstart, tend, algoParam, remark });
        });

        var errmsgs = '';
        if (errors.length > 0) {
            errmsgs = (errors.length > 20 ? '（前20条错误信息）<br>' : '') + errors.slice(0, 20).map(item => `行${item.line}：${item.message}`).join('<br/>');
        }

        if (oks.length == 0) {
            return this.interaction.showAlert(`导入文件，未包含有效数据：<br/><br/>${errmsgs}`);
        }

        /**
         * 当次导入算法篮子，数据预览
         */
        var previews = this.previews = this.typedsImports(oks);
        var render = (records) => {

            this.tablePreview.refill(records);
            
            /**
             * 部分导入成功，提示信息后置，防止显示层级被遮挡
             */

            if (errors.length > 0) {

                this.interaction.showAlert(`全部行数：${records.length}，
                                            导入行数：${oks.length}，
                                            错误行数：${errors.length}：<br/><br/>${errmsgs}`);
            }
        }

        if (this.isImportDialogCreated === undefined) {

            this.isImportDialogCreated = true;
            this.dialog2 = { title: '算法篮子下单', visible: true };
            this.createImportDialog(() => { render(previews); });
        }
        else {

            this.dialog2.visible = true;
            this.dialogImport.$nextTick(() => { render(previews); });
        }
    }

    /**
     * @param {Array<Array>} records 
     */
    typedsImports(records) {

        if (this.isNameMapCreated === undefined) {

            this.isNameMapCreated = true;
            /** 算法索引 */
            this.almap = {};
            this.algoes.forEach(item => { this.almap[item.id] = item.name; });
        }

        var dirs = this.directions;
        return records.map((eles, idx) => {

            let dir = eles.direction;
            let pid = eles.productId;
            let sid = eles.strategyId;
            let aid = eles.accountId;
            let alid = eles.algoId;
            let tstart = eles.tstart.toString();
            let tend = eles.tend.toString();
            
            return new SequencialAlgoOrder(idx, {
            
                direction: dir,
                directionName: dir == dirs.buy.code ? dirs.buy.mean : dirs.sell.mean,
                
                productId: pid,
                productName: this.getProductName(),
                strategyId: sid,
                strategyName: this.getStrategyName(),
                accountId: aid,
                accountName: this.getAccountName(),
                algoId: alid,
                algoName: this.almap[alid],

                instrument: eles.stockCode,
                instrumentName: eles.stockName,
                volume: eles.volume,
                startTime: `${tstart.substr(0, 2)}:${tstart.substr(2, 2)}:${tstart.substr(4, 2)}`,
                endTime: `${tend.substr(0, 2)}:${tend.substr(2, 2)}:${tend.substr(4, 2)}`,
                algoParam: eles.algoParam,
                remark: eles.remark,
            });
        });
    }

    /**
     * @param {SequencialAlgoOrder} record 
     * @returns 
     */
    identifyRecord(record) {
        return record.sequence;
    }

    createImportDialog(callback) {

        this.dialogImport = new Vue({

            el: this.$container.querySelector('.trade-form-inner > .dialog-import'),
            data: {
                dialog: this.dialog2,
            },
            methods: {

                confirm: () => {

                    this.tablePreview.clear();
                    this.dialog2.visible = false;
                    let copied = this.previews.slice(0);
                    this.trigger('place-algo-orders', copied);
                    this.previews.clear();
                },

                giveup: () => {

                    this.tablePreview.clear();
                    this.dialog2.visible = false;
                    this.previews.clear();
                },
            }
        });

        this.dialogImport.$nextTick(() => {

            if (this.tablePreview === undefined) {

                var $table = this.dialogImport.$el.querySelector('.data-list');
                this.tablePreview = new SmartTable($table, this.identifyRecord, this, {
    
                    tableName: 'smt-tat',
                    displayName: this.title,
                    defaultSorting: { prop: 'sequence', direction: 'ASC' },
                });
        
                this.tablePreview.setMaxHeight(300);
                this.tablePreview.setPageSize(99999);
            }

            callback();
        });
    }

    hope2Entrust() {

        var isOk = this.areParamsOk();
        if (typeof isOk == 'string') {
            return this.interaction.showError(isOk);
        }

        var acnt = this.accounts.find(x => x.value == this.accountId);
        var isCreditFirst = this.uistates.isCreditFirst && acnt && acnt.isCredit;
        this.trigger('place-algo-orders', this.formParams(), isCreditFirst);
    }

    formParams() {

        var states = this.uistates;
        var dirs = this.directions;
        var userAlgoParam = {

            strategyVolume: 0,
            strategyRate: 0,
        };
        
        this.algoParams.forEach(item => { userAlgoParam[item.property] = item.value; });

        if (this.isAlgoType1()) {
            userAlgoParam.strategyVolume = states.strategyVolume;
        }

        if (this.isAlgoType2()) {
            userAlgoParam.strategyRate = states.strategyRate;
        }

        return new AlgoOrderInfo({

            direction: dirs.buy.code,
            directionName: dirs.buy.mean,
            productId: null,
            productName: this.getProductName(),
            strategyId: null,
            strategyName: this.getStrategyName(),
            identityId: (this.accounts.find(x => x.value == this.accountId) || {}).identityId,
            accountId: this.accountId,
            accountName: this.getAccountName(),
            algoType: states.algoType,
            algoId: states.algoId,
            algoName: this.algoes.find(x => x.id == states.algoId).name,
            volume: states.volume,
            instrument: states.instrument,
            instrumentName: states.instrumentName,
            startTime: null,
            endTime: null,
            algoParam: JSON.stringify(userAlgoParam),
            remark: null,
        });
    }

    areParamsOk() {

        var uistates = this.uistates;

        if (this.helper.isNone(this.istates.productId)) {
            return '产品未选择';
        }
        else if (this.helper.isNone(this.istates.accountId)) {
            return '账号未选择';
        }
        else if (this.algoGrps.length == 0) {
            return '算法列表未加载';
        }
        else if (this.helper.isNone(this.uistates.algoId)) {
            return '算法未选择';
        }
        else if (!uistates.instrument) {
            return '目标合约未输入';
        }
        else if (typeof uistates.volume != 'number' || uistates.volume <= 0) {
            return '委托数量无效';
        }
        else if (uistates.volume % uistates.step != 0) {
            return `委托数量 ${uistates.volume} 非最小步长 ${uistates.step} 的整数倍`;
        }
        else if (this.isAlgoType1() && (typeof uistates.strategyVolume != 'number' || uistates.strategyVolume <= 0)) {
            return '封单量无效';
        }
        else if (this.isAlgoType2() && (typeof uistates.strategyRate != 'number' || uistates.strategyRate <= 0)) {
            return '总卖单金额无效';
        }

        return true;
    }

    async requestAlgoes() {

        var resp = await repoAlgo.queryAlgoes();
        if (resp.errorCode != 0) {
            return this.interaction.showError(`算法加载失败：${resp.errorCode}/${resp.errorMsg}`);
        }

        var algoes = resp.data || [];
        var map = algoes.groupBy(x => x.supplierName);
        var flattends = [];
        this.algoGrps.clear();
        
        for (let supplier_name in map) {

            let subset = map[supplier_name];
            let members = subset.map(x => formAlgo(x));
            this.algoGrps.push(new AlgoGroup(supplier_name, members));
            flattends.merge(members);
        }

        this.algoes.refill(flattends);

        function formAlgo(struc) {

            var paramStr = struc.param;
            var paramObj = {};

            try {
                
                if (typeof paramStr == 'string') {
                    paramObj = JSON.parse(paramStr);
                }
            }
            catch(ex) {
                console.error('cannot parse algo param: ' + paramStr);
            }

            return {

                id: struc.id, 
                name: struc.algorithmName,
                type: struc.algorithmType,
                paramObj: paramObj,
            };
        }
    }

    /**
     * @param {WarningMsg} item 
     */
    read(item) {
        this.warnings.remove(x => x === item);
    }

    handleRiskMsg(event, msg) {
        this.warnings.push(new WarningMsg(msg));
    }

    build($container) {

        super.build($container);
        this.createApp();
        this.requestAlgoes();
        this.renderProcess.on(this.serverEvent.riskAlertReceived, this.handleRiskMsg.bind(this));
    }
}

module.exports = View;