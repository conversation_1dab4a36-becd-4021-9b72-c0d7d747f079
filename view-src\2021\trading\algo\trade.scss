.algform {

    padding-right: 2px;

    .xtform .xtinput .xtlabel {
        width: 33%;
    }

    .xtform .xtinput > .el-input,
    .xtform .xtinput > .el-autocomplete,
    .xtform .xtinput > .el-input-number,
    .xtform .xtinput > .el-select,
    .xtform .xtinput > .el-date-editor,
    .xtform .xtinput > .el-range-editor {
        width: 65%;
    }

    .xtform .xtinput > .el-date-editor,
    .xtform .xtinput > .el-range-editor {
        
        position: relative;
        top: -5px;
    }

    .xtinput {

        &.shorten {

            .el-input-number {
                width: 68%;
            }
        }

        .xtlabel {
            text-align: left;
        }

        .el-checkbox {
            margin-right: 20px;
        }

        &.basket-button-row {
            
            margin-top: 20px !important;

            button {

                margin-left: 0 !important;
                margin-right: 10px !important;
                width: 45%;
            }
        }
    }

    .changed-icon {

        font-size: 30px;
        position: absolute;
        margin-top: -2px;
    }
}

.algform-internal {

    overflow: hidden;

    .form-external {
        padding: 10px;
    }

    .limit-btn,
    .unit-txt {

        position: absolute;
        right: 5px;
        z-index: 1;
        width: 24px;
        line-height: 24px;
        text-align: center;
        border-radius: 2px;
    }
}

.warning-msgs {

    max-width: 800px;
    max-height: 350px;
    overflow-y: auto;
    padding: 10px;

    .msg-item {

        margin-top: 5px;

        .time {
            line-height: 24px;
        }

        .content {

            display: inline-block;
            line-height: 20px;
            width: 100%;
        }
    }
}