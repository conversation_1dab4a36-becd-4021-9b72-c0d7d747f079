<div class="typical-data-view">

	<div class="user-toolbar themed-box">

		<el-radio-group v-model="states.status">
			<el-radio v-for="(item, item_idx) in statuses"
					  :key="item_idx"
					  :label="item.code"
					  @change="filterTasks">{{ item.mean }}</el-radio>
		</el-radio-group>

		<el-input placeholder="输入关键字搜索" 
				  prefix-icon="el-icon-search" 
				  class="keywords s-mgl-10"
				  v-model="states.keywords"
				  @change="filterTasks" clearable></el-input>

		<div class="s-pull-right">
			<el-button v-if="!states.isMain" type="danger" @click="back2Main">《 返回主任务</el-button>
			<el-button type="primary" @click="hope2CancelCheckeds">撤勾选</el-button>
			<el-button type="primary" @click="hope2CancelAll">撤全部</el-button>
			<el-button type="primary" @click="hope2Replace">追单</el-button>
		</div>

	</div>

	<div class="data-list">

		<table class="table-main-task">
			<tr>
				<th type="check" min-width="40" fixed></th>

				<th label="篮子名称" 
					min-width="120" 
					prop="basketName" filterable sortable searchable overflowt></th>

				<th label="篮子代码" 
					min-width="120" 
					prop="basketId" filterable sortable searchable overflowt></th>

				<th label="任务ID" 
					min-width="60" 
					prop="taskId" sortable searchable overflowt></th>

				<th label="创建时间" 
					min-width="70" 
					prop="createTime"
					formatter="formatTime" sortable></th>

				<th label="方向" 
					min-width="70" 
					prop="direction"
					formatter="formatDirection" 
					export-formatter="formatDirectionText" 
					filter-data-provider="rebindDirection" sortable></th>

				<th label="进度" 
					min-width="80" 
					prop="completionRate"
					align="right" percentage by100 sortable></th>

				<th label="下单方式" 
					min-width="80" 
					prop="executeType"
					formatter="formatMethod"
					filter-data-provider="rebindMethod"
					align="right" sortable></th>

				<th label="下单规模" 
					min-width="80" 
					prop="executeVolume"
					formatter="formatScale"
					align="right" sortable></th>

				<th label="目标量" 
					min-width="80" 
					prop="targetVolume"
					align="right" sortable thousands-int></th>

				<th label="成交量" 
					min-width="80" 
					prop="tradedVolume" 
					align="right" sortable thousands-int></th>

				<th label="剩余量" 
					min-width="80" 
					prop="leftVolume" 
					align="right" sortable thousands-int></th>

				<th label="目标额" 
					min-width="80" 
					prop="targetMoney"
					align="right" sortable thousands-int></th>

				<th label="成交额" 
					min-width="80" 
					prop="tradedMoney" 
					align="right" sortable thousands-int></th>

				<th label="剩余额" 
					min-width="80" 
					prop="leftMoney" 
					align="right" sortable thousands-int></th>

				<th label="子任务" 
					min-width="60"
					ifixed="right"
					formatter="formatGoDeep"></th>

				<th label="操作"
					min-width="60" 
					prop="isCompleted"
					ifixed="right"
					formatter="formatActions"></th>
					
			</tr>
		</table>

		<table class="table-child-task">
			<tr>
				<th type="check" min-width="40" fixed></th>

				<th label="篮子名称" 
					min-width="120" 
					prop="basketName" filterable sortable searchable overflowt></th>

				<th label="篮子代码" 
					min-width="120" 
					prop="basketId" filterable sortable searchable overflowt></th>

				<th label="任务ID" 
					min-width="60" 
					prop="taskId" sortable searchable overflowt></th>

				<th label="合约代码"
					min-width="80"
					prop="instrument" sortable searchable overflowt></th>

				<th label="合约名称"
					min-width="80"
					prop="instrumentName" sortable searchable overflowt></th>

				<th label="创建时间" 
					min-width="70" 
					prop="createTime"
					formatter="formatTime" sortable></th>

				<th label="方向" 
					min-width="70" 
					prop="direction"
					formatter="formatDirection" 
					export-formatter="formatDirectionText" 
					filter-data-provider="rebindDirection" sortable></th>

				<th label="进度" 
					min-width="80" 
					prop="completionRate"
					align="right" percentage by100 sortable></th>

				<th label="下单方式" 
					min-width="80" 
					prop="executeType"
					formatter="formatMethod"
					filter-data-provider="rebindMethod"
					align="right" sortable></th>

				<th label="目标量" 
					min-width="80" 
					prop="targetVolume"
					align="right" sortable thousands-int></th>

				<th label="成交量" 
					min-width="80" 
					prop="tradedVolume" 
					align="right" sortable thousands-int></th>

				<th label="剩余量" 
					min-width="80" 
					prop="leftVolume" 
					align="right" sortable thousands-int></th>

				<th label="目标额" 
					min-width="80" 
					prop="targetMoney"
					align="right" sortable thousands-int></th>

				<th label="成交额" 
					min-width="80" 
					prop="tradedMoney" 
					align="right" sortable thousands-int></th>

				<th label="剩余额" 
					min-width="80" 
					prop="leftMoney" 
					align="right" sortable thousands-int></th>

				<th label="操作"
					min-width="60" 
					prop="isCompleted"
					ifixed="right"
					formatter="formatActions"></th>
					
			</tr>
		</table>

	</div>

	<div class="user-footer themed-box">
		<el-pagination class="s-pull-right"
					   :page-sizes="paging.pageSizes"
					   :page-size.sync="paging.pageSize" 
					   :total="paging.total"
					   :current-page.sync="paging.page" 
					   :layout="paging.layout" 
					   @size-change="handlePageSizeChange"
					   @current-change="handlePageChange"></el-pagination>
	</div>

</div>