const IView = require('../../../../component/iview').IView;
const SmartTable = require('../../../../libs/table/smart-table').SmartTable;
const { ColumnCommonFunc } = require('../../../../libs/table/column-common-func');
const { BasketTask } = require('../../../../model/basket-task');
const repoBasket = require('../../../../repository/basket');

class View extends IView {

    get isMain() {
        return this.states.isMain;
    }

    get isChild() {
        return !this.states.isMain;
    }

    get table() {
        return this.isMain ? this.tmain : this.tchild;
    }

    constructor(view_name, is_standalone_window) {

        super(view_name, is_standalone_window, '我的任务');

        this.methods = this.systemTrdEnum.basketMethod;
        this.statuses = {

            all: { code: 0, mean: '全部' },
            progressing: { code: 1, mean: '进行中' },
            completed: { code: 2, mean: '已完成' },
        };

        this.states = {

            /** 任务状态 */
            status: this.statuses.all.code,
            /** 关键字 */
            keywords: null,
            /** 是否主任务模式 */
            isMain: true,
            /** 当前如果位于子任务界面，该子任务归属主任务 */
            mainTask: (/** @returns {BasketTask} */ function() { return null; })(),
        };

        var paging = this.systemSetting.tablePagination;
        this.paging = {

            pageSizes: paging.pageSizes,
            pageSize: paging.pageSize,
            layout: paging.layout,
            total: 0,
            page: 0,
        };

        /** 本地设置 */
        this.settings = {

            /** 任务状态更新频率 */
            frequency: 1000 * 40,
        };
    }

    /**
     * @param {BasketTask} record
     */
    identifyMain(record) {
        return record.taskId;
    }

    /**
     * @param {BasketTask} record
     */
    identifyChild(record) {
        return record.instrument;
    }

    createToolbarApp() {

        new Vue({

            el: this.$container.querySelector('.user-toolbar'),
            data: {

                states: this.states,
                statuses: this.statuses,
            },
            methods: this.helper.fakeVueInsMethod(this, [

                this.filterTasks,
                this.back2Main,
                this.hope2CancelCheckeds,
                this.hope2CancelAll,
                this.hope2Replace,
            ]),
        });
    }

    createFooterRowApp() {
        
        new Vue({

            el: this.$container.querySelector('.user-footer'),
            data: {
                paging: this.paging,
            },

            methods: this.helper.fakeVueInsMethod(this, [

                this.handlePageSizeChange,
                this.handlePageChange,
            ]),
        });
    }

    createTable() {

        var table = this.tmain = new SmartTable(this.$tableMain, this.identifyMain, this, {

            tableName: 'smt-bmt',
            displayName: this.title,
            defaultSorting: { prop: 'createTime', direction: 'desc' },
            recordsFiltered: this.handleTableFiltered.bind(this),
        });

        table.setPageSize(this.paging.pageSize);
    }

    createChildTable() {

        var table = this.tchild = new SmartTable(this.$tableChild, this.identifyChild, this, {

            tableName: 'smt-bct',
            displayName: this.title,
            defaultSorting: { prop: 'createTime', direction: 'desc' },
            recordsFiltered: this.handleTableFiltered.bind(this),
        });

        table.setPageSize(this.paging.pageSize);
        table.$component.style.display = 'none';
    }

    resetControls() {

        this.states.status = this.statuses.all.code;
        this.states.keywords = null;
    }

    handlePageChange() {
        this.table.setPageIndex(this.paging.page);
    }

    handlePageSizeChange() {

        this.table.setPageSize(this.paging.pageSize, true);
        this.table.setPageIndex(1);
    }

    handleTableFiltered(total) {
        this.paging.total = total;
    }

    rebindDirection(records, propName) {
        return SmartTable.MakeColFilters(records, propName, { translators: this.systemTrdEnum.tradingDirection });
    }

    /**
     * @param {BasketTask} record
     */
    formatMethod(record) {

        if (record.executeType == this.methods.volume.code) {
            return this.methods.volume.mean;
        }
        else if (record.executeType == this.methods.weight.code) {
            return this.methods.weight.mean;
        }
        else if (record.executeType == this.methods.weight2Account.code) {
            return this.methods.weight2Account.mean;
        }
        else if (record.executeType == this.methods.weight2Asset.code) {
            return this.methods.weight2Asset.mean;
        }
    }

    /**
     * @param {BasketTask} record
     */
    formatScale(record) {

        if (record.executeType == this.methods.volume.code) {
            return record.executeVolume + this.methods.volume.unit;
        }
        else if (record.executeType == this.methods.weight.code) {
            return record.executeVolume + this.methods.weight.unit;
        }
        else if (record.executeType == this.methods.weight2Account.code) {
            return record.executeVolume + this.methods.weight2Account.unit;
        }
        else if (record.executeType == this.methods.weight2Asset.code) {
            return record.executeVolume + this.methods.weight2Asset.unit;
        }
    }

    /**
     * @param {BasketTask} record
     */
    formatGoDeep(record) {
        return '<button event.onclick="go2Child">查看</button>';
    }

    /**
     * @param {BasketTask} record
     */
    formatActions(record) {
        return record.isCompleted ? '' : '<button class="danger" event.onclick="cancelSingle">撤销</button>';
    }

    /**
     * @param {BasketTask} records
     */
    rebindMethod(records, propName) {
        return SmartTable.MakeColFilters(records, propName, { translators: this.systemTrdEnum.basketMethod });
    }

    filterTasks() {

        var tableObj = this.table;
        var keywords = this.states.keywords;
        var isAll = this.states.status == this.statuses.all.code;
        var isProgressing = this.states.status == this.statuses.progressing.code;
        var isCompleted = this.states.status == this.statuses.completed.code;

        /**
         * @param {BasketTask} record 
         */
        function testRecords(record) {

            return (isAll || isProgressing && !record.isCompleted || isCompleted && record.isCompleted)
                && tableObj.matchKeywords(record);
        }

        tableObj.setPageIndex(1, false);
        tableObj.setKeywords(keywords, false);
        tableObj.customFilter(testRecords);
    }

    /**
     * @param {Boolean} isAll
     * @returns {Array<BasketTask>}
     */
    extractCheckeds(isAll) {
        return isAll ? this.table.extractAllRecords() : this.table.extractCheckedRecords();
    }

    /**
     * @param {BasketTask} task 
     */
    go2Child(task) {

        this.states.isMain = false;
        this.states.mainTask = task;
        this.states.keywords = null;
        this.states.status = this.statuses.all.code;
        this.paging.page = 0;
        this.tmain.clear();
        this.tmain.$component.style.display = 'none';
        this.tchild.$component.style.display = 'block';
        this.table.fitColumnWidth();
        this.requestChildTask();
    }

    back2Main() {

        this.states.isMain = true;
        this.states.mainTask = null;
        this.states.keywords = null;
        this.states.status = this.statuses.all.code;
        this.paging.page = 0;
        this.tchild.clear();
        this.tmain.$component.style.display = 'block';
        this.tchild.$component.style.display = 'none';
        this.table.fitColumnWidth();
        this.requestTask();
    }

    hope2CancelCheckeds() {

        var taskType = this.isMain ? '任务' : '子任务';

        if (this.table.rowCount == 0) {
            return this.interaction.showMessage('当前无' + taskType);
        }

        var checkeds = this.extractCheckeds();
        if (checkeds.length == 0) {
            return this.interaction.showMessage('请选择要撤销的' + taskType);
        }

        var progressings = checkeds.filter(item => !item.isCompleted);
        if (progressings.length == 0) {
            return this.interaction.showError(`勾选${taskType} = ${checkeds.length}，可撤销${taskType} = 0`);
        }

        this.interaction.showConfirm({

            title: `撤销${taskType}确认`,
            message: `勾选${taskType} = ${checkeds.length}，可撤销${taskType} = ${progressings.length}，是否确定？`,
            confirmed: () => {
                this.cancel(progressings);
            },
        });
    }

    /**
     * 单一撤销
     * @param {BasketTask} task 
     */
    cancelSingle(task) {

        var taskType = this.isMain ? '任务' : '子任务';
        this.interaction.showConfirm({

            title: `撤销${taskType}确认`,
            message: `是否撤销该${taskType}？`,
            confirmed: () => {
                this.cancel([task]);
            },
        });
    }

    /**
     * 撤销
     * @param {Array<BasketTask>} tasks 
     */
    cancel(tasks) {

        tasks.forEach(task => {
            this.renderProcess.send(this.systemEvent.transmitMsg2TradingServer, this.serverFunction.cancelBasketOrder, { taskId: task.taskId });
        });

        setTimeout(() => { this.requestTask(); }, 1000 * 2);
        this.interaction.showSuccess(`撤销请求已发出，数量 = ${tasks.length}`);
    }

    hope2CancelAll() {

        var taskType = this.isMain ? '任务' : '子任务';

        if (this.table.rowCount == 0) {

            this.interaction.showMessage('当前无' + taskType);
            return;
        }

        var all = this.extractCheckeds(true);
        var progressings = all.filter(item => !item.isCompleted);
        if (progressings.length == 0) {

            this.interaction.showError(`所有${taskType} = ${all.length}，可撤销${taskType} = 0`);
            return;
        }

        this.interaction.showConfirm({

            title: `撤销${taskType}确认`,
            message: `所有${taskType} = ${all.length}，可撤销${taskType} = ${progressings.length}，是否确定？`,
            confirmed: () => {
                this.cancel(progressings);
            },
        });
    }

    /**
     * @param {Array<BasketTask>} records
     * @returns {Array<BasketTask>}
     */
    typeRecords(records) {
        return records;
    }

    hope2Replace() {
        
        var taskType = this.isMain ? '任务' : '子任务';
        var table = this.table;

        if (table.rowCount == 0) {
            return this.interaction.showMessage('当前无' + taskType);
        }

        if (table.filteredRowCount == 0) {
            return this.interaction.showMessage('筛选结果无' + taskType);
        }

        var checkeds = this.typeRecords(table.extractCheckedRecords());
        if (checkeds.length == 0) {
            return this.interaction.showMessage('请选择要追单的' + taskType);
        }

        var cancellables = checkeds.filter(item => !item.isCompleted);
        if (cancellables.length == 0) {
            return this.interaction.showError(`勾选${taskType} = ${checkeds.length}，可（撤）追单数 = 0`);
        }

        this.replaceTask(cancellables);
    }

    /**
     * 追单
     * @param {Array<BasketTask>} tasks 
     */
    replaceTask(tasks) {
        
        if (this.dialogReplacing === undefined) {
            
            var DialogReplaceTask = require('../../fragment/dialog-replace-tasks');
            var dialog = new DialogReplaceTask('@2021/fragment/dialog-replace-tasks', false);
            dialog.loadBuild(this.$container.firstElementChild, null, _=> { dialog.trigger('showup', tasks, this.isChild); });
            this.dialogReplacing = dialog;
        }
        else {
            this.dialogReplacing.trigger('showup', tasks, this.isChild);
        }
    }

    refresh() {
        
        if (this.isMain) {
            this.requestTask();
        }
        else {
            this.requestChildTask();
        }
    }

    async requestTask(isRefresh) {

        if (this.isChild) {
            return;
        }

        var resp = await repoBasket.getBasketUserTask();
        if (resp.errorCode != 0) {
            return this.interaction.showError(`任务加载失败：${resp.errorCode}/${resp.errorMsg}`);
        }

        var records = resp.data;
        var tasks = records instanceof Array ? records.map(x => new BasketTask(false, x)) : [];
        
        if (isRefresh) {
            tasks.forEach(item => this.tmain.putRow(item));
        }
        else {
            this.tmain.refill(tasks);
        }
    }

    async requestChildTask(isRefresh) {

        if (this.isMain || !this.states.mainTask) {
            return;
        }

        var taskId = this.states.mainTask.taskId;
        var resp = await repoBasket.getTaskDetail({ task_id: taskId });
        if (resp.errorCode != 0) {
            return this.interaction.showError(`子任务加载失败：${resp.errorCode}/${resp.errorMsg}`);
        }

        var taskInfo = resp.data;
        var records = taskInfo.completionRateList;
        var tasks = records instanceof Array ? records.map(item => this.formChildTask(taskInfo, item)) : [];

        if (isRefresh) {
            tasks.forEach(item => this.tchild.putRow(item));
        }
        else {
            this.tchild.refill(tasks);
        }
    }

    formChildTask(taskInfo, struc) {

        return new BasketTask(true, {

            id: taskInfo.id,
            basketName: taskInfo.basketName,
            basketId: taskInfo.basketId,
            createTime: taskInfo.createTime,
            executeType: taskInfo.executeType,
            taskType: taskInfo.taskType,
            priceFollowType: taskInfo.priceFollowType,
            executeUser: taskInfo.executeUser,

            instrument: struc.instrument,
            instrumentName: struc.instrumentName,
            completionRate: struc.completionRate,
            executeVolume: null,
            targetVolume: struc.targetVolume,
            targetMoney: struc.targetMoney,
            tradedVolume: struc.tradedVolume,
            tradedMoney: struc.tradedMoney,
            cancelVolume: struc.cancelVolume,
            cancelMoney: struc.cancelMoney,
        });
    }

    listen2Events() {
        this.registerEvent('reload-basket-tasks', () => { this.requestTask(); });
    }

    keepRefreshed() {

        this.mainJob = setInterval(async () => {
            
            if (this._isMainJobRunning || this.isChild) {
                return;
            }

            this._isMainJobRunning = true;
            await this.requestTask(true);
            this._isMainJobRunning = false;

        }, this.settings.frequency);

        this.childJob = setInterval(async () => {
            
            if (this._isChildJobRunning || this.isMain) {
                return;
            }

            this._isChildJobRunning = true;
            await this.requestChildTask(true);
            this._isChildJobRunning = false;

        }, this.settings.frequency);
    }

    build($container) {
        
        super.build($container);
        this.$tableMain = this.$container.querySelector('.data-list > .table-main-task');
        this.$tableChild = this.$container.querySelector('.data-list > .table-child-task');
        this.helper.extend(this, ColumnCommonFunc);
        this.createToolbarApp();
        this.createFooterRowApp();
        this.createTable();
        this.createChildTable();
        this.requestTask();
        this.listen2Events();
        this.keepRefreshed();
    }
}

module.exports = View;