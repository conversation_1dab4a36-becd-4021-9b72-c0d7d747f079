const { TradeView } = require('../module/trade-view');
const { OrderPreview } = require('../../model/account');

class View extends TradeView {

    constructor(view_name) {
        super(view_name);
    }

    /**
     * @param {Array<OrderPreview>} previews 
     */
    sendOutOrder(previews) {

        var userId = this.userInfo.userId;
        var instrument = this.states.instrument;
        var price = this.uistates.price;
        var priceType = this.systemTrdEnum.pricingType.fixedPrice.code;
        var direction = this.uistates.direction;
        var hedgeFlag = this.systemTrdEnum.hedgeFlag.Speculate.code;

        previews.forEach(item => {
    
            this.renderProcess.send(this.systemEvent.transmitMsg2TradingServer, this.serverFunction.sendOrder, {

                strategyId: item.strategyId || item.fundId,
                accountId: item.accountId,
                userId: userId,
                price: price,
                volume: item.volumeOriginal,
                instrument: instrument,
                priceType: priceType,
                bsFlag: direction,
                businessFlag: 0,
                positionEffect: this.isSpot ? 0 : this.uistates.effect,
                customId: 'batch-spot-' + this.helper.makeToken(),
                orderTime: null,
                hedgeFlag: hedgeFlag,
            });
        });
    }

    createApp() {

        this.vueIns = new Vue({

            el: this.$container.querySelector('.trade-form-inner > .xtcontainer'),

            data: {

                channel: this.channel,
                modes: this.modes,
                directions: this.sdirections,
                effects: this.effects,
                methods: this.smethods,
                states: this.states,
                uistates: this.uistates,
            },

            computed: {

                isEffectApplicable: () => { return this.isEffectApplicable(); },
                isCloseTodayNotApplicable: () => { return this.isCloseTodayNotApplicable(); },
                isBuy: () => { return this.isBuy; },
                isSell: () => { return this.isSell; },
                scaleStep: () => { return this.decideScaleStep(); },
                maxScale: () => { return this.decideMaxScale(); },
            },

            methods: this.helper.fakeVueInsMethod(this, [

                this.setAsPrice,
                this.precisePrice,
                this.handleUserInput, 
                this.handleSelect,
                this.suggest,
                this.handleClearIns,
                this.handleDirectionChange,
                this.handlePositionEffectChange,
                this.handlePriceChange,
                this.handleScaleChange,
                this.hope2Entrust,
                this.handleMethodChange,
                this.handleModeChange,
                this.makeDisplayUnit,
                this.makeBuyText,
                this.makeSellText,
            ]),
        });
    }

    queryEffectName() {

        var effects = this.effects;
        switch (this.uistates.effect) {

            case effects.open.code : return effects.open.mean;
            case effects.close.code : return effects.close.mean;
            case effects.closeToday.code : return effects.closeToday.mean;
        }
    }

    makeBuyText() {
        return `买入${ this.isEffectApplicable() ? this.queryEffectName() : '' }`;
    }

    makeSellText() {
        return `卖出${ this.isEffectApplicable() ? this.queryEffectName() : '' }`;
    }

    build($container) {

        super.build($container);
        this.filterMethods();
        this.createApp();
    }
}

module.exports = View;