const { TradeView } = require('../module/trade-view');
const { OrderPreview, CreditOperation } = require('../../model/account');
const { CodeMeanItem } = require('../../model/message');

class View extends TradeView {

    /**
     * 是否为，融资融券
     */
    get isCreditOpen() {
        return this.localStates.creditType == this.creditTypes.open.code;
    }

    /**
     * 是否为，两融归还
     */
    get isCreditClose() {
        return this.localStates.creditType == this.creditTypes.close.code;
    }

    get isBuy() {

        var biz = this.getSelectedBiz();
        return biz && biz.direction === this.directions.buy.code;
    }

    get isSell() {
        
        var biz = this.getSelectedBiz();
        return biz && biz.direction === this.directions.sell.code;
    }

    /**
     * 是否为，两融调仓
     */
    get isCreditAdjust() {
        return this.localStates.creditType == this.creditTypes.adjust.code;
    }

    constructor(view_name) {

        super(view_name);

        /**
         * 两融业务大类
         */
        this.creditTypes = {

            open: new CodeMeanItem(1, '融资融券'),
            close: new CodeMeanItem(2, '两融归还'),
            adjust: new CodeMeanItem(3, '两融调仓'),
        };

        /**
         * 适配当前模式的两融大类
         */
        this.screditTypes = (/** @returns {Array<CodeMeanItem>} */ () => { return this.helper.dict2Array(this.creditTypes); })();
        var dirs = this.systemTrdEnum.tradingDirection;
        var bsFlag = this.systemTrdEnum.businessFlag;
        var bsDict = this.businessDict = {

            buyOpen: new CreditOperation('A', bsFlag.credit.code, dirs.buy.code, '融资买入', 'danger'),
            sellOpen: new CreditOperation('B', bsFlag.credit.code, dirs.sell.code, '融券卖出', 'success'),

            buyClose: new CreditOperation('C', bsFlag.close.code, dirs.buy.code, '买券还券', 'danger'),
            sellClose: new CreditOperation('D', bsFlag.close.code, dirs.sell.code, '卖券还款', 'success'),
            securityClose: new CreditOperation('E', bsFlag.closeWithSecurity.code, dirs.sell.code, '现券还券', 'primary')
        };

        /**
         * 两融业务具体业务操作类型
         */
        this.business = {

            opens: [bsDict.buyOpen, bsDict.sellOpen],
            closes: [bsDict.buyClose, bsDict.sellClose, bsDict.securityClose],
        };

        /**
         * 适配当前模式的两融业务类型
         */
        this.sbusiness = (/** @returns {Array<CreditOperation>} */ () => { return []; })();
        this.sbusiness.merge(this.business.opens);
        var defaultBiz = this.business.opens[0];

        /**
         * 本地状态对象
         */
        this.localStates = {

            creditType: this.creditTypes.open.code,
            businessCode: defaultBiz.code,
            buttonType: defaultBiz.buttonType,
            buttonText: defaultBiz.mean,
            effectBoxClass: '',
        };
    }

    handleModeChange() {

        this.filterCreditTypes();
        this.resetBusiness();
        super.handleModeChange();
    }

    setAsDirection(direction) {
        
        var bsDict = this.businessDict;

        if (this.isCreditOpen) {
            this.localStates.businessCode = this.isBuy ? bsDict.sellOpen.code : bsDict.buyOpen.code;
        }
        else if (this.isCreditClose) {
            this.localStates.businessCode = this.isBuy ? bsDict.sellClose.code : bsDict.buyClose.code;
        }

        this.handleBusinessChange();
    }

    handleCreditTypeChange() {

        this.resetBusiness();
        this.filterMethods();
        this.allocate();
    }

    handleBusinessChange() {

        this.filterMethods();
        this.updateButton();
        this.allocate();
    }

    resetBusiness() {

        var bizs = this.sbusiness;
        bizs.clear();

        if (this.isCreditOpen) {
            bizs.merge(this.business.opens);
        }
        else if (this.isCreditClose) {
            bizs.merge(this.business.closes);
        }

        this.localStates.businessCode = bizs.length > 0 ? bizs[0].code : null;
        this.localStates.effectBoxClass = this.isCreditClose ? 'minified' : '';
        this.updateButton();
    }

    getSelectedBiz() {

        let selected = this.business.opens.find(x => x.code == this.localStates.businessCode);
        if (selected === undefined) {
            selected = this.business.closes.find(x => x.code == this.localStates.businessCode);
        }

        return selected;
    }

    updateButton() {

        if (this.isCreditAdjust) {

            this.localStates.buttonText = this.creditTypes.adjust.mean;
            this.localStates.buttonType = 'primary';
        }
        else {

            let selected = this.getSelectedBiz();
            this.localStates.buttonText = selected.mean;
            this.localStates.buttonType = selected.buttonType;
        }
    }

    filterCreditTypes() {

        this.screditTypes.clear();
        var ctps = this.creditTypes;

        /**
         * 根据选择的模式，填充适配的两融类型
         */

        if (this.isDevide) {

            /**
             * 摊派模式，不支持两融调仓
             */
            
            this.screditTypes.merge([ctps.open, ctps.close]);

            if (this.isCreditAdjust) {
                this.localStates.creditType = ctps.open.code;
            }
        }
        else {

            /**
             * 复制模式，支持所有两融分类
             */

            this.screditTypes.merge(this.helper.dict2Array(ctps));
        }
    }

    formatDirName() {
        return this.getSelectedBiz().mean;
    }

    /**
     * @param {Array<OrderPreview>} previews 
     */
    sendOutOrder(previews) {

        var userId = this.userInfo.userId;
        var instrument = this.states.instrument;
        var price = this.uistates.price;
        var priceType = this.systemTrdEnum.pricingType.fixedPrice.code;
        var hedgeFlag = this.systemTrdEnum.hedgeFlag.Speculate.code;
        var business = this.getSelectedBiz();

        previews.forEach(item => {
    
            this.renderProcess.send(this.systemEvent.transmitMsg2TradingServer, this.serverFunction.sendOrder, {

                strategyId: item.strategyId || item.fundId,
                accountId: item.accountId,
                userId: userId,
                price: price,
                volume: item.volumeOriginal,
                instrument: instrument,
                priceType: priceType,
                bsFlag: business.direction,
                businessFlag: business.business,
                positionEffect: this.isSpot ? 0 : this.uistates.effect,
                customId: 'batch-credit-' + this.helper.makeToken(),
                orderTime: null,
                hedgeFlag: hedgeFlag,
            });
        });
    }

    isBusinessApplicable() {
        return !this.isCreditAdjust;
    }

    createApp() {

        this.vueIns = new Vue({

            el: this.$container.querySelector('.trade-form-inner > .xtcontainer'),

            data: {

                channel: this.channel,
                modes: this.modes,
                creditTypes: this.screditTypes,
                businesses: this.sbusiness,
                methods: this.smethods,
                states: this.states,
                uistates: this.uistates,
                localStates: this.localStates,
            },

            computed: {

                isBusinessApplicable: () => { return this.isBusinessApplicable(); },
                isBuy: () => { return this.isBuy; },
                isSell: () => { return this.isSell; },
                scaleStep: () => { return this.decideScaleStep(); },
                maxScale: () => { return this.decideMaxScale(); },
            },

            methods: this.helper.fakeVueInsMethod(this, [

                this.setAsPrice,
                this.precisePrice,
                this.handleUserInput, 
                this.handleSelect,
                this.suggest,
                this.handleClearIns,
                this.handleCreditTypeChange,
                this.handleBusinessChange,
                this.handlePriceChange,
                this.handleScaleChange,
                this.hope2Entrust,
                this.handleMethodChange,
                this.handleModeChange,
                this.makeDisplayUnit,
            ]),
        });
    }

    build($container) {

        super.build($container);
        this.filterMethods();
        this.createApp();
    }
}

module.exports = View;