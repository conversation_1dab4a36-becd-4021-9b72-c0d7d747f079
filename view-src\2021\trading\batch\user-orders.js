const { TradeRecordView } = require('../module/trade-record-view');
const { Order } = require('../../../../model/order');
const { repoOrder } = require('../../../../repository/order');

class View extends TradeRecordView {

    constructor(view_name, is_standalone_window) {

        super(view_name, is_standalone_window, '我的委托');
        this.statuses = {

            all: { code: 0, mean: '全部' },
            cancellable: { code: 1, mean: '可撤' },
            invalid: { code: 2, mean: '废单' },
        };

        this.states = {

            status: this.statuses.all.code,
            keywords: null,
        };

        this.invalidStatus = this.systemEnum.orderStatus.invalid.code;
        this.cancellables = [{ code: null, mean: null }];
        this.cancellables.pop();
        this.cancellables.merge(this.helper.dict2Array(this.systemEnum.orderStatus).filter(x => !x.isCompleted));
    }

    async queryFirstScreen() {
        return await repoOrder.quickMemQuery({ trade_user_id: this.userInfo.userId, pageSize: this.paging.pageSize, pageNo: 1 });
    }

    async queryAll() {
        return await repoOrder.batchMemQuery({ trade_user_id: this.userInfo.userId });
    }

    listen2DataChange() {
        this.standardListen(this.serverEvent.orderChanged, this.handleOrderChange.bind(this));
    }

    subChange() {
        this.standardSend(this.systemEvent.subscribeAccountChange, null, [this.serverEvent.orderChanged]);
    }

    unsubChange() {
        this.standardSend(this.systemEvent.unsubscribeAccountChange, null, [this.serverEvent.orderChanged]);
    }

    consumeBatchPush(titles, contents, totalSize) {

        super.consumeBatchPush(titles, contents, totalSize);
        var records = TradeRecordView.ModelConverter.formalizeOrders(titles, contents);
        this.tableObj.refill(records);
        this.filterOrders();
    }

    /**
     * @param {*} struc
     */
    handleOrderChange(struc) {

        var order = new Order(struc);
        var isNot4Me = order.userId != this.userInfo.userId;
        var isAdjustPos = order.adjustFlag;

        if (isNot4Me || isAdjustPos) {
            return;
        }
        else if (!this.isRecordAssetQualified(order.assetType)) {
            return;
        }

        this.tableObj.putRow(order);
    }

    resetControls() {

        super.resetControls();
        this.states.status = this.statuses.all.code;
        this.states.keywords = null;
    }

    createToolbarApp() {

        new Vue({

            el: this.$container.querySelector('.user-toolbar'),
            data: {

                states: this.states,
                statuses: this.statuses,
            },

            methods: this.helper.fakeVueInsMethod(this, [

                this.filterOrders,
                this.hope2CancelCheckeds,
                this.hope2CancelAll,
                this.hope2Replace,
            ]),
        });
    }

    /**
     * @param {Order} record
     */
    formatActions(record) {
        return record.isCompleted ? '' : '<button event.onclick="cancelSingle">撤单</button>';
    }

    filterByChannel() {
        this.filterOrders();
    }

    filterOrders() {

        var thisObj = this;
        var keywords = this.states.keywords;
        var isAll = this.states.status == this.statuses.all.code;
        var isCancellable = this.states.status == this.statuses.cancellable.code;
        var isInvalid = this.states.status == this.statuses.invalid.code;

        /**
         * @param {Order} record 
         */
        function filterByPinyin(record) {
            
            return thisObj.testPy(record.instrumentName, keywords)
                || thisObj.testPy(record.accountName, keywords)
                || thisObj.testPy(record.strategyName, keywords);
        }

        /**
         * @param {Order} record 
         */
        function testRecords(record) {

            return thisObj.isRecordAssetQualified(record.assetType) && (isAll 
                    || isCancellable && thisObj.cancellables.some(x => x.code == record.orderStatus) 
                    || isInvalid && record.orderStatus == thisObj.invalidStatus)
                    && (thisObj.tableObj.matchKeywords(record) || filterByPinyin(record));
        }

        this.tableObj.setPageIndex(1, false);
        this.tableObj.setKeywords(keywords, false);
        this.tableObj.customFilter(testRecords);
    }

    /**
     * @param {Boolean} isAll
     * @returns {Array<Order>}
     */
    extractCheckeds(isAll) {
        return isAll ? this.tableObj.extractAllRecords() : this.tableObj.extractCheckedRecords();
    }

    hope2CancelCheckeds() {

        if (this.tableObj.rowCount == 0) {

            this.interaction.showMessage('当前无委托');
            return;
        }

        var checkeds = this.extractCheckeds();
        if (checkeds.length == 0) {

            this.interaction.showMessage('请选择要撤单的委托');
            return;
        }

        var cancellables = checkeds.filter(item => !item.isCompleted);
        if (cancellables.length == 0) {

            this.interaction.showError(`勾选委托 = ${checkeds.length}，可撤委托 = 0`);
            return;
        }

        this.interaction.showConfirm({

            title: '撤单确认',
            message: `勾选委托 = ${checkeds.length}，可撤委托 = ${cancellables.length}，是否确定？`,
            confirmed: () => {
                this.cancel(cancellables);
            },
        });
    }

    /**
     * 单一撤单
     * @param {Order} order 
     */
    cancelSingle(order) {

        this.interaction.showConfirm({

            title: '撤单确认',
            message: '是否撤销该委托？',
            confirmed: () => {
                this.cancel([order]);
            },
        });
    }

    /**
     * 撤单
     * @param {Array<Order>} orders 
     */
    cancel(orders) {

        orders.forEach(order => {
            this.renderProcess.send(this.systemEvent.transmitMsg2TradingServer, this.serverFunction.cancelOrder, { orderId: order.id });
        });
        this.interaction.showSuccess(`撤单请求已发出，数量 = ${orders.length}`);
    }

    hope2CancelAll() {

        if (this.tableObj.rowCount == 0) {

            this.interaction.showMessage('当前无委托');
            return;
        }

        var all = this.extractCheckeds(true);
        var cancellables = all.filter(item => !item.isCompleted);
        if (cancellables.length == 0) {

            this.interaction.showError(`所有委托 = ${all.length}，可撤委托 = 0`);
            return;
        }

        this.interaction.showConfirm({

            title: '撤单确认',
            message: `所有委托 = ${all.length}，可撤委托 = ${cancellables.length}，是否确定？`,
            confirmed: () => {
                this.cancel(cancellables);
            },
        });
    }

    /**
     * @param {Array<Order>} records
     * @returns {Array<Order>}
     */
     typeRecords(records) {
        return records;
    }

    hope2Replace() {
        
        if (this.tableObj.rowCount == 0) {

            this.interaction.showMessage('当前无订单');
            return;
        }

        if (this.tableObj.filteredRowCount == 0) {

            this.interaction.showMessage('筛选结果无订单');
            return;
        }

        var checkeds = this.typeRecords(this.tableObj.extractCheckedRecords());
        if (checkeds.length == 0) {

            this.interaction.showMessage('请选择要追单的合约');
            return;
        }

        var filtereds = this.typeRecords(this.tableObj.extractFilteredRecords());
        var intersecs = checkeds.filter(item => filtereds.some(item2 => this.identifyRecord(item2) == this.identifyRecord(item)));
        var orders = intersecs.filter(item => !item.isCompleted);
        if (orders.length == 0) {

            this.interaction.showError(`勾选订单 = ${intersecs.length}，可（撤）追单数 = 0`);
            return;
        }

        this.replaceOrder(orders);
    }

    /**
     * 追单
     * @param {Array<Order>} orders 
     */
    replaceOrder(orders) {
        
        if (this.dialogReplacing === undefined) {
            
            var DialogReplaceOrder = require('./dialog-replace-orders');
            var dialog = new DialogReplaceOrder('@2021/fragment/dialog-replace-orders', false);
            dialog.loadBuild(this.$container.firstElementChild, null, _=> { dialog.trigger('showup', orders); });
            this.dialogReplacing = dialog;
        }
        else {
            this.dialogReplacing.trigger('showup', orders, true);
        }
    }

    build($container) {

        super.build($container, 'smt-fuo');
        this.subChange();
        this.turn2Request();
    }
}

module.exports = View;