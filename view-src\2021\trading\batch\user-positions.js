const { TradeRecordView } = require('../module/trade-record-view');
const { Position } = require('../../../../model/position');
const { repoPosition } = require('../../../../repository/position');

class View extends TradeRecordView {

    constructor(view_name, is_standalone_window) {

        super(view_name, is_standalone_window, '我的持仓');
        this.types = {

            all: { code: 0, mean: '全部' },
            product: { code: 1, mean: '产品持仓' },
            strategy: { code: 2, mean: '策略持仓' },
        };

        this.states = {

            type: this.types.all.code,
            keywords: null,
        };
    }

    handleChannelChange() {

        super.handleChannelChange();

        if (this.isFuture || this.isOption) {
            this.tableObj.showColumns(['保证金']);
        }
        else {
            this.tableObj.hideColumns(['保证金']);
        }
    }

    async queryFirstScreen() {
        return await repoPosition.quickMemQuery({ trade_user_id: this.userInfo.userId, pageSize: this.paging.pageSize, pageNo: 1 });
    }

    async queryAll() {
        return await repoPosition.batchMemQuery({ trade_user_id: this.userInfo.userId });
    }

    listen2DataChange() {
        this.standardListen(this.serverEvent.positionChanged, this.handlePositionChange.bind(this));
    }

    subChange() {
        this.standardSend(this.systemEvent.subscribeAccountChange, null, [this.serverEvent.positionChanged]);
    }

    unsubChange() {
        this.standardSend(this.systemEvent.unsubscribeAccountChange, null, [this.serverEvent.positionChanged]);
    }

    consumeBatchPush(titles, contents, totalSize) {

        super.consumeBatchPush(titles, contents, totalSize);
        var records = TradeRecordView.ModelConverter.formalizePositions(titles, contents);
        this.tableObj.refill(records);
        this.filterPositions();
    }

    /**
     * @param {*} struc
     */
    handlePositionChange(struc) {
        
        var position = new Position(struc);
        if (this.isRecordAssetQualified(position.assetType)) {
            this.tableObj.putRow(position);
        }
    }

    resetControls() {

        super.resetControls();
        this.states.type = this.types.all.code;
        this.states.keywords = null;
    }

    createToolbarApp() {

        new Vue({

            el: this.$container.querySelector('.user-toolbar'),
            data: {

                states: this.states,
                types: this.types,
            },

            methods: this.helper.fakeVueInsMethod(this, [

                this.filterPositions,
                this.hope2CloseCheckeds,
                this.createAsBasket,
            ]),
        });
    }

    /**
     * @param {Position} record 
     */
    formatActions(record) {
        return '';
    }

    filterByChannel() {
        this.filterPositions();
    }

    filterPositions() {

        var thisObj = this;
        var keywords = this.states.keywords;
        var isAll = this.states.type == this.types.all.code;
        var isOfProduct = this.states.type == this.types.product.code;
        var isOfStrategy = this.states.type == this.types.strategy.code;

        /**
         * @param {Position} record 
         */
        function filterByPinyin(record) {

            return thisObj.testPy(record.instrumentName, keywords)
                || thisObj.testPy(record.accountName, keywords)
                || thisObj.testPy(record.strategyName, keywords);
        }

        /**
         * @param {Position} record 
         */
        function testRecords(record) {

            return thisObj.isRecordAssetQualified(record.assetType) && (isAll 
                    || isOfProduct && thisObj.helper.isNone(record.strategyId) 
                    || isOfStrategy && thisObj.helper.isNotNone(record.strategyId))
                    && (thisObj.tableObj.matchKeywords(record) || filterByPinyin(record));
        }

        this.tableObj.setPageIndex(1, false);
        this.tableObj.setKeywords(keywords, false);
        this.tableObj.customFilter(testRecords);
    }

    /**
     * @param {Array<Position>} records
     * @returns {Array<Position>}
     */
    typeRecords(records) {
        return records;
    }

    hope2CloseCheckeds() {

        if (this.tableObj.rowCount == 0) {

            this.interaction.showMessage('当前无持仓');
            return;
        }

        if (this.tableObj.filteredRowCount == 0) {

            this.interaction.showMessage('筛选结果无持仓');
            return;
        }

        var checkeds = this.typeRecords(this.tableObj.extractCheckedRecords());
        if (checkeds.length == 0) {

            this.interaction.showMessage('请选择要平仓的合约');
            return;
        }

        var filtereds = this.typeRecords(this.tableObj.extractFilteredRecords());
        var intersecs = checkeds.filter(item => filtereds.some(item2 => this.identifyRecord(item2) == this.identifyRecord(item)));
        var closables = intersecs.filter(item => item.closableVolume > 0);
        if (closables.length == 0) {

            this.interaction.showError(`勾选持仓 = ${intersecs.length}，可平持仓 = 0`);
            return;
        }

        this.closePosition(closables);
    }

    /**
     * 平仓
     * @param {Array<Position>} positions 
     */
    closePosition(positions) {
        
        if (this.closeDialog === undefined) {
            
            const DialogClosePosition = require('../../fragment/dialog-close-positions');
            const dialog = new DialogClosePosition('@2021/fragment/dialog-close-positions', false);
            dialog.loadBuild(this.$container.firstElementChild, null, _=> { dialog.trigger('showup', positions); });
            this.closeDialog = dialog;
        }
        else {
            this.closeDialog.trigger('showup', positions);
        }
    }

    createAsBasket() {
        this.interaction.showAlert('暂未实现篮子导入功能');
    }

    build($container) {

        super.build($container, 'smt-fup');
        this.subChange();
        this.turn2Request();
        this.registerEvent('batch-order-made', _ => {

            /**
             * 延迟些许刷新持仓，获得完整度更高的，最新持仓数据
             */
            setTimeout(() => { this.reloadRecords(); }, 1000 * 2);
        });
    }
}

module.exports = View;