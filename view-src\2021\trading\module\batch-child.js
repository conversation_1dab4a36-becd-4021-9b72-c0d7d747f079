const IView = require('../../../../component/iview').IView;
const NumberMixin = require('../../../../mixin/number').NumberMixin;
const { TradeChannel, InstrumentInfo } = require('../../model/message');

class BatchChildView extends IView {

    get childViewHeight() {
        return 300 -28;
    }

    get NumberMixin() {
        return NumberMixin;
    }

    get isSpot() {
        return this.channel.options.isSpot;
    }

    get isCredit() {
        return this.channel.options.isCredit;
    }

    get isFuture() {
        return this.channel.options.isFuture;
    }
    
    get isOption() {
        return this.channel.options.isOption;
    }
    
    constructor(view_name) {

        super(view_name, false);

        /**
         * 当前交易频道
         */
        this.channel = new TradeChannel(null, null, null, []);

        this.states = {

            instrument: null,
            shortInstrument: null,
            instrumentName: null,
            precision: 2,
            unit: '股',
        };

        /** 监听交易渠道切换 */
        this.registerEvent('set-channel', this.handleChannelChange.bind(this));
        /** 监听设置目标合约 */
        this.registerEvent('set-as-instrument', this.setAsInstrument.bind(this));
    }

    /**
     * 将价格格式化为适合的精度
     * @param {Number} price 
     */
    precisePrice(price) {
        return typeof price == 'number' ? price.toFixed(this.states.precision) : price;
    }

    formatViewTitle() {
        return `${ (this.states.instrumentName || '').substr(0, 4) } (${ this.states.shortInstrument })`;
    }

    /**
     * @param {TradeChannel} newChannel 
     */
    handleChannelChange(newChannel) {

        var channel = this.channel;
        channel.code = newChannel.code;
        channel.mean = newChannel.mean;
        channel.assetType = newChannel.assetType;
        channel.supporteds.clear();
        channel.supporteds.merge(newChannel.supporteds);
        this.helper.extend(channel.options, newChannel.options);
    }

    /**
     * 设置为当前合约
     * @param {InstrumentInfo} insInfo
     */
    setAsInstrument(insInfo) {

        var last = this.states.instrument;
        var current = null;

        if (insInfo instanceof InstrumentInfo) {

            current = insInfo.instrument;
            this.states.instrument = insInfo.instrument;
            this.states.shortInstrument = insInfo.shortInstrument;
            this.states.instrumentName = insInfo.instrumentName;
            this.states.precision = insInfo.precision;
        }
        else {

            this.states.instrument = null;
            this.states.shortInstrument = null;
            this.states.instrumentName = null;
            this.states.precision = 2;
        }

        this.handleInstrumentChange(last, current);
    }

    /**
     * 处理当前合约变更事件
     * @param {String} lastIns 上一个合约代码（为null代表未设置）
     * @param {String} currentIns 当前新的合约代码（为null代表未设置）
     */
    handleInstrumentChange(lastIns, currentIns) {
        //
    }
}

module.exports = BatchChildView;
