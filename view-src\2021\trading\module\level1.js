const BatchChildView = require('./batch-child');
const { LevelMessage, TickData, PriceLevel } = require('../../model/message');

class View extends BatchChildView {

    constructor(view_name) {

        super(view_name);

        var levelNames = ['卖5', '卖4', '卖3', '卖2', '卖1', '买1', '买2', '买3', '买4', '买5'];
        this.levels = levelNames.map((name, idx) => new PriceLevel(idx >= 5 ? true : false, name, 0, 0));

        var margin = 10;
        this.uistates = {

            margin: margin,
            lineHeight: (this.childViewHeight - margin * 3 - 1) / levelNames.length,
            checkOpt: {

                /** 点击价格时，是否带入方向 */
                changeDirection: false,
                /** 点击价格时，是否触发弹出委托确认提示框 */
                promptConfirm: false,
            },
        };

        this.registerEvent('set-first-tick', this.updateLevels.bind(this));
    }

    createApp() {

        new Vue({

            el: this.$container.querySelector('.level1-frame'),
            data: {

                channel: this.channel,
                levels: this.levels,
                states: this.states,
                uistates: this.uistates,
            },
            mixins: [this.NumberMixin],
            methods: this.helper.fakeVueInsMethod(this, [

                this.formatViewTitle,
                this.handleSelect, 
                this.simplyHands,
                this.precisePrice,
            ]),
        });
    }

    /**
     * 简化手数显示
     * @param {Number} hands 
     */
    simplyHands(hands) {
        
        if (hands <= 999999) {
            return hands;
        }
        else if (hands >= 1000000 && hands <= 99999999) {
            return (hands / 10000).toFixed(1) + '万';
        }
        else {
            return (hands / 100000000).toFixed(1) + '亿';
        }
    }

    /**
     * @param {PriceLevel} level 
     */
    handleSelect(level) {

        this.trigger('level-selected', new LevelMessage({

            price: level.price, 
            isBuy: level.isBuy,
            changeDirection: this.uistates.checkOpt.changeDirection, 
            promptConfirm: this.uistates.checkOpt.promptConfirm,
        }));
    }

    /**
     * @param {TickData} tick 
     */
    updateLevels(tick) {

        for (let idx = 0; idx < 5; idx++) {

            let level = tick.sells[idx];
            this.levels[4 - idx].update(level.price, tick.preclose, Math.ceil(level.hands * 0.01));
        }

        for (let idx = 0; idx < 5; idx++) {

            let level = tick.buys[idx];
            this.levels[idx + 5].update(level.price, tick.preclose, Math.ceil(level.hands * 0.01));
        }
    }

    handleInstrumentChange(lastIns, currentIns) {
        this.resetLevels();
    }

    resetLevels() {

        /**
         * 合约产生变化时，首先将档位显示重置
         */
        this.levels.forEach(level => { level.update(0, 0, 0); });
    }

    build($container) {

        super.build($container);
        this.createApp();
    }
}

module.exports = View;