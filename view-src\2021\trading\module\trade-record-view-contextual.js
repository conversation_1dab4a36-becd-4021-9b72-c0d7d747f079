const { TradeRecordView } = require('./trade-record-view');
const { SimpleAccountItem } = require('../../model/account');

class ContextualTradeRecordView extends TradeRecordView {

    constructor(view_name, is_standalone_window, title) {

        super(view_name, is_standalone_window, title);
        this.registerEvent('set-context-account', this.handleAccountChange.bind(this));
    }

    /**
     * @param {SimpleAccountItem} account 
     */
    handleAccountChange(account) {

        var newId = account ? account.accountId : undefined;
        var lastId = this.accountId;

        if (lastId == newId) {
            return;
        }

        if (this.tableObj) {
            this.tableObj.clear();
        }

        if (this.helper.isNotNone(lastId)) {
            this.handleBeforeContextChange(lastId);
        }

        this.accountId = newId;

        if (this.helper.isNotNone(newId)) {
            
            this.subChange();
            this.turn2Request();
        }
    }

    /**
     * 处置上下文（即将）切换事件，在该方法实现：对上一主体尚未保存的状态作清理
     * @param {*} lastId 上一主体ID
     */
    handleBeforeContextChange(lastId) {
        throw new Error('not implemented');
    }

    filterByChannel() {
        
        /**
         * 账号交易数据，随账号变化而变化，不随交易渠道的变化而变，故无需响应该事件
         */
    }

    build($container, tableName) {
        super.build($container, tableName);
    }
}

module.exports = { ContextualTradeRecordView };