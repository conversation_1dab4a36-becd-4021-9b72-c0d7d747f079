const { ContextualTradeRecordView } = require('../module/trade-record-view-contextual');
const { Position } = require('../../../../model/position');
const { repoPosition } = require('../../../../repository/position');
const { repoTrading } = require('../../../../repository/trading');

class View extends ContextualTradeRecordView {

    constructor(view_name, is_standalone_window) {

        super(view_name, is_standalone_window, '账号持仓');

        /**
         * 页面状态对象
         */
        this.states = {
            keywords: null,
        };

        this.priceMap = {};
    }

    handleChannelChange() {

        super.handleChannelChange();

        if (this.isFuture || this.isOption) {
            this.tableObj.showColumns(['保证金']);
        }
        else {
            this.tableObj.hideColumns(['保证金']);
        }
    }

    async queryFirstScreen() {
        return await repoPosition.quickMemQuery({ account_ids: this.accountId, pageSize: this.paging.pageSize, pageNo: 1 });
    }

    async queryAll() {
        return await repoPosition.batchMemQuery({ account_ids: this.accountId });
    }

    /**
     * @param {Array<Position>} positions 
     */
    async updatePrice(positions) {

        var resp = await repoTrading.getMarketLatestPrice(this.systemEnum.assetsType.stock.code);
        var data = resp.data;

        for (let key in data) {
            this.priceMap[key] = data[key];
        }

        positions.forEach(pos => {
            
            let lastPrice = this.priceMap[pos.instrument] || 0;
            let floatProfit = (lastPrice - pos.avgPrice) * pos.totalPosition * pos.direction * pos.volumeMultiple;

            this.tableObj.updateRow({

                id: pos.id,
                lastPrice: lastPrice,
                floatProfit: floatProfit,
                profit: pos.closeProfit + floatProfit - pos.usedCommission,
            });
        });
    }

    listen2DataChange() {
        this.standardListen(this.serverEvent.positionChanged, this.handlePositionChange.bind(this));
    }

    subChange() {
        this.standardSend(this.systemEvent.subscribeAccountChange, this.accountId, [this.serverEvent.positionChanged]);
    }

    unsubChange() {
        this.standardSend(this.systemEvent.unsubscribeAccountChange, this.accountId, [this.serverEvent.positionChanged]);
    }

    handleBeforeContextChange(lastId) {
        this.standardSend(this.systemEvent.unsubscribeAccountChange, lastId, [this.serverEvent.positionChanged]);
    }

    consumeBatchPush(titles, contents, totalSize) {

        super.consumeBatchPush(titles, contents, totalSize);
        var records = View.ModelConverter.formalizePositions(titles, contents);
        this.tableObj.refill(records);
        this.filterPositions();
        this.updatePrice(records);
    }

    /**
     * @param {*} struc
     */
    handlePositionChange(struc) {
        
        var pos = new Position(struc);

        if (this.isRecordAssetQualified(pos.assetType)) {

            this.tableObj.putRow(pos);
            let lastPrice = this.priceMap[pos.instrument] || 0;
            let floatProfit = (lastPrice - pos.avgPrice) * pos.totalPosition * pos.direction * pos.volumeMultiple;

            this.tableObj.updateRow({ 
                
                id: pos.id, 
                lastPrice: lastPrice,
                floatProfit: floatProfit,
                profit: pos.closeProfit + floatProfit - pos.usedCommission,
            });
        }
    }

    resetControls() {

        super.resetControls();
        this.states.keywords = null;
    }

    createToolbarApp() {

        new Vue({

            el: this.$container.querySelector('.user-toolbar'),
            data: {
                states: this.states,
            },

            methods: this.helper.fakeVueInsMethod(this, [

                this.filterPositions,
                this.hope2CloseCheckeds,
                this.createAsBasket,
                this.refresh,
            ]),
        });
    }

    /**
     * @param {Position} record 
     */
    formatActions(record) {
        return '';
    }

    filterPositions() {

        var thisObj = this;
        var keywords = this.states.keywords;

        /**
         * @param {Position} record 
         */
        function filterByPinyin(record) {

            return thisObj.testPy(record.instrumentName, keywords)
                || thisObj.testPy(record.accountName, keywords)
                || thisObj.testPy(record.strategyName, keywords);
        }

        /**
         * @param {Position} record 
         */
        function testRecords(record) {
            return thisObj.tableObj.matchKeywords(record) || filterByPinyin(record);
        }

        this.tableObj.setPageIndex(1, false);
        this.tableObj.setKeywords(keywords, false);
        this.tableObj.customFilter(testRecords);
    }

    /**
     * @param {Array<Position>} records
     * @returns {Array<Position>}
     */
    typeRecords(records) {
        return records;
    }

    /**
     * @param {Position} record 
     */
    handleRowDbClick(record) {
        this.trigger('account-position-item-double-clicked', record);
    }

    hope2CloseCheckeds() {

        if (this.tableObj.rowCount == 0) {

            this.interaction.showMessage('当前无持仓');
            return;
        }

        if (this.tableObj.filteredRowCount == 0) {

            this.interaction.showMessage('筛选结果无持仓');
            return;
        }

        var checkeds = this.typeRecords(this.tableObj.extractCheckedRecords());
        if (checkeds.length == 0) {

            this.interaction.showMessage('请选择要平仓的合约');
            return;
        }

        var filtereds = this.typeRecords(this.tableObj.extractFilteredRecords());
        var intersecs = checkeds.filter(item => filtereds.some(item2 => this.identifyRecord(item2) == this.identifyRecord(item)));
        var closables = intersecs.filter(item => item.closableVolume > 0);
        if (closables.length == 0) {

            this.interaction.showError(`勾选持仓 = ${intersecs.length}，可平持仓 = 0`);
            return;
        }

        this.closePosition(closables);
    }

    /**
     * 平仓
     * @param {Array<Position>} positions 
     */
    closePosition(positions) {
        
        if (this.closeDialog === undefined) {
            
            const DialogClosePosition = require('../../fragment/dialog-close-positions');
            const dialog = new DialogClosePosition('@2021/fragment/dialog-close-positions', false);
            dialog.loadBuild(this.$container.firstElementChild, null, _=> { dialog.trigger('showup', positions); });
            this.closeDialog = dialog;
        }
        else {
            this.closeDialog.trigger('showup', positions);
        }
    }

    createAsBasket() {
        this.interaction.showAlert('暂未实现篮子导入功能');
    }

    build($container) {

        super.build($container, 'smt-tnap');
        this.registerEvent('reload-account-positions', _ => {

            /**
             * 延迟些许刷新持仓，获得完整度更高的，最新持仓数据
             */
            setTimeout(() => { this.reloadRecords(); }, 1000);
        });
    }
}

module.exports = View;