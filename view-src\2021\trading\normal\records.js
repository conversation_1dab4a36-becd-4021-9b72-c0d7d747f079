const { IView } = require('../../../../component/iview');
const { Tab } = require('../../../../component/tab');
const { TabList } = require('../../../../component/tab-list');
const { TradeChannel } = require('../../model/message');

class View extends IView {

    /**
     * @param {*} view_name 
     * @param {*} is_standalone_window 
     * @param {TradeChannel} defaultChannel 
     */
    constructor(view_name, is_standalone_window, defaultChannel) {

        super(view_name, is_standalone_window, '批量交易数据');
        this.setAsChannel(defaultChannel);
    }

    createDataViews() {

        var $tab = this.$container.querySelector('.category-tab');
        var $content = this.$container.querySelector('.data-views');
        var tabs = new TabList({

            allowCloseTab: false,
            hideTab4OnlyOne: false,
            embeded: true,
            $navi: $tab,
            $content: $content,
            tabCreated: this.handleTabCreated.bind(this),
            tabFocused: this.handleTabFocused.bind(this),
        });

        tabs.openTab(true, '@2021/trading/normal/account-orders', '委托');
        tabs.openTab(true, '@2021/trading/normal/account-exchanges', '成交');
        tabs.openTab(true, '@2021/fragment/credit/contract', '两融合约', { isBySingleAccount: true });
        tabs.openTab(true, '@2021/fragment/credit/debt', '两融标的');

        this.tabEntrust = tabs.tabs[0];
        this.tabExchange = tabs.tabs[1];
        this.tabCreditContract = tabs.tabs[2];
        this.tabCreditDebt = tabs.tabs[3];
        this.tabList = tabs;
    }

    /**
     * @param {Tab} tab 
     */
    handleTabCreated(tab) {

        tab.viewEngine.trigger('set-channel', this.currentChannel);
        /** 上下文账号主体变化 */
        tab.viewEngine.trigger('set-context-account', this.contextAccount);

        /**
         * 各个TAB视图采用延迟加载方式，第一个委托创建完毕，即通知外层模块
         */
        if (tab === this.tabEntrust) {
            this.trigger('default-tab-view-loaded');
        }

        setTimeout(() => { this.simulateWinSizeChange(); }, 300);
    }

    /**
     * @param {Tab} tab 
     */
    handleTabFocused(tab) {

        /** 上下文账号主体变化 */
        tab.viewEngine.trigger('set-context-account', this.contextAccount);
        setTimeout(() => { this.simulateWinSizeChange(); }, 300);
    }

    /**
     * 设置为当前的交易渠道
     * @param {TradeChannel} channel 
     */
    setAsChannel(channel) {
        this.currentChannel = channel;
    }

    /**
     * 设置为当前的上下文帐号
     * @param {*} account 
     */
    setAsAccount(account) {
        this.contextAccount = account;
    }

    /**
     * 将渠道切换事件，通知到交易数据的各个视图
     * @param {TradeChannel} channel 
     */
    showProperTabs(channel) {
        
        var tabCtr = this.tabList;

        if (channel.options.isCredit) {

            tabCtr.show(this.tabCreditContract);
            tabCtr.show(this.tabCreditDebt);
        }
        else {

            if (tabCtr.isFocused(this.tabCreditContract) || tabCtr.isFocused(this.tabCreditDebt)) {
                tabCtr.setFocus(this.tabEntrust);
            }

            tabCtr.hide(this.tabCreditContract);
            tabCtr.hide(this.tabCreditDebt);
        }
    }

    listen2Events() {

        var viewOrder = this.tabEntrust.viewEngine;
        var viewExchange = this.tabExchange.viewEngine;
        var EventSetChannel = 'set-channel';
        var EventSetContextAccount = 'set-context-account';

        this.registerEvent(EventSetChannel, (channel) => {

            this.setAsChannel(channel);

            /**
             * 将渠道切换事件，通知到交易数据的各个视图
             */

            viewOrder.trigger(EventSetChannel, channel);
            viewExchange.trigger(EventSetChannel, channel);

            /**
             * 根据交易渠道，动态调整需要展示的数据视图
             */

            this.showProperTabs(channel);
        });

        this.registerEvent(EventSetContextAccount, (account) => {

            this.setAsAccount(account);
            this.tabList.fireEventOnFocusedTab(EventSetContextAccount, account);
        });

        /** 监听窗口尺寸调整 */
        this.lisen2WinSizeChange((width, height, isMaximized) => {

            this.tabList.fireEventOnFocusedTab('table-max-height', height - (isMaximized ? 554 : 539));
            this.tabList.fireEventOnFocusedTab('table-scroll-2-left');
        });
    }

    build($container) {

        super.build($container);
        this.createDataViews();
        this.showProperTabs(this.currentChannel);
        this.listen2Events();
    }
}

module.exports = View;