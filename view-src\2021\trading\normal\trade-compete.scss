.ncform {

    padding-right: 2px;

    .xtinput {

        &.subsidary {
            margin: -6px 0;
        }

        &.shorten {

            .el-input-number {
                width: 68%;
            }
        }

        &.button-row {
            margin-top: 14px;
        }

        .xtlabel {
            text-align: left;
        }

        // .el-select {

        //     position: relative;
        //     top: -8px;
        // }

        button {
            width: 100%;
        }
    }
}

.ncform-internal {

    overflow: hidden;

    .form-external {
        padding: 0 10px;
    }

    .direction-row {

        .el-radio-group {

            .el-radio-button {

                .el-radio-button__inner {
                    width: 100%;
                }
            }
        }
    }

    .limit-btn,
    .unit-txt {

        position: absolute;
        right: 0;
        z-index: 1;
        width: 24px;
        line-height: 24px;
        text-align: center;
        border-radius: 2px;
    }

    .prices-box {

        display: inline-block;
        line-height: 24px;
        width: 77%;
        position: relative;
        top: -7px;

        .price-item {

            display: inline-block;

            &:first-child {
                text-align: left;
            }

            &:last-child {

                text-align: right;
                float: right;
            }

            > a {
                padding-left: 4px;
            }
        }
    }

    .shortcuts-box {

        display: inline-block;
        width: 77%;
        position: relative;
        top: -7px;

        .method-item {

            display: inline-block;
            text-align: center;
            line-height: 18px;
        }
    }

    .effect-box {
        
        display: inline-block;
        line-height: 24px;
        position: relative;
        top: -7px;
    }
}

.xtpop-body {

    .switch-item {

        line-height: 36px;
        text-align: center;

        span {
            padding-right: 15px;
        }
    }
}