<div class="trade-form nc2form s-full-height">
	<div class="trade-form-inner nc2form-internal themed-box s-scroll-bar s-full-height">
		<div class="xtcontainer s-border-box s-full-height">

			<template>
				<div class="xtheader themed-header">
					<span>{{ channel.mean }}</span>
					<el-popover placement="bottom"
								title="交易设置"
								width="130"
								trigger="click">

						<a slot="reference" class="s-pull-right themed-color themed-hover-color" title="设置">
							<i class="iconfont icon-shezhi11"></i>
						</a>

						<div class="xtpop-body">
							<div class="switch-item">
								<span>校验资金</span>
								<el-switch v-model="uistates.checkOpt.cash"></el-switch>
							</div>
							<div class="switch-item">
								<span>校验持仓</span>
								<el-switch v-model="uistates.checkOpt.position"></el-switch>
							</div>
						</div>

					</el-popover>
				</div>
			</template>

			<template>

				<div class="form-external s-unselectable">
	
					<form class="xtform">
	
						<div class="xtinput direction-row">

							<el-radio-group v-model="vstates.creditType" 
											@change="handleCreditTypeChange" 
											class="s-full-width">

								<el-radio-button v-for="(item, item_idx) in creditTypes"
												 :key="item_idx"
												 :label="item.code"
												 :style="{ width: 100 / creditTypes.length + '%' }">{{ item.mean }}</el-radio-button>
							</el-radio-group>

						</div>

						<div class="xtinput">
							<span class="xtlabel themed-color">账号</span>
							<el-select placeholder="请选择账号" 
									   v-model="localStates.accountId"
									   @change="handleAccountChange"
									   :filterable="true">
									   
								<el-option v-for="(item, item_idx) in accounts"
										   :key="item_idx" 
										   :label="item.accountName" 
										   :value="item.accountId"></el-option>
							</el-select>
						</div>

						<div class="xtinput">
							<span class="xtlabel themed-color">开平</span>
							<span class="effect-box" :class="vstates.effectBoxClass">
								<el-radio-group v-model="vstates.businessCode" @change="handleBusinessChange">
									<el-radio v-for="(item, item_idx) in businesses"
											  :key="item_idx"
											  :label="item.code">{{ item.mean }}</el-radio>
								</el-radio-group>
							</span>
						</div>
	
						<div class="xtinput">
							<span class="xtlabel themed-color">代码</span>
							<el-autocomplete v-model="uistates.keywords"
											 :fetch-suggestions="suggest"
											 @keydown.native="handleUserInput"
											 @clear="handleClearIns"
											 @select="handleSelect" clearable>
	
								<template slot-scope="{ item: ins }">
									<span class="item-code">[{{ ins.instrument }}] </span>
									<span class="item-name">{{ ins.instrumentName }}</span>
								</template>

							</el-autocomplete>
						</div>
	
						<div class="xtinput shorten">
							<el-tooltip placement="right" content="调整限价/市价" v-show="false">
								<span class="limit-btn themed-bg-harder themed-bright s-not-allowed" title="调整价格模式">限</span>
							</el-tooltip>
							<span class="xtlabel themed-color">价格</span>
							<el-input-number placeholder="委托价格"
											 v-model="uistates.price"
											 :min="uistates.floor" 
											 :max="uistates.ceiling > 0 ? uistates.ceiling : 999999999"
											 :step="uistates.priceStep"
											 @change="handlePriceChange"></el-input-number>
						</div>

						<div class="xtinput subsidary">
							<span class="xtlabel"></span>
							<span class="prices-box">
								<span class="price-item themed-bright">
									跌停<a class="s-color-green s-hover-underline" 
											@click="setAsPrice(uistates.floor)">{{ precisePrice(uistates.floor) }}</a>
								</span>
								<span class="price-item themed-bright">
									涨停<a class="s-color-red s-hover-underline" 
											@click="setAsPrice(uistates.ceiling)">{{ precisePrice(uistates.ceiling) }}</a>
								</span>
							</span>
						</div>
	
						<div class="xtinput shorten">
							<span class="unit-txt themed-color" title="单位">({{ makeDisplayUnit() }})</span>
							<span class="xtlabel themed-color">{{ uistates.method.mean }}</span>
							<el-input-number :placeholder="'按' + uistates.method.mean"
											 :min="0"
											 :max="maxScale"
											 :step="scaleStep"
											 v-model="uistates.scale"
											 @change="handleScaleChange"></el-input-number>
						</div>
	
						<div class="xtinput subsidary" v-show="false">
							<span class="xtlabel"></span>
							<span class="shortcuts-box">
								<a v-for="(item, item_idx) in shortcuts"
								   class="method-item s-hover-underline s-opacity-7 s-opacity-hover"
								   :class="localStates.shortcut && localStates.shortcut.code == item.code ? 'themed-selected-member' : null"
								   :style="{ width: '25%', 'text-align': item.align }"
								   :title="item.tooltip"
								   @click="handleShortcutClick(item)">{{ item.text }}</a>
							</span>
						</div>
	
						<div class="xtinput button-row">
							<el-button :type="vstates.buttonType" @click="hope2Entrust">{{ vstates.buttonText }}</el-button>
						</div>
	
					</form>

				</div>

			</template>
		</div>		
	</div>
</div>