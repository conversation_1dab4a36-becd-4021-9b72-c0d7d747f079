const NormalTradeView = require('./trade-view-normal');
const { CreditOperation } = require('../../model/account');
const { CodeMeanItem } = require('../../model/message');

class View extends NormalTradeView {

    /**
     * 是否为，融资融券
     */
    get isCreditOpen() {
        return this.vstates.creditType == this.creditTypes.open.code;
    }

    /**
     * 是否为，两融归还
     */
    get isCreditClose() {
        return this.vstates.creditType == this.creditTypes.close.code;
    }

    get isBuy() {
        return this.getSelectedBiz().direction === this.directions.buy.code;
    }

    get isSell() {
        return this.getSelectedBiz().direction === this.directions.sell.code;
    }

    constructor(view_name) {

        super(view_name, { isCredit: true });

        /**
         * 两融业务大类
         */
        this.creditTypes = {
            
            open: new CodeMeanItem(1, '融资融券'),
            close: new CodeMeanItem(2, '两融归还'),
        };

        var dirs = this.systemTrdEnum.tradingDirection;
        var bsFlag = this.systemTrdEnum.businessFlag;
        var bsDict = this.businessDict = {

            buyOpen: new CreditOperation('A', bsFlag.credit.code, dirs.buy.code, '融资买入', 'danger'),
            sellOpen: new CreditOperation('B', bsFlag.credit.code, dirs.sell.code, '融券卖出', 'success'),

            buyClose: new CreditOperation('C', bsFlag.close.code, dirs.buy.code, '买券还券', 'danger'),
            sellClose: new CreditOperation('D', bsFlag.close.code, dirs.sell.code, '卖券还款', 'success'),
            securityClose: new CreditOperation('E', bsFlag.closeWithSecurity.code, dirs.sell.code, '现券还券', 'primary')
        };

        /**
         * 两融业务具体业务操作类型
         */
        this.business = {

            opens: [bsDict.buyOpen, bsDict.sellOpen],
            closes: [bsDict.buyClose, bsDict.sellClose, bsDict.securityClose],
        };

        /**
         * 适配当前模式的两融业务类型
         */
        this.sbusiness = (/** @returns {Array<CreditOperation>} */ () => { return []; })();
        this.sbusiness.merge(this.business.opens);
        var defaultBiz = this.business.opens[0];

        /**
         * 本地状态对象
         */
        this.vstates = {

            creditType: this.creditTypes.open.code,
            businessCode: defaultBiz.code,
            buttonType: defaultBiz.buttonType,
            buttonText: defaultBiz.mean,
            effectBoxClass: '',
        };
    }

    setAsDirection(direction) {
        
        var bsDict = this.businessDict;

        if (this.isCreditOpen) {
            this.vstates.businessCode = this.isBuy ? bsDict.sellOpen.code : bsDict.buyOpen.code;
        }
        else if (this.isCreditClose) {
            this.vstates.businessCode = this.isBuy ? bsDict.sellClose.code : bsDict.buyClose.code;
        }

        this.handleBusinessChange();
    }

    handleCreditTypeChange() {
        this.resetBusiness();
    }

    handleBusinessChange() {
        this.updateButton();
    }

    resetBusiness() {

        var states = this.vstates;
        var bizs = this.sbusiness;
        bizs.clear();

        if (this.isCreditOpen) {
            bizs.merge(this.business.opens);
        }
        else if (this.isCreditClose) {
            bizs.merge(this.business.closes);
        }

        states.businessCode = bizs.length > 0 ? bizs[0].code : null;
        states.effectBoxClass = this.isCreditClose ? 'minified' : '';
        this.updateButton();
    }

    getSelectedBiz() {

        var states = this.vstates;
        var selected = this.business.opens.find(x => x.code == states.businessCode);
        if (selected === undefined) {
            selected = this.business.closes.find(x => x.code == states.businessCode);
        }

        return selected;
    }

    updateButton() {

        var states = this.vstates;
        var selected = this.getSelectedBiz();
        states.buttonText = selected.mean;
        states.buttonType = selected.buttonType;
    }

    formatDirName() {
        return this.getSelectedBiz().mean;
    }

    sendOutOrder() {

        var uistates = this.uistates;
        var account = this.account;
        var dict = this.systemTrdEnum;
        var business = this.getSelectedBiz();

        this.renderProcess.send(this.systemEvent.transmitMsg2TradingServer, this.serverFunction.sendOrder, {

            strategyId: account.fundId,
            accountId: account.accountId,
            userId: this.userInfo.userId,
            price: uistates.price,
            volume: uistates.scale,
            instrument: this.states.instrument,
            priceType: dict.pricingType.fixedPrice.code,
            bsFlag: business.direction,
            businessFlag: business.business,
            positionEffect: this.isSpot ? 0 : this.uistates.effect,
            customId: 'normal-credit-' + this.helper.makeToken(),
            orderTime: null,
            hedgeFlag: dict.hedgeFlag.Speculate.code,
        });
    }

    createApp() {

        this.vueIns = new Vue({

            el: this.$container.querySelector('.trade-form-inner > .xtcontainer'),

            data: {

                channel: this.channel,
                creditTypes: this.helper.dict2Array(this.creditTypes),
                accounts: this.saccounts,
                businesses: this.sbusiness,
                shortcuts: this.shortcuts,
                states: this.states,
                uistates: this.uistates,
                localStates: this.localStates,
                vstates: this.vstates,
            },

            computed: {

                isBuy: () => { return this.isBuy; },
                isSell: () => { return this.isSell; },
                scaleStep: () => { return this.decideScaleStep(); },
                maxScale: () => { return this.decideMaxScale(); },
            },

            methods: this.helper.fakeVueInsMethod(this, [

                this.setAsPrice,
                this.precisePrice,
                this.handleUserInput, 
                this.handleSelect,
                this.suggest,
                this.handleClearIns,
                this.handleCreditTypeChange,
                this.handleAccountChange,
                this.handleBusinessChange,
                this.handlePriceChange,
                this.handleScaleChange,
                this.hope2Entrust,
                this.handleMethodChange,
                this.handleShortcutClick,
                this.makeDisplayUnit,
            ]),
        });
    }

    build($container) {

        super.build($container);
        this.createApp();
    }
}

module.exports = View;