const { TradeView } = require('../module/trade-view');
const { SimpleAccountItem } = require('../../model/account');
const { CodeMeanItem } = require('../../model/message');

class VolumeShortcut {

    /**
     * @param {Number} percentage 
     * @param {String} text 
     */
    constructor(percentage, text) {

        this.percentage = percentage;
        this.text = text;
    }
}

class View extends TradeView {

    get account() {
        return this.accounts.find(x => x.accountId == this.localStates.accountId);
    }

    constructor(view_name, options = { isCredit: false }) {

        super(view_name);

        /** 是否为两融交易界面 */
        this.isCreditView = options.isCredit === true;
        var dirs = this.directions;
        /** 适配当前场景的交易方向 */
        this.sdirections = [

            new CodeMeanItem(dirs.buy.code, dirs.buy.mean),
            new CodeMeanItem(dirs.sell.code, dirs.sell.mean),
        ];

        /**
         * 数量快捷方式
         */
        this.shortcuts = [

            new VolumeShortcut(0.25, '1/4'),
            new VolumeShortcut(0.33333, '1/3'),
            new VolumeShortcut(0.5, '1/2'),
            new VolumeShortcut(1, '全部'),
        ];

        var accountItem = new SimpleAccountItem({});

        /**
         * 所有账号
         */
        this.accounts = [accountItem];
        this.accounts.pop();

        /**
         * 当前交易单元的可用账号
         */
        this.saccounts = [accountItem];
        this.saccounts.pop();

        /**
         * 本地状态对象
         */
        this.localStates = {

            accountId: null,
            shortcut: null,
        };

        this.registerEvent('set-accounts', this.resetAccounts.bind(this));
    }

    /**
     * @param {Array<SimpleAccountItem>} accounts 
     */
    resetAccounts(accounts) {

        this.accounts.clear();
        this.accounts.merge(accounts);
        this.resetTypedAccounts();
        this.brocastAccountChange();
    }

    resetTypedAccounts() {
        
        var typeds = this.isCreditView ? this.accounts.filter(x => x.assetType == this.channel.assetType && x.isCredit)
                                       : this.accounts.filter(x => x.assetType == this.channel.assetType);
                                       
        var states = this.localStates;
        this.saccounts.clear();
        this.saccounts.merge(typeds);

        if (typeds.length == 0) {
            states.accountId = null;
        }
        else {

            let inside = typeds.some(x => x.accountId == states.accountId);
            if (!inside) {
                states.accountId = typeds[0].accountId;
            }
        }
    }

    brocastAccountChange() {
        this.trigger('selected-one-account', this, this.account);
    }

    handleAccountChange() {
        this.brocastAccountChange();
    }

    handleChannelChange(channel) {

        super.handleChannelChange(channel);
        this.resetTypedAccounts();
        this.brocastAccountChange();
    }

    handleShortcutClick() {
        this.interaction.showError('暂未实现快捷设置');
    }

    queryEffectName() {

        var effects = this.effects;
        switch (this.uistates.effect) {

            case effects.open.code : return effects.open.mean;
            case effects.close.code : return effects.close.mean;
            case effects.closeToday.code : return effects.closeToday.mean;
        }
    }

    makeBuyText() {
        return `买入${ this.isEffectApplicable() ? this.queryEffectName() : '' }`;
    }

    makeSellText() {
        return `卖出${ this.isEffectApplicable() ? this.queryEffectName() : '' }`;
    }

    formatDirName() {
        return this.isBuy ? this.directions.buy.mean : this.directions.sell.mean;
    }

    hope2Entrust() {

        if (this.saccounts.length == 0) {

            this.interaction.showError('无可用账号');
            return;
        }

        if (this.helper.isNone(this.localStates.accountId)) {

            this.interaction.showError('请选择交易账号');
            return;
        }

        var uistates = this.uistates;
        if (!this.states.instrument) {

            this.interaction.showError('请输入合约');
            return;
        }

        if (!(uistates.price > 0)) {

            this.interaction.showError('请输入有效委托价格');
            return;
        }

        if (!Number.isInteger(this.helper.safeDevide(uistates.price, uistates.priceStep))) {

            this.interaction.showError(`委托价格${uistates.price}，非最小价差${uistates.priceStep}，的整数倍`);
            return;
        }

        if (uistates.price < uistates.floor) {

            this.interaction.showError(`委托价格${uistates.price} < 跌停价${uistates.floor}`);
            return;
        }

        if (uistates.ceiling > 0 && uistates.price > uistates.ceiling) {

            this.interaction.showError(`委托价格${uistates.price} > 涨停价${uistates.ceiling}`);
            return;
        }

        if (!(uistates.scale > 0)) {

            this.interaction.showError('请输入委托数量');
            return;
        }

        let dirName = this.formatDirName();
        let dirCls = this.isBuy ? 's-color-red' : 's-color-green';
        let volume = uistates.scale;
        let amount = uistates.scale * uistates.price;
        
        let mentions = [

            ['账号', `${this.account.accountName}`],
            ['方向', dirName],
            ['合约', `${this.states.instrument} / ${this.states.instrumentName}`],
            ['价格', uistates.price],
            ['数量', typeof volume == 'number' ? volume.thousands() : volume],
            ['金额', typeof amount == 'number' ? amount.thousandsDecimal() : amount],
        ];

        let message = mentions.map(item => `<div><span>${item[0]}：</span><span class="${item[2] || ''}">${item[1]}</span></div>`).join('');
        this.interaction.showConfirm({

            title: '普通委托确认',
            message: message,
            confirmed: () => {

                this.sendOutOrder();
                this.trigger('normal-order-made');
                this.uistates.scale = 0;
                this.interaction.showSuccess('委托已发出');
            },
        });
    }

    sendOutOrder() {
        throw new Error('not implemented');
    }

    build($container) {
        super.build($container);
    }
}

module.exports = View;