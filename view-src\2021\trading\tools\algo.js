const { IView } = require('../../../../component/iview');
const TradeView = require('../algo/trade');
const AlgorithmOrderView = require('../algo/order');
const DataRecordsView = require('../algo/records');
const { AlgoOrderInfo } = require('../../model/message');
const { AlgoOrder } = require('../../../../model/algo-order');
const repoAlgo = require('../../../../repository/algorithm');

class View extends IView {

    constructor(view_name, is_standalone_window) {
        super(view_name, is_standalone_window, '算法交易');
        this.settings = {
            /** 最大并发的母单数量 */
            maxConcurrent: 3,
        };
    }

    createTradeView() {

        var $root = this.$tradeRoot = this.$container.querySelector('.block-trade');
        var tradeView = new TradeView('@2021/trading/algo/trade');
        tradeView.loadBuild($root);
        tradeView.registerEvent('place-algo-orders', this.handlePlaceRequest.bind(this));
        this.tradeView = tradeView;
    }

    isTooMuchRunning() {
        return this.algoOrderView.runnings.length >= this.settings.maxConcurrent;
    }

    /**
     * @param {Boolean} isPreview 
     * @param {AlgoOrderInfo|Array<AlgoOrderInfo>} algOrders
     * @param {Boolean} isCreditFirst
     */
    async handlePlaceRequest(algOrders, isCreditFirst) {

        if (this.isTooMuchRunning()) {

            this.interaction.showError(`当前尚有${this.settings.maxConcurrent}条（或以上）未完成的委托，请稍后下单`);
            return;
        }

        var message = null;
        
        if (algOrders instanceof AlgoOrderInfo) {

            let mentions = [

                ['方向', algOrders.directionName],
                ['账号', algOrders.accountName],
                ['算法', `${algOrders.algoId} / ${algOrders.algoName}`],
                ['数量', algOrders.volume.thousands() + '<label class="s-color-red s-bold">（股）</label>'],
            ];

            let paramStr = algOrders.algoParam;
            if (typeof paramStr == 'string' && paramStr.length > 0) {

                try {

                    let pobj = JSON.parse(paramStr);
                    let { strategyVolume, strategyRate } = pobj;
                    
                    if (strategyVolume > 0) {
                        mentions.push(['封单量', strategyVolume.thousands() + '<label class="s-color-red s-bold">（手）</label>']);
                    }
                    
                    if (strategyRate > 0) {
                        mentions.push(['总卖单金额', strategyRate.thousands() + '<label class="s-color-red s-bold">（万）</label>']);
                    }
                }
                catch(ex) {}
            }

            message = mentions.map(item => `<div><span>${item[0]}：</span><span class="${item[2] || ''}">${item[1]}</span></div>`).join('');
        }
        else {
            message = `篮子算法条数 = ${algOrders.length}`;
        }

        this.interaction.showConfirm({

            title: '算法下单确认',
            message: message,
            confirmed: async () => {
                
                var dtos = this.formOrders(algOrders instanceof AlgoOrderInfo ? [algOrders] : algOrders);
                var resp = await repoAlgo.order(dtos, isCreditFirst);
                
                if (resp.errorCode == 0) {
                    
                    setTimeout(() => { this.algoOrderView.trigger('reload-algo-orders'); }, 1000 * 1);
                    this.interaction.showSuccess('算法单已发送');
                }
                else {
                    this.interaction.showError(`算法单处理失败：${resp.errorCode}/${resp.errorMsg}`);
                }
            },
        });
    }

    /**
     * @param {Array<AlgoOrderInfo>} orders 
     */
    formOrders(orders) {

        return orders.map(item => ({

            userId: this.userInfo.userId,
            direction: item.direction,
            identityId: item.identityId,
            accountId: item.accountId,
            algorithmMappingId: item.algoId,
            algorithmType: item.algoType,
            algoParam: item.algoParam,
            instrument: item.instrument,
            volume: item.volume,
            effectiveTime: undefined,
            expireTime: undefined,
            limitAction: undefined,
            afterAction: undefined,
            taskName: undefined,
        }));
    }

    createAlgoOrderView() {

        var $root = this.$container.querySelector('.block-algo-orders');
        var view = new AlgorithmOrderView('@2021/trading/algo/order');
        view.loadBuild($root);
        view.registerEvent('algo-order-selected', this.handleAlgoOrderSelected.bind(this));
        this.algoOrderView = view;
    }

    /**
     * @param {AlgoOrder} algord
     */
    handleAlgoOrderSelected(algord) {

        /** 当前选中的算法母单 */
        this.algord = algord;
        this.recordView.trigger('set-context-algo-order', algord.id);
    }

    createDataRecords() {

        var $root = this.$container.querySelector('.data-records');
        var view = new DataRecordsView('@2021/trading/algo/records');
        view.loadBuild($root);
        this.recordView = view;
    }

    build($container) {

        super.build($container);
        this.createDataRecords();
        this.createTradeView();
        this.createAlgoOrderView();
        setTimeout(() => { this.simulateWinSizeChange(); }, 1000);
    }
}

module.exports = View;
