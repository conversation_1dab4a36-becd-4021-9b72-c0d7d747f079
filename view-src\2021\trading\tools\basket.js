const { IView } = require('../../../../component/iview');
const TradeView = require('../basket/trade');
const BasketView = require('../../fragment/basket');
const AccountGroupView = require('../../fragment/account-group');
const DataRecordsView = require('../basket/records');
const { WeightedAccountDetail, OrderPreview } = require('../../model/account');
const { TradeChannel, BasketOrderParam } = require('../../model/message');
const repoBasket = require('../../../../repository/basket');

class View extends IView {

    constructor(view_name, is_standalone_window) {
        super(view_name, is_standalone_window, '篮子交易');
    }

    createBasketView() {

        var $root = this.$container.querySelector('.block-basket');
        var view = new BasketView('@2021/fragment/basket');
        view.loadBuild($root);
        view.registerEvent('basket-local-changed', this.handleBasketLocalChange.bind(this));
        view.registerEvent('basket-changed', this.handleBasketChange.bind(this));
        this.basketView = view;
    }

    createAccountGroup() {

        var $root = this.$container.querySelector('.block-accounts');
        var view = new AccountGroupView('@2021/fragment/account-group', false, { fixAssetType: this.systemEnum.assetsType.stock.code });
        view.loadBuild($root, null, () => {

            var isShareMode = true;
            /** 设置为分摊模式 */
            view.trigger('trade-mode-change', isShareMode);
            /** 隐藏部分数据列 */
            view.trigger('toggle-show-columns', false);
        });
        view.registerEvent('selected-accounts-changed', this.handleAccountChange.bind(this));
        this.accountGroupView = view;
    }

    createDataRecords() {

        var astype = this.systemEnum.assetsType;
        var tchannel = new TradeChannel(1, '现货竞价', astype.stock.code, [astype.stock.code, astype.fund.code], { isSpot: true });
        var $root = this.$container.querySelector('.data-records');
        var view = new DataRecordsView('@2021/trading/basket/records', false, tchannel);
        view.loadBuild($root);
        this.recordView = view;
    }

    createTradeView() {

        var $tradeRoot = this.$tradeRoot = this.$container.querySelector('.block-trade');
        var tradeView = new TradeView('@2021/trading/basket/trade');
        tradeView.loadBuild($tradeRoot);
        tradeView.registerEvent('preview-basket-orders', this.handlePreviewRequest.bind(this));
        tradeView.registerEvent('place-basket-orders', this.handlePlaceRequest.bind(this));
        this.tradeView = tradeView;
    }

    /**
     * @param {Boolean} isLocalChanged 
     */
    handleBasketLocalChange(isLocalChanged) {

        /** 篮子列表，是否有未保存的改变 */
        this.isBasketLocalChanged = isLocalChanged;
        // console.log('factor > basket local change: ' + isLocalChanged);
    }

    /**
     * @param {Number} basketId 
     * @param {String} basketName 
     * @param {Boolean} isEtf 
     */
    handleBasketChange(basketId, basketName, isEtf) {

        /** 篮子列表选择的篮子ID */
        this.basketId = basketId;
        /** 篮子列表选择的篮子名称 */
        this.basketName = basketName;
        /** 篮子列表选择的篮子，是否为ETF篮子 */
        this.isEtf = isEtf;
        /** 当前是否选择了有效的篮子 */
        this.isBasketOk = this.helper.isNotNone(basketId);
        // console.log('factor > basket change: ' + basketId + '/' + basketName + '/' + isEtf);
        this.tradeView.trigger('set-as-basket', basketId, basketName, isEtf);
        this.spreadConditionChange();
    }

    /**
     * @param {Array<WeightedAccountDetail>} accounts 
     */
    handleAccountChange(accounts) {

        /** 账号组选定的账号 */
        this.accounts = accounts;
        /** 当前是否选择了至少一个账号 */
        this.areAccountsOk = accounts instanceof Array && accounts.length > 0;
        // console.log('factor > account change: ', accounts);
        this.spreadConditionChange();
    }

    spreadConditionChange() {
        this.tradeView.trigger('set-criteria-as-changed');
    }

    /**
     * @param {BasketOrderParam} trdParam 
     */
    async handlePreviewRequest(trdParam) {        
        this.check2Send(true, trdParam);
    }

    /**
     * @param {BasketOrderParam} trdParam 
     */
    handlePlaceRequest(trdParam) {
        this.check2Send(false, trdParam);
    }

    /**
     * @param {Boolean} isPreview 
     * @param {BasketOrderParam} trdParam 
     */
    async check2Send(isPreview, trdParam) {

        var isOk = this.isValidatedOk();
        if (typeof isOk == 'string') {
            return this.interaction.showError(isOk);
        }

        if (this.isBasketLocalChanged) {

            this.interaction.showConfirm({

                title: '操作确认',
                message: '当前篮子载入后，已发生变动，但尚未保存；<br/>预览或下单，将针对已保存的篮子，是否继续？',
                confirmed: () => {
                    this.sendRequest(isPreview, trdParam);
                },
            });
        }
        else {
            this.sendRequest(isPreview, trdParam);
        }
    }

    /**
     * @param {Boolean} isPreview 
     * @param {BasketOrderParam} trdParam 
     */
    async sendRequest(isPreview, trdParam) {

        if (isPreview) {

            let previewParam = this.formOrder(isPreview, trdParam);
            let resp = await repoBasket.makeBasketOrderPreview(previewParam);

            if (resp.errorCode != 0) {
                this.interaction.showError(`篮子交易预览失败：${resp.errorCode}/${resp.errorMsg}`);
            }
            else {
                this.setAsPreview(resp.data || []);
            }
        }
        else {

            let regu = trdParam.regulation;
            let regulars = [];
            regu.suspend && regulars.push('停牌');
            regu.cash && regulars.push('现金替代');
            regu.ceiling && regulars.push('涨停');
            regu.floor && regulars.push('跌停');
            
            let mentions = [

                ['方向', trdParam.directionName],
                ['篮子', `${trdParam.basketId} / ${trdParam.basketName}`],
                ['账户数', this.accounts.length],
                ['下单方式', trdParam.methodName],
                [trdParam.methodLabel, `${trdParam.scale} (${trdParam.methodUnit})`],
                ['跟盘价', trdParam.stageName],
                ['偏移量', trdParam.offset],
                ['限制', regulars.length == 0 ? '[无]' : regulars.join('/')],
            ];
    
            let message = mentions.map(item => `<div><span>${item[0]}：</span><span class="${item[2] || ''}">${item[1]}</span></div>`).join('');
            this.interaction.showConfirm({

                title: '操作确认',
                message: message,
                confirmed: () => {
                    
                    let orderParam = this.formOrder(isPreview, trdParam);
                    this.renderProcess.send(this.systemEvent.transmitMsg2TradingServer, this.serverFunction.sendBasketOrder, orderParam);
                    setTimeout(() => { this.recordView.trigger('reload-basket-tasks'); }, 1000 * 1);
                    this.interaction.showSuccess('篮子下单请求，已发送');
                },
            });
        }        
    }

    isValidatedOk() {

        if (!this.isBasketOk) {
            return '请选择交易篮子';
        }
        else if (!this.areAccountsOk) {
            return '请选择交易账号';
        }

        return true;
    }

    /**
     * @param {Boolean} isPreview 
     * @param {BasketOrderParam} trdParam 
     */
    formOrder(isPreview, trdParam) {

        var regul = trdParam.regulation;

        return {

            /** 篮子ID */
            basketId: trdParam.basketId,
            /** 价格类型 */
            priceFollowType: trdParam.stage,
            /** 偏移量 */
            priceDeviation: trdParam.offset,
            /** 方向：买入1，卖出-1，调仓0 */
            taskType: trdParam.direction,
            /** 预览模式，true返回预览结果，服务器不下单，false直接下单 */
            preview: isPreview,
            /** 篮子限制规则 */
            OrderRegulation: {

                excludeSuspension: regul.suspend,
                excludeCashSubstitution: regul.cash,
                excludeUpperLimit: regul.ceiling,
                excludeLowerLimit: regul.floor,
            },
            /** 操作的篮子数量，或者金额，或者比例 */
            executeVolume: trdParam.scale,
            /** 下单方式 */
            executeType: trdParam.method,
            /** 账号具体分配规则 */
            taskDetails: this.accounts.map(item => ({

                fundId: item.fundId,
                strategyId: item.strategyId,
                accountId: item.accountId,
                multiple: item.multiple,
            })),
        }
    }

    /**
     * @param {Array} feedbacks 
     */
    setAsPreview(feedbacks) {

        var previews = feedbacks.map(item => {

            let fundId = item.fundId;
            let strategyId = item.strategyId;
            let accountId = item.accountId;
            let instrument = item.instrument;
            let remark = item.remark;

            return new OrderPreview({

                id: `${fundId}/${strategyId || 'nostrategy'}/${accountId}/${instrument}`,
                accountId: accountId,
                accountName: item.accountName,
                fundId: fundId,
                fundName: item.fundName,
                strategyId: strategyId,
                strategyName: item.strategyName,
    
                instrument: instrument,
                instrumentName: item.instrumentName,
                assetType: item.assetType,
    
                direction: item.direction,
                positionEffect: 0,
                orderPrice: item.orderPrice,
                volumeOriginal: item.volumeOriginal,
                remark: typeof remark == 'string' ? { content: remark, clsname: 's-color-red' } : null,
            });
        });

        this.recordView.trigger('set-as-order-preview', true, previews);
    }

    build($container) {

        super.build($container);
        this.createBasketView();
        this.createAccountGroup();
        this.createTradeView();
        this.createDataRecords();
        setTimeout(() => { this.simulateWinSizeChange(); }, 1000);
    }
}

module.exports = View;
