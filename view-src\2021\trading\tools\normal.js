const { IView } = require('../../../../component/iview');
const ChannelView = require('../../fragment/trading-channel');
const PricesView = require('../module/prices');
const LevelsView = require('../module/level1');
const NormalTradeView = require('../normal/trade-view-normal');
const CompeteTradeView = require('../normal/trade-compete');
const CreditTradeView = require('../normal/trade-credit');
const AccountDetailView = require('../module/account-detail');
const AccountPositionView = require('../normal/account-positions');
const DataRecordsView = require('../normal/records');
const { SimpleAccountItem } = require('../../model/account');
const { Position } = require('../../../../model/position');
const { TradeChannel, InstrumentInfo, LevelMessage, TickData } = require('../../model/message');
const repoAccount = require('../../../../repository/account').repoAccount;

class View extends IView {

    /**
     * 适配当前交易渠道的交易视图
     */
    get focusedTradeView() {
        return this._focusedTradeView;
    }

    /**
     * 是否，交易视图，已加载完毕
     */
    get isTradingViewLoaded() {
        return this.modules.trading.loaded;
    }

    /**
     * 是否，默认的交易数据视图，已加载完毕
     */
    get isDataRecordViewLoaded() {
        return this.modules.dataRecord.loaded;
    }

    constructor(view_name, is_standalone_window) {

        super(view_name, is_standalone_window, '普通交易');

        var astype = this.systemEnum.assetsType;
        var accountItem = (/** @returns {SimpleAccountItem} */ function () { return null; })();
        this.channels = {

            spot: new TradeChannel(1, '现货竞价', astype.stock.code, [astype.stock.code, astype.fund.code], { isSpot: true }),
            credit: new TradeChannel(2, '融资融券', astype.stock.code, [astype.stock.code, astype.fund.code], { isCredit: true }),
            // future: new TradeChannel(3, '期货', astype.future.code, [astype.future.code], { isFuture: true }),
            // option: new TradeChannel(4, '期权', astype.option.code, [astype.option.code], { isOption: true }),
            
            // fix: new TradeChannel(5, '盘后定价', astype.stock.code, null, { isAfterClose: true }),
            // bond: new TradeChannel(6, '债券', astype.stock.code, [astype.bond.code], { isBond: true }),
            // apply: new TradeChannel(7, '申购认购', astype.stock.code, [astype.stock.code, astype.fund.code, astype.bond.code], { isApply: true }),
            // pledge: new TradeChannel(8, '质押出入库', astype.bond.code, [astype.bond.code], { isPledge: true }),
            // block: new TradeChannel(9, '大宗交易', astype.stock.code, [astype.stock.code, astype.bond.code, astype.fund.code], { isBlock: true }),
            // constr: new TradeChannel(10, '固收', astype.bond.code, [astype.bond.code], { isConstr: true }),
            // protocol: new TradeChannel(11, '协议回购', astype.buyback.code, [astype.buyback.code], { isProtocol: true }),
        };

        this.defaultChannel = this.channels.spot;
        this.states = {

            /** 当前的交易频道 */
            channel: this.defaultChannel,
            /** 当前的交易合约 */
            instrument: null,
            /** 当前的交易合约名称 */
            instrumentName: null,
        };

        this.accounts = [accountItem];
        this.accounts.pop();

        /** 各模块任务状态 */
        this.modules = {

            /** 交易视图模块 */
            trading: {

                /** 交易视图，是否已加载完毕 */
                loaded: false,
                /** 由交易模块，上报的当前账号 */
                account: accountItem,
            },

            /** 交易数据模块 */
            dataRecord: {

                /** 交易数据视图，是否已加载完毕 */
                loaded: false,
            },

            /**
             * 是否，视图之间的状态已协同
             */
            isLinked: false,
        };
    }

    createChannel() {

        var $root = this.$container.querySelector('.trading-channel');
        var view = new ChannelView('@2021/fragment/trading-channel');
        view.trigger('set-as-channels', this.helper.dict2Array(this.channels));
        view.registerEvent('channel-selected', this.handleChannelChange.bind(this));
        view.loadBuild($root);
        view.trigger('set-default-channel', this.defaultChannel);
        this.channelView = view;
    }

    /**
     * @param {TradeChannel} channel 
     */
    handleChannelChange(channel) {

        this.states.channel = channel;
        this.showProperTradeView();
        this.brocastChannel();
    }

    createPrices() {

        var $root = this.$container.querySelector('.block-prices');
        var view = new PricesView('@2021/trading/module/prices');
        view.registerEvent('price-selected', this.handlePriceSelect.bind(this));
        view.loadBuild($root);
        this.priceView = view;
    }

    /**
     * @param {Number} price 
     */
    handlePriceSelect(price) {
        this.focusedTradeView.trigger('set-as-price', price);
    }

    createLevel() {

        var $root = this.$container.querySelector('.block-levels');
        var view = new LevelsView('@2021/trading/module/level1');
        view.registerEvent('level-selected', this.handleLevelSelect.bind(this));
        view.loadBuild($root);
        this.levelView = view;
    }

    /**
     * @param {LevelMessage} message 
     */
    handleLevelSelect(message) {
        this.focusedTradeView.trigger('set-as-price-and-direction', message);
    }

    createTradeViews() {

        var $tradeRoot = this.$tradeRoot = this.$container.querySelector('.block-trade');
        this.$firstColumn = this.$container.querySelector('col.first-column');

        /**
         * 竞价交易视图
         */

        var competeView = new CompeteTradeView('@2021/trading/normal/trade-compete');
        var $root4Compete = document.createElement('div');
        $root4Compete.classList.add('s-full-height');
        $tradeRoot.appendChild($root4Compete);
        competeView.loadBuild($root4Compete);
        this.trade4Compete = competeView;
        this._focusedTradeView = competeView;

        /**
         * 两融交易视图
         */

        var creditView = new CreditTradeView('@2021/trading/normal/trade-credit');
        var $root4Credit = document.createElement('div');
        $root4Credit.classList.add('s-full-height');
        $tradeRoot.appendChild($root4Credit);
        creditView.loadBuild($root4Credit, null, () => { $root4Credit.style.display = 'none'; });
        this.trade4Credit = creditView;
        
        /**
         * 监听，从交易面板，抛出的各种典型事件
         */

        this.tradeViews = [competeView, creditView];
        this.tradeViews.forEach(thisView => {

            /** 监听账号选择事件 */
            thisView.registerEvent('selected-one-account', this.handleAccountSelected.bind(this));
            /** 监听合约选择事件 */
            thisView.registerEvent('selected-one-instrument', this.handleInstrumentSelected.bind(this));
            /** 下单动作发出，通知持仓数据模块 */
            var orderMade = 'normal-order-made';
            thisView.registerEvent(orderMade, _ => { this.accountPosView.trigger('reload-account-positions'); });
        });
    }

    /**
     * 根据交易渠道，展示适配的交易界面
     */
    showProperTradeView() {
        
        var channel = this.states.channel;
        var channels = this.channels;
        var expected;

        switch (channel) {

            case channels.spot:
            // case channels.future:
            // case channels.option:

                expected = this.trade4Compete;
                this.$firstColumn.setAttribute('width', 769);
                this.$tradeRoot.style.width = '254px';
                break;

            case channels.credit:

                expected = this.trade4Credit;
                this.$firstColumn.setAttribute('width', 789);
                this.$tradeRoot.style.width = '274px';
                break;
        }

        this.tradeViews.forEach(thisView => {

            if (thisView === expected) {
                if (thisView.$container) {
                    thisView.$container.parentElement.style.display = 'block';
                }
            }
            else {
                if (thisView.$container) {
                    thisView.$container.parentElement.style.display = 'none';
                }
            }
        });

        if (!expected) {
            this.trade4Compete.show();
        }

        this._focusedTradeView = expected || this.trade4Compete;
    }

    createAccountDetail() {

        var $root = this.$container.querySelector('.block-account-detail');
        var view = new AccountDetailView('@2021/trading/module/account-detail');
        view.loadBuild($root);
        this.accountDetailView = view;
    }

    createPositionView() {

        var $root = this.$container.querySelector('.block-account-position');
        var view = new AccountPositionView('@2021/trading/normal/account-positions');
        view.loadBuild($root, null, () => { 
            
            view.trigger('set-channel', this.defaultChannel);
            view.tableObj.setMaxHeight(222);
        });
        view.registerEvent('account-position-item-double-clicked', this.handlePosChecked.bind(this));
        this.accountPosView = view;
    }

    /**
     * @param {Position} position 
     */
    handlePosChecked(position) {
        this.focusedTradeView.trigger('pick-up-instrument-by-record', position.instrument, position.direction);
    }

    brocastChannel() {

        var channel = this.states.channel;
        var eventName = 'set-channel';
        this.priceView.trigger(eventName, channel);
        this.levelView.trigger(eventName, channel);
        this.focusedTradeView.trigger(eventName, channel);
        this.accountPosView.trigger(eventName, channel);
        this.recordView.trigger(eventName, channel);
    }

    /**
     * 对视图状态及任务调度，作交叉检测
     * 1. 各个视图，汇报各自状态
     * 2. 相关所有视图，全部就位后，完成视图初始任务的串联
     */
    doCrossCheck() {

        var ename = 'set-context-account';
        var selected = this.modules.trading.account;

        this.accountDetailView.trigger(ename, selected);
        this.accountPosView.trigger(ename, selected);

        /**
         * 首次视图联动，涉及到视图，是否已完成加载问题
         */
        if (!this.modules.isLinked) {

            /**
             * 主要关注，（默认展示的）交易数据视图，已经就位
             */
            if (this.isDataRecordViewLoaded && this.isTradingViewLoaded) {

                this.modules.isLinked = true;
                this.recordView.trigger(ename, selected);
            }
        }
        else {
            this.recordView.trigger(ename, selected);
        }
    }

    /**
     * @param {NormalTradeView} tradeView 
     * @param {SimpleAccountItem} selected 
     */
    handleAccountSelected(tradeView, selected) {

        /**
         * 1. 只由当前焦点状态的，交易视图，引起的账号切换
         * 2. 由交易渠道引起的变化，最终体现为账号切换
         */
        var isFocused = tradeView === this.focusedTradeView;
        if (!isFocused) {
            return;
        }

        this.modules.trading.loaded = true;
        this.modules.trading.account = selected;
        this.doCrossCheck();
    }

    /**
     * @param {InstrumentInfo} insInfo 
     */
    handleInstrumentSelected(insInfo) {

        /**
         * 向各个子视图，广播合约变化，子视图根据合约（合约可能为null）设置相应的信息
         */

        var eventName = 'set-as-instrument';
        this.priceView.trigger(eventName, insInfo);
        this.levelView.trigger(eventName, insInfo);
        this.focusedTradeView.trigger(eventName, insInfo);
        this.recordView.trigger(eventName, insInfo);

        /**
         * 提取上一个合约 & 与当前合约，并写入当前合约
         */

        var lastIns = this.states.instrument;
        var currentIns = insInfo ? insInfo.instrument : null;
        var currentInsName = insInfo ? insInfo.instrumentName : null;

        this.states.instrument = currentIns;
        this.states.instrumentName = currentInsName;

        /**
         * 首次合约信息变更时，启动监听
         */

        if (this.hasListened2TickChange === undefined) {

            /**
             * 是否已开启TICK数据监听
             */
            this.hasListened2TickChange = true;

            /**
             * 监听订阅回执
             */
            this.standardListen(this.serverEvent.subscribeTickReceived, (...args) => {
                this.handleTickChange(true, ...args);
            });
    
            /**
             * 监听TICK数据持续推送
             */
            this.standardListen(this.serverEvent.tickPriceChanged, (...args) => {
                this.handleTickChange(false, ...args);
            });
        }

        /**
         * 退订上一合约行情 & 订阅新合约行情
         */
        this.subscribeTick(lastIns, currentIns);
    }

    /**
     * 退订上一合约行情 & 订阅新合约行情
     * @param {String} lastIns 上一合约
     * @param {String} currentIns 当前合约
     */
    subscribeTick(lastIns, currentIns) {

        if (lastIns) {
            this.standardSend(this.systemEvent.unsubscribeTick, [lastIns], this.systemTrdEnum.tickType.tick);
        }

        if (currentIns) {
            this.standardSend(this.systemEvent.subscribeTick, [currentIns], this.systemTrdEnum.tickType.tick);
        }
    }

    /**
     * 处理TICK数据变化
     * @param {*} isReceipt 是否为订阅回执
     * @param {*} instrument 该TICK所属合约
     * @param {*} tickType 该TICK数据类型
     * @param {*} tick TICK数据本身
     */
    handleTickChange(isReceipt, instrument, tickType, tick) {

        if (instrument != this.states.instrument || tickType != this.systemTrdEnum.tickType.tick) {
            return;
        }

        var eventName = 'set-first-tick';
        var tickd = new TickData(tick);
        this.priceView.trigger(eventName, tickd);
        this.levelView.trigger(eventName, tickd);

        if (isReceipt) {
            this.focusedTradeView.trigger(eventName, tickd);
        }
    }

    createDataRecords() {

        var $root = this.$container.querySelector('.data-records');
        var view = new DataRecordsView('@2021/trading/normal/records', false, this.defaultChannel);
        view.loadBuild($root);
        view.registerEvent('default-tab-view-loaded', () => {

            this.modules.dataRecord.loaded = true;
            this.doCrossCheck();
        });

        this.recordView = view;
    }

    async requestAccounts() {

        this.accounts.clear();
        var resp = await repoAccount.queryUserScopeAccounts();

        if (resp.errorCode == 0) {

            let records = resp.data;
            let accounts = records instanceof Array ? records.map(x => new SimpleAccountItem(x)) : [];
            this.accounts.merge(accounts.sort(x => x.accountName));
        }
        else {
            this.interaction.showError('获取账号资金列表发生异常：' + resp.errorMsg);
        }

        this.distributeAccounts();
    }

    distributeAccounts() {

        this.tradeViews.forEach(thisView => {

            /**
             * 将账号列表推送给各个交易视图（而后，交易视图根据各自的要求，过滤需要的账号）
             */
            thisView.trigger('set-accounts', this.accounts);
        });
    }

    build($container) {

        super.build($container);
        this.createChannel();
        this.createPrices();
        this.createLevel();
        this.createTradeViews();
        this.createAccountDetail();
        this.createPositionView();
        this.createDataRecords();
        this.brocastChannel();
        this.showProperTradeView();
        this.requestAccounts();
        setTimeout(() => { this.simulateWinSizeChange(); }, 1000);
    }
}

module.exports = View;
