
const remote = require('@electron/remote');
const path = require('path');
const fs = require('fs');
const { IView } = require('../../../../component/iview');
const { SmartTable } = require('../../../../libs/table/smart-table');
const { ColumnCommonFunc } = require('../../../../libs/table/column-common-func');
const { repoFund } = require('../../../../repository/fund');
const { repoAccount } = require('../../../../repository/account');
const { repoTrading } = require('../../../../repository/trading');

class View extends IView {

    get lastWorkingFolder() {
        return localStorage.getItem('file-order-folder') || null;
    }
    
    set lastWorkingFolder(val) {
        localStorage.setItem('file-order-folder', val || '');
    }

    get lastScanFrequency() {

        let freq = parseInt(localStorage.getItem('file-order-frequency'));
        return typeof freq == 'number' && freq >= this.minimalInterval ? freq : this.minimalInterval;
    }

    set lastScanFrequency(val) {

        if (val >= this.minimalInterval) {
            localStorage.setItem('file-order-frequency', val);
        }
    }

    constructor(view_name) {

        super(view_name, false, '文件扫单');

        // 2次扫描，最小间隔（毫秒）
        this.minimalInterval = 10;
        this.today = new Date().toISOString().substr(0, 10).replace(/-/g, '');
        this.headers = {

            downloadAccountFinance: ['accountId','accountName','assetType','available','balance','closeProfit','commission','connectionStatus','frozenCommission','frozenMargin','fundShare','inMoney','margin','marketValue','outMoney','positionProfit','preBalance','risePercent','tradingDay','withdrawQuota'],
            downloadOrder: ['accountId','accountName','adjustFlag','assetType','cancelledVolume','commission','coverFlag','createTime','customId','direction','errorCode','errorMsg','exchangeOrderId','financeAccount','forceClose','foreign','frozenCommission','frozenMargin','frozenVolume','fundId','fundName','hedgeFlag','id','instrument','instrumentName','orderPrice','orderPriceType','orderStatus','orderTime','positionEffect','remark','strategyId','strategyName','tradeTime','tradedAmount','tradedPrice','tradedVolume','tradingDay','userId','userName','volumeOriginal','updateTime'],
            downloadPosition: ['accountId','accountName','assetType','avgPrice','closeProfit','direction','frozenTodayVolume','frozenVolume','fundId','fundName','id','instrument','instrumentName','marketValue','positionCost','todayPosition','tradingDay','usedCommission','usedMargin','yesterdayPosition'],
            downloadExchange: ['accountId','accountName','assetType','commission','direction','exchangeOrderId','fundId','fundName','id','instrument','instrumentName','orderId','positionEffect','strategyId','strategyName','today','tradeId','tradeTime','tradedPrice','tradingDay','userId','userName','volume'],
        };
    }

    chooseSetFolder() {

        var dialog_option = {

            title: '选择文件扫单工作目录', 
            properties: ['openDirectory'] 
        };

        const targetPath = remote.dialog.showOpenDialogSync(this.thisWindow, dialog_option);
        if (targetPath instanceof Array && targetPath.length > 0) {
            this.tbdata.folder = targetPath[0];
        }
    }

    startScan() {
        
        if (!this.tbdata.folder || !fs.existsSync(this.tbdata.folder)) {
            return this.interaction.showError('请指定有效的扫单工作目录');
        }
        else if (typeof this.tbdata.scanFrequency != 'number' || this.tbdata.scanFrequency < this.minimalInterval) {
            return this.interaction.showError(`请输入扫单频率，最低间隔${this.minimalInterval}毫秒`);
        }
        else if (this.tbdata.accountIds.length == 0) {
            return this.interaction.showError('请勾选要扫单的账号');
        }

        this.loggerSys.info('file-ordering > to start to scan');
        this.tbdata.running = true;
        this.network.isScanRunning = true;
        this.lastWorkingFolder = this.tbdata.folder;
        this.lastScanFrequency = this.tbdata.scanFrequency;
        this.renderProcess.send(this.systemEvent.fileOrderSetting, true, {

            folder: this.tbdata.folder, 
            frequency: this.tbdata.scanFrequency,
            accountIds: this.tbdata.accountIds,
        });
    }

    downloadAccountData(keep_silence) {

        if (!this.tbdata.folder || !fs.existsSync(this.tbdata.folder)) {
            keep_silence !== true ? this.interaction.showAlert('请指定有效的扫单工作目录') : null;
            return;
        }

        this.tbdata.lastDownloadTime = new Date().format('yyyy-MM-dd hh:mm:ss');
        keep_silence !== true ? this.interaction.showSuccess('下载账号全量数据，请求已发出') : null;
        this.begin2Download(keep_silence);
    }

    async begin2Download(keep_silence) {

        var working_dir = this.tbdata.folder;
        if (!working_dir || !fs.existsSync(working_dir)) {
            keep_silence !== true ? this.interaction.showError('指定的工作目录不存在') : null;
            return;
        }

        var download_folder = path.join(working_dir, 'downloads');
        if (!fs.existsSync(download_folder)) { 
            try {
                fs.mkdirSync(download_folder);
            }
            catch(ex) {
                keep_silence !== true ? this.interaction.showAlert(`下载目录创建失败：${download_folder}`) : null;
                return;
            }
        }

        var data_type = {

            finance: 'finance', 
            order: 'order', 
            position: 'position', 
            exchange: 'exchange' 
        };

        var account_list = await this.requestAccountIdList();
        if (account_list.length == 0) {
            keep_silence !== true ? this.interaction.showError('您名下没有绑定交易账号') : null;
            return;
        }

        this.loggerSys.debug(`file-ordering > to download account data/${JSON.stringify(account_list)}`);
        account_list.forEach(account => {

            let account_id = account.accountId;
            let account_name = account.accountName;
            this.requestAcountFinance(account_id, this.constructAccountDataFileName(account_id, account_name, download_folder, data_type.finance));
            this.requestAccountOrders(account_id, this.constructAccountDataFileName(account_id, account_name, download_folder, data_type.order));
            this.requestAccountPositions(account_id, this.constructAccountDataFileName(account_id, account_name, download_folder, data_type.position));
            this.requestAccountExchanges(account_id, this.constructAccountDataFileName(account_id, account_name, download_folder, data_type.exchange));
        });
    }

    async requestAccountIdList() {

        if (this.accounts instanceof Array) {
            return this.accounts;
        }

        var funds = await this.requestFunds();
        var account_map = {};

        funds.forEach(the_fund => {

            the_fund.accounts.forEach(the_act => {

                let account_id = the_act.accountId;
                let account_name = the_act.accountName;
                if (account_map[account_id] === undefined) {
                    account_map[account_id] = { accountId: account_id, accountName: account_name };
                }
            });
        });

        var diff_accounts = [];
        for(let key in account_map) {
            diff_accounts.push(account_map[key]);
        }

        return (this.accounts = diff_accounts);
    }

    stopScan() {

        this.tbdata.running = false;
        this.network.isScanRunning = true;
        this.loggerSys.info('file-ordering > to stop to scan');
        this.renderProcess.send(this.systemEvent.fileOrderSetting, false);
    }

    toggleShowHistory() {

        this.tbdata.showHistory = !this.tbdata.showHistory;
        this.seekHistory();
    }

    refreshHistory() {
        this.seekHistory();
    }

    createHistoryTable() {

        this.helper.extend(this, ColumnCommonFunc);
        this.tableObj = new SmartTable(this.$table, this.identifyRecord, this, {

            tableName: 'smt-tts',
            displayName: this.title,
            defaultSorting: { prop: 'resolveTime', direction: 'desc' },
        });

        this.tableObj.setPageSize(9999);
        this.tableObj.setMaxHeight(600);
    }

    async requestAccounts() {

        var resp = await repoAccount.batchGetAccountCash(this.systemEnum.assetsType.stock.code);
        var accounts = resp.data || [];
        this.tbdata.accounts.clear();

        if (accounts instanceof Array && accounts.length > 0) {

            /**
             * 需要对账号进行去重，此处拿到的账号有重复情况
             */

            let map = {};
            let results = [{ id: 0, name: null, only_name: null }].splice(1);
            accounts.forEach(item => {

                let account_id = item.accountId;
                let account_name = item.accountName;
                if (map[account_id]) {
                    return;
                }

                let data = { id: account_id, name: `${account_name} - ${account_id}`, only_name: account_name };
                results.push(data);
                map[account_id] = true;
            });

            let sorted = results.sort((a, b) => this.helper.compare(a.only_name, b.only_name));
            this.tbdata.accounts.merge(sorted);
        }
    }

    seekHistory(keep_silence) {

        if (this.isRefreshJobRunning === true) {

            if (keep_silence !== true) {
                this.interaction.showMessage('刷新动作进行中...');
            }
            return;
        }

        if (!this.tbdata.folder || !fs.existsSync(this.tbdata.folder)) {
            keep_silence !== true ? this.interaction.showAlert('请指定有效的扫单工作目录') : null;
            return;
        }

        var today = new Date().toISOString().substr(0, 10).replace(/-/g, '');
        var his_folder = path.join(this.tbdata.folder, 'entrusts', today + '_history');
        if (!fs.existsSync(his_folder)) {
            keep_silence !== true ? this.interaction.showMessage('尚未发现今天委托历史目录') : null;
            return;
        }

        this.tbdata.lastRefreshTime = new Date().format('yyyy-MM-dd hh:mm:ss');
        keep_silence !== true ? this.interaction.showSuccess('已开始扫描委托历史文件') : null;

        // 标识已开始扫描刷新委托文件清单
        this.isRefreshJobRunning = true;

        // 如果为人工操作，则清除列表
        if (keep_silence !== true) {
            this.tableObj.refill([]);
        }

        // check the path is folder or file
        fs.stat(his_folder, (read_dir_err, dir_descrip) => {

            if (read_dir_err) {

                keep_silence !== true ? this.interaction.showError(read_dir_err.message) : null;
                this.isRefreshJobRunning = false;
                return;
            }
            else if (dir_descrip.isFile()) {

                this.isRefreshJobRunning = false;
                keep_silence !== true ? this.interaction.showError('预期文件下单历史目录不存在') : null;
                return;
            }
            
            // read file(order) list in the path
            fs.readdir(his_folder, (err, files) => {
            
                if (err) {

                    this.isRefreshJobRunning = false;
                    keep_silence !== true ? this.interaction.showError('文件下单历史目录读取异常') : null;
                    return;
                }
                else if (!(files instanceof Array) || files.length == 0) {

                    this.isRefreshJobRunning = false;
                    // keep_silence !== true ? this.interaction.showInfo('还没有文件下单历史') : null;
                    return;
                }
    
                var total_item = files.length;
                var finished_item = 0;

                files.forEach(file_name => {

                    let file_full_path = path.join(his_folder, file_name);
                    let is_order = file_name.toLowerCase().indexOf('order.') == 0;
                    let is_cancel = file_name.toLowerCase().indexOf('cancel.') == 0;

                    try {

                        fs.stat(file_full_path, (read_file_err, file_descrip) => {

                            if (!read_file_err && file_descrip) {

                                let file_info = {

                                    resolveTime: file_descrip.ctime.format(), 
                                    fileName: file_name, 
                                    operType: is_order ? '下单' : is_cancel ? '撤单' : '其他' 
                                };

                                this.tableObj.putRow(file_info);
                            }

                            finished_item++;
                            if (finished_item == total_item) {
                                this.isRefreshJobRunning = false;
                            }
                        });
                    }
                    catch(ex) {
                        this.isRefreshJobRunning = false;
                    }
                });
            });
        });
    }

    downloadTemplate() {
        this.renderProcess.send(this.systemEvent.downloadTemplate, `file://${__dirname}/../../../../data-template/file-ordering-template.xlsx`);
    }

    handleAccountsChange() {
        // console.log(JSON.parse(JSON.stringify(this.tbdata.accountIds)));
    }

    createToolbar() {

        this.tbdata = {

            folder: this.lastWorkingFolder,
            scanFrequency: this.lastScanFrequency,
            downloadFrequency: 60,
            refreshFrequency: 60,
            lastDownloadTime: null,
            lastRefreshTime: null,
            running: false,
            showHistory: false,
            accountIds: [],
            accounts: [],
        };

        this.vueApp = new Vue({

            el: this.$toolbar,
            data: this.tbdata,
            methods: this.helper.fakeVueInsMethod(this, [

                this.chooseSetFolder,
                this.startScan,
                this.downloadAccountData,
                this.stopScan,
                this.toggleShowHistory,
                this.refreshHistory,
                this.downloadTemplate,
                this.handleAccountsChange,
            ]),

            watch: {

                showHistory: () => {

                    // if (this.tbdata.showHistory) {
                    //     this.$table.style.display = 'block';
                    // }
                    // else {
                    //     this.$table.style.display = 'none';
                    // }
                }
            }
        });
    }

    createNotice() {

        this.network = { 

            connected: null, 
            showNetworkNotice: false,
            isScanRunning: false,
        };

        this.noticeApp = new Vue({

            el: this.$notice,
            data: {
                network: this.network,
            },
            methods: this.helper.fakeVueInsMethod(this, [this.closeNetworkNotice]),
        });
    }

    closeNetworkNotice() {
        this.network.showNetworkNotice = false;
    }

    listen2Events() {

        this.renderProcess.on(this.systemEvent.networkStatusChange, (event, { ok, content }) => {

            if (!!ok) {

                /**
                 * 交易服务器连接恢复正常
                 */
                
                this.network.connected = true;
                this.hideNetworkNoticeTimer = setTimeout(() => {

                    this.network.showNetworkNotice = false;
                    this.hideNetworkNoticeTimer = null;
                }, 5000);

                this.loggerSys.info('file-ordering > trading server connection recovered');
            } 
            else {
                
                /**
                 * 监听交易服务器网络断线
                 */

                if(this.hideNetworkNoticeTimer) {
                    try {
                        clearTimeout(this.hideNetworkNoticeTimer);
                        this.hideNetworkNoticeTimer = null;
                    }
                    catch(ex) {}
                }

                this.network.connected = false;
                this.network.showNetworkNotice = true;
                this.loggerSys.info('file-ordering > trading server disconnected');
            }
        });

        this.renderProcess.on(this.serverEvent.algoOrderReceived, (event, reply) => {
            console.log({ reply });
            let { errorCode, errorMsg, data } = reply;

            if (errorCode == 0) {
                this.interaction.showSuccess('算法扫单已处理');
            }
            else {
                this.interaction.showError(`算法扫单错误：${errorCode}/${errorMsg}`);
            }
        });
    }

    scheduleJobs() {

        this.downloadJob = setInterval(() => { this.downloadAccountData(true); }, this.tbdata.downloadFrequency * 1000);
        this.refreshJob = setInterval(() => { this.seekHistory(true); }, this.tbdata.refreshFrequency * 1000);
    }

    constructAccountDataFileName(account_id, account_name, download_folder, data_name) {
        return path.join(download_folder, `${this.today}-${account_id}-${account_name}-${data_name}.csv`);
    }

    async requestFunds() {

        if (this.funds instanceof Array) {
            return this.funds;
        }

        var resp = await repoFund.getAll();
        var funds = resp.data || [];
        this.funds = funds instanceof Array ? funds.map(x => {

            let fund_id = x.id;
            let accounts = x.accounts;
            return { id: fund_id, accounts: accounts instanceof Array ? accounts : [] }; 
        }) : [];

        return this.funds;
    }

    async requestAcountFinance(account_id, full_path) {

        var resp = await repoAccount.getAccountDetail(account_id);
        var records = [resp.data || {}];
        var content = this.formatAllRecords(this.headers.downloadAccountFinance, records);
        fs.writeFile(full_path, content, { encoding: 'utf8' }, (err) => {
            if (err) {
                this.loggerSys.fatal('file-ordering > writing account finance records error > ' + err.message);
            }
        });
    }

    async requestAccountOrders(account_id, full_path) {

        var resp = await repoTrading.getTodayOrders({ accountId: account_id });
        var records = resp.data || [];
        var content = this.formatAllRecords(this.headers.downloadOrder, records);
        fs.writeFile(full_path, content, { encoding: 'utf8' }, (err) => {
            if (err) { 
                this.loggerSys.fatal('file-ordering > writing account order records error > ' + err.message); 
            }
        });
    }

    async requestAccountPositions(account_id, full_path) {

        var resp = await repoTrading.getTodayPositions({ accountId: account_id });
        var records = resp.data || [];
        var content = this.formatAllRecords(this.headers.downloadPosition, records);
        fs.writeFile(full_path, content, { encoding: 'utf8' }, (err) => { 
            if (err) { 
                this.loggerSys.fatal('file-ordering > writing account position records error > ' + err.message); 
            }
        });
    }

    async requestAccountExchanges(account_id, full_path) {

        var resp = await repoTrading.getTodayWaterflows({ accountId: account_id });
        var records = resp.data || [];
        var content = this.formatAllRecords(this.headers.downloadExchange, records);
        fs.writeFile(full_path, content, { encoding: 'utf8' }, (err) => {
            if (err) { 
                this.loggerSys.fatal('file-ordering > writing account exchange records error > ' + err.message); 
            }
        });
    }

    /**
     * @param {Array} fields 
     * @param {Array} records 
     */
    formatAllRecords(fields, records) {
        
        if (!(fields instanceof Array) || !(records instanceof Array)) {
            return '';
        }

        var lines = [fields.join(',')];
        records.forEach(rd => {

            let values = [];
            fields.forEach(field_name => {
                values.push(rd[field_name] === null || rd[field_name] === undefined ? '' : rd[field_name]); 
            });
            lines.push(values.join(','));
        });

        var content = lines.join('\n') + '\n';
        return Buffer.concat([Buffer.from('\xEF\xBB\xBF', 'binary'), Buffer.from(content, 'utf8')]);
    }

    identifyRecord(record) {
        return record.fileName;
    }

    dispose() {
        this.stopScan();
    }

    build($container) {

        super.build($container);
        this.$toolbar = this.$container.querySelector('.scan-toolbar');
        this.$notice = this.$container.querySelector('.scan-notice');
        this.$table = this.$container.querySelector('.table-scan-history');
        this.createToolbar();
        this.createNotice();
        this.createHistoryTable();
        this.listen2Events();
        this.scheduleJobs();
        this.seekHistory(true);
        this.requestAccounts(true);
    }
}

module.exports = View;