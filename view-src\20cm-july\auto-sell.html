<div class="v20cm-july auto-sell" v-cloak>
  <template>
    <algorithm-dialog :visible.sync="algorithmDialog.visible" :default-setting="remoteSettings.defaultParam" :item="algorithmDialog.item" @save="handleSave"></algorithm-dialog>
    <default-param-dialog :visible.sync="defaultParamDialog.visible" :item="remoteSettings.defaultParam" @save="handleDefaultParamSave"></default-param-dialog>
    <div class="regular-view">
      <div class="altorithm-list">
        <div class="action-row">
          <el-button icon="el-icon-plus" type="primary" @click="add">新建卖出算法</el-button>
          <el-button icon="el-icon-s-tools" type="primary" @click="setting">默认参数设置</el-button>
        </div>
        <!-- <div class="algorithm-item" :class="renderActiveClass(item)" v-for="item in algorithmRows" :key="item.id" @click="handleActive(item)">
          <div class="status" @click="switchStatus(item)" :class="getItemStatusClass(item.strikeBoardStatus)">
            <i class="status-icon" :class="getItemClass(item.strikeBoardStatus)"></i>
            <div class="label">{{renderStatus(item.strikeBoardStatus)}}</div>
          </div>
          <div class="info">
            <div v-for="(row, idx) in item.rows" :key="idx" class="info-row">
              <div v-for="info in row.vals" :key="info.label" class="label-value">
                <div class="info-label">{{ info.label }}</div>
                <div class="info-value">{{ info.formatter ? info.formatter(info.value) : info.value }}</div>
              </div>
            </div>
          </div>
          <div class="action">
            <div class="row">
              <el-button class="view-params" type="primary" @click="viewParams(item)">查看参数</el-button>
            </div>
            <div class="row">
              <el-button type="warning" :disabled="shouldDisableCancel(item)" @click="cancel(item)">撤单</el-button>
              <el-button type="success" @click="clone(item)">新建</el-button>
              <el-button :disabled="shouldDisable(item)" type="danger" @click="deleteRow(item)">删除</el-button>
            </div>
          </div>
        </div> -->
        <algorithm-table
          ref="table"
          :algorithm-rows="algorithmRows"
          @switch-status="switchStatus"
          @view-params="viewParams"
          @cancel="cancel"
          @clone="clone"
          @delete-row="deleteRow"
          @active="handleActive"
        ></algorithm-table>
      </div>
      <div class="order-list">
        <div class="table-header">
          <div class="table-title">成交汇总</div>
          <el-button icon="el-icon-refresh" type="primary" @click="refresh">刷新</el-button>
        </div>
        <div class="summary">
          <el-table class="summary-table" :data="summaryData">
            <el-table-column v-for="col in columns" :key="col.prop" :prop="col.prop" :label="col.label" :width="col.width">
              <template slot-scope="scope"> {{ col.render ? col.render(scope.row[col.prop]) : scope.row[col.prop] }} </template>
            </el-table-column>
          </el-table>
        </div>
        <div class="table-header">
          <div class="table-title">委托记录</div>
          <el-button icon="el-icon-refresh" type="primary" @click="refresh">刷新</el-button>
        </div>
        <div class="table-order">
          <table>
            <tr>
              <th label="证券代码" prop="instrument" min-width="100" formatter="shortizeCodeCol" searchable sortable overflowt></th>
              <th label="证券名称" prop="instrumentName" min-width="100" searchable sortable overflowt></th>
              <th label="委托金额" prop="orderAmount" min-width="100" align="right" thousands-int sortable overflowt></th>
              <th label="委托数量" prop="volumeOriginal" min-width="100" align="right" thousands-int sortable overflowt></th>
              <th label="成交" prop="tradedVolume" min-width="100" align="right" thousands-int sortable overflowt></th>
              <th label="委托时间" prop="orderTime" min-width="100" formatter="formatTime" sortable overflowt></th>
              <th label="委托价格" prop="orderPrice" min-width="100" align="right" formatter="formatPrice" sortable overflowt></th>
              <th label="成交价格" prop="tradedPrice" min-width="100" align="right" formatter="formatPrice" sortable overflowt></th>

              <th
                label="状态"
                prop="orderStatus"
                min-width="70"
                watch="orderStatus, errorMsg"
                align="right"
                formatter="formatOrderStatus"
                export-formatter="formatOrderStatusText"
                sortable
                overflowt
              ></th>

              <!-- <th label="方向" prop="direction" min-width="70" align="right" formatter="formatDirection" export-formatter="formatDirectionText" sortable overflowt></th> -->

              <th label="交易方式" min-width="100" prop="businessFlag" formatter="formatBusinessFlag" export-formatter="formatBusinessFlag" sortable></th>

              <th label="成交时间" prop="tradeTime" min-width="70" formatter="formatTime"></th>
              <th label="报单编号" prop="exchangeOrderId" min-width="130" searchable sortable overflowt></th>
              <th label="错误信息" prop="errorMsg" min-width="150" overflowt sortable></th>
              <th label="操作" prop="isCompleted" fixed="right" fixed-width="60" formatter="formatActions" exportable="false"></th>
            </tr>
          </table>
        </div>
      </div>
    </div>
  </template>
</div>
