@mixin el-table-styles {
  &::before {
    background-color: transparent;
  }
  th,
  tr,
  td {
    background-color: #1e2836;
    transition: none;
  }
  .el-table__body {
    tr {
      &.current-row {
        & > td {
          background-color: #2c79f2;
        }
      }
      &.hover-row {
        & > td {
          background-color: #324f80;
        }
      }
    }
  }
  .el-table__header-wrapper,
  .el-table__fixed-header-wrapper {
    th {
      padding: 0 10px;
      border-right: 1px solid #0c1016;
      background-color: #1e2836;
      line-height: 25px;
      border-bottom-color: #0c1016;
      .cell {
        font-size: 16px;
        color: #8cb5ed;
        padding-left: 0 !important;
      }
    }
  }
  .el-table__fixed-right::before,
  .el-table__fixed::before {
    background-color: transparent;
  }
  .el-table__row {
    td {
      border-bottom-color: #0c1016;
      border-right: 1px solid #0c1016;
      font-size: 16px;
      padding: 0;
      .cell {
        color: #fff;
        line-height: 32px;
        .flag {
          height: unset;
          padding: 3px 6px;
          * {
            font-size: 16px;
          }
        }
        .status {
          display: flex;
          align-items: center;
          justify-content: center;
          cursor: pointer;
          &.pause {
            i {
              color: #8bc34a;
            }
          }
          &.play {
            i {
              color: #ff9800;
            }
          }
          &.check {
            i {
              color: #4caf50;
            }
          }
          &.create {
            i {
              color: #9e9e9e;
            }
          }
          .status-icon {
            font-size: 16px;
          }
        }
        .el-progress {
          &-bar {
            padding-right: 30px;
            margin-right: -35px;
          }
          &__text {
            color: #fff;
          }
        }
      }
    }
  }
  .el-table__empty-block {
    min-height: 10px;
    .el-table__empty-text {
      line-height: 25px;
      font-size: 16px;
      color: #fff;
    }
  }
}
.auto-sell {
  height: 100%;
  .regular-view {
    display: flex;
    flex-direction: column;
    width: 100%;
    height: 100%;
    .altorithm-list {
      height: 500px;
      overflow-y: auto;
      background-color: rgb(26, 33, 43);
      border: 4px solid #0c1016;
      .el-table {
        @include el-table-styles;
        background-color: transparent;
      }
      .action-row {
        display: flex;
        justify-content: space-between;
        align-items: center;
        height: 40px;
        padding: 0 10px;

        .el-button * {
          font-size: 16px;
        }
      }
      .algorithm-item {
        display: flex;
        align-items: center;
        width: 100%;
        height: 60px;
        box-shadow: 0px 0px 13px 0px rgb(0 0 0 / 30%);
        margin-bottom: 10px;
        border: 1px solid transparent;
        box-sizing: border-box;
        cursor: pointer;
        &.active {
          box-shadow: 0px 0px 20px 0px rgb(4 218 247 / 20%) inset;
          background: linear-gradient(0deg, rgba(0, 56, 139, 0.2), rgba(0, 56, 139, 0.2));
          border: 1px solid #0c99fe;
        }
        .status {
          width: 80px;
          display: flex;
          flex-direction: column;
          height: 100%;
          align-items: center;
          justify-content: center;
          cursor: pointer;
          height: 100%;
          &.pause {
            background-color: #2196f3;
          }
          &.play {
            background-color: #ff9800;
          }
          &.check {
            background-color: #4caf50;
          }
          &.create {
            background-color: #607d8b;
          }
          .status-icon {
            font-size: 20px;
            margin-bottom: 10px;
          }
          .label {
            font-size: 16px;
          }
        }
        .info {
          min-width: 1px;
          flex: 1;
          display: flex;
          flex-direction: column;
          padding: 0 10px;
          .info-row {
            flex: 1;
            display: flex;
            align-items: center;
            .label-value {
              flex: 1;
              display: flex;
              align-items: center;
              .info-label {
                margin-right: 10px;
              }
            }
          }
        }
        .action {
          width: 120px;
          padding-right: 4px;
          .row {
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100%;
            & + .row {
              margin-top: 5px;
            }
            .el-button {
              padding: 0 4px;
              height: 18px;
              flex: 1;
              // &.view-params {
              //   width: 100%;
              // }
              & + .el-button {
                margin-left: 5px;
              }
            }
          }
        }
      }
    }
    .order-list {
      min-height: 1px;
      flex: 1;
      background-color: rgb(26, 33, 43);
      .summary {
        border: 4px solid #0c1016;
        border-top: none;
        * {
          background-color: transparent;
          color: #fff;
        }
        .el-table {
          @include el-table-styles;
        }
      }
      .table-header {
        display: flex;
        align-items: center;
        height: 32px;
        padding-left: 10px;
        border-left: 4px solid #0c1016;
        border-right: 4px solid #0c1016;
        border-bottom: 4px solid #0c1016;
        .table-title {
          font-size: 16px;
          margin-right: 10px;
        }
      }
      .table-order {
        border-left: 4px solid #0c1016;
        border-right: 4px solid #0c1016;
        height: calc(100% - 128px);

        .header-cell {
          line-height: 32px;
          font-size: 16px;
        }

        .smart-table-body-inner {
          td {
            font-size: 16px;
          }
        }
      }
    }
  }
  .algorithm-dialog {
    .el-dialog__title {
      font-size: 16px;
    }
    .form {
      .el-form-item {
        &__label {
          color: #fff;
          font-size: 16px;
          display: flex;
          align-items: center;
          justify-content: end;
          .form-label {
            display: flex;
            align-items: center;
            .label {
              margin-right: 2px;
            }
          }
        }
        &__content {
          & > * {
            width: 100%;
          }
          .number-wrapper {
            display: flex;
            align-items: center;
            .el-input-number {
              min-width: 1px;
              flex: 1;
            }
            .unit {
              width: 0;
              margin-left: 15px;
              white-space: nowrap;
              display: flex;
              justify-content: center;
              font-size: 16px;
            }
          }
        }
        .el-input__inner {
          font-size: 16px;
        }
      }
    }
    .param-setting {
      .hint-row {
        display: flex;
        align-items: center;
        padding-left: 40px;
        margin-bottom: 20px;
        height: 20px;
        .title {
          color: #fff;
        }
        .hint {
          color: #eee;
          margin-left: 20px;
          font-size: 16px;
        }
      }
    }
    .el-dialog__footer {
      .el-button * {
        font-size: 16px;
      }
    }
  }
}

.el-select-dropdown__item {
  font-size: 16px;
}