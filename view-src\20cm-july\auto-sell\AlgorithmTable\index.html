<el-table row-key="id" highlight-current-row :height="tableHeight" class="algorithm-table" :data="algorithmRows" @current-change="handleActive">
  <el-table-column show-overflow-tooltip width="80" fixed="left" label="启停">
    <template slot-scope="scope">
      <div class="status" @click="switchStatus(scope.row)" :class="getItemStatusClass(scope.row.strikeBoardStatus)">
        <i class="status-icon" :class="getItemClass(scope.row.strikeBoardStatus)"></i>
      </div>
    </template>
  </el-table-column>
  <el-table-column show-overflow-tooltip v-for="col in columns" :key="col.prop" :prop="col.prop" :label="col.label" :width="col.width">
    <template slot-scope="scope"> {{ col.render ? col.render(scope.row) : scope.row[col.prop] }} </template>
  </el-table-column>
  <el-table-column show-overflow-tooltip width="150" label="进度">
    <template slot-scope="scope">
      <el-progress :percentage="getPercentage(scope.row)" :stroke-width="14" color="#67c23a"></el-progress>
    </template>
  </el-table-column>
  <el-table-column show-overflow-tooltip fixed="right" width="260" label="操作">
    <template slot-scope="scope">
      <el-button class="flag" size="mini" type="info" @click="viewParams(scope.row)">查看参数</el-button>
      <el-button class="flag" type="primary" :disabled="shouldDisableCancel(scope.row)" @click="cancel(scope.row)">撤单</el-button>
      <el-button class="flag" type="success" @click="clone(scope.row)">新建</el-button>
      <el-button class="flag" :disabled="shouldDisable(scope.row)" type="danger" @click="deleteRow(scope.row)">删除</el-button>
    </template>
  </el-table-column>
</el-table>
