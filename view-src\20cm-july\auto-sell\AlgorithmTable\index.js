const BaseComponent = require('../../../admin/indicator/components/BaseComponent');
const { algorithmTypeEnum, fullParams, fullParamsEnum } = require('../../../../config/auto-sell');
const { TaskObject, TaskStatus, TaskStatuses } = require('../../objects');

module.exports = class DragWidget extends BaseComponent {
  constructor() {
    super(__dirname);
    const self = this;
    this.options = {
      props: {
        algorithmRows: {
          type: Array,
        },
      },
      data() {
        return {
          tableHeight: 430,
          helper: self.parent.helper,
          columns: [
            {
              label: '状态',
              prop: 'strikeBoardStatus',
              width: 80,
              render: (row) => {
                let status = row.strikeBoardStatus;
                let matched = TaskStatuses.find((x) => x.value == status);
                return matched ? matched.label : status;
              },
            },
            {
              label: '股票名称',
              prop: 'instrumentName',
            },
            {
              label: '股票代码',
              prop: 'instrument',
            },
            {
              label: '算法名称',
              prop: 'algorithmType',
              render: (row) => {
                let type = row.boardStrategy.strategyType;
                return Object.values(algorithmTypeEnum).find((x) => x.value == type).label;
              },
            },
            {
              label: '计划总量',
              prop: 'targetVolume',
            },
            {
              label: '完成总量',
              prop: 'tradedVolume',
            },
            {
              label: '未成委托量',
              prop: 'supplementVolume',
            },
            {
              label: '委托笔数',
              prop: 'totalOrderCount',
            },
            {
              label: '创建时间',
              prop: 'createTime',
              width: 200,
              render: (row) => {
                return this.helper.time2String(row.createTime);
              },
            },
          ],
        };
      },
      computed: {},
      watch: {},
      methods: {
        getPercentage(item) {
          let targetVolume = item.targetVolume;
          let tradedVolume = item.tradedVolume;
          return targetVolume > 0 ? Math.round((tradedVolume / targetVolume) * 100) : 0;
        },
        handleActive(item) {
          this.$emit('active', item);
        },
        switchStatus(item) {
          this.$emit('switch-status', item);
        },
        getItemStatusClass(status) {
          if (TaskObject.isTaskRunning(status)) {
            return 'pause';
          } else if (TaskObject.isTaskPaused(status)) {
            return 'play';
          } else if (TaskObject.isTaskFinished(status)) {
            return 'check';
          } else if (TaskObject.isTaskCreated(status)) {
            return 'create';
          }
          return '';
        },
        getItemClass(status) {
          if (TaskObject.isTaskRunning(status)) {
            return 'el-icon-video-pause';
          } else if (TaskObject.isTaskPaused(status)) {
            return 'el-icon-video-play';
          } else if (TaskObject.isTaskFinished(status)) {
            return 'el-icon-check';
          } else if (TaskObject.isTaskCreated(status)) {
            return 'el-icon-video-play';
          }
          return '';
        },
        viewParams(item) {
          this.$emit('view-params', item);
        },
        shouldDisableCancel(item) {
          return TaskObject.isTaskCreated(item.strikeBoardStatus);
        },
        cancel(item) {
          this.$emit('cancel', item);
        },
        clone(item) {
          this.$emit('clone', item);
        },
        shouldDisable(item) {
          return TaskObject.isTaskRunning(item.strikeBoardStatus);
        },
        deleteRow(item) {
          this.$emit('delete-row', item);
        },
      },
    };
  }
};
