<div class="trade-view-block">
	
	<div class="view-toolbar s-pdl-10 s-pdr-10">

		<span class="instrument-info">
			<span v-if="states.instrument">{{ states.instrumentName }}</span>
			<span v-else>---</span>
		</span>

		<template v-if="typeof states.increaseRate == 'number'">
			<span class="s-pull-right" :class="states.colorClass">{{ precisePercent(states.increaseRate) }}%</span>
		</template>

	</div>

	<div class="s-full-height">

		<div 
			v-for="(item, item_idx) in levels" 
			:key="item_idx" 
			:class="'level-item-' + item_idx" 
			class="level-item"
			@click="setAsPrice(item.price)">

			<span class="level-name s-ellipsis">{{ item.label }}</span>
	
			<span class="level-price s-ellipsis s-unselectable" :class="item.colorClass">
				{{ precisePrice(item.price) }}
			</span>

			<span class="level-hands s-ellipsis s-unselectable" :class="item.colorClass">
				{{ simplyHands(item.hands) }}
			</span>
	
		</div>

	</div>

</div>