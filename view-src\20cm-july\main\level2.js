const { IView } = require('../../../component/iview');
const { SubscribeManager } = require('../subscribe-manager');
const { TickData, PriceLevel } = require('../../2021/model/message');
const { <PERSON><PERSON><PERSON>el<PERSON> } = require('../../../libs/helper-biz');
const { NumberMixin } = require('../../../mixin/number');
const { DatetimeMixin } = require('../../../mixin/date-time');
const { repoInstrument } = require('../../../repository/instrument');

/**
 * @returns {Array<PriceLevel>}
 */
function MakeLevels(count) {

    var levels = [];

    for (let idx = count; idx >= 1; idx--) {
        levels.push(new PriceLevel(false, '卖' + idx, 0, 0));
    }

    for (let idx = 1; idx <= count; idx++) {
        levels.push(new PriceLevel(true, '买' + idx, 0, 0));
    }

    return levels;
}

module.exports = class Level2View extends IView {

    constructor() {

        super('@20cm-july/main/level2', false, '五档行情');

        /** 档位总数 */
        this.tlevel = 5;
        this.levels = MakeLevels(this.tlevel);
        this.registerEvent('test-instrument', this.testInstrument.bind(this));
        this.submgr = new SubscribeManager(this);
    }

    getLastPrice() {
        return this.states.prices.lastPrice;
    }

    /**
     * 设置为当前合约
     */
    setAsInstrument(instrument, instrumentName) {

        var ref = this.states;
        var last = ref.instrument;

        /**
         * 合约未变更，无需订阅
         */
        if (instrument == last) {
            return;
        }

        this.resetProperties();
        this.resetLevels();
        
        ref.instrument = instrument;
        ref.instrumentName = instrumentName;
        this.subscribeTick(last, instrument);

        if (instrument) {
            this.requestLimitedPrice(instrument, instrumentName);
        }
    }

    /**
     * 检查某合约，是否为当前行情面板的合约
     * @param {String} instrument
     */
    testInstrument(instrument) {

        if (this.states.instrument != instrument) {
            return;
        }

        this.subscribeTick(instrument, null);
        this.resetProperties();
        this.resetLevels();
    }

    resetProperties() {

        var ref = this.states;
        ref.instrument = null;
        ref.instrumentName = null;
        ref.prices.yesterdayClose = null;
        ref.prices.lastPrice = null;
        ref.prices.ceiling = null;
        ref.prices.floor = null;
        ref.increaseRate = null;
        ref.colorClass = null;
    }

    resetLevels() {

        /**
         * 合约产生变化时，首先将档位显示重置
         */
        this.levels.forEach(level => { level.update(0, 0, 0); });
    }

    /**
     * @param {*} last 上个合约
     * @param {*} current 当前合约
     */
    subscribeTick(last, current) {

        /**
         * 首次合约信息变更时，启动监听
         */

        if (this.hasListened2TickChange === undefined) {

            /**
             * 是否已开启TICK数据监听
             */
            this.hasListened2TickChange = true;

            /**
             * 监听订阅回执
             */
            this.standardListen(this.serverEvent.subscribeTickReceived, (...args) => {
                this.handleTickChange(true, ...args);
            });

            /**
             * 监听TICK数据持续推送
             */
            this.standardListen(this.serverEvent.tickPriceChanged, (...args) => {
                this.handleTickChange(false, ...args);
            });
        }

        const ref = this.systemTrdEnum.tickType;

        if (last) {
            
            this.submgr.unsubscribe(last, ref.tick);
            this.submgr.unsubscribe(last, ref.transaction);
        }

        if (current) {

            this.submgr.subscribe(current, ref.tick, true);
            this.submgr.subscribe(current, ref.transaction, true);
        }
    }

    async requestLimitedPrice(instrument, instrumentName) {

        var resp = await repoInstrument.queryPrice(instrument);
        var { errorCode, errorMsg, data } = resp;
        var { assetType, preClosePrice, lastPrice, lowerLimitPrice, optionType, priceTick, strikePrice, upperLimitPrice, volumeMultiple } = data || {};
        var ref = this.states.prices;

        if (errorCode == 0 && data && upperLimitPrice > 0) {

            ref.yesterdayClose = preClosePrice;
            ref.lastPrice = lastPrice;
            ref.ceiling = upperLimitPrice;
            ref.floor = lowerLimitPrice;
            let rate = 100 * (lastPrice - preClosePrice) / (preClosePrice || 99999999);
            this.states.increaseRate = rate;
            this.states.colorClass = rate > 0 ? 's-color-red' : rate < 0 ? 's-color-green' : '';
        }
        else {

            ref.yesterdayClose = 0;
            ref.lastPrice = 0;
            ref.ceiling = 9999;
            ref.floor = 0;
            this.states.increaseRate = null;
            this.states.colorClass = null;
            this.interaction.showError(`${instrumentName}，涨跌停价格未获得：${errorCode}/${errorMsg}`);
        }
    }

    /**
     * 处理TICK数据变化
     * @param {*} isReceipt 是否为订阅回执
     * @param {*} instrument 该TICK所属合约
     * @param {*} tickType 该TICK数据类型
     * @param {*} tick TICK数据本身
     */
    handleTickChange(isReceipt, instrument, tickType, tick) {
        const ref = this.states;

        if (instrument != ref.instrument) {
            return;
        }
        else if (tickType == this.systemTrdEnum.tickType.tick) {
                        
            let tickd = new TickData(tick);
            this.updateLevels(tickd);
            let rate = (tickd.latest / tickd.preclose - 1) * 100;
            ref.increaseRate = rate;
            ref.colorClass = rate > 0 ? 's-color-red' : rate < 0 ? 's-color-green' : '';
            this.calcPriceCage(tickd);
        }
    }

    calcPriceCage(tick) {
        let lowerSellLimit = 0;
        let upperBuyLimit = 0;

        if (tick.buys[0].price > 0) {
            lowerSellLimit = Math.min(tick.buys[0].price * 0.98, tick.buys[0].price - 0.1);
        } else if (tick.sells[0].price > 0) {
            lowerSellLimit = Math.min(tick.sells[0].price * 0.98, tick.sells[0].price - 0.1);
        } else if (tick.latest > 0) {
            lowerSellLimit = Math.min(tick.latest * 0.98, tick.latest - 0.1);
        } else {
            lowerSellLimit = Math.min(tick.preclose * 0.98, tick.preclose - 0.1);
        }


        if (tick.sells[0].price > 0) {
            upperBuyLimit = Math.max(tick.sells[0].price * 1.02, tick.sells[0].price + 0.1);
        } else if (tick.buys[0].price > 0) {
            upperBuyLimit = Math.max(tick.buys[0].price * 1.02, tick.buys[0].price + 0.1);
        } else if (tick.latest > 0) {
            upperBuyLimit = Math.max(tick.latest * 1.02, tick.latest + 0.1);
        } else {
            upperBuyLimit = Math.max(tick.preclose * 1.02, tick.preclose + 0.1);
        }

        this.trigger('cage-price-change', lowerSellLimit, upperBuyLimit);
    }

    /**
     * @param {TickData} tick 
     */
    updateLevels(tick) {

        const total = this.tlevel;

        for (let idx = 0; idx < total; idx++) {

            let sdata = tick.sells[idx];
            this.levels[total - 1 - idx].update(sdata.price, tick.preclose, Math.ceil(sdata.hands * 0.01), tick.sells[0].price);
        }

        for (let idx = 0; idx < total; idx++) {

            let bdata = tick.buys[idx];
            let price = bdata.price > 0 ? bdata.price : tick.buys[0].price;
            this.levels[total + idx].update(bdata.price, tick.preclose, Math.ceil(bdata.hands * 0.01), tick.buys[0].price);
        }
    }

    handleReconnect() {
        this.subscribeTick(undefined, this.states.instrument);
    }

    createApp() {

        this.states = {

            instrument: null,
            instrumentName: null,
            increaseRate: null,
            colorClass: '',

            prices: {

                yesterdayClose: null,
                lastPrice: null,
                ceiling: null,
                floor: null,
            },
        };

        new Vue({

            el: this.$container.firstElementChild,
            data: {

                states: this.states,
                levels: this.levels,
            },
            mixins: [NumberMixin, DatetimeMixin],
            methods: this.helper.fakeVueInsMethod(this, [

                this.simplyHands,
                this.precisePercent,
                this.precisePrice,
                this.setAsPrice,
            ]),
        });
    }

    /**
     * 简化手数显示
     * @param {Number} volume 
     */
    simplyHands(volume) {
        return volume + ' 手';
    }

    precisePercent(price) {
        return typeof price == 'number' ? price.toFixed(2) : price;
    }

    getPricePrecision() {
        
        let instrument = this.states.instrument;
        return BizHelper.getPricePrecision(instrument);
    }

    precisePrice(price) {
        return typeof price == 'number' ? price.toFixed(this.getPricePrecision()) : price;
    }

    setAsPrice(price) {
        this.trigger('level-selected', price);
    }

    build($container) {

        super.build($container);
        this.createApp();
        this.renderProcess.on(this.systemEvent.tradingServerReestablished, () => { this.handleReconnect(); });
    }
};