const { BaseView } = require('../base-view');
const { SmartTable } = require('../../../libs/table/smart-table');
const { Order } = require('../../../model/order');
const { Position } = require('../../../model/position');
const { ModelConverter } = require('../../../model/model-converter');
const { repoOrder } = require('../../../repository/order');
const { repoPosition } = require('../../../repository/position');
const { ZtSetting } = require('../objects');

/**
 * @returns {Array<Position>}
 */
function MakePositions() {
    return [];
}

class AggregatedPosition {

    /**
     * @param {Position} struc 
     */
    constructor(struc, weightedPrice) {

        this.instrument = struc.instrument;
        this.instrumentName = struc.instrumentName;
        this.pricePrecision = struc.pricePrecision;
        this.weightedPrice = weightedPrice;

        this.totalPosition = struc.totalPosition;
        this.closableVolume = struc.closableVolume;
        this.marketValue = struc.marketValue;
        this.yesterdayPosition = struc.yesterdayPosition;
        this.frozenVolume = struc.frozenVolume;
        this.profit = struc.profit;
        this.lastPrice = 0;
    }
}

/**
 * 一个新的订单，首次发生成交（买入方向）时，需要进行提示（仅提示1次），该map存储数据为：订单id/true，标识为已提示
 */
const GNotifyingMap = {};

class RecordsView extends BaseView {

    get notifyingMap() {
        return GNotifyingMap;
    }

    constructor() {

        super('@20cm-july/main/records', false, '交易数据');
        this.tabs = { entrust: 1, position: 2, tradeSummary: 3 };
        this.positions = MakePositions();
        this.ztsetting = ZtSetting.makeDefault();
    }

    /**
     * @param {ZtSetting} setting 
     */
    setAsSetting(setting) {
        Object.assign(this.ztsetting, this.helper.deepClone(setting));
    }

    createToolbar() {

        this.states = {
            
            focused: this.tabs.entrust,
            onlyCancellable: true,
            isIntegrated: false,
            isOccupied: false,
            keywords: {
                order: null,
                position: null,
                summary: null,
            
            }
        };

        const vapp = new Vue({

            el: this.$container.querySelector('.view-toolbar'),
            data: {

                states: this.states,
                tabs: this.tabs,
            },
            methods: this.helper.fakeVueInsMethod(this, [

                this.handleTabChange,
                this.handleFilterChange,
                this.popout,
                this.occupy,
                this.refreshOrders,
                this.refreshPositions,
                this.filterPositions,
                this.refreshSummarys,
                this.filterSummarys
            ]),
        });

        vapp.$nextTick(() => { this.handleTabChange(); });
    }

    isEntrustFocused() {
        return this.states.focused == this.tabs.entrust;
    }

    isPositionFocused() {
        return this.states.focused == this.tabs.position;
    }

    handleTabChange() {

        const t1 = this.tableOrder;
        const t2 = this.tablePosition;
        const t3 = this.tableSummary;

        if (this.isEntrustFocused()) {
            t2.$component.style.display = 'none';
            t3.$component.style.display = 'none';
            t1.$component.style.display = 'block';
            t1.fitColumnWidth();
        }
        else if (this.isPositionFocused()) {
            t1.$component.style.display = 'none';
            t3.$component.style.display = 'none';
            t2.$component.style.display = 'block';
            t2.fitColumnWidth();
        } else {
            t1.$component.style.display = 'none';
            t2.$component.style.display = 'none';
            t3.$component.style.display = 'block';
            t3.fitColumnWidth();
        }
    }

    popout() {
        this.trigger('popout');
    }

    handleFilterChange() {
        this.tableOrder.customFilter((record) => { return this.testOrder(record); });
    }

    /**
     * @param {Order} record
     */
    testOrder(record) {

        let is_status_ok = !this.states.onlyCancellable || this.states.onlyCancellable && !record.isCompleted;
        let keywords = this.states.keywords.order;
        let is_kw_ok = !keywords
                    || record.instrument.indexOf(keywords) >= 0
                    || record.instrumentName.indexOf(keywords) >= 0 
                    || this.helper.pinyin(record.instrumentName).indexOf(keywords) >= 0;

        return is_status_ok && is_kw_ok;
    }

    filterPositions() {
        this.tablePosition.customFilter((record) => { return this.testPosition(record); });
    }

    filterSummarys() {
        this.tableSummary.customFilter((record) => { return this.testSummary(record); });
    }
    
    /**
     * @param {AggregatedPosition} record
     */
    testPosition(record) {

        let keywords = this.states.keywords.position;
        let is_ok = !keywords
                    || record.instrument.indexOf(keywords) >= 0
                    || record.instrumentName.indexOf(keywords) >= 0 
                    || this.helper.pinyin(record.instrumentName).indexOf(keywords) >= 0;

        return is_ok;
    }

    /**
     * @param {Order} record
     */
    testSummary(record) {

        let keywords = this.states.keywords.summary;
        let is_kw_ok = !keywords
                    || record.instrument.indexOf(keywords) >= 0
                    || record.instrumentName.indexOf(keywords) >= 0 
                    || this.helper.pinyin(record.instrumentName).indexOf(keywords) >= 0;

        return is_kw_ok;
    }


    occupy() {
        this.trigger('occupy-change', (this.states.isOccupied = !this.states.isOccupied));
    }

    get tableOrder() {
        return this.tableObj;
    }

    /**
     * @param {Order} record 
     */
    identifyOrder(record) {
        return record.id;
    }

    /**
     * @returns {Array<Order}
     */
    typedAsOrders(records) {
        return records;
    }

    createTable() {
        
        const $table = this.$container.querySelector('.table-order');
        const ref = new SmartTable($table, this.identifyOrder, this, {

            tableName: 'smt-20cm-july-order',
            displayName: '委托',
            defaultSorting: { prop: 'orderTime', direction: 'desc' },
            virtual: true,
            rowDbClicked: this.handleOrderRowPunch.bind(this),
        });

        ref.setPageSize(99999);
        return ref;
    }

    /**
     * @param {AggregatedPosition} record 
     */
    identifyPosition(record) {
        return record.instrument;
    }

    /**
     * @param {Order} record 
     */
    identifySummary(record) {
        return record.instrumentName + record.direction
    }

    /**
     * @param {Order} record 
     */
    handleOrderRowPunch(record) {

        if (!this.ztsetting.doubleClick2Cancel) {
            return;
        }

        this.log(`user double clicked an entrust to cancel: ${JSON.stringify(record)}`);
        this.cancel(record);
    }

    /**
     * @param {AggregatedPosition} record 
     */
    handlePositionRowPunch(record) {

        var { instrumentName, closableVolume } = record;
        
        if (closableVolume <= 0) {
            this.interaction.showError(`${instrumentName}，无可平仓位`);
        }

        this.log(`double click a position record, ${JSON.stringify(record)}`);
        this.trigger('hit-position', record);
    }

    /**
     * @param {Order} record 
     */
    handleSummaryRowPunch(record) {

    }

    /**
     * @returns {AggregatedPosition}
     */
    findPosition(instrument) {

        /**
         * 外界所关心的某个合约的持仓变动
         */
        this.caringInstrument = instrument;
        return this.tablePosition.getRowData(instrument);
    }

    createTablePosition() {
        
        const $table = this.$container.querySelector('.table-position');
        const ref = new SmartTable($table, this.identifyPosition, this, {

            tableName: 'smt-20cm-july-position',
            displayName: '持仓',
            defaultSorting: { prop: 'createTime', direction: 'desc' },
            virtual: true,
            rowDbClicked: this.handlePositionRowPunch.bind(this),
        });

        ref.setPageSize(99999);
        this.tablePosition = ref;
    }

    createTableSummary() {
        
        const $table = this.$container.querySelector('.table-summary');
        const ref = new SmartTable($table, this.identifySummary, this, {

            tableName: 'smt-20cm-july-summary',
            displayName: '成交汇总',
            defaultSorting: { prop: 'orderTime', direction: 'desc' },
            virtual: true,
            rowDbClicked: this.handleSummaryRowPunch.bind(this),
        });

        ref.setPageSize(99999);
        this.tableSummary = ref;
    }

    /**
     * 单一撤单
     * @param {Order} order 
     */
    cancel(order) {

        this.confirm(false, `是否撤销：${order.instrumentName} ?`, () => {

            this.log(`to cancel an order: ${JSON.stringify(order)}`);
            this.renderProcess.send(this.systemEvent.transmitMsg2TradingServer, this.serverFunction.cancelOrder, { orderId: order.id });
            this.interaction.showSuccess(`${order.instrumentName}，撤单请求已发出`);
        });
    }

    /**
     * @param {Order} record
     */
    formatActions(record) {
        return record.isCompleted ? '' : '<button class="danger" event.onclick="cancel">撤单</button>';
    }

    async requestOrders() {

        var resp = await repoOrder.batchMemQuery({ trade_user_id: this.userInfo.userId });
        if (resp.errorCode != 0) {

            this.interaction.showError(`委托查询错误：${resp.errorCode}/${resp.errorMsg}`);
            return;
        }
        
        var records = resp.data;
        /** 首行数据，为标题栏 */
        var titles = records.shift();
        var orders = ModelConverter.formalizeOrders(titles, records);

        /**
         * 委托时间，后端返回格式不规整，需在前面补0，进行对齐（便于排序功能的一致性）
         */

        orders.forEach(item => {

            let ot = item.orderTime;
            if (typeof ot == 'string' && ot.length == 5 || typeof ot == 'number' && ot < 100000) {
                item.orderTime = '0' + ot;
            }
        });
        
        this.tableObj.refill(orders);
        this.mapNotifieds(orders);
        this.handleFilterChange();
    }

    /**
     * @param {Array<Order>} orders
     */
    mapNotifieds(orders) {

        var stses = this.systemEnum.orderStatus;
        orders.forEach(order => {

            let tradedAll = order.orderStatus == stses.traded.code;
            let tradedPartial = order.orderStatus == stses.partialTraded.code;

            if (order.parentOrderId && (tradedAll || tradedPartial)) {
                this.notifyingMap[order.id] = true;
            }
        });
    }

    /**
     * @param {Order} order
     */
    ask2Notify(order) {

        var stses = this.systemEnum.orderStatus;
        var dirs = this.systemTrdEnum.tradingDirection;
        var tradedAll = order.orderStatus == stses.traded.code;
        var tradedPartial = order.orderStatus == stses.partialTraded.code;
        var isBuy = order.direction == dirs.buy.code;
        var pid = order.parentOrderId;
        var isRequired = pid && (tradedAll || tradedPartial) && this.notifyingMap[pid] === undefined;

        if (!isRequired) {
            return;
        }

        this.notifyingMap[pid] = true;
        let directionLabel = isBuy ? '买入' : '卖出';
        this.interaction.notify({

            title: `${directionLabel}提示`,
            type: 'success',
            position: 'bottom-right',
            message: `${order.instrument}/${order.instrumentName}，已${isBuy ? '买入' : '卖出'}${order.tradedVolume}`,
        });

        this.ring4OrderFirstTrade(isBuy);
    }

    /**
     * @param {*} struc
     */
    handleOrderChange(struc) {

        var order = new Order(struc);
        var is4Me = order.userId == this.userInfo.userId;
        var isAdjustPos = order.adjustFlag;

        if (!is4Me || isAdjustPos) {
            return;
        }

        this.log(`received an order change notify: ${JSON.stringify(struc)}`);
        this.tableOrder.putRow(order);
        this.ask2Notify(order);

        if (this.states.onlyCancellable && order.isCompleted) {
            this.handleFilterChange();
        }
    }

    listen2Change() {
        this.standardListen(this.serverEvent.orderChanged, this.handleOrderChange.bind(this));
    }

    subChange() {
        this.standardSend(this.systemEvent.subscribeAccountChange, null, [this.serverEvent.orderChanged]);
    }

    unsubChange() {
        this.standardSend(this.systemEvent.unsubscribeAccountChange, null, [this.serverEvent.orderChanged]);
    }

    dispose() {
        this.unsubChange();
    }

    refresh() {
        
        this.interaction.showSuccess('刷新请求已发出');
        this.isEntrustFocused() ? this.requestOrders() : this.isPositionFocused() ? this.requestPositions() : this.requestSummarys();
    }

    refreshOrders() {

        this.interaction.showSuccess('委托：刷新请求已发出');
        this.requestOrders();
    }

    refreshPositions() {

        this.interaction.showSuccess('持仓：刷新请求已发出');
        this.requestPositions();
    }

    refreshSummarys() {
        this.interaction.showSuccess('成交汇总：刷新请求已发出');
        this.requestSummarys();
    }

    async requestPositions() {

        var resp = await repoPosition.batchMemQuery({ trade_user_id: this.userInfo.userId });
        if (resp.errorCode != 0) {

            this.interaction.showError(`持仓查询错误：${resp.errorCode}/${resp.errorMsg}`);
            return;
        }

        var records = resp.data;
        /** 首行数据，为标题栏 */
        var titles = records.shift();
        var positions = ModelConverter.formalizePositions(titles, records);
        this.positions.refill(positions);
        
        var aggregateds = [new AggregatedPosition({}, 0)].splice(1);
        var map = {};

        positions.forEach(item => {
            
            let matched = map[item.instrument];
            if (matched === undefined) {

                let sames = positions.filter(x => x.instrument == item.instrument);
                let tpos = sames.map(x => x.totalPosition).sum();
                let wprice = tpos == 0 ? 0 : sames.map(x => x.totalPosition * x.avgPrice / tpos).sum();
                let agg = new AggregatedPosition(item, wprice);
                aggregateds.push(agg);
                map[item.instrument] = agg;
            }
            else if (matched instanceof AggregatedPosition) {

                matched.totalPosition += item.totalPosition;
                matched.closableVolume += item.closableVolume;
                matched.marketValue += item.marketValue;
                matched.yesterdayPosition += item.yesterdayPosition;
                matched.frozenVolume += item.frozenVolume;
                matched.profit += item.profit;
            }
        });

        this.tablePosition.refill(aggregateds);

        /**
         * 主动式上报当前外界所关心的持仓
         */
        let caring = aggregateds.find(x => x.instrument == this.caringInstrument);
        if (caring) {
            this.trigger('cared-position-update', caring);
        }
    }

    async requestSummarys() {
        let orders = [new Order({})].slice(1);
        orders = this.tableObj.extractAllRecords();
        let map = {};
        // 将委托按代码及方向汇总
        orders.forEach(order => {
            let key = `${order.instrument}-${order.direction}`;
            if (map[key] === undefined) {
                map[key] = [order];
            } else {
                map[key].push(order);
            }
        })

        let results = [];
        for (let key in map) {
            let instrumentOrders = [new Order({})].slice(1);
            instrumentOrders = map[key];
            let order = instrumentOrders[0];
            let tradedVolume = instrumentOrders.map(x => x.tradedVolume).sum();
            let tradedAmount = instrumentOrders.map(x => x.tradedAmount).sum();
            let tradedAvgPrice = tradedVolume == 0 ? 0 : tradedAmount / tradedVolume;
            let orderTime = Math.max(...instrumentOrders.map(x => x.orderTime));
            results.push({
                instrument: order.instrument,
                instrumentName: order.instrumentName,
                direction: order.direction,
                tradedVolume,
                tradedAvgPrice,
                tradedAmount,
                orderTime
            });
        }
        this.tableSummary.refill(results);
    }

    arrangePositionUpdate() {

        setInterval(async () => {
            
            if (this.isBackgroundRefresh) {
                return;
            }

            try {

                this.isBackgroundRefresh = true;
                await this.requestPositions();
            }
            catch(ex) {
                console.error(ex);
            }
            finally {
                this.isBackgroundRefresh = false;
            }

        }, 1000 * 5);
    }

    exportSome() {

        var date = new Date().format('yyyyMMdd');
        if (this.isEntrustFocused()) {
            this.tableOrder.exportAllRecords(`委托-${this.userInfo.userName}-${date}`);
        }
        else if (this.isPositionFocused()) {
            this.tablePosition.exportAllRecords(`持仓-${this.userInfo.userName}-${date}`);
        } else {
            this.tableSummary.exportAllRecords(`成交汇总-${this.userInfo.userName}-${date}`);
        }
    }

    build($container) {

        super.build($container);
        this.createToolbar();
        this.createTablePosition();
        this.createTableSummary();
        this.listen2Change();
        this.subChange();
        this.requestOrders();
        this.requestPositions();
        this.requestSummarys();
        this.arrangePositionUpdate();
        this.renderProcess.on(this.systemEvent.tradingServerReestablished, () => { this.subChange(); });
        setInterval(() => {
            this.requestSummarys();
        }, 3000)
    }
};


module.exports = {

    AggregatedPosition,
    RecordsView,
};
