<div class="trade-view-block">
    <div class="title">{{ isMannualBuy() ? '限价买入' : '限价卖出' }}</div>
    <div class="trading-panel s-full-height">
        <div>
            <div class="input-row">
                <label class="input-text">代码</label>
                <el-autocomplete
                    placeholder="输入代码或名称"
                    class="input-ctr"
                    v-model="mannual.keywords"
                    :fetch-suggestions="handleSuggest"
                    @keydown.native="handleInput"
                    @clear="handleClear"
                    @select="handleSelect"
                    prefix-icon="iconfont icon-sousuo"
                    clearable
                >
                    <template slot-scope="{ item: ins }">
                        <span class="item-name">{{ ins.instrumentName }} </span>
                        <span class="item-code">[{{ ins.instrument }}]</span>
                    </template>
                </el-autocomplete>
            </div>

            <div class="input-row">
                <label class="input-text">价格</label>
                <el-input-number
                    class="input-ctr concentrated-input"
                    :step="decidePriceStep()"
                    :min="0"
                    :max="9999.99"
                    v-model="mannual.price"
                    @input.native="calculateByPrice($event)"
                ></el-input-number>
            </div>

            <div class="input-row" style="margin-top: 5px; height: 15px; text-align: center">
                <a class="link-btn s-pull-left s-color-green" @click="setAsPrice(mannual.lowerSellLimit)"
                    >卖下限
                    <span class="s-bold">{{ precisePrice(mannual.lowerSellLimit) }}</span>
                </a>
                <a class="link-btn s-pull-right s-color-red" @click="setAsPrice(mannual.upperBuyLimit)"
                    >买上限
                    <span class="s-bold">{{ precisePrice(mannual.upperBuyLimit) }}</span>
                </a>
            </div>

            <div class="input-row" style="margin-top: 5px; height: 15px; text-align: center">
                <a class="link-btn s-pull-left s-color-green" @click="setAsPrice(mannual.floor)"
                    >跌停
                    <span class="s-bold">{{ precisePrice(mannual.floor) }}</span>
                </a>
                <span>{{ mannual.estimated }}</span>
                <a class="link-btn s-pull-right s-color-red" @click="setAsPrice(mannual.ceiling)"
                    >涨停
                    <span class="s-bold">{{ precisePrice(mannual.ceiling) }}</span>
                </a>
            </div>

            <div v-if="mannual.isByVolume" class="input-row">
                <el-tooltip content="点击切换到金额下单" placement="left">
                    <label class="input-text" :class="mannual.direction == directions.buy ? 'by-volume-flag' : ''" @click="toggleMethod">数量</label>
                </el-tooltip>
                <div class="input-ctr with-unit concentrated-input">
                    <el-input-number class="s-full-width" :step="100" :min="0" :max="9999999900" v-model="mannual.volume" @input.native="calculateByVolume($event)"></el-input-number>
                    <span class="ctr-unit">股</span>
                </div>
            </div>

            <div v-else class="input-row">
                <el-tooltip content="点击切换到数量下单" placement="left">
                    <label class="input-text by-amount-flag" @click="toggleMethod">金额</label>
                </el-tooltip>
                <div class="input-ctr with-unit concentrated-input">
                    <el-input-number class="s-full-width" :step="100000" :min="0" :max="**********" v-model="mannual.amount" @input.native="calculateByAmount($event)"></el-input-number>
                    <span class="ctr-unit">元</span>
                </div>
            </div>

            <div class="input-row ratios" style="margin-top: 15px; height: 25px">
                <el-tooltip placement="left">
                    <div slot="content" v-html="isMannualBuy() ? getAccountAvailableCondition() : getPositionCondition()"></div>
                    <a class="link-btn" @click="setByRatio(1)">全部</a>
                </el-tooltip>
                <a class="link-btn" @click="setByRatio(2)">1/2</a>
                <a class="link-btn" @click="setByRatio(3)">1/3</a>
                <a class="link-btn" @click="setByRatio(4)">1/4</a>
                <template v-if="!mannual.isEditingCustomRatio">
                    <a class="link-btn" @click="setByCustomRatio(mannual.customRatio)">{{ mannual.customRatio }}%</a>
                    <el-tooltip content="自定义百分比">
                        <i class="el-icon-edit s-mgl-5 s-fs-16" @click="mannual.isEditingCustomRatio = true"></i>
                    </el-tooltip>
                </template>
                <template v-else>
                    <a class="custom-ratio">
                        <el-input-number
                            placeholder="百分比"
                            :controls="false"
                            :step="1"
                            :min="1"
                            :max="100"
                            v-model="mannual.customRatio"
                            @blur="handleCustomRatioBlur"
                            @change="handleCustomRatioChange"
                        ></el-input-number>
                        <span>%</span>
                    </a>
                </template>
            </div>

            <div class="input-row" style="margin-top: 7px">
                <div v-if="isMannualBuy()">
                    <div v-if="states.isCreditAccount">
                        <el-button size="small" type="danger" @click="mbuy" style="width: 47%">普通买入</el-button>
                        <el-button size="small" type="danger" @click="mcredit" style="width: 47%">融资买入</el-button>
                    </div>
                    <el-button v-else size="small" class="s-full-width" type="danger" @click="mbuy">{{ '买入' }}</el-button>
                </div>
                <el-button v-else size="small" class="s-full-width" type="success" @click="msell">{{ '卖出' }}</el-button>
            </div>
        </div>
    </div>
</div>
