const { IView } = require('../../../component/iview');
const { BizHelper } = require('../../../libs/helper-biz');
const { AggregatedPosition } = require('./records');
const { AccountSimple } = require('../../20cm/components/objects');
const { repoInstrument } = require('../../../repository/instrument');
const { repoAccount } = require('../../../repository/account');

/**
 * @returns {AggregatedPosition}
 */
function MakeAggregatedPosition() {
    return null;
}
module.exports = class RecordsView extends IView {
    constructor(type) {
        super('@20cm-july/main/trading', false, '交易');
        this.type = type;
        this.defaults = { customRatio: 20 };
        this.accounts = [new AccountSimple({})].splice(1);
        this.directions = { buy: 1, sell: -1 };

        /**
         * 持仓请求，数据到达，回调
         */
        this.positionCallbacks = [function (position = new AggregatedPosition()) {}].splice(1);
    }

    readSetCustomRatio(ratio) {
        let key = 'manual-trade-custom-rate';
        if (typeof ratio == 'number' && ratio >= 1) {
            localStorage.setItem(key, ratio);
        }

        let recent = parseFloat(localStorage.getItem(key));
        return recent >= 1 ? recent : this.defaults.customRatio;
    }

    createApp() {
        this.states = {
            isCreditAccount: false,
            availableCash: 0,
            linkedPosition: MakeAggregatedPosition(),
        };

        this.mannual = {
            instrument: null,
            instrumentName: null,
            direction: this.directions[this.type],
            keywords: null,
            price: 0,
            ceiling: 0,
            floor: 0,
            volume: 0,
            amount: 0,
            isByVolume: true,
            estimated: null,
            lowerSellLimit: 0,
            upperBuyLimit: 0,
            customRatio: this.readSetCustomRatio(),
            isEditingCustomRatio: false,
        };

        const $vapp = new Vue({
            el: this.$container.firstElementChild,
            data: {
                states: this.states,
                directions: this.directions,
                mannual: this.mannual,
            },
            computed: {},
            watch: {
                'mannual.price': () => {
                    if (this.mannual.isByVolume) {
                        this.estimateCost(this.mannual.price, this.mannual.volume);
                    } else {
                        this.estimateVoume(this.mannual.price, this.mannual.amount);
                    }
                },

                'mannual.volume': () => {
                    this.estimateCost(this.mannual.price, this.mannual.volume);
                },

                'mannual.amount': () => {
                    this.estimateVoume(this.mannual.price, this.mannual.amount);
                },
            },
            methods: this.helper.fakeVueInsMethod(this, [
                this.isMannualBuy,
                this.handleSuggest,
                this.handleInput,
                this.handleSelect,
                this.handleClear,
                this.setAsPrice,
                this.setByRatio,
                this.setByCustomRatio,
                this.mbuy,
                this.mcredit,
                this.msell,
                this.getPositionCondition,
                this.getAccountAvailableCondition,
                this.calculateByPrice,
                this.calculateByVolume,
                this.calculateByAmount,
                this.toggleMethod,
                this.precisePrice,
                this.decidePriceStep,
                this.handleCustomRatioChange,
                this.handleCustomRatioBlur,
            ]),
        });
    }

    precisePrice(price) {
        return typeof price == 'number' ? price.toFixed(this.getPricePrecision()) : price;
    }

    decidePriceStep() {
        return this.getPricePrecision() == 3 ? 0.001 : 0.01;
    }

    getPricePrecision() {
        let instrument = this.mannual.instrument;
        return BizHelper.getPricePrecision(instrument);
    }

    calculateByPrice($event) {
        let price = parseFloat($event.target.value);
        if (this.mannual.isByVolume) {
            this.estimateCost(price, this.mannual.volume);
        } else {
            this.estimateVoume(price, this.mannual.amount);
        }
    }

    calculateByVolume($event) {
        this.estimateCost(this.mannual.price, parseFloat($event.target.value));
    }

    calculateByAmount($event) {
        this.estimateVoume(this.mannual.price, parseFloat($event.target.value));
    }

    estimateCost(price, volume) {
        if (price > 0 && volume > 0) {
            let amount = price * volume;
            let result;

            if (amount < 10000) {
                result = amount.toFixed(0);
            } else if (amount < 100000) {
                result = (amount / 10000).toFixed(1) + '万';
            } else if (amount < 100000000) {
                result = (amount / 10000).toFixed(0) + '万';
            } else {
                result = (amount / 100000000).toFixed(2) + '亿';
            }

            this.mannual.estimated = `预估 ${result}`;
        } else {
            this.mannual.estimated = null;
        }
    }

    transferAmount2Volume(price, amount) {
        return price > 0 && amount > 0 ? Math.floor(amount / price / 100) * 100 : 0;
    }

    estimateVoume(price, amount) {
        if (price > 0 && amount > 0) {
            let volume = this.transferAmount2Volume(price, amount);
            let result;

            if (volume < 10000) {
                result = volume + '股';
            } else {
                result = volume / 100 + '手';
            }

            this.mannual.estimated = `预估 ${result}`;
        } else {
            this.mannual.estimated = null;
        }
    }

    toggleMethod() {
        if (this.isMannualSell()) {
            return;
        }

        const ref = this.mannual;
        ref.isByVolume = !ref.isByVolume;

        if (ref.isByVolume) {
            this.estimateCost(ref.price, ref.volume);
        } else {
            this.estimateVoume(ref.price, ref.amount);
        }
    }

    isMannualBuy() {
        return this.type == 'buy';
    }

    isMannualSell() {
        return this.type == 'sell';
    }

    handleInput() {
        const ref = this.mannual;

        if (event.keyCode == 8) {
            event.returnValue = false;
            ref.keywords = null;
            this.handleClear();
        } else if (typeof ref.keywords == 'string' && ref.keywords.trim().length == 0) {
            this.handleClear();
        }
    }

    handleClear() {
        this.linkPosition(null);
        this.setAsInstrument(null, null);
        this.trigger('set-instrument', null, null);
    }

    /**
     * @param {String} keywords
     * @param {Function} callback
     */
    handleSuggest(keywords, callback) {
        if (typeof keywords != 'string' || keywords.trim().length < 1) {
            callback([]);
            return;
        }

        let matches = BizHelper.filterInstruments(this.systemEnum.assetsType.stock.code, keywords);
        if (matches.length == 1) {
            callback([]);
            this.handleSelect(matches[0]);
            return;
        }

        callback(matches);
    }

    handleSelect(selected) {
        var { instrument, instrumentName } = selected;
        this.setAsInstrument(instrument, instrumentName);
        this.trigger('set-instrument', instrument, instrumentName);
    }

    /**
     * @param {String} instrument
     */
    shortizeCode(instrument) {
        return instrument.split('.')[1];
    }

    async setAsInstrument(instrument, instrumentName) {
        this.log(`set stock to: ${instrument}/${instrumentName}`);
        var keywords = instrument ? `${this.shortizeCode(instrument)}-${instrumentName}` : null;

        let ref = this.mannual;
        ref.keywords = keywords;
        ref.instrument = instrument;
        ref.instrumentName = instrumentName;

        let result = await this.requestPriceInfo(instrument);
        let ref2 = this.mannual;
        this.setAsPrice(result.lastPrice);
        ref2.ceiling = result.upperLimitPrice;
        ref2.floor = result.lowerLimitPrice;

        if (!instrument) {
            ref2.upperBuyLimit = 0;
            ref2.lowerSellLimit = 0;
        } else {
            ref2.upperBuyLimit = Math.min(ref2.ceiling, ref2.upperBuyLimit);
            ref2.lowerSellLimit = Math.max(ref2.floor, ref2.lowerSellLimit);
        }

        ref2.volume = 0;
        this.sendPositionRequest(instrument);
    }

    sendPositionRequest(instrument, callback) {
        this.log(`send a position request from trading panel`);
        typeof callback == 'function' && this.positionCallbacks.push(callback);
        this.trigger('request-position', instrument);
    }

    setAsPrice(value) {
        this.log(`set price in trading panel: ${value}`);
        this.mannual.price = this.precisePrice(value) || 0;
    }

    setByRatio(shares) {
        this.handleRatioChange(shares);
    }

    setByCustomRatio(ratio) {
        this.handleRatioChange(100 / ratio);
    }

    handleCustomRatioChange() {
        let ratio = this.mannual.customRatio;
        let defaultv = this.defaults.customRatio;

        if (typeof ratio != 'number' || isNaN(ratio) || ratio < 1) {
            ratio = this.mannual.customRatio = defaultv;
        }

        this.readSetCustomRatio(ratio);
        this.mannual.isEditingCustomRatio = false;
        this.handleRatioChange(100 / ratio);
    }

    handleCustomRatioBlur() {
        this.mannual.isEditingCustomRatio = false;
    }

    handleRatioChange(shares) {
        if (this.isMannualBuy()) {
            if (!this.hasAnyAccount()) {
                return this.interaction.showError('账号信息此刻未获得，请稍后重试');
            }

            if (!(this.mannual.price > 0)) {
                return this.interaction.showError('请输入买入价格，再点击比例');
            }

            /** 总可用资金 */
            let available = this.firstAccount.available;
            /** 扣除印花税 */
            let tax = (available * 1) / 1000;
            /** 实际可用 */
            let actural = available - tax;
            /** 可使用净金额 */
            // let netAmount = Math.floor((available - tax) / shares);
            let expectedAmount = this.firstAccount.balance / shares;
            let netAmount = expectedAmount > actural ? actural : expectedAmount;

            /**
             * 使用金额反算数量
             */

            if (this.mannual.isByVolume) {
                let { price, lowerSellLimit, upperBuyLimit, floor, ceiling } = this.mannual;
                // let uprice = true ? price : this.isMannualBuy() ? Math.min(upperBuyLimit, ceiling) : Math.max(lowerSellLimit, floor);
                // let decided = parseInt(netAmount / uprice / 100) * 100;
                let decided = parseInt(netAmount / price / 100) * 100;
                this.mannual.volume = Math.max(100, decided);
            } else {
                /**
                 * 直接设置金额
                 */
                this.mannual.amount = Math.floor(netAmount);
            }

            this.log(`click a cash percentage to buy, manual = ${JSON.stringify(this.mannual)}`);
        } else {
            let ref = this.states.linkedPosition;

            if (this.hasLinkedPosition() && ref.instrument == this.mannual.instrument) {
                let closable = ref.closableVolume;
                this.mannual.volume = shares == 1 ? closable : closable <= 100 ? closable : parseInt(closable / shares / 100) * 100;
                this.log(`click a position percentage to sell, manual = ${JSON.stringify(this.mannual)}`);
            } else {
                this.interaction.showError('仓位信息，不明确，请手动输入数量');
            }
        }
    }

    /**
     * @param {AggregatedPosition} position
     */
    linkPosition(position) {
        this.states.linkedPosition = position ? this.helper.deepClone(position) : null;
    }

    hasLinkedPosition() {
        return !!this.states.linkedPosition;
    }

    getPositionCondition() {
        var lines = [
            { label: '合约', value: this.mannual.instrumentName || 'N/A' },
            { label: '总仓位', value: 0 },
            { label: '可平仓位', value: 0 },
        ];

        if (this.hasLinkedPosition()) {
            let ref = this.states.linkedPosition;
            lines[0].value = ref.instrumentName;
            lines[1].value = ref.totalPosition.thousands();
            lines[2].value = ref.closableVolume.thousands();
        }

        return lines.map((x) => `<span style="line-height:18px;">${x.label}: ${x.value}</span>`).join('<br>');
    }

    getAccountAvailableCondition() {
        return `可用现金：${(this.states.availableCash || 0).thousands()}`;
    }

    /**
     * @param {AggregatedPosition} position
     */
    hotUpdatePosition(position) {
        this.linkPosition(position);
    }

    /**
     * @param {AggregatedPosition} position
     */
    setPositionByRequest(position) {
        this.linkPosition(position);

        while (this.positionCallbacks.length > 0) {
            try {
                this.positionCallbacks.shift()();
            } catch (ex) {
                console.error(ex);
            }
        }
    }

    /**
     * @param {AggregatedPosition} position
     */
    setPositionByExternal(position) {
        /**
         * 当前合约所关联的持仓信息
         */
        this.linkPosition(position);

        /**
         * 设置合约信息
         */
        this.setAsInstrument(position.instrument, position.instrumentName);

        /**
         * 设置除合约以外的，其它输入项
         */

        // this.mannual.direction = this.directions.sell;
        // this.mannual.isByVolume = true;
    }

    validateMannualParams() {
        var ref = this.mannual;
        var pi = this.priceInfo;
        var lp = this.states.linkedPosition;

        if (!this.hasAnyAccount()) {
            return '没有可用于交易的账号';
        }

        if (!ref.instrument) {
            return '合约未指定';
        }

        if (!(ref.price > 0)) {
            return '价格，未有效设置';
        }

        if (pi && pi.instrument == ref.instrument && !(ref.price >= pi.lowerLimitPrice && ref.price <= pi.upperLimitPrice)) {
            return `价格，超出涨跌停价格：${pi.lowerLimitPrice} ~ ${pi.upperLimitPrice}`;
        }

        let isByAmount = !this.mannual.isByVolume;
        if (isByAmount && this.transferAmount2Volume(ref.price, ref.amount) < 100) {
            return '金额，至少需要能够买入100股';
        }

        if (isByAmount && ref.amount > this.states.availableCash) {
            return `金额 ${ref.amount.thousands()} > 可用现金 ${(this.states.availableCash || 0).thousands()}`;
        }

        if (this.mannual.isByVolume && !(ref.volume > 0)) {
            return '数量，未有效设置';
        }

        if (this.isMannualSell() && this.hasLinkedPosition() && lp.instrument == ref.instrument && ref.volume > lp.closableVolume) {
            return `数量，超出最大可平数量 = ${lp.closableVolume.thousands()}`;
        }
    }

    mbuy() {
        this.mtrade();
    }

    mcredit() {
        this.mtrade(true);
    }

    msell() {
        this.mtrade();
    }

    mtrade(is_credit_buy = false) {
        let instrument = this.mannual.instrument;
        if (!instrument) {
            return this.interaction.showError('交易合约缺失');
        }

        this.sendPositionRequest(instrument, () => {
            this.mtradeExec(is_credit_buy);
        });
    }

    mtradeExec(is_credit_buy = false) {
        var error = this.validateMannualParams();
        if (error) {
            this.interaction.showError(error);
            return;
        }

        var ref = this.mannual;
        var isBuy = ref.direction == this.directions.buy;
        var firstAcnt = this.firstAccount;
        var cvolume = this.isMannualSell() || this.mannual.isByVolume ? ref.volume : this.transferAmount2Volume(ref.price, ref.amount);
        var direction_text = isBuy ? (is_credit_buy ? '融资买入' : '限价买入') : '限价卖出';
        var mentions = [
            ['账号', firstAcnt.accountName],
            ['合约', ref.instrument + '/' + ref.instrumentName],
            ['方向', direction_text, isBuy ? 's-color-red' : 's-color-green'],
            ['价格', ref.price],
            ['数量', cvolume.thousands()],
            ['金额', (ref.price * cvolume).thousands()],
        ];

        var message = mentions.map((item) => `<div><span>${item[0]}：</span><span class="${item[2] || ''}">${item[1]}</span></div>`).join('');
        var order = {
            accountId: firstAcnt.accountId,
            strategyId: firstAcnt.fundId,
            userId: this.userInfo.userId,
            instrument: ref.instrument,
            volume: cvolume,
            price: ref.price,
            priceType: this.systemTrdEnum.pricingType.fixedPrice.code,
            bsFlag: ref.direction,
            businessFlag: is_credit_buy ? this.systemTrdEnum.businessFlag.credit.code : 0,
            positionEffect: 0,
            orderTime: new Date().getTime(),
            hedgeFlag: this.systemTrdEnum.hedgeFlag.Speculate.code,
            customId: '20cm-july-mannual-' + new Date().getTime(),
        };

        this.confirm(true, message, () => {
            console.log('to send out an order', order);
            this.placeMannualOrder(order);
            this.interaction.showSuccess('手动订单已发送');
        });
    }

    /**
     * @param {{ instrument: string, volume: number }} order
     */
    placeMannualOrder(order) {
        let ins = order.instrument;
        let max_volume = 100 * (ins.indexOf('.688') > 0 ? 1000 : ins.indexOf('.3') > 0 ? 3000 : 10000);
        let target_volume = order.volume;
        let waterflow_no = 0;

        while (target_volume > 0) {
            let current = Object.assign({}, order);
            current.volume = Math.min(max_volume, target_volume);
            current.customId = `${current.customId}-${waterflow_no++}`;
            target_volume -= current.volume;
            this.renderProcess.send(this.systemEvent.transmitMsg2TradingServer, this.serverFunction.sendOrder, current);
            this.log(`to send out a manual order: ${JSON.stringify(current)}`);
        }

        setTimeout(() => {
            this.requestAccounts();
            this.sendPositionRequest(this.mannual.instrument);
        }, 1000);
    }
    
    confirm(isRequired, message, callback) {
        if (isRequired) {
            this.interaction.showConfirm({
                title: '操作确认',
                message: message,
                confirmed: () => {
                    callback();
                },
            });
        } else {
            callback();
        }
    }

    async requestPriceInfo(instrument) {
        var output = { instrument, preClosePrice: 0, lastPrice: 0, upperLimitPrice: 0, lowerLimitPrice: 0 };
        if (!instrument) {
            return output;
        }

        var resp = await repoInstrument.queryPrice(instrument);
        var { errorCode, errorMsg, data } = resp;
        var { assetType, preClosePrice, lastPrice, lowerLimitPrice, optionType, priceTick, strikePrice, upperLimitPrice, volumeMultiple } = data || {};

        if (errorCode == 0 && data && upperLimitPrice > 0) {
            output.preClosePrice = preClosePrice;
            output.lastPrice = lastPrice;
            output.upperLimitPrice = upperLimitPrice;
            output.lowerLimitPrice = lowerLimitPrice;
        }

        return (this.priceInfo = output);
    }

    hasAnyAccount() {
        return this.accounts.length > 0;
    }

    get firstAccount() {
        return this.accounts[0];
    }

    async requestAccounts() {
        if (this.isRequestingAccount) {
            return;
        }

        try {
            this.isRequestingAccount = true;
            var resp = await repoAccount.getAccountDetailInfo({ userId: this.userInfo.userId });
            var { errorCode, errorMsg, data } = resp;
            var records = (data || {}).list || [];
            var accounts = errorCode == 0 ? AccountSimple.Convert(records) : [];
            this.accounts.refill(accounts);
            // 获取到账号后，设置账号是否为信用账号
            this.states.isCreditAccount = accounts.length > 0 && !!accounts[0].credit;
            // 获取到账号后，设置账号可用现金
            this.states.availableCash = accounts.length > 0 ? Math.floor(accounts[0].available) : 0;
        } catch (ex) {
            console.error(ex);
        } finally {
            this.isRequestingAccount = false;
        }
    }

    /**
     * @param {String} message
     */
    log(message) {
        this.loggerTrading.info('ztlog:' + message);
    }

    build($container) {
        super.build($container);
        this.createApp();
        this.requestAccounts();
        setInterval(() => {
            this.requestAccounts();
        }, 1000 * 20);
    }
};
