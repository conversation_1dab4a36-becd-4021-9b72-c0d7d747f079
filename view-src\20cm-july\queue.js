const { IView } = require('../../component/iview');
const { TaskObject } = require('./objects');
const { SubscribeManager } = require('./subscribe-manager');
const { NumberMixin } = require('../../mixin/number');

module.exports = class QueueView extends IView {

    constructor() {

        super('@20cm-july/queue', false, '队列');

        /**
         * 合约/任务/订单委托数量，映射表（json map）
         * 1. 一层key：合约
         * 2. 二层key：task id
         * 3. value：多个委托单，委托数量
         * 两层map结构目的，在于避免前后任务为相同合约，造成在高亮的判断逻辑，存在可能串的问题
         */
        this.allOrdersMap = {};

        /** 远端涨停价，排队队列（剩余量） */
        this.leftRemotes = [];
        /** 远端涨停价，排队队列（已展示量） */
        this.remotes = [];
        this.firstScreenCount = 14 * 9;
        this.batchCount = 14 * 3;
        this.hasSubscribedFrontDataMap = {};
        this.isFrontCancelOn = this.app.frontCancel === true;

        this.states = {

            /** 当前选中的任务ID */
            taskId: null,
            /** 当前选中的任务合约 */
            instrument: null,
            /** 当前选中的任务合约名称 */
            instrumentName: null,
            buy1: null,
            buy1Amount: null,
            ahead: null,
            aheadAmount: null,
            front_cancel: null,
            total: null,
            /** 涨停价 */
            ceil: null,
        };

        /**
         * 如果合约变化，由监控任务的切换引起，则该字段保存该监控任务ID值
         */
        this.taskInfo = {

            /** 上一个任务id */
            previous: null, 
            /** 当前任务id */
            current: null,
        };

        this.submgr = new SubscribeManager(this);
    }
    
    /**
     * @param {String} instrument
     */
    shortizeCode(instrument) {
        return instrument.split('.')[1];
    }

    createToolbar() {

        new Vue({

            el: this.$container.firstElementChild,
            data: {

                isFrontCancelOn: this.isFrontCancelOn,
                text: this.title,
                states: this.states,
                remotes: this.remotes,
            },
            mixins: [NumberMixin],
            methods: this.helper.fakeVueInsMethod(this, [

                this.loadMore,
                this.hasMatched,
                this.shortizeCode,
            ]),
        });
    }

    setAsInstrument(instrument, instrumentName, task_id) {

        this.states.taskId = task_id;
        const ti = this.taskInfo;
        ti.previous = ti.current;
        ti.current = task_id;
        
        var ref = this.states;
        var last = ref.instrument;

        /**
         * 合约未变更，无需订阅
         */
        if (instrument == last || this.helper.isNone(instrument) && this.helper.isNone(last)) {
            return;
        }

        // todo22: 调用新增接口，获取账号挂单
        var entrusts = [];

        ref.instrument = instrument;
        ref.instrumentName = instrumentName;
        ref.buy1 = null;
        ref.buy1Amount = null;
        ref.ahead = null;
        ref.aheadAmount = null;
        ref.front_cancel = null;
        ref.total = null;

        this.resetQueue([]);
        this.subscribeTick(last, instrument);
    }

    /**
     * @param {Array<TaskObject>} tasks 
     */
    mapOrders(tasks) {

        // 遍历监控任务，将下挂订单放到字典表里
        tasks.forEach(item => {

            let key_instrument = item.instrument;
            let key_task_id = item.id;

            // 合约层，下层为taskid层
            let level1 = this.allOrdersMap[key_instrument];
            if (level1 == undefined) {
                level1 = this.allOrdersMap[key_instrument] = {};
            }

            let dict1 = item.orderInfo || {};
            let list = [];

            for (let key in dict1) {

                let dict2 = dict1[key] || {};
                if (this.helper.isJson(dict2)) {

                    for (let key2 in dict2) {

                        let volume = dict2[key2];
                        list.push(typeof volume == 'number' ? parseInt(volume / 100) : 0);
                    }
                }
            }

            level1[key_task_id] = list;
        });

        const ref = this.systemTrdEnum.tickType;
        
        tasks.forEach(item => {
            
            if (this.hasSubscribedFrontDataMap[item.id] == undefined) {
                
                this.hasSubscribedFrontDataMap[item.id] = true;
                this.submgr.subscribe(item.id, ref.front, true);
            }
        });
    }

    /**
     * @param {Array<Number>} remotes 远端挂单
     */
    resetQueue(remotes) {

        this.states.total = remotes.length;
        var hands = remotes.map(x => Math.ceil(x * 0.01));

        if (hands.length <= this.firstScreenCount) {

            this.leftRemotes.clear();
            this.remotes.refill(hands);
        }
        else {
            
            this.remotes.refill(hands.splice(0, Math.max(this.firstScreenCount, this.remotes.length)));
            this.leftRemotes.refill(hands);
        }
    }

    /**
     * @param {*} last 上个合约
     * @param {*} current 当前合约
     */
    subscribeTick(last, current) {

        /**
         * 首次合约信息变更时，启动监听
         */

        if (this.hasListened2TickChange === undefined) {

            /**
             * 是否已开启TICK数据监听
             */
            this.hasListened2TickChange = true;

            /**
             * 监听订阅回执
             */
            this.standardListen(this.serverEvent.subscribeTickReceived, (...args) => {
                this.handleTickChange(true, ...args);
            });

            /**
             * 监听TICK数据持续推送
             */
            this.standardListen(this.serverEvent.tickPriceChanged, (...args) => {
                this.handleTickChange(false, ...args);
            });
        }

        const ref = this.systemTrdEnum.tickType;
        const ti = this.taskInfo;

        if (last) {

            this.submgr.unsubscribe(last, ref.tick);
            this.submgr.unsubscribe(last, ref.orderQueue);
        }

        // if (ti.previous) {
        //     this.submgr.unsubscribe(ti.previous, ref.front);
        // }

        if (current) {

            this.submgr.subscribe(current, ref.tick, true);
            this.submgr.subscribe(current, ref.orderQueue, true);
        }

        if (ti.current) {
            this.submgr.subscribe(ti.current, ref.front, true);
        }
    }

    /**
     * @param {String} message 
     */
    log(message) {
        this.loggerTrading.info('ztlog(n/a scenarios):' + message);
    }

    /**
     * 处理TICK数据变化
     * @param {*} isReceipt 是否为订阅回执
     * @param {*} instrument 该TICK所属合约 | 或者 task id （仅对前序总量适用）
     * @param {*} tickType 该TICK数据类型
     * @param {*} tick TICK数据本身
     */
    handleTickChange(isReceipt, instrument, tickType, tick) {

        if (instrument != this.states.instrument && instrument != this.taskInfo.current) {
            return;
        }

        const ref = this.systemTrdEnum.tickType;

        if (tickType == ref.tick) {
            
            /**
             * remark：不使用tick的推送更新买一量，tick/3秒，涨停预埋单实时推送
             */
            // let tickd = new TickData(tick);
            // let buy1 = tickd.buys[0];
            // this.states.buy1 = buy1.hands * 0.01;
            
            let ceiling = tick.ceiling;
            this.states.ceil = ceiling;
            if (typeof ceiling != 'number') {
                this.log('invalid ceiling price/' + JSON.stringify({ instrument, tickType, tick }));
            }
        }
        else if (tickType == ref.ztymd) {

            let matched = tick[this.states.instrument];
            if (matched) {

                this.states.buy1 = matched.limitBuy * 0.01;              
                this.states.buy1Amount = (matched.limitBuy * this.states.ceil) / 100000000;
                
                if (typeof this.states.buy1 != 'number' || typeof this.states.buy1Amount != 'number') {
                    this.log('invalid buy1 volume/' + JSON.stringify({ instrument, tickType, tick }));
                }
            }
        }
        else if (tickType == ref.orderQueue) {

            if (tick instanceof Array) {
                this.resetQueue(tick);
            }
        }
        else if (tickType == ref.front) {

            this.states.ahead = typeof tick == 'number' ? parseInt(tick / 100) : null;
            this.states.aheadAmount = (typeof tick == 'number' && typeof this.states.ceil == 'number') ? parseInt((tick) * (this.states.ceil) / 10000) : null;

            if (typeof this.states.ahead != 'number' || typeof this.states.aheadAmount != 'number') {
                this.log('invalid ahead volume & amount/' + JSON.stringify({ instrument, tickType, tick, ceil: this.states.ceil }));
            }
        }
        else if (tickType == ref.frontCancel) {

            this.states.front_cancel = typeof tick == 'number' ? parseInt(tick / 100) : null;
            if (typeof this.states.front_cancel != 'number') {
                this.log('invalid front cancel volume/' + JSON.stringify({ instrument, tickType, tick }));
            }
        }
    }

    hasMatched(hands) {

        let { instrument, taskId } = this.states;

        if (this.helper.isNone(instrument) || this.helper.isNone(taskId)) {
            return false;
        }

        var volumes = (this.allOrdersMap[instrument] || {})[taskId];
        return volumes instanceof Array && volumes.indexOf(hands) >= 0;
    }

    loadMore() {

        if (this.leftRemotes.length > 0) {
            this.remotes.merge(this.leftRemotes.splice(0, this.batchCount));
        }
    }

    handleReconnect() {
        this.subscribeTick(undefined, this.states.instrument);
    }

    build($container) {

        super.build($container);
        this.createToolbar();
        this.renderProcess.on(this.systemEvent.tradingServerReestablished, () => { this.handleReconnect(); });
    }
};