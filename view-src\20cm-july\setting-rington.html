<div class="view-setting">

    <el-dialog 
        width="300px"
        title="交易铃音设置"
        class="dialog-20cm-july-rington"
        :visible="dialog.visible"
        :close-on-click-modal="false"
        :close-on-press-escape="false"
        :show-close="false"
        @close="close">

        <div class="setting-form">

            <div class="rington-item">
                <label class="ring-name">监控买入</label>
                <el-select v-model="rington.entrusted" placeholder="无提示音" clearable>
                    <el-option v-for="item in (rington.entrusted ? keepRingtonSelf(rington.entrusted) : otherRingtons)" :key="item.code" :label="item.mean" :value="item.code"></el-option>
                </el-select>
                <el-tooltip v-if="rington.entrusted" placement="top" content="试听">
                    <i class="play-btn el-icon-video-play" @click="play(rington.entrusted, rington.customized.entrusted)"></i>
                </el-tooltip>
                <a class="custom-media s-ellipsis" @click="chooseRington('entrusted')" :style="{ visibility: makeCustomizedVisiblity(rington.entrusted)}">
                    <i class="el-icon-star-on"></i>
                    {{ rington.customized.entrusted ? showPlainName(rington.customized.entrusted) : '[请选择铃音]' }}
                </a>
            </div>

            <div class="rington-item">
                <label class="ring-name">买入撤单</label>
                <el-select v-model="rington.canceled" placeholder="无提示音" clearable>
                    <el-option v-for="item in (rington.canceled ? keepRingtonSelf(rington.canceled) : otherRingtons)" :key="item.code" :label="item.mean" :value="item.code"></el-option>
                </el-select>
                <el-tooltip v-if="rington.canceled" placement="top" content="试听">
                    <i class="play-btn el-icon-video-play" @click="play(rington.canceled, rington.customized.canceled)"></i>
                </el-tooltip>
                <a class="custom-media s-ellipsis" @click="chooseRington('canceled')" :style="{ visibility: makeCustomizedVisiblity(rington.canceled) }">
                    <i class="el-icon-star-on"></i>
                    {{ rington.customized.canceled ? showPlainName(rington.customized.canceled) : '[请选择铃音]' }}
                </a>
            </div>

            <div class="rington-item">
                <label class="ring-name">买入成交</label>
                <el-select v-model="rington.bought" placeholder="无提示音" clearable>
                    <el-option v-for="item in (rington.bought ? keepRingtonSelf(rington.bought) : otherRingtons)" :key="item.code" :label="item.mean" :value="item.code"></el-option>
                </el-select>
                <el-tooltip v-if="rington.bought" placement="top" content="试听">
                    <i class="play-btn el-icon-video-play" @click="play(rington.bought, rington.customized.bought)"></i>
                </el-tooltip>
                <a class="custom-media s-ellipsis" @click="chooseRington('bought')" :style="{ visibility: makeCustomizedVisiblity(rington.bought) }">
                    <i class="el-icon-star-on"></i>
                    {{ rington.customized.bought ? showPlainName(rington.customized.bought) : '[请选择铃音]' }}
                </a>
            </div>

            <div class="rington-item">
                <label class="ring-name">卖出成交</label>
                <el-select v-model="rington.sold" placeholder="无提示音" clearable>
                    <el-option v-for="item in (rington.sold ? keepRingtonSelf(rington.sold) : otherRingtons)" :key="item.code" :label="item.mean" :value="item.code"></el-option>
                </el-select>
                <el-tooltip v-if="rington.sold" placement="top" content="试听">
                    <i class="play-btn el-icon-video-play" @click="play(rington.sold, rington.customized.sold)"></i>
                </el-tooltip>
                <a class="custom-media s-ellipsis" @click="chooseRington('sold')" :style="{ visibility: makeCustomizedVisiblity(rington.sold) }">
                    <i class="el-icon-star-on"></i>
                    {{ rington.customized.sold ? showPlainName(rington.customized.sold) : '[请选择铃音]' }}
                </a>
            </div>

            <div class="s-center s-pdt-5 s-pdb-5">
                <el-button type="primary" size="small" @click="save">保存</el-button>
                <el-button type="info" size="small" class="s-mgl-10" @click="cancel">取消</el-button>
            </div>

        </div>

    </el-dialog>

</div>