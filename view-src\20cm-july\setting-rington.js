const { BaseView } = require('./base-view');

module.exports = class RingSettingView extends BaseView {

    constructor() {

        super('@20cm-july/setting-rington', false, '交易铃音设置');
        this.ztrington = {

            entrusted: null,
            bought: null,
            canceled: null,
            sold: null,
            
            customized: {

                entrusted: null,
                bought: null,
                canceled: null,
                sold: null,
            },
        };

        this.dialog = {
            visible: true,
        };
    }
    
    createApp() {

        new Vue({

            el: this.$container.firstElementChild,
            data: {

                dialog: this.dialog,
                rington: this.ztrington,
            },
            computed: {

                otherRingtons: () => {

                    let rt = this.ztrington;
                    return this.ringtons.filter(x => x.isCustomized || ![rt.entrusted, rt.bought, rt.canceled, rt.sold].some(y => y == x.code));
                },
            },
            methods: this.helper.fakeVueInsMethod(this, [

                this.keepRingtonSelf,
                this.play,
                this.chooseRington,
                this.makeCustomizedVisiblity,
                this.showPlainName,
                this.close,
                this.cancel,
                this.save,
            ]),
        });
    }

    keepRingtonSelf(rington) {
        return this.ringtons.filter(x => x.code == rington || x.isCustomized);
    }

    chooseRington(which) {
        
        const { dialog } = require('@electron/remote');
        const paths = dialog.showOpenDialogSync(this.thisWindow, {
            
            title: '选择提示音',
            filters: [{ name: '音频文件', extensions: ['mp3', 'wav'] }],
        });

        this.handleRingtonFile(which, paths);
    }

    handleRingtonFile(which, paths) {

        if (!paths) {
            return;
        }

        var fpath = paths[0];
        var customized = this.ztrington.customized;

        switch (which) {

            case 'entrusted': customized.entrusted = fpath; break;
            case 'bought': customized.bought = fpath; break;
            case 'canceled': customized.canceled = fpath; break;
            case 'sold': customized.sold = fpath; break;
        }
    }

    isCustomizedRington(rington) {
        return rington == this.ringtons.find(x => x.isCustomized).code;
    }

    makeCustomizedVisiblity(rington) {
        return this.isCustomizedRington(rington) ? 'visible' : 'hidden';
    }

    showPlainName(crington) {

        if (typeof crington != 'string') {
            return crington;
        }

        let levels = crington.replace(/\\/g, '/').split('/');
        return levels[levels.length - 1];
    }

    close() {
        this.dialog.visible = false;
    }

    cancel() {
        this.close();
    }

    save() {

        const ref = this.ztrington;
        const isnone = this.helper.isNone;
        var message;

        // if (isnone(ref.entrusted) || this.isCustomizedRington(ref.entrusted) && isnone(ref.customized.entrusted)) {
        //     message = '监控买入，请选择铃音';
        // }
        // else if (isnone(ref.canceled) || this.isCustomizedRington(ref.canceled) && isnone(ref.customized.canceled)) {
        //     message = '买入撤单，请选择铃音';
        // }
        // else if (isnone(ref.bought) || this.isCustomizedRington(ref.bought) && isnone(ref.customized.bought)) {
        //     message = '买入成交，请选择铃音';
        // }
        // else if (isnone(ref.sold) || this.isCustomizedRington(ref.sold) && isnone(ref.customized.sold)) {
        //     message = '卖出成交，请选择铃音';
        // }

        if (message) {
            return this.interaction.showError(message);
        }
        
        this.trigger('rington-change', this.ztrington);
        this.close();
    }

    showup() {
        this.dialog.visible = true;
    }

    update2Latest(latest) {

        if (!this.helper.isJson(latest)) {
            return;
        }
        
        const customized_key_name = 'customized';
        this.helper.extend(this.ztrington.customized, latest[customized_key_name]);
        delete latest[customized_key_name];
        this.helper.extend(this.ztrington, latest);
    }

    createTable() {
        // 该方法无需实现，仅避免基类视图调用未实现的方法时，产生报错
    }
    
    build($container) {

        super.build($container);
        this.createApp();
    }
};