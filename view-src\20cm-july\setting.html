<div class="view-trade-setting">
    <el-dialog
        width="395px"
        title="交易设置"
        class="dialog-20cm-july-setting"
        :visible="dialog.visible"
        :close-on-click-modal="false"
        :close-on-press-escape="false"
        :show-close="false"
        @close="close"
    >
        <div class="setting-form">
            
            <div class="setting-item">
                <label class="setting-name">双击撤单</label>
                <el-switch v-model="setting.doubleClick2Cancel"></el-switch>
            </div>

            <div class="setting-item">
                <label class="setting-name">大号字体</label>
                <el-switch v-model="setting.biggerFont"></el-switch>
            </div>
            
            <template v-if="states.isThsSupported">

                <div class="setting-item">
                    <label class="setting-name">
                        <span class="s-pdr-5">代码快捷键</span>
                        <el-tooltip>
                            <div slot="content" style="font-size: 14px; line-height: 24px;">
                                <span>通过快捷按键，快速捕捉同花顺或通达信软件，当前窗口展示的股票，并进行自动填充</span>
                                <br />
                                <span class="s-color-red">目前支持：同花顺、通达信</span>
                            </div>
                            <i class="el-icon-question"></i>
                        </el-tooltip>
                    </label>
                    <el-switch v-model="setting.ths.manualConfirm"></el-switch>
                    <label class="setting-input-desc s-pdl-5 s-color-red" v-if="setting.ths.manualConfirm">需要确认弹窗</label>
                    <label class="setting-input-desc s-pdl-5 s-color-grey" v-else>不用确认提示</label>
                </div>
                
                <div class="setting-item" v-if="shortcuts.enabled && shortcuts.hasKey1">
                    <label class="setting-name">快捷键默认策略</label>
                    <el-select placeholder="默认快捷策略1" class="s-mgl-5 s-mgr-5" style="width: 150px" v-model="setting.ths.strategy" clearable>
                        <el-option v-for="(item, item_idx) in strategies" :key="item_idx" :label="item" :value="item"></el-option>
                    </el-select>
                </div>
    
                <div class="setting-item" v-if="shortcuts.enabled && shortcuts.hasKey2">
                    <label class="setting-name">快捷键2默认策略</label>
                    <el-select placeholder="默认快捷策略2" class="s-mgl-5 s-mgr-5" style="width: 150px" v-model="setting.ths.strategy2" clearable>
                        <el-option v-for="(item, item_idx) in strategies" :key="item_idx" :label="item" :value="item"></el-option>
                    </el-select>
                </div>

                <div class="setting-item">
                    <label class="setting-name">
                        <span class="s-pdr-5">立即启动</span>
                        <el-tooltip>
                            <div slot="content">
                                <span>填充合约后，立即启动监控</span>
                            </div>
                            <i class="el-icon-question"></i>
                        </el-tooltip>
                    </label>
                    <el-switch v-model="setting.ths.immediate"></el-switch>
                </div>

            </template>

            <div class="s-center s-pdt-5 s-pdb-5">
                <el-button type="primary" size="small" @click="save">保存</el-button>
                <el-button type="info" size="small" class="s-mgl-10" @click="cancel">取消</el-button>
            </div>
        </div>
    </el-dialog>
</div>
