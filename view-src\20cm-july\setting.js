const { IView } = require('../../component/iview');
const { ZtSetting } = require('./objects');

module.exports = class SettingView extends IView {

    constructor() {

        super('@20cm-july/setting', false, '涨停交易设置');
        this.ztsetting = ZtSetting.makeDefault();
        this.strategies = [];
        this.dialog = { visible: true };
        this.states = { isThsSupported: false };
        this.localShortcutSetting = {
            enabled: false, 
            hasKey1: false, 
            hasKey2: false,
        };
    }

    affectShortcuts(enabled, hasKey1, hasKey2) {
        Object.assign(this.localShortcutSetting, { enabled, hasKey1, hasKey2 });
    }
    
    createApp() {

        new Vue({

            el: this.$container.firstElementChild,
            data: {

                dialog: this.dialog,
                setting: this.ztsetting,
                strategies: this.strategies,
                states: this.states,
                shortcuts: this.localShortcutSetting,
            },
            methods: this.helper.fakeVueInsMethod(this, [

                this.close,
                this.cancel,
                this.save,
            ]),
        });
    }


    close() {
        this.dialog.visible = false;
    }

    cancel() {
        this.close();
    }

    save() {
        
        this.trigger('ztsetting-change', this.ztsetting);
        this.close();
    }

    exposeThsChoice() {
        this.states.isThsSupported = true;
    }

    /**
     * @param {Array<string>} strategies 
     */
    setStrategies(strategies) {

        this.strategies.refill(strategies);

        /**
         * 反向判断是否有对应名称的策略
         */
        setTimeout(() => {

            if (!strategies.some(x => x == this.ztsetting.ths.strategy)) {
                this.ztsetting.ths.strategy = null;
            }

            if (!strategies.some(x => x == this.ztsetting.ths.strategy2)) {
                this.ztsetting.ths.strategy2 = null;
            }
        }, 200);
    }

    showup() {
        this.dialog.visible = true;
    }

    /**
     * @param {ZtSetting} latest 
     */
    update2Latest(latest) {

        if (this.helper.isJson(latest)) {
            
            const ref = this.ztsetting;
            const ths_latest = latest.ths || {};
            ref.biggerFont = !!latest.biggerFont;
            ref.doubleClick2Cancel = !!latest.doubleClick2Cancel;
            ref.ths.manualConfirm = !!ths_latest.manualConfirm;
            ref.ths.immediate = !!ths_latest.immediate;
            ref.ths.strategy = ths_latest.strategy;
            ref.ths.strategy2 = ths_latest.strategy2;
        }
    }

    build($container) {

        super.build($container);
        this.createApp();
    }
};