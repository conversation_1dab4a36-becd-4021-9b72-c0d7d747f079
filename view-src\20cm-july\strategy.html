<div class="trade-view-block">

	<div class="view-toolbar strategy-toolbar s-pdl-10">

		<el-tabs type="card" size="mini" v-model="states.choosed" @tab-click="handleSwitched">
			<template v-for="(name, name_idx) in strategies">
				<el-tab-pane v-if="canShowByName(name, name_idx)" :key="name" :label="name" :name="name"></el-tab-pane>
			</template>
		</el-tabs>

		<span class="strategy-tools">

			<template v-if="states.taskInfo.instrument">
				<span class="s-pdr-10">{{ states.taskInfo.instrumentName }}/{{ shortizeCode(states.taskInfo.instrument) }}</span>
			</template>

			<el-tooltip placement="top" content="保存所有">
				<el-button size="small" type="primary" class="s-mgl-10" style="height: 20px; margin-top: 4px;" @click="saveAll">保存</el-button>
			</el-tooltip>

			<el-popover placement="top" title="策略另存为" width="400" trigger="manual" v-model="states.saving.visible">

				<div class="s-pd-10">
					<label class="s-pdr-10">策略名称</label>
					<el-input size="small" class="s-w-200" v-model.trim="states.saving.name" clearable></el-input>
					<el-button type="primary" size="small" class="s-mgl-10" style="height: 20px; margin-top: 4px;" @click="saveAs">另存为</el-button>
					<el-button type="info" size="small" class="s-mgl-10" style="height: 20px; margin-top: 4px;" @click="states.saving.visible = false;">取消</el-button>
				</div>

				<el-button
					slot="reference" 
					size="small" 
					type="success" 
					class="s-mgl-10" 
					style="height: 20px; margin-top: 4px;" 
					@click="states.saving.visible = !states.saving.visible">另存为</el-button>

			</el-popover>

			<el-tooltip placement="top" content="删除当前">
				<el-button size="small" type="danger" class="s-mgl-10" style="height: 20px; margin-top: 4px;" @click="remove">删除</el-button>
			</el-tooltip>

		</span>

	</div>

	<div class="block block1">
		<br>
		<el-button v-if="canStart()" class="btn01" size="small" type="primary" @click="startTask">开始监控</el-button>
		<el-button v-else-if="canStop()" class="btn01" size="small" type="danger" @click="stopTask">停止监控</el-button>
		<el-button v-else-if="canCancel()" class="btn01" size="small" type="warning" @click="cancelTask">撤单</el-button>
		<el-button v-else class="btn01" size="small" type="primary" :disabled="true">开始监控</el-button>
		<br>
		<el-button class="btn02" size="small" type="primary" @click="reset">重置</el-button>
	</div>

	<div class="block block2">
		<div class="block-title">买入触发条件</div>
		<div class="block-row" :class="trg.inline ? 'inline': ''" v-for="(trg, trg_idx) in current.btriggers" :key="trg_idx">
			<el-checkbox :disabled="checkDisabled(trg, current.btriggers)" v-model="trg.checked" @change="handleParamChange">{{ trg.name }}</el-checkbox>
			<template v-for="(cdt, cdt_idx) in trg.conditions">
				<span v-show="cdt.visible" :key="cdt_idx" class="conditions-box" >
					<span v-if="cdt.compare" class="compare">{{ cdt.compare }}</span>
					<el-select v-if="isChoiceMode(cdt)" size="small" v-model="cdt.threshold" @change="handleInputCdtChange(cdt, trg)" :disabled="cdt.readonly">
						<el-option v-for="(item, item_idx) in cdt.choices" :key="item_idx" :label="item.label" :value="item.value"></el-option>
					</el-select>
					<el-input-number 
						v-else
						size="small" 
						:controls="false" 
						:step="1" 
						:min="0"
						:max="cdt.max"
						v-model="cdt.threshold" 
						@change="handleInputCdtChange(cdt, trg)"
						:disabled="cdt.readonly"
						></el-input-number>
					<span v-if="cdt.unit" class="unit">{{ cdt.unit }}</span>
				</span>
			</template>
		</div>
	</div>

	<div class="block block3">
		<div class="block-title">撤单条件</div>
		<div class="block-row" v-for="(trg, trg_idx) in current.ctriggers" :key="trg_idx">
			<el-checkbox v-model="trg.checked" @change="handleParamChange">{{ trg.name }}</el-checkbox>
			<template v-for="(cdt, cdt_idx) in trg.conditions">
				<span v-show="cdt.visible" :key="cdt_idx" class="conditions-box">
					<span v-if="cdt.compare" class="compare">{{ cdt.compare }}</span>
					<el-select v-if="isChoiceMode(cdt)" size="small" v-model="cdt.threshold" @change="handleInputCdtChange(cdt, trg)" :disabled="cdt.readonly">
						<el-option v-for="(item, item_idx) in cdt.choices" :key="item_idx" :label="item.label" :value="item.value"></el-option>
					</el-select>
					<el-input-number 
						v-else
						size="small" 
						:controls="false" 
						:step="1" 
						:min="0"
						:max="cdt.max"
						v-model="cdt.threshold" 
						@change="handleInputCdtChange(cdt, trg)"
						:disabled="cdt.readonly"
						></el-input-number>
					<span v-if="cdt.unit" class="unit">{{ cdt.unit }}</span>
				</span>
			</template>
		</div>
	</div>

	<div class="block block4">

		<div class="figuring">
			<span>委托数量（手）: </span>
			<el-tooltip v-if="figures.length > 0">
				<div slot="content">
					<div v-for="(item, item_idx) in figures" :key="item_idx" class="figure-row" style="max-width: 1000px; line-height: 18px;">
						<template>{{ item.accountName }}: </template>
						<template>{{ item.volumes.slice(0, 50).join(', ') }}</template>
						<template>{{ item.volumes.length > 50 ? '... ' : '' }}</template>
						<template>（共{{ item.volumes.length }}笔）</template>
					</div>
				</div>
				<span class="hands">{{ figures.map(x => x.total).slice(0, 5).join('/') }}</span>
			</el-tooltip>
			<span v-else class="hands">N/A</span>
		</div>

		<div class="block-row">
			<el-checkbox v-model="current.limit.max.checked" @change="handleLimitChange">最大证券数</el-checkbox>
			<el-input-number size="small" :controls="false" :step="1" :min="0"
				v-model="current.limit.max.volume" @change="handleLimitChange"></el-input-number>
			<span class="unit">手</span>
			<el-checkbox class="s-mgl-10" v-model="current.limit.decrease.checked" @change="handleLimitChange">逐笔下降</el-checkbox>
		</div>

		<div class="block-row s-mgt-10">
			<el-checkbox v-model="current.limit.topmost.checked" @change="handleLimitChange">最大下单量</el-checkbox>
			<el-input-number size="small" :controls="false" :step="1" :min="0"
				v-model="current.limit.topmost.volume" @change="handleLimitChange"></el-input-number>
			<span class="unit">手</span>
		</div>

		<div class="block-row s-mgt-10">
			<span style="padding-right: 20px; padding-left: 20px;">买入仓位</span>
			<el-radio-group size="mini" v-model="current.limit.position.method" @change="handleLimitChange">
				<el-radio :label="by.money">金额</el-radio>
				<el-radio :label="by.ratio">比例</el-radio>
			</el-radio-group>
		</div>

		<div class="block-row" style="padding-left: 86px;">
				
			<template v-if="isByMoney(current)">
				<el-input-number size="small" :controls="false" :step="1" :min="0" 
					v-model="current.limit.position.amount" @change="handleLimitChange"></el-input-number>
				<span class="unit">万元</span>
			</template>
			
			<template v-else>
				<el-input-number size="small" :controls="false" :step="1" :min="0" :max="100" 
					v-model="current.limit.position.percentage" @change="handleLimitChange"></el-input-number>
				<span class="unit">%</span>
				<span>{{ intoAmount() }}</span>
				<div class="pct-shortcut-panel">
					<a v-for="(item, item_idx) in devides" :key="item_idx" @click="handlePctShortcutClick(item)" class="pct-shortcut">
						<template>{{ item == 1 ? '全' : '1/' + item }}</template>
					</a>
				</div>
			</template>

		</div>

		<div class="block-row">
			<span style="padding-left: 20px;">
				<template>总可用</template>
				<template>{{ isInstrumentCreditableBuy() && hasAnyCreditAccount() && current.limit.creditFlag ? '(含融资)' : '' }}</template>
				<template>&nbsp;{{ showTotalCash() }}</template>
			</span>
		</div>

		<template v-if="isInstrumentCreditableBuy() && hasAnyCreditAccount()">
			<div class="block-row">
				<el-checkbox v-model="current.limit.creditFlag" @change="handleLimitChange">优先融资</el-checkbox>
			</div>
		</template>

	</div>

</div>
