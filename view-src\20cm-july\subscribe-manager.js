const { IView } = require('../../component/iview');

/**
 * 订阅图谱
 */
const SubscribingMap = {};

class SubscribeManager {

    /**
     * @param {IView} iview 
     */
    constructor(iview) {
        this.iview = iview;
    }

    /**
     * 生成组合Key
     * @param {String} instrument 
     * @param {Number} tick_type 
     */
    composeKey(instrument, tick_type) {
        return `${instrument}.${tick_type}`;
    }

    /**
     * 订阅行情
     * @param {String} instrument 
     * @param {Number} tick_type 
     */
    subscribe(instrument, tick_type, forced = false) {

        if (!instrument) {
            return;
        }

        let key = this.composeKey(instrument, tick_type);
        let counter = SubscribingMap[key];
        let accepted = false;

        if (counter === undefined) {
            
            counter = 1;
            accepted = true;
            this.iview.standardSend(this.iview.systemEvent.subscribeTick, [instrument], tick_type);
        }
        else {
            counter += 1;
        }

        /**
         * 强制订阅（通常由于需要使用立即的订阅回执，而TICK可能间隔3秒才能收到）
         */
        if (!accepted && forced) {
            this.iview.standardSend(this.iview.systemEvent.subscribeTick, [instrument], tick_type);
        }

        SubscribingMap[key] = counter;
    }

    /**
     * 退订行情
     * @param {String} instrument 
     * @param {Number} tick_type 
     */
    unsubscribe(instrument, tick_type) {

        if (!instrument) {
            return;
        }

        let key = this.composeKey(instrument, tick_type);
        let counter = SubscribingMap[key];

        if (typeof counter == 'number') {
            counter -= 1;
        }
        
        if (counter <= 0 || counter === undefined) {

            this.iview.standardSend(this.iview.systemEvent.unsubscribeTick, [instrument], tick_type);
            delete SubscribingMap[key];
        }
        else {
            // still has subscribers alive
        }
    }
}

module.exports = { SubscribeManager };