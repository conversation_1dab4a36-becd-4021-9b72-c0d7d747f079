const TradeRouteView = require('./trade');
const { SubscribeManager } = require('./subscribe-manager');
const { BaseView } = require('./base-view');
const { Splitter } = require('../../component/splitter');
const { SmartTable } = require('../../libs/table/smart-table');
const { Cm20FunctionCodes } = require('../../config/20cm');
const { BizHelper } = require('../../libs/helper-biz');
const { TickData } = require('../2021/model/message');
const { BuyTask, ZtStandardStrategy, TaskObject, TaskStatus, FromTable, RowBehaviors } = require('./objects');
const { StockQuote } = require('../../model/stock-quote');
const { repoInstrument } = require('../../repository/instrument');
const { repo20Cm } = require('../../repository/20cm');

module.exports = class TaskView extends BaseView {

    constructor() {

        super('@20cm-july/task', false, '证券列表');
        this.forwardId = 9000;
        /** 合约与监控任务的映射（用于价格列更新） */
        this.instrument2TaskMap = {};
        /** 合约与涨停价映射表 */
        this.instrument2CeilingPriceMap = {};
        this.submgr = new SubscribeManager(this);
    }

    /**
     * @param {TradeRouteView} obj
     */
    attachRootObj(obj) {
        this.rootObj = obj;
    }

    createToolbar() {

        this.states = {

            keywords: null,
            instrument: null,
            instrumentName: null,
        };

        const vappIns = new Vue({

            el: this.$container.querySelector('.view-toolbar'),
            data: {

                title: this.title,
                states: this.states,
            },
            methods: this.helper.fakeVueInsMethod(this, [

                this.handleSuggest,
                this.handleInput,
                this.handleSelectProxy,
                this.handleClear,
                this.refresh,
                this.download,
                this.downloadTaskLog,
                this.shortizeCode,
                this.batchStart,
                this.batchStop,
            ]),
        });

        vappIns.$nextTick(() => {

            let $search = this.ashtml(vappIns.$refs.kw.$el.querySelector('input'));
            $search.classList.add('mousetrap');
            $search.onfocus = () => { this.isSearchFocused = true; };
            $search.onblur = () => { this.isSearchFocused = false; };
            this.$search = $search;
            this.focusOnSearch();
        });
    }

    /**
     * @param {HTMLInputElement} data
     */
    ashtml(data) {
        return data;
    }

    focusOnSearch() {

        this.$search.focus();
        this.$search.select();
    }

    handleInput() {

        const ref = this.states;

        if (event.keyCode == 8) {

            event.returnValue = false;
            ref.keywords = null;
            this.handleClear();
        }
        else if (typeof ref.keywords == 'string' && ref.keywords.trim().length == 0) {
            this.handleClear();
        }
    }

    handleClear() {

        const ref = this.states;
        ref.keywords = null;
        ref.instrument = null;
        ref.instrumentName = null;
    }

    /**
     * @param {String} keywords
     * @param {Function} callback
     */
    handleSuggest(keywords, callback) {

        if (typeof keywords != 'string' || keywords.trim().length < 1) {

            callback([]);
            return;
        }

        let matches = BizHelper.filterInstruments(this.systemEnum.assetsType.stock.code, keywords);
        if (matches.length == 1) {

            callback([]);
            this.setTriggerTime(0);
            this.handleSelect(matches[0], false);
            return;
        }

        callback(matches);
    }

    /**
     * @param {StockQuote} quote
     */
    handlePipeMsg(quote) {

        let { instrument, instrumentName } = quote;
        this.log(`pipe message brings stock: ${instrument}/${instrumentName}`);
        this.setTriggerTime(0);
        this.handleSelect({ instrument, instrumentName }, true);
    }

    handleThsMsg(instrument, instrumentName, strategyName, immediate) {


        this.log(`ths shortcut message brings stock: ${instrument}/${instrumentName}/${strategyName}`);
        this.setTriggerTime(0);
        this.handleSelect({ instrument, instrumentName, strategyName }, immediate);
    }

    handleSelectProxy(selected) {

        this.setTriggerTime(new Date().getTime());
        this.handleSelect(selected, false);
    }

    /**
     * 记录下，选择一个目标合约，被设置的具体时间戳（用于控制回车选择合约与回车下单的时间差）
     */
    setTriggerTime(ts) {

        /** 确定目标合约的时间戳（等于0表示无需检查时间差） */
        this.lastTs = ts;
    }

    handleSelect(selected, isFromPipeOrThs = false) {

        var { instrument, instrumentName, strategyName } = selected;
        const ref = this.states;
        ref.keywords = `${this.shortizeCode(instrument)}-${instrumentName}`;
        ref.instrument = instrument;
        ref.instrumentName = instrumentName;
        this.log(`manual select stock/${instrument}/${instrumentName}`);

        if (this.hasSameUnstarted(instrument)) {

            this.handleClear();
            // this.interaction.showError(`${instrumentName}，不能重复添加`);
            return;
        }

        // /**
        //  * 延时目的：避免因为auto complete待选项，因为敲击回车选中，引起回车快捷事件
        //  */
        // setTimeout(() => { this.$search.blur(); }, 100);

        let task = this.tell2CeateTask(this.rootObj.ask4Strategy(strategyName));
        this.trigger('task-added', task.strategyName);

        /**
         * 立即自动启动由管道带入的合约任务
         */
        if (isFromPipeOrThs) {
            this.start4Row(task);
        }
    }

    hasSameUnstarted(instrument) {

        var tasks = this.tableObj.extractAllRecords().map(rowd => this.typeds(rowd));
        return tasks.some(x => x.instrument == instrument && (this.isTaskCreated(x.status) || this.helper.isNone(x.id)));
    }

    /**
     * @param {ZtStandardStrategy} strategy
     */
    tell2CeateTask(strategy) {

        var { instrument, instrumentName } = this.states;
        var localId = ++this.forwardId;
        var task = new BuyTask({

            localId,
            id: null,
            instrument,
            instrumentName,
            strategyId: undefined,
            strategyName: strategy.name,
            strategy: strategy,
            percent: null,
            ceilingPrice: null,
            status: TaskStatus.created.value,
            targetVolume: 0,
            tradedVolume: 0,
            usedMargin: 0,
            createTime: null,
            hasCanceled: false,
        });

        this.tableObj.insertRow(task);
        this.mapTask(task.instrument, task);
        this.updateCeiling(this.tableObj, task);
        this.log(`use add a new task, and this row is selected: ${JSON.stringify(task)}`);

        /**
         * 始终选中新加入的任务（选中事件，将在 row select 回调中触发）
         */

        this.tableObj.selectRow(this.identify(task));

        /**
         * 成功加入一条数据后，删除已输入的合约
         */
        this.handleClear();

        /**
         * 返回新创建好的任务条目
         */
        return task;
    }

    /**
     * @param {Array<BuyTask>} tasks
     */
    push(tasks) {

        var table = this.tableObj;
        var table2 = this.tableObj2;

        tasks.forEach(item => {

            let row_key = this.identify(item);
            let status = item.status;
            let should_be_deleted = this.isTaskOrdered(status) || this.isTaskFinished(status) || this.isTaskDeleted(status);

            if (should_be_deleted) {

                /**
                 * 在左上表格，则删除
                 */

                if (table.hasRow(row_key)) {

                    let is_selected = table.isRowSelected(row_key);
                    table.deleteRow(row_key);

                    if (is_selected) {
                        this.tolerate(FromTable.added, RowBehaviors.delete, item);
                    }
                }

                /**
                 * 在左下表格，则删除
                 */

                if (table2.hasRow(row_key)) {

                    let is_selected = table2.isRowSelected(row_key);
                    table2.deleteRow(row_key);

                    if (is_selected) {
                        this.tolerate(FromTable.canceled, RowBehaviors.delete, item);
                    }
                }

                this.unmapTask(item.instrument);
            }
            else if (item.hasCanceled) {

                /**
                 * 处于已撤状态的任务，只可能从：已下单 》已撤，不可能还存在于左上侧列表，
                 * 故：无需尝试尝试左上侧的残留（因为无残留）
                 */

                table2.putRow(item);
                this.mapTask(item.instrument, item);
                this.updateCeiling(table2, item);
            }
            else {

                if (table2.hasRow(row_key)) {

                    let is_selected = table2.isRowSelected(row_key);
                    table2.deleteRow(row_key);

                    if (is_selected) {
                        this.tolerate(FromTable.canceled, RowBehaviors.delete, item);
                    }
                }

                table.putRow(item);
                this.mapTask(item.instrument, item);
                this.updateCeiling(table, item);
            }
        });
    }

    /**
     * @returns {BuyTask}
     */
    typeds(data) {
        return data;
    }

    /**
     * @param {BuyTask} task 来源监控任务
     * @param {ZtStandardStrategy} strategy
     */
    outupdate(task, strategy) {

        let row_key = this.identify(task);
        let mright = this.tableObj;
        let expected = this.typeds(this.tableObj.getRowData(row_key));

        if (!expected) {

            mright = this.tableObj2;
            expected = this.typeds(this.tableObj2.getRowData(row_key));
        }

        if (!expected) {
            return;
        }

        mright.updateRow({

            id: expected.id,
            localId: expected.localId,
            strategyName: strategy.name,
            strategy: strategy,
        });

        if (this.isTaskRunning(expected.status)) {

            /**
             * 1. 在运行状态前提下；
             * 2. 提交策略切换 | 策略内部参数更新，带来的变动需求；
             */
            this.submit(expected, Cm20FunctionCodes.request.modify, true);
        }
    }

    /**
     * @param {BuyTask} task
     */
    hasTaskInside(task) {

        let row_key = this.identify(task);
        return this.tableObj.hasRow(row_key) || this.tableObj2.hasRow(row_key);
    }

    /**
     * @param {BuyTask} task
     */
    outstart(task) {

        if (!this.hasTaskInside(task)) {
            return this.interaction.showError(`未匹配到可以启动的监控：${task.instrumentName}，请尝试从列表操作！`);
        }
        else {
            this.start(task);
        }
    }

    /**
     * @param {BuyTask} task
     */
    outstop(task) {

        if (!this.hasTaskInside(task)) {
            return this.interaction.showError(`未匹配到可以停止的监控：${task.instrumentName}，请尝试从列表操作！`);
        }
        else {
            this.stop(task);
        }
    }

    /**
     * @param {TaskObject} data
     * @param {*} reqId 该 request id 对应的是本地新插入记录的 local id
     */
    attachTaskId(data, reqId) {

        if (this.tableObj.hasRow(reqId)) {

            // 删除静态的数据行
            this.tableObj.deleteRow(reqId);
            let settings = TaskObject.transerTriggers(this.getContextDataItem(this.dataKey.logInInput));
            let task = TaskObject.Convert2BuyTask(data, settings.buys, settings.cancels);
            // 插入回执返回的新数据行，并选中
            this.tableObj.insertRow(task);
            this.tableObj.selectRow(this.identify(task));
            this.mapTask(task.instrument, task);
            this.updateCeiling(this.tableObj, task);
        }
        else if (reqId > 0) {
            console.warn('maybe: unable to attach task id', { task: data, reqId });
        }
    }

    /**
     * @param {BuyTask} record
     */
    identify(record) {
        return record.id || record.localId;
    }

    createTable() {

        const $table = this.$container.querySelector('.table-task');
        const ref = new SmartTable($table, this.identify, this, {

            tableName: 'smt-20cm-task',
            displayName: this.title,
            defaultSorting: { prop: 'localId', direction: 'desc' },
            rowSelected: (rowd) => { this.handleRowSelect(FromTable.added, rowd); },
        });

        ref.setPageSize(99999);
        return ref;
    }

    createTable2() {

        var $table = this.$container.querySelector('.table-task-2');
        const table = new SmartTable($table, this.identify, this, {

            tableName: 'smt-20cm-task-2',
            displayName: this.title,
            defaultSorting: { prop: 'localId', direction: 'desc' },
            rowSelected: (rowd) => { this.handleRowSelect(FromTable.canceled, rowd); },
        });

        table.setPageSize(99999);
        this.tableObj2 = table;
    }

    /**
     * @param {*} from 来自哪个表格的选中
     * @param {BuyTask} task
     */
    handleRowSelect(from, task) {
        this.tolerate(from, RowBehaviors.select, task);
    }

    createSplitter() {

        let $splitter = this.$container.querySelector('.splitter-line');

        /**
         * 未下单的两个任务列表
         */
        this.splitter = new Splitter('20cm-product-task', $splitter, this.handleSpliting.bind(this), {

            previousMinHeight: 60,
            nextMinHeight: 60,
        });

        setTimeout(() => { this.splitter.recover(); }, 1000);
        this.thisWindow.on('resize', () => { this.splitter.sync(); });
    }

    /**
     * @param {Number} previous_height
     * @param {Number} next_height
     */
    handleSpliting(previous_height, next_height) {
        //
    }

    tolerate(from, behavior, task) {
        this.trigger('tolerate', from, behavior, task);
    }

    sortableRows() {

        var ref = $(this.tableObj.$bodyTable.querySelector('tbody'));
        ref.sortable();
    }

    refresh() {

        this.interaction.showSuccess('刷新请求已发出');
        this.trigger('refresh-task');
    }

    download() {
        this.tableObj.exportAllRecords();
    }

    render2String(record, field_value) {
        return typeof field_value == 'number' ? field_value.toString() : field_value;
    }

    renderType(record) {
        return record.type == 1 ? '买入' : '剔除';
    }

    identifyTaskLog(record) {
        return record.sequenceId;
    }

    batchStart() {
      this.tableObj.allRows.forEach(row => {
        let task = row.rowData;
        if (this.isTaskCreated(task.status) || this.isTaskPaused(task.status)) {
            this.start4Row(task);
        }
      });
    }

    batchStop() {
      this.tableObj.allRows.forEach(row => {
        let task = row.rowData;
        if (this.isTaskRunning(task.status)) {
            this.stop4Row(task);
        }
      });
      
    }

    async downloadTaskLog() {

        var today = new Date().format('yyyyMMdd');
        var resp = await repo20Cm.queryTaskHistory(today);
        var { errorCode, errorMsg, data } = resp;

        if (errorCode != 0 || !(data instanceof Array)) {
            return this.interaction.showError('策略日志导出错误: ' + errorMsg);
        }

        data.forEach((item, item_idx) => { item.sequenceId = item_idx + 1; });

        if (this.texpt == undefined) {

            var $table = this.$container.querySelector('.table-4-export');
            this.texpt = new SmartTable($table, this.identifyTaskLog, this, {

                tableName: '20cm-product-task-log',
                displayName: '策略日志',
                pageSize: 9999,
            });
        }

        this.texpt.refill(data);
        this.texpt.exportAllRecords(`策略日志-${today}`);
    }

    clickBody() {
        document.body.click();
    }

    /**
     * @param {BuyTask} task
     */
    start4Row(task) {

        /**
         * 为避免可能存在的场景：
         * 1. 策略面板，改变一个控件的数值，未失去焦点，直接点击行内的启动按钮；
         * 2. 点击行内启动按钮，该按钮取消了节点元素向上冒泡；
         * 3. 导致光标还停留在，策略面板的某个数值输入控件，获取的值并非新值；
         */
        this.clickBody();
        this.start(task);
    }

    /**
     * @param {BuyTask} task
     */
    start(task) {
        this.submit(task, Cm20FunctionCodes.request.start, true);
    }

    /**
     * @param {BuyTask} task
     */
    stop4Row(task) {

        this.clickBody();
        this.stop(task);
    }

    /**
     * @param {BuyTask} task
     */
    stop(task) {
        this.submit(task, Cm20FunctionCodes.request.stop, false);
    }

    /**
     * @param {BuyTask} task
     */
    submit(task, request_code, param_checked_required) {
        this.toSubmit(task, request_code, param_checked_required);
    }

    /**
     * @param {BuyTask} task
     */
    remove(task) {

        /**
         * 1. 上方已启动过的任务、下方任务，向服务器递交删除请求即可；
         * 2. 该数据行的实际删除，由后续推送的数据，触发删除；
         */
        if (this.helper.isNotNone(task.id)) {

            // this.interaction.showSuccess('删除请求已提交');
            this.submit(task, Cm20FunctionCodes.request.delete, false);
            return;
        }

        /**
         * 仅第一个列表，存在新加入未提交过的，静态任务数据行（没有id字段，仅有local id）
         */

        var table = this.tableObj;
        var is_selected = table.isRowSelected(task.localId);
        table.deleteRow(task.localId);
        this.unmapTask(task.instrument);

        if (is_selected) {
            this.tolerate(FromTable.added, RowBehaviors.delete, task);
        }
    }

    /**
     * @param {number} percent
     * @param {BuyTask} task
     */
    formatPercentageClass(percent, task) {
        return percent > 0 ? 's-color-red' : percent < 0 ? 's-color-green' : '';
    }

    /**
     * @param {BuyTask} task
     */
    formatRunStop(task) {

        if (this.isTaskRunning(task.status)) {
            return `<a class="row-oper-icon" event.onclick="stop4Row"><i class="iconfont icon-pause"></i></a>`;
        }
        else if (this.isTaskCreated(task.status) || this.isTaskPaused(task.status)) {
            return `<a class="row-oper-icon" event.onclick="start4Row"><i class="iconfont icon-play"></i></a>`;
        }

        return '';
    }

    /**
     * @param {BuyTask} task
     */
    formatOper(task) {

        return this.isTaskDeletable(task.status) ?
                '<a class="row-oper-icon" event.onclick="remove"><i class="el-icon-delete"></i></a>' :
                '';
    }

    /**
     * @param {*} instrument
     */
    unmapTask(instrument) {
        delete this.instrument2TaskMap[instrument];
    }

    /**
     * @param {*} instrument
     * @param {BuyTask} task
     */
    mapTask(instrument, task) {

        this.instrument2TaskMap[instrument] = task;
        this.subscribeTick(undefined, instrument);
    }

    /**
     * @param {SmartTable} table
     * @param {BuyTask} task
     */
    async updateCeiling(table, task) {

        if (this.helper.isNotNone(task.ceilingPrice)) {
            return;
        }

        var ceiling = await this.requestPriceInfo(task.instrument);
        table.updateRow({ id: task.id, localId: task.localId, ceilingPrice: ceiling });
    }

    async requestPriceInfo(instrument, forced = false) {

        var ref = this.instrument2CeilingPriceMap;
        if (!forced && typeof ref[instrument] == 'number') {
            return ref[instrument];
        }

        var ceiling = 0;
        var resp = await repoInstrument.queryPrice(instrument);
        if (resp.errorCode == 0 && resp.data) {
            ceiling = resp.data.upperLimitPrice || 0;
        }

        ref[instrument] = ceiling;
        return ceiling;
    }

    /**
     * 处理TICK数据变化
     * @param {*} isReceipt 是否为订阅回执
     * @param {*} instrument 该TICK所属合约
     * @param {*} tickType 该TICK数据类型
     * @param {*} tick TICK数据本身
     */
    handleTickChange(isReceipt, instrument, tickType, tick) {

        if (tickType != this.systemTrdEnum.tickType.tick) {
            return;
        }

        var task = this.typeds(this.instrument2TaskMap[instrument]);
        if (!task) {
            return;
        }

        var row_key = this.identify(task);
        var tickd = new TickData(tick);
        var rate = tickd.latest / tickd.preclose - 1;
        var change = { id: task.id, localId: task.localId, percent: rate };

        if (this.tableObj.hasRow(row_key)) {
            this.tableObj.updateRow(change);
        }

        if (this.tableObj2.hasRow(row_key)) {
            this.tableObj2.updateRow(change);
        }
    }

    /**
     * @param {*} last 上个合约
     * @param {*} current 当前合约
     */
    subscribeTick(last, current) {

        /**
         * 首次合约信息变更时，启动监听
         */

        if (this.hasListened2TickChange === undefined) {

            /**
             * 是否已开启TICK数据监听
             */
            this.hasListened2TickChange = true;

            /**
             * 监听订阅回执
             */
            this.standardListen(this.serverEvent.subscribeTickReceived, (...args) => {
                this.handleTickChange(true, ...args);
            });

            /**
             * 监听TICK数据持续推送
             */
            this.standardListen(this.serverEvent.tickPriceChanged, (...args) => {
                this.handleTickChange(false, ...args);
            });
        }

        const ref = this.systemTrdEnum.tickType;

        if (last) {
            
            this.submgr.unsubscribe(last, ref.tick);
            this.submgr.unsubscribe(last, ref.transaction);
        }

        if (current) {
            
            this.submgr.subscribe(current, ref.tick);
            this.submgr.subscribe(current, ref.transaction);
        }
    }

    try2ReupdatePrice() {

        const market_open_time_delay = '09:15:10';
        const launch_time = new Date().format('hh:mm:ss');
        if (launch_time >= market_open_time_delay) {
            // 启动时间已在开盘后
            return;
        }

        this.redoJobId = setInterval(async () => {
            
            const now_time = new Date().format('hh:mm:ss');
            if (now_time < market_open_time_delay) {
                return;
            }
            
            clearInterval(this.redoJobId);
            let tasks = this.tableObj.extractAllRecords().map(x => this.typeds(x));
            let tasks2 = this.tableObj2.extractAllRecords().map(x => this.typeds(x));

            for (let i = 0; i < tasks.length; i++) {

                let item = tasks[i];
                let ceiling = await this.requestPriceInfo(item.instrument, true);
                this.tableObj.updateRow({ id: item.id, localId: item.localId, ceilingPrice: ceiling });
            }

            for (let i = 0; i < tasks2.length; i++) {

                let item = tasks2[i];
                let ceiling = await this.requestPriceInfo(item.instrument, true);
                this.tableObj2.updateRow({ id: item.id, localId: item.localId, ceilingPrice: ceiling });
            }

        }, 1000 * 5);
    }

    build($container) {

        super.build($container);
        this.createToolbar();
        this.createTable2();
        this.sortableRows();
        this.createSplitter();
        this.try2ReupdatePrice();
    }
}
