const { CriteriaParam, BuyCancelTrigger } = require('./objects');
const { helper } = require('../../libs/helper');
const { repoInstrument } = require('../../repository/instrument');

/**
 * 买入策略的价格类型变化处理函数
 * @param {CriteriaParam} cp 当前输入项
 * @param {BuyCancelTrigger} trigger 触发项
 * @param {string} instrument 当前合约
 */
async function customPriceTypeChange(cp, trigger, instrument) {
    
    if (cp.variable != 'customPriceType') {
        return;
    }

    let matched = trigger.conditions.find(x => x.variable == 'customPrice');
    if (!matched) {
        return;
    }

    if (!instrument) {

        matched.threshold = null;
        return;
    }

    let resp = await repoInstrument.queryPrice(instrument);
    let { errorCode, errorMsg, data } = resp;
    if (!helper.isJson(data)) {

        matched.threshold = null;
        return;
    }

    if (cp.threshold == 1) {
        
        // 涨停价格
        matched.threshold = data.upperLimitPrice;
    }
    else if (cp.threshold == 2) {
        
        // 笼子价格
        matched.threshold = +(Math.floor(data.upperLimitPrice * 0.98 * 100) / 100).toFixed(2);
    }
    else if (cp.threshold == 3) {
        
        // 自定义价格
        // matched.threshold = null;
    }
}

const TriggerHandlers = {
    customPriceTypeChange,
};

module.exports = { TriggerHandlers };
