<div class="buys-inter">

	<div v-for="(unit, unit_idx) in units" 
		:key="unit_idx"
		class="stock-unit typical-content"
		:class="isFocused(unit) ? 'focused' : ''"
		@click="setAsCurrent(unit)">

		<div class="title typical-header">
			<span>{{ unit.stock.name }} ({{ unit.stock.code }})</span>
			<span class="s-pull-right">
				<el-button type="primary" size="small" @click="popupEntrust(unit)" class="s-mgr-5" style="position: relative; top: -2px;">全息</el-button>
				<el-button type="danger" size="small" @click="cancelOrder(unit)" style="position: relative; top: -2px;">撤单</el-button>
				<button class="collapser-btn s-mgl-5 s-opacity-7 s-opacity-hover" @click="unit.expanded = !unit.expanded;" style="position: relative; top: -1px;">
					<i :class="unit.expanded ? 'el-icon-arrow-up' : 'el-icon-arrow-down'"></i>
				</button>
			</span>
		</div>

		<div class="bulletin themed-box" v-show="unit.expanded">

			<label class="prop-name">买1: </label>
			<label class="prop-value">
				<span v-if="typeof unit.buy1.volume != 'number'">---</span>
				<span v-else>{{ thousands(unit.buy1.volume) }}</span>
				<span>手/</span>
				<span v-if="typeof unit.buy1.amount != 'number'">---</span>
				<span v-else-if="unit.buy1.amount < 10000">{{ (unit.buy1.amount / 10000).toFixed(2) }}万元</span>
				<span v-else-if="unit.buy1.amount < 100000">{{ (unit.buy1.amount / 10000).toFixed(1) }}万元</span>
				<span v-else>{{ thousands(unit.buy1.amount / 10000) }}万元</span>
			</label>

			<label class="prop-name">封单笔数: </label>
			<label class="prop-value">{{ typeof unit.buy1.fdbs == 'number' ? thousands(unit.buy1.fdbs) : '---' }}</label>

			<el-tooltip placement="bottom-end">
				<div slot="content">
					<div v-for="item in unit.paramb" :key="item.label" style="line-height: 22px;">
						<label class="prop-name" style="display: inline-block; width: 100px; text-align: right;">{{ item.label }}：</label>
						<label v-if="item.isPercent" class="prop-value">{{ item.value || '---' }}</label>
						<label v-else class="prop-value">{{ typeof item.value == 'number' ? thousands(item.value) : '---' }}</label>
					</div>
				</div>
				<span class="s-pull-right themed-left-border s-pdl-10 s-pdr-10 shine-color s-hover-underline">
					<label class="prop-name">数据</label>
					<label class="prop-value">{{ typeof unit.paramb.bl.value == 'number' ? thousands(unit.paramb.bl.value) : '---' }}</label>
				</span>
			</el-tooltip>

		</div>

		<div class="unit-body" v-show="unit.expanded">

			<div class="trans-panel s-pdl-10 themed-box themed-left-border">
				
				<div v-for="(item, item_idx) in unit.transactions"
					:key="item_idx" 
					:class="isMinuteEnd(unit, item.time, item_idx) ? 'new-minute-start' : ''"
					class="trans-item">
					
					<div class="trans-time s-pull-left">
						<template>{{ item.time == null ? '00:00:00 000' : formatTransTime(item.time) }}</template>
					</div>
	
					<div class="trans-p-h">
	
						<span class="trans-price s-ellipsis" :class="decidePriceColorClass(item.price)">
							<template>{{ precisePrice(item.price) }}</template>
						</span>
		
						<span class="trans-hands s-ellipsis" 
							:class="item.direction > 0 ? 's-color-red' : item.direction < 0 ? 's-color-green' : ''">
							<template>{{ simplyHands(item.volume) }}</template>
						</span>
					</div>
	
				</div>

			</div>
	
			<div class="condition-row s-mgb-10">

				<!-- <label class="prop-name">票池: </label> -->
				<label class="prop-value">{{ unit.ticketPoolName || '[票池名称]' }}</label>

				<label class="prop-name s-pdl-10">封单参数: </label>
				<label class="prop-value">{{ typeof unit.scale.fdcs == 'number' ? thousands(unit.scale.fdcs) : '---' }} 手</label>

				<label class="prop-name s-pdl-10 s-color-red">剩: </label>
				<label class="prop-value s-color-red">{{ typeof unit.scale.syss == 'number' ? thousands(unit.scale.syss) : '---' }}手</label>
	
			</div>
	
			<div class="condition-row">
				<el-checkbox v-model="unit.conditions.cancel.checked" @change="handleSomeChange(unit)">撤单保护</el-checkbox>
				<el-checkbox v-model="unit.conditions.trade.checked" @change="handleSomeChange(unit)">成交保护</el-checkbox>
			</div>

			<div class="condition-row">

				<el-checkbox v-model="unit.conditions.zdy.checked" @change="handleSomeChange(unit)">自定义下降比例</el-checkbox>

				<el-input-number v-model="unit.conditions.zdy.percent" 
								@change="handleSomeChange(unit)" 
								:disabled="!unit.conditions.zdy.checked"
								:min="0"
								:max="100"
								:step="1"
								:controls="false"
								size="mini"
								class="short-input"></el-input-number>
								 
				<label class="input-unit s-pdl-5">%</label>

				<el-input-number v-model="unit.conditions.zdy.time" 
								@change="handleSomeChange(unit)"
								:disabled="!unit.conditions.zdy.checked" 
								:min="0"
								:max="999999999"
								:step="1"
								:controls="false"
								size="mini"
								class="short-input s-mgl-10"></el-input-number>
								 
				<label class="input-unit s-pdl-5">毫秒</label>

			</div>

			<div class="condition-row">

				<el-checkbox v-model="unit.conditions.jzcd.checked" @change="handleSomeChange(unit)">精准撤单</el-checkbox>

				<el-input-number v-model="unit.conditions.jzcd.hands" 
								@change="handleSomeChange(unit)" 
								:disabled="!unit.conditions.jzcd.checked"
								:min="0"
								:max="999999999"
								:step="1"
								:controls="false"
								size="mini"
								class="medium-input"></el-input-number>
								 
				<label class="input-unit s-pdl-5">手</label>

			</div>

			<div class="condition-row">

				<div class="s-pull-right" style="margin-top: -10px; margin-right: 10px;">
					<el-button type="primary" size="small" @click="cancel(unit)" style="height: 30px; font-size: 14px; font-weight: bold;">取消</el-button>
				</div>
				
				<el-checkbox v-model="unit.conditions.dyfd.checked" @change="handleSomeChange(unit)">低于封单量</el-checkbox>

				<el-input-number v-model="unit.conditions.dyfd.hands" 
								@change="handleSomeChange(unit)" 
								:disabled="!unit.conditions.dyfd.checked"
								:min="0"
								:max="999999999"
								:step="1"
								:controls="false"
								size="mini"
								class="medium-input"></el-input-number>
								 
				<label class="input-unit s-pdl-5">手</label>

			</div>

			<div class="condition-row">

				<el-checkbox v-model="unit.conditions.bd.checked" @change="handleSomeChange(unit)">补单</el-checkbox>

				<el-input-number v-model="unit.conditions.bd.hands"
								@change="handleSomeChange(unit)"
								:disabled="!unit.conditions.bd.checked"
								:min="0"
								:max="999999999"
								:step="1"
								:controls="false"
								size="mini"
								class="medium-input s-mgl-10"></el-input-number>

			</div>

			<div class="sum-row s-mgt-5 s-mgb-5" style="line-height: 22px;">

				<label class="prop-name">资金(万): </label>
				<label class="prop-value">{{ typeof unit.available == 'number' ? thousands(unit.available / 10000) : '---' }}</label>
				<button class="collapser-btn-2 s-pull-right s-mgr-10 s-opacity-7 s-opacity-hover" @click="unit.isEntrustCollapsed = !unit.isEntrustCollapsed;">
					<i class="shine-color" :class="unit.isEntrustCollapsed ? 'el-icon-arrow-right' : 'el-icon-arrow-up'"></i>
				</button>

			</div>

			<div class="entrusts s-mgb-5" v-if="unit.entrusts.length > 0" v-show="!unit.isEntrustCollapsed">
				<div v-for="ent in unit.entrusts" :key="ent.accountId" class="each-entrust">
					<span class="aname s-ellipsis">{{ ent.accountName }}: </span>
					<span class="hands s-ellipsis">
						<template v-if="ent.volumes instanceof Array">
							<template v-if="ent.volumes.length > 6">
								<el-tooltip placement="right" :content="ent.volumes.join(', ')">
									<span>{{ ent.volumes.slice(0, 6).join(', ') }}...</span>
								</el-tooltip>
							</template>
							<template v-else>{{ ent.volumes.join(', ') }}</template>
						</template>
						<template v-else>---</template>
					</span>
				</div>
			</div>

		</div>

		<div class="remote-queue themed-box themed-top-border themed-left-border" v-show="unit.expanded && !unit.isEntrustCollapsed">

			<ul class="infinite-list" v-infinite-scroll="() => { load(unit); }"> 
				<li
					v-for="hands in unit.remotes"
					:class="colorizeHands(unit, hands)"
					class="infinite-list-item remote themed-right-border themed-bottom-border s-ellipsis">{{ hands }}</li>
			</ul>
		</div>

	</div>
</div>

<template v-if="units.length == 0">
	<div class="no-data-notice">
		<div class="displaying">
			<i class="el-icon-moon"></i> 没有运行中的监控
		</div>
	</div>
</template>