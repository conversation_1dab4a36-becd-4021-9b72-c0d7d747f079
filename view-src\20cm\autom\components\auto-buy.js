const { BaseView } = require('../../base-view');
const { PopGovener } = require('./pop-governer');
const { TickData, TransactionItem } = require('../../../2021/model/message');
const {

    TaskStatus, 
    Entrance,
    StrategyParamNames,
    AutoBuyUnit,
    TaskObject, 
    Definition, 
    UserSetting, 
    AccountSimple,

} = require('../../components/objects');

const { Cm20FunctionCodes } = require('../../../../config/20cm');
const { NumberMixin } = require('../../../../mixin/number');
const { repoInstrument } = require('../../../../repository/instrument');
const { repo20Cm } = require('../../../../repository/20cm');

module.exports = class AutoBuyView extends BaseView {

    constructor() {

        super('@20cm/autom/components/auto-buy', false, '自动买入监控');

        this.gsettings = {

            main: this.app.bigOrderOptions.main,
            others: this.app.bigOrderOptions.others,
        };

        this.consts = {

            powering: 10000,

            /**
             * 最大可融资额度，向下折扣（确保额度使用安全性）
             */
            safeDiscount: 0.9,
        };

        this.percentages = Entrance.makePercentages();
        this.protections = [

            new Definition(1, 1),
            new Definition(2, 2),
            new Definition(3, 3),
        ];

        this.accounts = [new AccountSimple({})].slice(1);
        this.units = [new AutoBuyUnit({})].slice(1);
        this.unitsMap = {};
        this.states = {
            focused: this.units.length > 0 ? this.units[0] : null,
        };

        this.popper = new PopGovener(this);

        this.registerEvent('setting-updated', (settings) => { this.setAsSettings(settings); });
        this.registerEvent('no-focused-unit', () => { this.clearCurrent(); });
        this.registerEvent('account-change', (accounts) => { this.handleAccountChange(accounts); });
        this.registerEvent('set-tasks', (tasks) => { this.handleTaskChange(tasks); });
    }

    /**
     * @param {UserSetting} settings 
     */
    setAsSettings(settings) {

        this.settings = settings;
        this.popper.updateSettings(settings);
    }

    /**
     * @param {Array<AccountSimple} updates 
     */
    handleAccountChange(updates) {
        this.accounts.refill(updates);
    }

    createApp() {

        this.vapp = new Vue({

            el: this.$container.firstElementChild,
            data: {

                accounts: this.accounts,
                percentages: this.percentages,
                protections: this.protections,
                units: this.units,
                states: this.states,
            },
            mixins: [NumberMixin],
            methods: this.helper.fakeVueInsMethod(this, [

                this.popupEntrust,
                this.isFocused,
                this.setAsCurrent,
                this.formatTransTime,
                this.precisePrice,
                this.simplyHands,
                this.isMinuteEnd,
                this.decidePriceColorClass,
                this.cancel,
                this.cancelOrder,
                this.handlePercentChange,
                this.handleSomeChange,
                this.colorizeHands,
                this.load,
            ]),
        });
    }

    /**
     * @param {AutoBuyUnit} unit
     */
    colorizeHands(unit, hands) {

        if (unit.entrustsMap[hands] === true) {
            return 'highlighted';
        }
        
        var ins = unit.stock.code;
        if (ins.indexOf('SHSE.60') == 0 || ins.indexOf('SZSE.00') == 0) {
            return hands >= this.gsettings.main ? 'large-scale' : '';
        }
        else {
            return hands >= this.gsettings.others ? 'large-scale' : '';
        }
    }

    /**
     * @param {AutoBuyUnit} unit 
     */
    load(unit) {
        unit.loadMore();
    }

    /**
     * @param {AutoBuyUnit} unit 
     */
    chooseKey(unit) {
        return unit.taskId;
    }

    /**
     * @param {Array<TaskObject>} tasks
     */
    handleTaskChange(tasks) {

        tasks.forEach(task => {

            let status = task.strikeBoardStatus;

            if (status == TaskStatus.deleted || status == TaskStatus.supplemented) {

                let matched = this.units.find(x => x.taskId == task.id);
                if (matched) {
                    this.ring4Canceled(matched);
                }
            }

            if (!TaskObject.isOrdered(status)) {

                this.units.remove(x => x instanceof AutoBuyUnit && x.taskId == task.id);
                delete this.unitsMap[task.id];
                this.unsubscribeTick(task.instrument);
                this.popper.close(task.id);
                this.log(`unit removed from auto list, task id/${task.id}, task status/${status}, stock/${task.instrument}/${task.instrumentName}`);

                /**
                 * todo21: 可能解决了缺陷：添加一个合约 》启动 》已买 》完成结束 》重新添加相同合约 》动态实时提交的参数为上一次的参数痕迹 
                 */
                if (this.states.focused && this.states.focused.taskId === task.id) {

                    this.clearCurrent();
                    this.trigger('unit-removed', task.instrument);
                }

                return;
            }
            // else if (!TaskObject.isAlive(status)) {

            //     console.error('unexpected task status = ' + status);
            //     return;
            // }

            let unit = this.units.find(x => x.taskId == task.id);
            if (unit === undefined) {

                /**
                 * 作为一个新的监控
                 */
                unit = new AutoBuyUnit(task);

                this.units.unshift(unit);
                this.unitsMap[this.chooseKey(unit)] = unit;
                this.subscribeTick(unit.stock.code);
                this.ring4Bought(unit);
                this.requestParamb(unit);
                this.log(`unit added to auto list, task id/${task.id}, task status/${status}, stock/${task.instrument}/${task.instrumentName}`);
            }

            this.update(unit, task);
            unit.setAsTask(task);
            unit.updateEntrusts(task.xorders);
            this.popper.updateEntrust(task.id, task.usedMargin / 10000, task.xorders);
        });

        /**
         * 设置默认选中
         */

        if (!this.states.focused && this.units.length > 0) {
            this.setAsCurrent(this.units[0]);
        }
    }

    /**
     * @param {AutoBuyUnit} unit 
     * @param {TaskObject} task 
     */
    update(unit, task) {

        unit.ticketPoolId = task.ticketPoolId
        unit.ticketPoolName = task.ticketPoolName;
        unit.scale.fdcs = task.boardStrategy.strategyVolume;

        var ucd = unit.conditions;
        var tcd = task.cancelCondition;
        ucd.cancel.checked = tcd.cancelProtectedEnabled;
        ucd.trade.checked = tcd.tradeProtectedEnabled;
        ucd.zdy.checked = tcd.customDownRateOpen;
        ucd.zdy.percent = tcd.customDownRate;
        ucd.zdy.time = tcd.customDownRateTime;
        ucd.jzcd.checked = tcd.followCancelOpen;
        ucd.jzcd.hands = tcd.followCancel;
        ucd.dyfd.checked = tcd.lineupOrderVolumeOpen;
        ucd.dyfd.hands = tcd.lineupOrderVolume;
        ucd.bd.checked = tcd.supplementEnabled;
        ucd.bd.hands = tcd.supplementOrderVolume;
        unit.available = task.usedMargin;
    }

    percentagize(rate) {
        return typeof rate == 'number' ? (rate * 100).toFixed(2) + '%' : rate;
    }

    /**
     * @param {AutoBuyUnit} unit 
     */
    async requestParamb(unit) {

        var stock = unit.stock.code;
        var resp = await repo20Cm.queryParamb(stock);
        var { err, errMsg, data } = resp;
        var bizdata = data[stock];
        
        if (err != 0 || !this.helper.isJson(bizdata)) {

            for (let key in unit.paramb) {
                unit.paramb[key].value = null;
            }

            return;
        }

        var {

            trading_day, 
            b_amount, 
            b_minute_amount,
            sealing_plate_rate, 
            disposable_sealing_plate_rate, 
            high_open_rate, 
            high_open_premium_rate, 
            avg_yield_rate,
            peak_yield_rate,
            avg_seal_plate_volume,
            board_volume,
        } = bizdata;

        var paramb = unit.paramb;
        paramb.fbgl.value = this.percentagize(sealing_plate_rate);
        paramb.ycxfbgl.value = this.percentagize(disposable_sealing_plate_rate);
        paramb.gkgl.value = this.percentagize(high_open_rate);
        paramb.cryjl.value = this.percentagize(high_open_premium_rate);
        paramb.jjsylj.value = this.percentagize(avg_yield_rate);
        paramb.zgsylj.value = this.percentagize(peak_yield_rate);
        paramb.zdfblj.value = avg_seal_plate_volume;
        paramb.bscjl.value = board_volume;
        paramb.bl.value = b_amount;

        if (this.hasStartedParambUpdate == undefined) {

            this.hasStartedParambUpdate = true;
            setInterval(() => {
                this.units.forEach(unit => {
                    if (typeof unit.paramb.bl.value != 'number') {
                        this.requestParamb(unit);
                    }
                });
            }, 1000 * 60 * 1);
        }
    }

    subscribeTick(stockCode) {

        if (this.hasListened2TickChange === undefined) {

            this.hasListened2TickChange = true;
            this.standardListen(this.serverEvent.subscribeTickReceived, (...args) => { this.handleTickChange(true, ...args); });
            this.standardListen(this.serverEvent.tickPriceChanged, (...args) => { this.handleTickChange(false, ...args); });
        }

        this.standardSend(this.systemEvent.subscribeTick, [stockCode], this.systemTrdEnum.tickType.tick);
        this.standardSend(this.systemEvent.subscribeTick, [stockCode], this.systemTrdEnum.tickType.transaction);
        this.standardSend(this.systemEvent.subscribeTick, [stockCode], this.systemTrdEnum.tickType.orderQueue);
    }

    unsubscribeTick(stockCode) {

        if (this.units.some(x => x.stock.code == stockCode)) {
            return;
        }

        this.standardSend(this.systemEvent.unsubscribeTick, [stockCode], this.systemTrdEnum.tickType.tick);
        this.standardSend(this.systemEvent.unsubscribeTick, [stockCode], this.systemTrdEnum.tickType.transaction);
        this.standardSend(this.systemEvent.unsubscribeTick, [stockCode], this.systemTrdEnum.tickType.orderQueue);
    }

    /**
     * 处理TICK数据变化
     * @param {*} isReceipt 是否为订阅回执
     * @param {*} instrument 该TICK所属合约
     * @param {*} tickType 该TICK数据类型
     * @param {*} tick TICK数据本身
     */
    handleTickChange(isReceipt, instrument, tickType, tick) {

        var isTick = tickType == this.systemTrdEnum.tickType.tick;
        var isYmd = tickType == this.systemTrdEnum.tickType.ztymd;
        var isTrans = tickType == this.systemTrdEnum.tickType.transaction;
        var isQueue = tickType == this.systemTrdEnum.tickType.orderQueue;

        if (!isTick && !isYmd && !isTrans && !isQueue) {
            return;
        }

        var targets = this.units.filter(x => x.stock.code == instrument);
        if (targets.length == 0) {
            return;
        }

        if (isTick) {

            targets.forEach(target => {

                let tickd = new TickData(tick);
                let buy1 = tickd.buys[0];
                let volume = buy1.hands;
                target.updateBuy1(volume * 0.01, volume * buy1.price, null);
            });
        }
        else if (isYmd) {

            let tacticQuote = tick[instrument];
            targets.forEach(target => {
                //
            });
        }
        else if (isTrans && !isReceipt) {

            let now = new Date().getTime();
            let ms = 3000;
            let ti = new TransactionItem(tick);
            let vbuy = ti.direction > 0 ? ti.volume : 0;
            let vsell = ti.direction < 0 ? ti.volume : 0;

            targets.forEach(target => {

                let list = target.transactions;
                let ts = target.trans;
                let is_as_new = ts.time == null || now - ts.time >= ms;

                if (is_as_new) {

                    list.shift();
                    list.push(ti);
                    ts.time = now;
                    ts.buy = vbuy;
                    ts.sell = vsell;
                }
                else {

                    ts.buy += vbuy;
                    ts.sell += vsell;
                }

                let latest = list[list.length - 1];
                latest.time = ti.time;
                latest.price = ti.price;
                latest.volume = ts.buy + ts.sell;
                latest.direction = ts.buy > ts.sell ? 1 : ts.buy < ts.sell ? -1 : 0;
            });
        }
        else if (isQueue) {
            
            if (tick instanceof Array) {
                targets.forEach(target => {
                    if (this.isVisible(target)) {
                        target.updateQueue(tick);
                    }
                });
            }
        }
    }

    /**
     * @param {AutoBuyUnit} unit 
     * @returns 
     */
    isVisible(unit) {

        if (this.$unit_box == undefined) {
            this.$unit_box = this.vapp.$el.querySelector('.buys-inter');
        }

        var $ubox = this.$unit_box;
        var $cnodes = $ubox.childNodes;
        var box_offset_height = $ubox.offsetHeight;
        var box_scroll_height = $ubox.scrollHeight;
        var box_top = $ubox.scrollTop;

        if (box_scroll_height - box_offset_height <= 100) {

            /**
             * 允许在误差或少量滚动条范围内，进行兼容
             */
            return true;
        }

        var u_index = this.units.findIndex(item => item === unit);
        var unit_margin_top = 4;
        var u_start = 0;

        for (let i = 0; i < u_index; i++) {
            u_start += ($cnodes[i].offsetHeight + (i > 0 ? unit_margin_top : 0));
        }

        var u_end = u_start + $cnodes[u_index].offsetHeight;
        var is_top_visible = u_end > box_top;
        var is_bottom_visible = u_start < box_top + box_offset_height;
        return is_top_visible && is_bottom_visible;
    }

    /**
     * @param {AutoBuyUnit} unit 
     */
    setAsCurrent(unit) {

        if (this.isFocused(unit)) {
            return;
        }

        this.states.focused = unit;
        this.log(`focus on an unit in auto list, stock/${unit.stock.code}/${unit.stock.name}`);
        this.trigger('unit-focused', this.title, unit.stock.code);
    }

    clearCurrent() {
        this.states.focused = null;
    }

    /**
     * @param {AutoBuyUnit} unit 
     */
    isFocused(unit) {
        return unit === this.states.focused;
    }

    /**
     * @param {Number|String} time 时间，格式为 hmmssfff000 整形数值
     */
    formatTransTime(time) {
        
        let stime = time.toString();
        if (stime.length == 11) {
            stime = '0' + stime;
        }

        return `${stime.substr(0, 2)}:${stime.substr(2, 2)}:${stime.substr(4, 2)} ${stime.substr(6, 3)}`;
    }

    /**
     * 将价格格式化为适合的精度
     * @param {Number} price 
     */
    precisePrice(price) {
        return typeof price == 'number' ? price.toFixed(2) : price;
    }

    /**
     * 简化手数显示
     * @param {Number} volume 
     */
    simplyHands(volume) {
        
        return volume;

        // if (volume <= 999999) {
        //     return volume;
        // }
        // else if (volume >= 1000000 && volume <= 99999999) {
        //     return (volume / 10000).toFixed(1) + '万';
        // }
        // else {
        //     return (volume / 100000000).toFixed(1) + '亿';
        // }
    }

    /**
     * 将价格格式化为适合的精度
     * @param {Number} price 
     */
    decidePriceColorClass(price) {

        if (price == 0 || typeof price != 'number') {
            return '';
        }
        
        let yesterday_close = 0;
        if (yesterday_close == 0 || yesterday_close == null) {
            return '';
        }
        else {
            return price > yesterday_close ? 's-color-red' : price < yesterday_close ? 's-color-green' : '';
        }
    }

    /**
     * @param {AutoBuyUnit} unit
     * @param {Number|String} time 时间，格式为 hmmssfff000 整形数值
     */
    isMinuteEnd(unit, time, trans_idx) {
        
        if (trans_idx <= 0 || trans_idx == unit.transactions.length - 1) {
            return false;
        }
        
        let trans2 = unit.transactions[trans_idx + 1];
        if (!trans2) {
            return false;
        }
        
        let time2 = trans2.time;
        if (!time || !time2) {
            return false;
        }

        let stime = time.toString();
        let stime2 = time2.toString();

        if (stime.length == 11) {
            stime = '0' + stime;
        }

        if (stime2.length == 11) {
            stime2 = '0' + stime2;
        }
        
        return stime.substr(2, 2) != stime2.substr(2, 2);
    }

    /**
     * @param {AutoBuyUnit} unit
     */
    popupEntrust(unit) {
        this.popper.open(unit.taskId, unit.stock.code, unit.stock.name);
    }

    /**
     * @param {AutoBuyUnit} unit 
     * @param {Boolean} isManual 
     */
    isCheckedOk(unit, isManual) {
        
        var message = null;

        if (this.accounts.length == 0) {
            return this.interaction.showError('没有可用账号，用于买入');
        }
        
        if (message) {
            this.interaction.showError(message);
        }

        return !message;
    }

    /**
     * @param {AutoBuyUnit} unit 
     */
    submitChange(unit) {

        var u_cd = unit.conditions;
        var t_cd = unit.task.cancelCondition;
        var tsk = unit.task;

        var task = new TaskObject({

            id: tsk.id,
            taskId: tsk.taskId,
            userId: tsk.userId,
            userName: tsk.userName,
            instrument: tsk.instrument,
	        instrumentName: null,
            stockLimitType: tsk.stockLimitType,
            direction: tsk.direction,
            priceFollowType: tsk.priceFollowType,
            orderPrice: tsk.orderPrice,
            limitPositionType: tsk.limitPositionType,
            positionPercent: tsk.positionPercent,
            strikeBoardStatus: tsk.strikeBoardStatus,
            supplementVolume: u_cd.bd.hands,
            supplementOpen: u_cd.bd.checked,
            splitInterval: tsk.splitInterval,
            splitType: tsk.splitType,
            // splitDetail: this.settings.spliting,
            splitDetail: tsk.splitDetail,
            cash: tsk.cash,
            creditFlag: tsk.creditFlag,
            boardStrategy: this.helper.deepClone(tsk.boardStrategy),

            cancelCondition: {

                afterLimitTickCount: t_cd.afterLimitTickCount,
                afterLimitTickEnabled: t_cd.afterLimitTickEnabled,
                beforeTradeCancel: t_cd.beforeTradeCancel,
                beforeCancelOpen: t_cd.beforeCancelOpen,
                downRate: t_cd.downRate,
                downRateOpen: t_cd.downRateOpen,
                extremeBack: t_cd.extremeBack,
                extremeBackOpen: t_cd.extremeBackOpen,
                sellOrderVolume: t_cd.sellOrderVolume,
                sellOrderVolumeOpen: t_cd.sellOrderVolumeOpen,

                customDownRate: u_cd.zdy.percent,
                customDownRateTime: u_cd.zdy.time,
                customDownRateOpen: u_cd.zdy.checked,

                cancelProtectedVolume: t_cd.cancelProtectedVolume,
                cancelProtectedTime: t_cd.cancelProtectedTime,
                cancelProtectedEnabled: u_cd.cancel.checked,

                tradeProtectedTime: t_cd.tradeProtectedTime,
                tradeProtectedEnabled: u_cd.trade.checked,

                lineupOrderVolume: u_cd.dyfd.hands,
                lineupOrderVolumeOpen: u_cd.dyfd.checked,
                effectiveTradedAmount: t_cd.effectiveTradedAmount,

                supplementTime: tsk.supplementTime,
                supplementOrderVolume: u_cd.bd.hands,
                supplementEnabled: u_cd.bd.checked,

                followCancel: u_cd.jzcd.hands,
                followCancelOpen: u_cd.jzcd.checked,

                hasSupplement: t_cd.hasSupplement,
                hasCanceled: t_cd.hasCanceled,
            },
        });

        this.renderProcess.send(this.systemEvent.transmitMsg2TradingServer, Cm20FunctionCodes.request.modify, task);
        this.log(`to submit an auto unit change, task = ${JSON.stringify(task)}`);
        return true;
    }

    confirm(isRequired, message, confirmed_callback, canceled_callback) {

        if (isRequired) {
            
            this.interaction.showConfirm({

                title: '操作确认',
                message: message,
                confirmed: () => {
                    confirmed_callback();
                },
                canceled: () => {
                    canceled_callback();
                },
            });
        }
        else {
            confirmed_callback();
        }
    }

    /**
     * @param {AutoBuyUnit} unit 
     */
    cancel(unit) {
        
        unit.conditions.cancel.checked = false;
        unit.conditions.trade.checked = false;
        unit.conditions.zdy.checked = false;
        unit.conditions.jzcd.checked = false;
        unit.conditions.dyfd.checked = false;
        unit.conditions.bd.checked = false;
        this.handleSomeChange(unit);
    }

    /**
     * @param {AutoBuyUnit} unit 
     */
    cancelOrder(unit) {

        this.confirm(this.settings ? this.settings.prompt.mcancel : true, `${unit.stock.name}, 进行撤单？`, () => {

            this.log(`to cancel a auto-buy task, stock/${unit.stock.code}/${unit.stock.name}, task id/${unit.taskId}`);
            this.renderProcess.send(this.systemEvent.transmitMsg2TradingServer, Cm20FunctionCodes.request.cancel, { id: unit.taskId });
        });
    }

    /**
     * @param {AutoBuyUnit} unit 
     */
    handlePercentChange(unit) {
        this.handleSomeChange(unit);
    }

    /**
     * @param {AutoBuyUnit} unit 
     */
    handleSomeChange(unit) {
        
        if (this.submitChange(unit)) {

            this.log(`submit the hot change of an auto unit`);
            this.interaction.showSuccess(`${unit.stock.name}，监控参数变动，已提交。`);
        }
    }

    /**
     * @param {AutoBuyUnit} unit 
     */
    ring4Canceled(unit) {

        if (this.settings) {

            let rt = this.settings.rington;
            this.play(rt.canceled, rt.customized ? rt.customized.canceled : undefined);
        }
    }

    /**
     * @param {AutoBuyUnit} unit 
     */
    ring4Bought(unit) {

        if (this.settings) {

            let rt = this.settings.rington;
            this.play(rt.entrusted, rt.customized ? rt.customized.entrusted : undefined);
        }
    }

    handleReconnect() {

        this.units.forEach(item => {

            this.log(`to resub tick in auto view while re-connected, stock/${item.stock.code}`);
            this.subscribeTick(item.stock.code);
        });
    }

    build($container) {

        super.build($container);
        this.createApp();
        this.renderProcess.on(this.systemEvent.tradingServerReestablished, () => { this.handleReconnect(); });
    }
};