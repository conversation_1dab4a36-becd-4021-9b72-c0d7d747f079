<div class="header-inter" style="background-color: #0C1016;">

	<el-button size="mini" @click="openSetting">
		<i class="el-icon-setting"></i> 设置</el-button>

	<el-button size="mini" @click="openPool">
		<i class="el-icon-files"></i> 票池</el-button>

	<el-button size="mini" @click="openStra">
		<i class="el-icon-menu"></i> 策略管理</el-button>

	<el-button size="mini" @click="openCompleted">
		<i class="el-icon-tickets"></i> 查看历史策略</el-button>

	<el-input v-model="date" class="s-mgl-10 s-mgr-10" placeholder="日期YYYYMMDD" style="width: 100px; border: 1px solid #666;"></el-input>

	<el-button size="mini" @click="downloadTaskLog" class="s-mgr-10">
		<i class="el-icon-download"></i> 导出策略日志</el-button>
		
	<span class="monitor s-pull-right">
		<label class="prop-name">总可用</label>
		<label class="prop-value">{{ typeof zky == 'number' ? thousandsDecimal(zky) : '--' }}</label>
		<label class="prop-name">总可融</label>
		<label class="prop-value">{{ typeof zkr == 'number' ? thousandsDecimal(zkr) : '--' }}</label>
		<label class="prop-name">总资产</label>
		<label class="prop-value">{{ typeof zzc == 'number' ? thousandsDecimal(zzc) : '--' }}</label>
		<label class="prop-name">冻结资金</label>
		<label class="prop-value">{{ typeof djzj == 'number' ? thousandsDecimal(djzj) : '--' }}</label>
	</span>

	<div style="height: 0; overflow: auto;">
		<div class="table-4-export">
			<table>
				<tr>
					<th label="ID" prop="id" formatter="render2String"></th>
					<th label="日期" prop="tradingDay"></th>
					<th label="策略ID" prop="strikeId" formatter="render2String"></th>
					<th label="合约代码" prop="instrument"></th>
					<th label="合约名称" prop="instrumentName"></th>
					<th label="自动策略ID" prop="taskId" formatter="render2String"></th>
					<th label="票池" prop="ticketPoolName"></th>
					<th label="设置" prop="autoStrikeBoardSettingName"></th>
					<th label="类型" prop="type" formatter="renderType"></th>
					<th label="剔除原因" prop="reason"></th>
					<th label="下单时间" prop="orderTime" formatter="render2String"></th>
					<th label="下单参数" prop="orderParam"></th>
					<th label="下单回报用时(毫秒)" prop="orderUseTime"></th>
					<th label="撤单时间" prop="cancelTime" formatter="render2String"></th>
					<th label="撤单参数" prop="cancelParam"></th>
					<th label="补单时间" prop="supplementTime" formatter="render2String"></th>
					<th label="补单参数" prop="supplementPram"></th>
				</tr>
			</table>
		</div>
	</div>
	
</div>
