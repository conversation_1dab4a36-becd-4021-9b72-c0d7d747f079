const { IView } = require('../../../../component/iview');
const { NumberMixin } = require('../../../../mixin/number');
const { Setting2Pool } = require('../../components/objects');
const { repo20Cm } = require('../../../../repository/20cm');
const { SmartTable } = require('../../../../libs/table/smart-table');

module.exports = class HeaderView extends IView {

    constructor() {

        super('@20cm/autom/components/header', false);
        this.registerEvent('account-change', (args) => { this.updateSummary(args); });
        this.registerEvent('strategy-change', (strategy, type) => { this.handleStrategyChange(strategy, type); });
    }

    updateSummary({ zky, zkr, zzc, djzj }) {

        this.data.zky = zky;
        this.data.zkr = zkr;
        this.data.zzc = zzc;
        this.data.djzj = djzj;
    }

    /**
     * @param {Setting2Pool} strategy 
     * @param {String} type 
     */
    handleStrategyChange(strategy, type) {
        
        let behavior = type == 'started' ? '<span class="s-color-blue s-bold">启动</span>' : '<span class="s-color-red s-bold">停止</span>'
        this.interaction.notify({

            position: 'top-right',
            title: '策略启停提醒',
            duration: 0,
            dangerouslyUseHTMLString: true,
            message: `${behavior} | ${strategy.autoStrikeBoardSettingName} | ${strategy.ticketPoolName} | ${strategy.stockLimitType == 1 ? '10%' : '20%'}`,
        });
    }

    async downloadTaskLog() {

        var resp = await repo20Cm.queryTaskHistory(this.data.date);
        var { errorCode, errorMsg, data } = resp;

        if (errorCode != 0 || !(data instanceof Array)) {
            return this.interaction.showError('策略日志导出错误: ' + errorMsg);
        }

        data.forEach((item, item_idx) => { item.sequenceId = item_idx + 1; });

        if (this.tableObj == undefined) {

            var $table = this.vapp.$el.querySelector('.table-4-export');
            this.tableObj = new SmartTable($table, this.identify, this, {

                tableName: '20cm-auto-task-log',
                displayName: '策略日志',
                pageSize: 9999,
            });
        }

        this.tableObj.refill(data);
        this.tableObj.exportAllRecords(`策略日志-${this.data.date || new Date().format('yyyyMMdd')}`);
    }

    identify(record) {
        return record.sequenceId;
    }

    renderType(record) {
        return record.type == 1 ? '买入' : '剔除';
    }

    render2String(record, field_value) {
        return typeof field_value == 'number' ? field_value.toString() : field_value;
    }

    createApp() {

        this.data = {

            zky: null,
            zkr: null,
            zzc: null,
            djzj: null,
            date: new Date().format('yyyyMMdd'),
        };

        this.vapp = new Vue({

            el: this.$container.firstElementChild.firstElementChild,
            data: this.data,
            mixins: [NumberMixin],
            methods: {
                
                openSetting: () => {
                    this.trigger('open-auto-setting');
                },

                openPool: () => {
                    this.trigger('open-auto-pool');
                },

                openStra: () => {
                    this.trigger('open-auto-stra');
                },

                openCompleted: () => {
                    this.trigger('open-auto-completed');
                },

                downloadTaskLog: () => {
                    this.downloadTaskLog();
                },
            }
        });
    }

    build($container) {

        super.build($container);
        this.createApp();
    }
};