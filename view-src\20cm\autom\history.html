<div class="v20cm v20cm-history themed-bg-harder" v-show="dialog.visible">
	<el-dialog width="520px"
				title="自动买入 - 策略历史"
				class="dialog-20cm-history"
				:visible="dialog.visible"
				:close-on-click-modal="false"
				:close-on-press-escape="false"
				:show-close="true"
				@close="close">

		<template>
			<div class="buy-histories">
				<div v-for="(task, task_idx) in tasks" 
					:key="task_idx"
					class="identity-history-task stock-task">
			
					<div class="title typical-header">
						<span>{{ task.instrument }} ({{ task.instrumentName }})</span>
					</div>

					<div class="task-body" style="padding-bottom: 0;">

						<div class="condition-row s-mgt-10 s-mgb-10">

							<label class="prop-name">票池: </label>
							<label class="prop-value">{{ task.ticketPoolName || '[票池名称]' }}</label>
							<label class="prop-name s-pdl-10">封单参数: </label>
							<label class="prop-value">
								{{ typeof task.boardStrategy.strategyVolume == 'number' 
									? thousands(task.boardStrategy.strategyVolume) : '---' }} 手</label>

						</div>

						<div class="condition-row">

							<el-checkbox :disabled="true" v-model="task.cancelCondition.cancelProtectedEnabled">撤单保护</el-checkbox>
							<label class="input-unit s-pdl-5">保护手数</label>
							<el-input :disabled="true" v-model="task.cancelCondition.cancelProtectedVolume" class="medium-input"></el-input>
							<label class="input-unit s-pdl-5">保护分钟</label>
							<el-input :disabled="true" v-model="task.cancelCondition.cancelProtectedTime" class="short-input"></el-input>

						</div>

						<div class="condition-row">

							<el-checkbox :disabled="true" v-model="task.cancelCondition.tradeProtectedEnabled">成交保护</el-checkbox>
							<label class="input-unit s-pdl-5">保护分钟</label>
							<el-input :disabled="true" v-model="task.cancelCondition.tradeProtectedTime" class="short-input"></el-input>

						</div>

						<div class="condition-row">

							<el-checkbox :disabled="true" v-model="task.cancelCondition.customDownRateOpen">自定义下降比例</el-checkbox>
							<el-input :disabled="true" v-model="task.cancelCondition.customDownRate" class="short-input"></el-input>
							<label class="input-unit s-pdl-5">%</label>
							<el-input :disabled="true" v-model="task.cancelCondition.customDownRateTime" class="short-input"></el-input>
							<label class="input-unit s-pdl-5">毫秒</label>

						</div>

						<div class="condition-row">

							<el-checkbox :disabled="true" v-model="task.cancelCondition.followCancelOpen">精准撤单</el-checkbox>
							<el-input :disabled="true" v-model="task.cancelCondition.followCancel" class="medium-input"></el-input>
							<label class="input-unit s-pdl-5">手</label>

						</div>

						<div class="condition-row">

							<el-checkbox :disabled="true" v-model="task.cancelCondition.lineupOrderVolumeOpen">低于封单量</el-checkbox>
							<el-input :disabled="true" v-model="task.cancelCondition.lineupOrderVolume" class="medium-input"></el-input>
							<label class="input-unit s-pdl-5">手</label>

						</div>

						<div class="condition-row">

							<el-checkbox :disabled="true" v-model="task.cancelCondition.supplementEnabled">补单</el-checkbox>
							<el-input :disabled="true" v-model="task.cancelCondition.supplementOrderVolume" class="medium-input"></el-input>

						</div>

						<div class="condition-row s-mgt-10 s-mgb-10">

							<label class="prop-name">资金(万): </label>
							<label class="prop-value">{{ typeof task.usedMargin == 'number' ? thousands(task.usedMargin / 10000) : '---' }}</label>

						</div>
		
					</div>
			
				</div>
			</div>
			<template v-if="tasks.length == 0">
				<div class="no-data-notice">
					<div style="line-height: 100px; text-align: center;">
						<i class="el-icon-moon"></i> 没有已完成策略
					</div>
				</div>
			</template>
		</template>
	</el-dialog>
</div>
