const { BaseView } = require('../base-view');
const { NumberMixin } = require('../../../mixin/number');
const { TaskObject } = require('../components/objects');
const { repo20Cm } = require('../../../repository/20cm');
const { repoInstrument } = require('../../../repository/instrument');

module.exports = class HistoryView extends BaseView {

    constructor(view_name, is_standalone_window) {

        super(view_name, is_standalone_window, '策略历史');

        this.dialog = {
            visible: true,
        };

        this.consts = {
            powering: 10000,
        };

        this.tasks = [new TaskObject({})].splice(1);
        this.states = {
            focused: this.tasks.length > 0 ? this.tasks[0] : null,
        };
    }

    createApp() {

        this.historyApp = new Vue({

            el: this.$container.firstElementChild,
            data : {

                dialog: this.dialog,
                states: this.states,
                tasks: this.tasks,
            },
            mixins: [NumberMixin],
            methods: this.helper.fakeVueInsMethod(this, [this.close]),
        });
    }

    /**
     * @param {TaskObject} task 
     */
    async requestLimitedPrice(task) {

        var resp = await repoInstrument.queryPrice(task.stock.code);
        var { errorCode, errorMsg, data } = resp;
        var { assetType, preClosePrice, lastPrice, lowerLimitPrice, optionType, priceTick, strikePrice, upperLimitPrice, volumeMultiple } = data || {};

        if (errorCode == 0 && data && upperLimitPrice > 0) {

            task.updateLimits(lowerLimitPrice, upperLimitPrice);
            task.updateLatest(lastPrice);
        }
    }

    close() {

        this.dialog.visible = false;
        this.setWindowAsBlockedWaiting(false);
    }
    
    /**
     * @param {Array<TaskObject>} tasks
     */
    fillTasks(tasks) {

        tasks.forEach(t => {
            t.cancelCondition.cancelProtectedVolume = parseInt(t.cancelCondition.cancelProtectedVolume / 100);
        });

        this.tasks.refill(tasks);
        // tasks.forEach(task => { this.requestLimitedPrice(task); });
    }

    async requestTasks() {

        var resp = await repo20Cm.queryFinishedTasks(1);
        var { errorCode, errorMsg, data } = resp;

        if (errorCode != 0) {
            return this.interaction.showError(`已完成策略，查询错误：${errorMsg}`);
        }

        var tasks = data || [];
        this.fillTasks(tasks);
    }

    /**
     * @param {HTMLElement} $container 
     */
    build($container) {

        super.build($container);
        this.createApp();
        this.requestTasks();
    }
};