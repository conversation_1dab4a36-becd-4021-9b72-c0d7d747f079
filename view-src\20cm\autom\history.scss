.v20cm-history {

	padding: 0;
	
	.dialog-20cm-history {
	
		.el-dialog__body {
			padding: 0;
		}
	}
}

.buy-histories {

	padding: 0 10px 10px 10px;
	max-height: 800px;
	overflow-y: auto;

	.stock-task {

		margin-top: 10px;
		background-color: #23354D;
		border-radius: 2px 2px 0 0;
		border: 1px solid #283A56;

		.title {

			height: 28px;
			line-height: 28px;
			padding: 0 8px;
			border-bottom: 1px solid #0c1016;
		}

		.task-body {
			padding-left: 10px;
		}

		&.focused {

			border: 1px solid #2099FE;

			.title {
				background-color: #1D5DB0;
			}

			.task-body {
				background-color: #113362;
			}
		}
	}

	.condition-row {
		margin-top: 5px;
	}

	.short-input {
		width: 55px;
	}

	.medium-input {
		width: 70px;
	}

	.el-input--mini .el-input__inner {

		height: 22px;
		line-height: 22px;
	}

	.el-checkbox__input.is-disabled.is-checked .el-checkbox__inner {
		background-color: #ee094b;
	}
}