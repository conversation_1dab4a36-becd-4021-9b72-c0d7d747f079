<div class="v20cm v20cm-pool themed-bg-harder" v-show="dialog.visible">
	<el-dialog width="650px"
				title="自动监控票池管理"
				:visible="dialog.visible"
				:close-on-click-modal="false"
				:close-on-press-escape="false"
				:show-close="true"
				@close="close">

		<template>

			<div class="content-area">

				<div class="toolbar">

					<el-radio v-model="states.way" :label="ways.existed" @change="handleWayChange">导入已有票池</el-radio>
					<el-radio v-model="states.way" :label="ways.creating" @change="handleWayChange">导入新票池</el-radio>
					<el-select v-if="is2Existsed()" v-model="states.poolId" @change="handlePoolChange" style="width: 140px;" clearable>
						<el-option v-for="(item, item_idx) in pools" :key="item_idx" :value="item.id" :label="item.ticketPoolName"></el-option>
					</el-select>
					<el-input v-else v-model.trim="states.name" placeholder="给该票池取个名字" style="width: 160px;" class="s-mgl-10" clearable></el-input>

					<el-tooltip content="可支持 txt/csv/excel 格式，无需标题，一行一个合约">
						<el-button type="success" @click="importStocks" class="s-mgl-10">
							<i class="iconfont icon-daoru"></i> 导入标的</el-button>
					</el-tooltip>

					<el-button type="primary" @click="add2Pool" class="s-mgl-10">
						<i class="iconfont icon-baocun"></i> 保存票池</el-button>

					<el-button v-if="is2Existsed()" type="danger" @click="deletePool" class="s-mgl-10">
						<i class="el-icon-remove"></i> 删除票池</el-button>

				</div>

				<div class="cate-title">
					<span class="title">10% 股票</span>
					<span class="title">20% 股票</span>
				</div>

				<div class="stocks">

					<div class="stock-cate">
						<div class="stock-cate-inter">
							<div v-for="(item, item_idx) in stocks10" :key="item.instrument" class="each-stock">
								{{ stocks10.length - item_idx }}. {{ item.instrument }} / {{ item.instrumentName }}</div>
						</div>
					</div>

					<div class="stock-cate">
						<div class="stock-cate-inter">
							<div v-for="(item, item_idx) in stocks20" :key="item.instrument" class="each-stock">
								{{ stocks20.length - item_idx }}. {{ item.instrument }} / {{ item.instrumentName }}</div>
						</div>
					</div>

				</div>

			</div>

		</template>	

	</el-dialog>
	
</div>
