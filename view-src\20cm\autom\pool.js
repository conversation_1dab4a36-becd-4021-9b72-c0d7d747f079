const { BaseView } = require('../base-view');
const { repo20Cm } = require('../../../repository/20cm');
const { BizHelper } = require('../../../libs/helper-biz');

/**
 * @returns {Array<{ id: Number, ticketPoolName: String, ticketPoolType: Number }>}
 */
function makePools() {
    return [];
}

/**
 * @returns {Array<PoolStock>}
 */
function makeStocks() {
    return [];
}

class PoolStock {

    constructor(struc) {

        this.id = struc.id;
        this.instrument = struc.instrument;
        this.instrumentName = struc.instrumentName;
        this.stockLimitType = struc.stockLimitType;
        this.ticketPoolId = struc.ticketPoolId;
        this.ticketPoolName = struc.ticketPoolName;
    }
}

module.exports = class PoolView extends BaseView {

    constructor(view_name, is_standalone_window) {

        super(view_name, is_standalone_window, '票池管理');

        this.dialog = {
            visible: true,
        };

        this.ways = { existed: 1, creating: 2 };
        this.stockTypes = { ten: 1, twenty: 2 };
        this.states = { poolId : null, way: this.ways.existed, name: null };

        /** 票池 */
        this.pools = makePools();

        /** 票池 */
        this.stocks10 = makeStocks();
        this.stocks20 = makeStocks();
    }

    createApp() {

        this.poolApp = new Vue({

            el: this.$container.firstElementChild,

            data : {

                dialog: this.dialog,
                ways: this.ways,
                states: this.states,
                pools: this.pools,
                stocks10: this.stocks10,
                stocks20: this.stocks20,
            },
            computed: {
                //
            },
            methods: this.helper.fakeVueInsMethod(this, [

                this.close,
                this.importStocks,
                this.add2Pool,
                this.deletePool,
                this.is2Existsed,
                this.handleWayChange,
                this.handlePoolChange,
            ]),
        });
    }

    is2Existsed() {
        return this.states.way == this.ways.existed;
    }

    close() {

        this.dialog.visible = false;
        this.setWindowAsBlockedWaiting(false);
    }

    async importStocks() {

        const { dialog } = require('@electron/remote');
        const paths = dialog.showOpenDialogSync(this.thisWindow, {
            
            title: '导入标的',
            filters: [{ name: '支持文件类型', extensions: ['xlsx', 'xls', 'csv', 'txt'] }],
        });

        this.handleUploadDialog(paths);
    }

    handleUploadDialog(paths) {

        if (!paths) {
            return;
        }

        /**
         * @param {String} expression 
         */
        function shorted(expression) {
            return expression.replace(/szse|shse|szse|shse|sh|sz|\./ig, '');
        }

        const fs = require('fs');
        const xlsx = require('node-xlsx');
        var fullPath = paths[0];
        var isTxt = fullPath.endsWith('.txt');
        var records = [{ code: '', exch: '', name: '' }].splice(1);
        var AllStocks = BizHelper.stocks;

        if (isTxt) {

            let binar = fs.readFileSync(fullPath, {});
            let content = binar.toString('utf8');
            let list = content.split('\n').map(x => x.trim()).filter(x => x.length > 0);

            let stocks = AllStocks.filter(st => {
                return list.some(token => shorted(token) == st.instrument || token == st.instrumentName);
            });

            if (stocks.length > 0) {
                records.merge(stocks.map(x => ({ code: x.instrument, exch: x.exchangeId, name: x.instrumentName })));
            }
        }
        else {
            
            let file = fs.readFileSync(fullPath);
            let sheets = xlsx.parse(file);
            let matrix = sheets[0].data;

            if (matrix instanceof Array) {

                matrix.forEach(values => {

                    let token = values instanceof Array && values.length > 0 ? values[0] : null;
                    if (!token) {
                        return;
                    }

                    let tkstr = token.toString();
                    while (tkstr.length < 6) {
                        tkstr = '0' + tkstr;
                    }

                    let matched = AllStocks.find(x => shorted(tkstr) == x.instrument || tkstr == x.instrumentName || token == x.instrumentName);
                    if (matched) {
                        records.push({ code: matched.instrument, exch: matched.exchangeId, name: matched.instrumentName });
                    }
                });
            }
        }
        
        if (records.length == 0) {
            return this.interaction.showAlert('导入文件未包含标的');
        }

        var tens = records.filter(x => x.code.indexOf('30') != 0 && x.code.indexOf('688') != 0);
        var twenties = records.filter(x => x.code.indexOf('30') == 0 || x.code.indexOf('688') == 0);

        this.stocks10.refill(tens.map(x => ({

            instrument: x.exch + '.' + x.code, 
            instrumentName: x.name, 
            stockLimitType: this.stockTypes.ten, 
            ticketPoolId: this.states.poolId,
            ticketPoolName: null,
        })));

        this.stocks20.refill(twenties.map(x => ({

            instrument: x.exch + '.' + x.code, 
            instrumentName: x.name, 
            stockLimitType: this.stockTypes.twenty, 
            ticketPoolId: this.states.poolId,
            ticketPoolName: null,
        })));
    }

    async add2Pool() {

        var { poolId, name } = this.states;

        if (this.is2Existsed()) {
            
            if (this.pools.length == 0) {
                return this.interaction.showError('无可用票池，请选择导入为新票池');
            }
            else if (this.helper.isNone(poolId)) {
                return this.interaction.showError('请选择目标票池');
            }
        }
        else {

            if (!name) {
                return this.interaction.showError('请指定一个票池名称');
            }
            else if (this.pools.some(x => x.ticketPoolName == name)) {
                return this.interaction.showError('相同票池名称已存在');
            }
        }

        if (this.stocks10.length == 0 && this.stocks20.length == 0) {
            return this.interaction.showError('票池内未包含标的');
        }

        var resp;
        var stocks = this.stocks10.concat(this.stocks20).map(x => x.instrument);

        if (this.is2Existsed()) {

            let selected = this.pools.find(x => x.id == this.states.poolId);
            resp = await repo20Cm.add2Pool(selected.id, stocks);
        }
        else {

            let resp2 = await repo20Cm.createPool({ ticketPoolName: this.states.name, ticketPoolType: 2 });
            let { data, errorCode, errorMsg } = resp2;
            
            if (errorCode != 0) {
                return this.interaction.showError('新票池创建失败');
            }

            let poolId = data.id;
            this.pools.push(data);
            this.states.poolId = poolId;
            resp = await repo20Cm.add2Pool(poolId, stocks);
        }

        var { data, errorCode, errorMsg } = resp;

        if (errorCode == 0) {
            this.interaction.showSuccess('票池标的已保存');
        }
        else {
            this.interaction.showError(errorMsg);
        }
    }

    async deletePool() {
        
        if (!this.is2Existsed()) {

            /**
             * 不适用的场景
             */
            return;
        }

        var resp = await repo20Cm.deletePool(this.states.poolId);
        var { data, errorCode, errorMsg } = resp;
        
        if (errorCode != 0) {
            this.interaction.showError('票池删除失败：' + errorMsg);
        }
        else {
            
            this.interaction.showSuccess('票池已删除');
            this.pools.remove(x => x.id == this.states.poolId);
            this.states.poolId = this.pools.length > 0 ? this.pools[0].id : null;
            this.handlePoolChange();
        }
    }

    handleWayChange() {

        this.states.name = null;
        this.handlePoolChange();
    }

    async handlePoolChange() {

        if (this.helper.isNone(this.states.poolId) && this.states.way == this.ways.creating) {

            this.stocks10.clear();
            this.stocks20.clear();
            return;
        }
        
        var resp = await repo20Cm.queryPoolDetail(this.states.poolId);
        var { data, errorCode, errorMsg } = resp;

        if (errorCode != 0 || !(data instanceof Array)) {
            return this.interaction.showError('票池包含标的查询错误');
        }

        var stocks = data.map(x => new PoolStock(x));
        this.stocks10.refill(stocks.filter(x => x.stockLimitType == this.stockTypes.ten));
        this.stocks20.refill(stocks.filter(x => x.stockLimitType == this.stockTypes.twenty));
    }

    async loadPools() {

        var resp = await repo20Cm.queryPools();

        if (resp.errorCode != 0) {
            return this.interaction.showError('票池数据，获取出错');
        }

        var pools = resp.data;
        if (pools instanceof Array && pools.length > 0) {

            this.pools.refill(pools);
            this.states.poolId = pools[0].id;
        }
        else {

            this.pools.clear();
            this.states.poolId = null;
        }

        this.handlePoolChange();
    }
    
    /**
     * @param {HTMLElement} $container 
     */
    build($container) {

        super.build($container);
        this.createApp();
        this.loadPools();
    }
};