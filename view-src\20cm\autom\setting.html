<div class="v20cm v20cm-auto-setting themed-bg-harder" v-show="dialog.visible">
	<el-dialog width="1100px"
				title="自动化监控设置"
				class="dialog-20cm-auto-setting"
				:visible="dialog.visible"
				:close-on-click-modal="false"
				:close-on-press-escape="false"
				:show-close="true"
				@close="close">

		<template>

			<div class="content-area">

				<div class="block-title">
					<span>基础选股设置</span>
					<el-button type="primary" size="small" @click="saveBasic" class="s-mgl-20">
						<i class="iconfont icon-baocun"></i>
						保存基础设置
					</el-button>
				</div>

				<div class="basic-setting">
	
					<div v-for="(item, item_idx) in [basic.ten, basic.twenty]" :key="item_idx" class="half-box">
	
						<div class="sub-title s-mgb-5">{{ item_idx == 0 ? 10 : 20 }}% 股票</div>
	
						<div class="input-row">
							<span>过去</span>
							<el-input-number :controls="false" :precision="0" v-model="item.a1"></el-input-number>
							<span>天</span>
							<span>, 涨幅&lt;</span>
							<el-input-number :controls="false" :precision="2" v-model="item.a2"></el-input-number>
							<span>% </span>
							<el-input-number :controls="false" :precision="0" v-model="item.a3"></el-input-number>
							<span>天未涨停</span>
						</div>
	
						<div class="input-row">
							<span>流通市值: </span>
							<el-input-number :controls="false" :precision="0" v-model="item.b1"></el-input-number>
							<span> - </span>
							<el-input-number :controls="false" :precision="0" v-model="item.b2"></el-input-number>
							<span>亿元</span>
						</div>

						<div class="input-row">
							<span>剔除当天振幅 &gt; </span>
							<el-input-number :controls="false" :precision="1" :min="0" :max="99.9" v-model="item.c1"></el-input-number>
							<span>%</span>
							<span>, 收盘涨幅 &gt; </span>
							<el-input-number :controls="false" :precision="1" :min="-99.9" :max="99.9" v-model="item.c2"></el-input-number>
							<span>%</span>
						</div>
	
					</div>
	
				</div>

				<div class="block-title">
					<span>自定义设置</span>
					<el-select v-model="states.settingId" @change="handleSettingChange" class="select-ctr s-mgl-20" clearable>
						<el-option v-for="(item, item_idx) in settings" :key="item_idx" :value="item.id" :label="item.name"></el-option>
					</el-select>
					<el-button type="primary" size="small" @click="createSetting" class="s-mgl-5">
						<i class="iconfont icon-tianjia"></i>
						创建
					</el-button>
					<el-button v-if="states.settingId" type="danger" size="small" @click="deleteteSetting" class="s-mgl-5">
						<i class="el-icon-delete"></i>
						删除
					</el-button>
				</div>
	
				<div class="custom-setting-external">

					<div class="custom-toolbar">
						<span>保存名称</span>
						<el-input v-model.trim="setting.name" placeholder="给该设置取个名字" style="width: 160px;" clearable></el-input>
						<el-button type="primary" size="small" @click="saveAuto" class="s-mgl-5">
							<i class="iconfont icon-baocun"></i>
							保存
						</el-button>
						<span class="s-pdl-20 s-pdr-5">票池</span>
						<el-select v-model="states.poolId" @change="handlePoolChange" class="select-ctr" clearable>
							<el-option v-for="(item, item_idx) in pools" :key="item_idx" :value="item.id" :label="item.ticketPoolName"></el-option>
						</el-select>
						<el-button type="primary" size="small" @click="linkPool">
							<i class="el-icon-link"></i>
							关联票池
						</el-button>
					</div>

					<div class="custom-setting">

						<div v-for="(item, item_idx) in [setting.ten, setting.twenty]" :key="item_idx" class="half-box">
							
							<div class="sub-title bottom-lined">{{ item_idx == 0 ? 10 : 20 }}% 选股 - 选股设置</div>
		
							<div class="input-row">
								<el-checkbox v-model="item.a1">涨停后</el-checkbox>
								<el-input-number :controls="false" :precision="0" v-model="item.a2" :disabled="!item.a1"></el-input-number>
								<span>个Tick未达一板封量</span>
							</div>
		
							<div class="input-row">
								<el-checkbox v-model="item.b1">板前成交量大于前一板总量的</el-checkbox>
								<el-input-number :controls="false" :precision="2" v-model="item.b2" :disabled="!item.b1"></el-input-number>
								<span>%</span>
							</div>
		
							<div class="input-row">
								<el-checkbox v-model="item.c1">开盘涨幅: </el-checkbox>
								<el-input-number :controls="false" :precision="2" v-model="item.c2" :disabled="!item.c1"></el-input-number>
								<span>% - </span>
								<el-input-number :controls="false" :precision="2" v-model="item.c3" :disabled="!item.c1"></el-input-number>
								<span>%&nbsp;&nbsp;</span>
								<el-checkbox v-model="item.c4">板前成交额: </el-checkbox>
								<el-input-number :controls="false" :precision="0" v-model="item.c5" :disabled="!item.c4"></el-input-number>
								<span>万</span>
							</div>
		
							<div class="input-row">
								<el-checkbox v-model="item.d1">涨停价: </el-checkbox>
								<el-input-number :controls="false" :precision="2" v-model="item.d2" :disabled="!item.d1"></el-input-number>
								<span>元 - </span>
								<el-input-number :controls="false" :precision="2" v-model="item.d3" :disabled="!item.d1"></el-input-number>
								<span>元</span>
							</div>
		
							<div class="sub-title bottom-lined">交易设置</div>
		
							<div class="input-row">
								<span>买入金额: </span>
								<el-input-number :controls="false" :precision="0" v-model="item.e1"></el-input-number>
								<span>万&nbsp;&nbsp;</span>
								<el-checkbox v-model="item.e2">优先融资</el-checkbox>
							</div>
		
							<div class="input-row">
								<el-checkbox v-model="item.f1">封单比例:</el-checkbox>
								<el-input-number :controls="false" :precision="2" v-model="item.f2" :disabled="!item.f1"></el-input-number>
								<span>%&nbsp;&nbsp;</span>
								<el-checkbox v-model="item.f3">最小封单量:</el-checkbox>
								<el-input-number :controls="false" :precision="0" v-model="item.f4" :disabled="!item.f3"></el-input-number>
								<span>手</span>
							</div>

							<div class="input-row">
								<el-checkbox v-model="item.m1">跟单:</el-checkbox>
								<el-input-number :controls="false" :precision="0" v-model="item.m2" :disabled="!item.m1"></el-input-number>
								<span>张&nbsp;&nbsp;</span>
								<el-input-number :controls="false" :precision="0" v-model="item.m3" :disabled="!item.m1"></el-input-number>
								<span>万</span>
							</div>
		
							<div class="input-row">
								<span>运行时间: </span>
								<el-input-number :controls="false" :precision="0" v-model="item.g1"></el-input-number>
								<span> - </span>
								<el-input-number :controls="false" :precision="0" v-model="item.g2"></el-input-number>
								<el-checkbox v-model="item.g3">涨停序列: </el-checkbox>
								<el-input-number :controls="false" :precision="0" v-model="item.g4" :disabled="!item.g3"></el-input-number>
							</div>
		
							<div class="sub-title bottom-lined">撤单设置</div>
		
							<div class="input-row">
								<el-checkbox v-model="item.h1">撤单保护: </el-checkbox>
								<el-input-number :controls="false" :precision="0" v-model="item.h2" :disabled="!item.h1"></el-input-number>
								<span>手</span>
								<span> 时间:</span>
								<el-input-number :controls="false" :precision="0" v-model="item.h3" :disabled="!item.h1"></el-input-number>
								<span>分钟</span>
							</div>
		
							<div class="input-row">
								<el-checkbox v-model="item.i1">成交保护 </el-checkbox>
								<span>&nbsp;&nbsp;时间:</span>
								<el-input-number :controls="false" :precision="0" v-model="item.i2" :disabled="!item.i1"></el-input-number>
								<span>分钟</span>
							</div>
		
							<div class="input-row">
								<el-checkbox v-model="item.j1">低于封单量</el-checkbox>
								<el-input-number :controls="false" :precision="0" v-model="item.j2" :disabled="!item.j1"></el-input-number>
								<span>手</span>
							</div>
		
							<div class="input-row">
								<el-checkbox v-model="item.k1">自定义下降比例</el-checkbox>
								<el-input-number :controls="false" :precision="2" v-model="item.k2" :disabled="!item.k1"></el-input-number>
								<span>%</span>
								<el-input-number :controls="false" :precision="0" v-model="item.k3" :disabled="!item.k1"></el-input-number>
								<span>毫秒</span>
								<span>&nbsp;&nbsp;有效成交额: </span>
								<el-input-number :controls="false" :precision="0" v-model="item.k4"></el-input-number>
								<span>万</span>
							</div>
		
							<div class="sub-title bottom-lined">补单设置</div>
		
							<div class="input-row">
								<el-checkbox v-model="item.l1">增加委托量</el-checkbox>
								<el-input-number :controls="false" :precision="0" v-model="item.l2" :disabled="!item.l1"></el-input-number>
								<span>手</span>
								<span>&nbsp;&nbsp;观测时间: </span>
								<el-input-number :controls="false" :precision="0" :min="10" :max="2999" v-model="item.l3" :disabled="!item.l1"></el-input-number>
								<span>毫秒</span>
							</div>
		
						</div>
		
					</div>
	
				</div>

			</div>

		</template>	

	</el-dialog>
	
</div>
