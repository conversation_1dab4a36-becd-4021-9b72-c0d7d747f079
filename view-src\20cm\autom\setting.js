const { BaseView } = require('../base-view');
const { NumberMixin } = require('../../../mixin/number');
const { UserAutoSetting, UserBasicSetting, Setting2Pool } = require('../components/objects');
const { repo20Cm } = require('../../../repository/20cm');

const Map1 = {

    beforeIncreaseRatioDays: 'a1',
    beforeIncreaseRatio: 'a2',
    beforeStockNotLimitDays: 'a3',
    circulationMarketValueMin: 'b1',
    circulationMarketValueMax: 'b2',
    todayAmplitude: 'c1',
    closeIncreaseRatio: 'c2',
};

const Map2 = {

    stock: {

        afterLimitTickEnabled: 'a1',
        afterLimitTickCount: 'a2',
        secondBoardExceptOneEnabled: 'b1',
        secondBoardExceptOneBoard: 'b2',
        openIncreaseRangeEnabled: 'c1',
        openIncreaseRangeMin: 'c2',
        openIncreaseRangeMax: 'c3',
        beforeBoardTradedAmountEnabled: 'c4',
        beforeBoardTradedAmount: 'c5',
        limitUpPriceEnabled: 'd1',
        limitUpPriceMin: 'd2',
        limitUpPriceMax: 'd3',
    },

    trans: {

        buyAmount: 'e1',
        priorityFinancingEnabled: 'e2',
        lineupOrderRateEnable: 'f1',
        lineupOrderRate: 'f2',
        lineupOrderVolumeMinEnabled: 'f3',
        lineupOrderVolumeMin: 'f4',
        orderCountEnabled: 'm1',
        orderCount: 'm2',
        orderAmount: 'm3',
        runTimeStart: 'g1',
        runTimeEnd: 'g2',
        limitSequencesEnabled: 'g3',
        limitSequences: 'g4',
    },

    cancel: {

        cancelProtectedEnabled: 'h1',
        cancelProtectedVolume: 'h2',
        cancelProtectedTime: 'h3',
        tradeProtectedEnabled: 'i1',
        tradeProtectedTime: 'i2',
        lineupOrderVolumeOpen: 'j1',
        lineupOrderVolume: 'j2',
        customDownRateOpen: 'k1',
        customDownRate: 'k2',
        customDownRateTime: 'k3',
        effectiveTradedAmount: 'k4',
        supplementEnabled: 'l1',
        supplementOrderVolume: 'l2',
        supplementTime: 'l3',
    },
};

const BasicProperties = {

    ten: 'stockSelectSetTen',
    twenty: 'stockSelectSetTwenty',
};

const AutoProperties = {

    name: 'autoStrikeBoardSettingName',

    ten: {

        stock: 'stockSelectSetTen',
        trans: 'transactionSetTen',
        cancel: 'cancelSetTen',
    },

    twenty: {

        stock: 'stockSelectSetTwenty',
        trans: 'transactionSetTwenty',
        cancel: 'cancelSetTwenty',
    },
};

function defaultBasic10() {
    return new UserBasicSetting({ a1: 5, a2: 30, a3: 10, b1: 25, b2: 500, c1: 8, c2: 5 });
}

function defaultBasic20() {
    return new UserBasicSetting({ a1: 5, a2: 40, a3: 10, b1: 25, b2: 500, c1: 12, c2: 7 });
}

function defaultAuto10() {

    // return new UserAutoSetting({

    //     a1: true, a2: 5, 
    //     b1: true, b2: 60, 
    //     c1: true, c2: -5, c3: 9, c4: true, c5: 15000, 
    //     d1: true, d2: 3, d3: 80, 
    //     e1: 500, e2: false, 
    //     f1: true, f2: 300, f3: true, f4: 50,
    //     g1: '0930', g2: '1000', g3: true, g4: 8, 
    //     h1: true, h2: 5000, h3: 5, 
    //     i1: false, i2: 10000, 
    //     j1: false, j2: 10, 
    //     k1: false, k2: 1, k3: 500, k4: 100, 
    //     l1: false, l2: 25000, l3: 1000, 
    //     m1: false, m2: 2, m3: 5000,
    // });

    return new UserAutoSetting({});
}

function defaultAuto20() {

    // return new UserAutoSetting({

    //     a1: true, a2: 5, 
    //     b1: true, b2: 60, 
    //     c1: true, c2: -5, c3: 9, c4: true, c5: 15000, 
    //     d1: true, d2: 3, d3: 80, 
    //     e1: 500, e2: false, 
    //     f1: true, f2: 300, f3: true, f4: 50,
    //     g1: '0930', g2: '1000', g3: true, g4: 8, 
    //     h1: true, h2: 5000, h3: 5, 
    //     i1: false, i2: 10000, 
    //     j1: false, j2: 10, 
    //     k1: false, k2: 1, k3: 500, k4: 100, 
    //     l1: false, l2: 25000, l3: 1000, 
    //     m1: false, m2: 2, m3: 5000,
    // });

    return new UserAutoSetting({});
}

function cast2Basic(data) {
    
    var basic = new UserBasicSetting({});

    for (let key_name in data) {

        let sht = Map1[key_name];
        if (sht !== undefined) {
            basic[sht] = data[key_name];
        }
    }
    
    return basic;
}

function castFromBasic(basic) {
    
    var explain = {};
    
    for (let key_name in Map1) {

        let sht = Map1[key_name];
        explain[key_name] = basic[sht];
    }

    return explain;
}

function cast2Auto(data) {
    
    var { stock, trans, cancel } = data;
    var merged_data = Object.assign({}, cancel, stock, trans);
    var keyMap = Object.assign({}, Map2.stock, Map2.trans, Map2.cancel);

    var auto = new UserAutoSetting({});

    for (let key_name in merged_data) {

        let sht = keyMap[key_name];
        if (sht !== undefined) {
            auto[sht] = merged_data[key_name];
        }
    }

    return auto;
}

function castFromAuto(id, name, ten, twenty) {

    function assgin(data, longKeyMap) {

        var obj = {};
        for (let key_name in longKeyMap) {

            let sht = longKeyMap[key_name];
            obj[key_name] = data[sht];
        }

        return obj;
    }
    
    var dto = {

        id,
        [AutoProperties.name]: name,
        [AutoProperties.ten.stock]: assgin(ten, Map2.stock),
        [AutoProperties.ten.trans]: assgin(ten, Map2.trans),
        [AutoProperties.ten.cancel]: assgin(ten, Map2.cancel),
        [AutoProperties.twenty.stock]: assgin(twenty, Map2.stock),
        [AutoProperties.twenty.trans]: assgin(twenty, Map2.trans),
        [AutoProperties.twenty.cancel]: assgin(twenty, Map2.cancel),
    };

    return dto;
}

/**
 * @returns {Array<{ id: Number, name: String, data }>}
 */
function makeSettings() {
    return [];
}

/**
 * @returns {Array<{ id: Number, ticketPoolName: String, ticketPoolType: Number }>}
 */
function makePools() {
    return [];
}

module.exports = class SettingView extends BaseView {

    constructor(view_name, is_standalone_window) {

        super(view_name, is_standalone_window, '功能设置');

        this.dialog = {
            visible: true,
        };

        /**
         * 界面用户选择情况
         */
        this.states = {

            settingId: null,
            poolId: null,
        };

        /**
         * 基础设置
         */
        this.basic = {

            id: null,
            ten: defaultBasic10(),
            twenty: defaultBasic20(),
        };

        /**
         * 全量自定义设置列表
         */
        this.settings = makeSettings();

        /**
         * 自定义设置：当前选择的设置条目，对应的数据
         */
        this.setting = {

            id: null,
            name: null,
            ten: defaultAuto10(), 
            twenty: defaultAuto20(),
        };

        /**
         * 票池
         */
        this.pools = makePools();
    }

    createApp() {

        this.settingApp = new Vue({

            el: this.$container.firstElementChild,

            data : {

                dialog: this.dialog,
                basic: this.basic,
                settings: this.settings,
                setting: this.setting,
                states: this.states,
                pools: this.pools,
            },
            mixins: [NumberMixin],
            computed: {
                //
            },
            methods: this.helper.fakeVueInsMethod(this, [                                                                                                                                                                                                      

                this.close,
                this.saveBasic,
                this.saveAuto,
                this.createSetting,
                this.deleteteSetting,
                this.linkPool,
                this.handleSettingChange,
                this.handlePoolChange,
            ]),
        });
    }

    close() {

        this.dialog.visible = false;
        this.setWindowAsBlockedWaiting(false);
    }

    /**
     * @param {UserBasicSetting} ubs 
     */
    isBasicParamsOk(ubs) {
        return ubs.a1 >= 1 && typeof ubs.a2 == 'number' && ubs.a3 >= 1 && ubs.b1 >= 1 && ubs.b1 <= ubs.b2 && typeof ubs.c1 == 'number' && typeof ubs.c2 == 'number';
    }

    async saveBasic() {

        var ref = this.basic;

        if (!this.isBasicParamsOk(ref.ten)) {
            return this.interaction.showError('10% 基础设置，参数有误');
        }
        else if (!this.isBasicParamsOk(ref.twenty)) {
            return this.interaction.showError('20% 基础设置，参数有误');
        }

        var dto = {

            id: ref.id,
            [BasicProperties.ten]: castFromBasic(ref.ten),
            [BasicProperties.twenty]: castFromBasic(ref.twenty),
        };

        var isCreation = this.helper.isNone(dto.id);
        var resp = await repo20Cm.updateBasicSetting(dto);
        var { data, errorCode, errorMsg } = resp;

        if (errorCode == 0) {

            if (isCreation) {
                ref.id = data.id;
            }

            this.interaction.showSuccess('基本设置：已保存');
        }
        else {
            this.interaction.showError(errorMsg);
        }
    }

    /**
     * @param {UserAutoSetting} uas 
     */
    checkAutoParams(uas) {
        
        var is_1_ok = (uas.a1 === false || uas.a1 && uas.a2 >= 1)
                        && (uas.b1 === false || typeof uas.b2 == 'number')
                        && (uas.c1 === false || typeof uas.c2 == 'number' && typeof uas.c3 == 'number' && uas.c2 <= uas.c3) && (uas.c4 === false || uas.c5 >= 1)
                        && (uas.d1 === false || typeof uas.d2 == 'number' && typeof uas.d3 == 'number' && uas.d2 <= uas.d3);

        var is_2_ok = uas.e1 >= 1 
                        && (uas.f1 === false || uas.f2 >= 0) && (uas.f3 === false || uas.f4 >= 1)
                        && (uas.m1 === false || uas.m2 >= 0 && uas.m3 >= 0)
                        && (typeof uas.g1 == 'number' && typeof uas.g2 == 'number' && uas.g1 < uas.g2) && (uas.g3 === false || uas.g4 >= 1);

        var is_3_ok = (uas.h1 === false || uas.h2 >= 1 && uas.h3 >= 1)
                        && (uas.i1 === false || uas.i2 >= 1)
                        && (uas.j1 === false || uas.j2 >= 1)
                        && (uas.k1 === false || uas.k2 >= 0 && uas.k3 >=1 && uas.k4 >= 1);

        var is_4_ok = uas.l1 === false || uas.l2 >= 1 && uas.l3 >= 10 && uas.l3 <= 2999;
        var error = null;

        if (!is_1_ok) {
            error = '选股设置，参数错误';
        }
        else if (!is_2_ok) {
            error = '交易设置，参数错误';
        }
        else if (!is_3_ok) {
            error = '撤单设置，参数错误';
        }
        else if (!is_4_ok) {
            error = '补单设置，参数错误';
        }

        return error;
    }

    isExisting() {
        return this.helper.isNotNone(this.setting.id);
    }

    async saveAuto() {

        var ref = this.setting;
        var isCreation = this.helper.isNone(ref.id);

        if (!ref.name) {
            return this.interaction.showError('设置名称不能为空');
        }

        var matched = this.settings.find(x => x.name == ref.name);
        if (matched) {

            if (isCreation || matched.id != ref.id) {
                return this.interaction.showError('该名称已存在');
            }
        }

        var ten_error = this.checkAutoParams(ref.ten);
        if (ten_error) {
            return this.interaction.showError('10%' + ten_error);
        }
        
        var twenty_error = this.checkAutoParams(ref.twenty);
        if (twenty_error) {
            return this.interaction.showError('20%' + twenty_error);
        }

        var dto = castFromAuto(ref.id, ref.name, ref.ten, ref.twenty);
        var resp = await repo20Cm.updateAutoSetting(dto);
        var { data, errorCode, errorMsg } = resp;

        if (errorCode == 0) {

            if (isCreation) {
                this.appendAuto(data);
            }
            else {
                this.loadAutoSetting(ref.id);
            }

            this.interaction.showSuccess('自定义设置：已保存');
        }
        else {
            this.interaction.showError(errorMsg);
        }
    }

    appendAuto(data) {

        var setting = this.shapeAuto(data);
        this.settings.push(setting);
        this.states.settingId = setting.id;
    }

    async linkPool() {

        if (this.helper.isNone(this.states.settingId)) {
            return this.interaction.showError('未选择设置');
        }
        else if (this.helper.isNone(this.states.poolId)) {
            return this.interaction.showError('未选择票池');
        }

        var resp = await repo20Cm.queryBindings();
        var { data, errorCode, errorMsg } = resp;

        if (errorCode != 0) {
            return this.interaction.showError('设置/票池绑定数据，查询错误，无法继续操作');
        }

        var records = data instanceof Array ? data.map(x => new Setting2Pool(x)) : [];
        var limit_types = { ten: 1, twenty: 2 };
        var sid = this.states.settingId;
        var pid = this.states.poolId;
        var has_ten = records.some(x => x.ticketPoolId == pid && x.autoStrikeBoardSettingId == sid && x.stockLimitType == limit_types.ten);
        var has_twenty = records.some(x => x.ticketPoolId == pid && x.autoStrikeBoardSettingId == sid && x.stockLimitType == limit_types.twenty);
        var resp_ten = undefined;
        var resp_twenty = undefined;

        if (!has_ten) {
            resp_ten = await repo20Cm.bind2Pool({ autoStrikeBoardSettingId: sid, ticketPoolId: pid, stockLimitType: limit_types.ten });
        }

        if (!has_ten) {
            resp_twenty = await repo20Cm.bind2Pool({ autoStrikeBoardSettingId: sid, ticketPoolId: pid, stockLimitType: limit_types.twenty });
        }

        var is_ten_ok = has_ten || resp_ten.errorCode == 0;
        var is_twenty_ok = has_twenty || resp_twenty.errorCode == 0;

        if (is_ten_ok == false || is_twenty_ok == false) {
            this.interaction.showError('绑定失败：' + (resp_ten.errorMsg || resp_twenty.errorMsg));
        }
        else {
            this.interaction.showSuccess('绑定成功');
        }
    }

    setBasicForm(item) {

        var ref = this.basic;
        ref.id = item.id;
        ref.ten = cast2Basic(item[BasicProperties.ten]);
        ref.twenty = cast2Basic(item[BasicProperties.twenty]);
    }

    clearBasicForm() {

        var ref = this.basic;
        ref.id = null;
        ref.ten = defaultBasic10();
        ref.twenty = defaultBasic20();
    }

    createSetting() {

        this.states.settingId = null;
        this.clearAutoForm();
    }

    async deleteteSetting() {

        if (this.helper.isNone(this.states.settingId)) {
            return;
        }

        var resp = await repo20Cm.deleteAutoSetting(this.states.settingId);
        var { data, errorCode, errorMsg } = resp;

        if (errorCode == 0) {

            this.interaction.showSuccess('设置已删除');
            this.settings.remove(x => x.id == this.states.settingId);
            this.states.settingId = this.settings.length > 0 ? this.settings[0].id : null;
            this.handleSettingChange();
        }
        else {
            this.interaction.showError('设置删除失败：' + errorMsg);
        }
    }

    handleSettingChange() {
        
        if (this.isSettingChoosed()) {

            let matched = this.settings.find(x => x.id == this.states.settingId);
            this.setAutoForm(matched);
        }
        else {
            this.clearAutoForm();
        }
    }

    isSettingChoosed() {
        return this.helper.isNotNone(this.states.settingId);
    }

    setAutoForm(item) {
    
        var { id, name, data } = item;
        var { ten, twenty } = data;

        var ref = this.setting;
        ref.id = id;
        ref.name = name;
        ref.ten = cast2Auto(ten);
        ref.twenty = cast2Auto(twenty);
    }

    clearAutoForm() {

        var ref = this.setting;
        ref.id = null;
        ref.name = null;
        ref.ten = defaultAuto10();
        ref.twenty = defaultAuto20();
    }

    handlePoolChange() {
        //
    }

    async loadBasicSetting() {

        var basicResp = await repo20Cm.queryBasicSetting();        
        var basics = basicResp.data;

        if (basicResp.errorCode != 0) {
            return this.interaction.showError('基础设置，获取出错');
        }

        if (basics instanceof Array && basics.length > 0) {
            this.setBasicForm(basics[0]);
        }
        else {
            this.clearBasicForm();
        }
    }

    shapeAuto(aut) {

        return {

            id: aut.id, 
            name: aut[AutoProperties.name],
            data: {

                ten: {

                    stock: aut[AutoProperties.ten.stock],
                    trans: aut[AutoProperties.ten.trans],
                    cancel: aut[AutoProperties.ten.cancel],
                },

                twenty: {

                    stock: aut[AutoProperties.twenty.stock],
                    trans: aut[AutoProperties.twenty.trans],
                    cancel: aut[AutoProperties.twenty.cancel],
                },
            },
        };
    }

    async loadAutoSetting(setting_id) {

        var autoResp = await repo20Cm.queryAutoSettings();
        var autos = autoResp.data;

        if (autoResp.errorCode != 0) {
            return this.interaction.showError('自定义设置，获取出错');
        }
        
        if (!(autos instanceof Array) || autos.length == 0) {

            this.settings.clear();
            this.states.settingId = null;
            this.clearAutoForm();
            return;
        }

        var dtos = autos.map(aut => this.shapeAuto(aut));
        this.settings.refill(dtos);

        if (setting_id) {

            let matched = this.settings.find(x => x.id == setting_id);
            this.states.settingId = matched.id;
            this.setAutoForm(matched);
        }
        else {

            let first = dtos[0];
            this.states.settingId = first.id;
            this.setAutoForm(first);
        }
    }

    async loadPools() {

        var resp = await repo20Cm.queryPools();

        if (resp.errorCode != 0) {
            return this.interaction.showError('票池数据，获取出错');
        }

        var pools = resp.data;
        if (pools instanceof Array && pools.length > 0) {

            this.pools.refill(pools);
            this.states.poolId = pools[0].id;
        }
        else {

            this.pools.clear();
            this.states.poolId = null;
        }
    }

    reload() {
        
        this.loadBasicSetting();
        this.loadAutoSetting(this.states.settingId);
    }

    /**
     * @param {HTMLElement} $container 
     */
    build($container) {

        super.build($container);
        this.createApp();
        this.loadBasicSetting();
        this.loadAutoSetting();
        this.loadPools();
    }
};