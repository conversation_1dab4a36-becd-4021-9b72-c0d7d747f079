.dialog-20cm-auto-setting {
	
	.el-dialog__body {
		padding: 0;
	}
}

.v20cm-auto-setting {

	width: 830px;
	height: 650px !important;
	border-radius: 4px;

	.content-area {
		padding: 0 10px 10px 10px;
	}

	.basic-setting {

		height: 123px;
		padding-left: 10px;
		overflow: hidden;
		background-color: #283A56;
		border: 1px solid #324F80;
		border-radius: 5px;
	}

	.custom-setting-external {
		
		padding-left: 10px;
    	padding-right: 10px;
		background-color: #283A56;
		border: 1px solid #324F80;
		border-radius: 5px;
	}

	.custom-setting {

		margin-bottom: 10px;
		height: 519px;
	}

	.half-box {

		float: left;
		width: 50%;
		height: 100%;
	}

	.block-title {

		height: 32px;
		line-height: 32px;
		font-size: 14px;
		font-weight: bold;
	}

	.sub-title {

		margin-bottom: 4px;
		height: 28px;
		line-height: 28px;
		font-weight: bold;
	}

	.custom-toolbar {
		
		height: 32px;
		line-height: 32px;
		border-bottom: 1px solid #324F80;
	}

	.bottom-lined {
		border-bottom: 1px solid #324F80;
	}

	.input-row {

		margin-bottom: 7px;

		.el-input-number,
		> .el-input {

			width: 80px;
			margin-left: 5px;
			margin-right: 5px;
		}
	}
	
	.select-ctr {

		.el-input {
			width: 160px;
		}
	}
}