<div class="v20cm v20cm-stra themed-bg-harder" v-show="dialog.visible">
	<el-dialog width="900px"
				title="策略管理"
				:visible="dialog.visible"
				:close-on-click-modal="false"
				:close-on-press-escape="false"
				:show-close="true"
				@close="close">

		<template>

			<div class="toolbar s-pdl-10">

				<el-button type="info" @click="add">
					<i class="iconfont icon-jia"></i> 添加</el-button>

				<el-button type="primary" @click="startAll">
					<i class="el-icon-caret-right"></i> 启动{{ states.hasChecked ? '勾选' : '全部' }}</el-button>

				<el-button type="danger" @click="stopAll">
					<i class="el-icon-video-pause"></i> 停止{{ states.hasChecked ? '勾选' : '全部' }}</el-button>

				<el-button type="primary" @click="refresh" class="s-pull-right s-mgt-5 s-mgr-10">
					<i class="el-icon-refresh"></i> 刷新</el-button>
			</div>

			<div class="content-area">

				<table>
					<tr>
						<th type="check" fixed-width="60"></th>
						<th label="设置" watch="isEditable" prop="autoStrikeBoardSettingId" min-width="100" formatter="renderSetting" sortable></th>
						<th label="票池" watch="isEditable" prop="ticketPoolId" min-width="100" formatter="renderPool" sortable></th>
						<th label="类型" watch="isEditable" prop="stockLimitType" min-width="80" formatter="renderLimitType" sortable></th>
						<th label="状态" watch="isEditable" prop="autoStrikeBoardStatus" min-width="70" formatter="renderStatus" sortable></th>
						<th label="已买数量" watch="isEditable" prop="instrumentOrderCount" min-width="70" thousands sortable></th>
						<th label="已撤数量" watch="isEditable" prop="instrumentCancelCount" min-width="70" thousands sortable></th>
						<th label="已成数量" watch="isEditable" prop="instrumentTradeCount" min-width="70" thousands sortable></th>
						<th label="买入金额" watch="isEditable" prop="orderAmount" min-width="70" thousands sortable></th>
						<th label="成交金额" watch="isEditable" prop="tradedAmount" min-width="70" thousands sortable></th>
						<th label="操作" watch="isEditable" prop="autoStrikeBoardStatus" fixed-width="60" formatter="renderAction"></th>
						<th label="删除" watch="isEditable" prop="autoStrikeBoardStatus" fixed-width="60" formatter="renderDelete"></th>
					</tr>
				</table>

			</div>

		</template>	

	</el-dialog>
	
</div>
