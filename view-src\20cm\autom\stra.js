const { BaseView } = require('../base-view');
const { Setting2Pool } = require('../components/objects');
const { SmartTable } = require('../../../libs/table/smart-table');
const { ColumnCommonFunc } = require('../../../libs/table/column-common-func');
const { repo20Cm } = require('../../../repository/20cm');
const { Cm20FunctionCodes } = require('../../../config/20cm');

class UserStrategy extends Setting2Pool {

    constructor(isEditable, struc) {

        super(struc);
        this.isEditable = !!isEditable;
    }
}

module.exports = class StraView extends BaseView {

    constructor(view_name, is_standalone_window) {

        super(view_name, is_standalone_window, '策略管理');

        this.dialog = {
            visible: true,
        };

        this.states = {

            hasChecked: false,
            newRowId: new Date().getTime().toString(),
        };

        this.limits = [

            { label: '10%', value: '1' },
            { label: '20%', value: '2' },
        ];

        this.status = {

            editing: { label: '待保存', value: -1 },
            suspend: { label: '未启动', value: 0 },
            running: { label: '运行中', value: 1 },
            stoped: { label: '已停止', value: 2 },
            ended: { label: '已结束', value: 3 },
        };

        var status = this.status;
        this.statuses = [status.editing, status.suspend, status.running, status.stoped, status.ended];
        this.settings = [{ id: null, name: null }].slice(1);
        this.pools = [{ id: null, name: null }].slice(1);
        this.strategies = [new UserStrategy(false, {})].slice(1);
    }

    createApp() {

        this.appIns = new Vue({

            el: this.$container.firstElementChild,
            data : {

                dialog: this.dialog,
                states: this.states,
            },
            methods: this.helper.fakeVueInsMethod(this, [

                this.close,
                this.add,
                this.startAll,
                this.stopAll,
                this.refresh,
            ]),
        });

        this.appIns.$nextTick(() => { 
            
            this.buildTable(this.appIns.$el.querySelector('table'));
            this.requests();
            this.listen2Events();
        });
    }

    listen2Events() {

        this.renderProcess.on(Cm20FunctionCodes.auto.start_resp.toString(), (event, { data, errorCode, errorMsg }) => {

            if (errorCode == 0) {
                this.interaction.showSuccess('已启动');
            }
            else {
                this.interaction.showError('启动操作错误');
            }
        });

        this.renderProcess.on(Cm20FunctionCodes.auto.stop_resp.toString(), (event, { data, errorCode, errorMsg }) => {

            if (errorCode == 0) {
                this.interaction.showSuccess('已停止');
            }
            else {
                this.interaction.showError('停止操作错误');
            }
        });
    }

    async requests() {

        await this.loadPools();
        await this.loadSettings();
        await this.requestStras();
    }

    /**
     * @param {UserStrategy} record 
     */
    identifyRecord(record) {
        return record.id;
    }

    buildTable($table) {

        this.helper.extend(this, ColumnCommonFunc);
        this.tableObj = new SmartTable($table, this.identifyRecord, this, {

            tableName: '20cm-auto-stra',
            displayName: this.title,
            rowChecked: this.handleCheckChange.bind(this),
            allRowsChecked: this.handleCheckChange.bind(this),
        });

        this.tableObj.setPageSize(999);
        this.tableObj.setMaxHeight(250);
        this.tableObj.fitColumnWidth();
    }

    close() {

        this.dialog.visible = false;
        this.setWindowAsBlockedWaiting(false);
    }

    handleCheckChange() {
        this.states.hasChecked = this.tableObj.hasAnyRowsChecked;
    }

    /**
     * @param {UserStrategy} record 
     */
    handleSettingChange(record, $select, $cell, previous, name) {
        record.autoStrikeBoardSettingId = $select.value;
    }

    /**
     * @param {UserStrategy} record 
     */
    handlePoolChange(record, $select, $cell, previous, name) {
        record.ticketPoolId = $select.value;
    }

    /**
     * @param {UserStrategy} record 
     */
    handleTypeChange(record, $select, $cell, previous, name) {
        record.stockLimitType = parseInt($select.value);
    }

    /**
     * @param {String} handler
     * @param {String} value
     * @param {Array<{ label, value }>} options
     */
    renderSelect(handler, value, options) {
        
        // .sort((a, b) => a.label > b.label ? 1 : a.label < b.label ? -1 : 0)

        return `<select event.onchange="${handler}">
                    ${options.map(x => `<option value="${x.value}">${x.label}</option>`).join('')}
                </select>`;
    }

    /**
     * @param {UserStrategy} record 
     */
    renderSetting(record) {

        return record.isEditable ? this.renderSelect('handleSettingChange', record.autoStrikeBoardSettingId, this.settings.map(x => ({ label: x.name, value: x.id }))) 
                                : (this.settings.find(x => x.id == record.autoStrikeBoardSettingId) || {}).name;
    }

    /**
     * @param {UserStrategy} record 
     */
    renderPool(record) {

        return record.isEditable ? this.renderSelect('handlePoolChange', record.ticketPoolId, this.pools.map(x => ({ label: x.name, value: x.id })))
                                : (this.pools.find(x => x.id == record.ticketPoolId) || {}).name;
    }

    /**
     * @param {UserStrategy} record 
     */
    renderLimitType(record) {

        return record.isEditable ? this.renderSelect('handleTypeChange', record.stockLimitType, this.limits.map(x => x))
                                : (this.limits.find(x => x.value == record.stockLimitType) || {}).label;
    }

    /**
     * @param {UserStrategy} record 
     */
    renderStatus(record) {
        return (this.statuses.find(x => x.value == record.autoStrikeBoardStatus) || {}).label;
    }

    /**
     * @param {UserStrategy} record 
     */
    renderAction(record) {
        
        var status = this.status;

        switch (record.autoStrikeBoardStatus) {

            case status.editing.value: return '<button class="success" event.onclick="save">保存</button>';
            case status.suspend.value:
            case status.stoped.value: 
            case status.ended.value: return '<button event.onclick="start">启动</button>';
            case status.running.value: return '<button class="danger" event.onclick="stop">停止</button>';
        }
    }

    /**
     * @param {UserStrategy} record 
     */
    renderDelete(record) {
        
        var status = this.status;
        var del_btn = '<button class="danger" event.onclick="remove">删除</button>';

        switch (record.autoStrikeBoardStatus) {

            case status.editing.value: return del_btn;
            case status.suspend.value:
            case status.stoped.value:
            case status.ended.value: return del_btn;
            case status.running.value: return '';
        }
    }

    add() {

        if (this.tableObj.hasRow(this.states.newRowId)) {
            return this.interaction.showError('请先保存，再行添加');
        }
        
        var data = {

            amount: 0,
            autoStrikeBoardSettingId: this.settings.length > 0 ? this.settings[0].id : '',
            autoStrikeBoardSettingName: null,
            autoStrikeBoardStatus: this.status.editing.value,
            endTime: null,
            id: this.states.newRowId,
            instrumentCancelCount: 0,
            instrumentOrderCount: 0,
            instrumentTradeCount: 0,
            orderAmount: 0,
            sequence: null,
            startTime: null,
            stockLimitType: this.limits[0].value,
            ticketPoolId: this.pools.length > 0 ? this.pools[0].id : '',
            ticketPoolName: null,
            tradedAmount: 0,
        };

        this.tableObj.insertRow(new UserStrategy(true, data));
    }

    /**
     * @returns {Array<UserStrategy>}
     */
    typeds(records) {
        return records;
    }

    /**
     * @param {UserStrategy} record 
     */
    async remove(record) {
        
        var asbs = record.autoStrikeBoardStatus;
        if (asbs == this.status.editing.value) {
            return this.tableObj.deleteRow(record.id);
        }
        else if (asbs == this.status.running.value) {
            return this.interaction.showError('运行中的策略，不可删除');
        }
        
        /**
         * 提交保存
         */

        var resp = await repo20Cm.deletePoolBindding(record.id);
        var { errorCode, errorMsg, data } = resp;

        if (errorCode == 0) {
            this.interaction.showSuccess('已删除');
        }
        else {
            this.interaction.showError('删除失败');
        }

        /**
         * 无论是否删除成功，都重新加载一次
         */

        this.doReload();
    }

    /**
     * @param {UserStrategy} record 
     */
    async save(record) {

        var { autoStrikeBoardSettingId, ticketPoolId, stockLimitType } = record;
        
        if (this.helper.isNone(autoStrikeBoardSettingId)) {
            return this.interaction.showError('设置未选择');
        }
        else if (this.helper.isNone(ticketPoolId)) {
            return this.interaction.showError('票池未选择');
        }
        else if (this.helper.isNone(stockLimitType)) {
            return this.interaction.showError('涨停类型，未选择');
        }
        
        var strategies = this.typeds(this.tableObj.extractAllRecords());
        var distincts = strategies.filter(x => x.autoStrikeBoardSettingId == autoStrikeBoardSettingId && x.ticketPoolId == ticketPoolId && x.stockLimitType == stockLimitType);
        if (distincts.length >= 2) {
            return this.interaction.showError('设置、票池、类型，三者组合，存在重复');
        }

        /**
         * 新加行置位初始状态
         */

        var cloned = this.helper.deepClone(record);
        cloned.isEditable = false;
        cloned.autoStrikeBoardStatus = this.status.suspend.value;
        this.tableObj.updateRow(cloned);

        /**
         * 提交保存
         */

        var resp = await repo20Cm.bind2Pool({ autoStrikeBoardSettingId, ticketPoolId, stockLimitType });
        var { errorCode, errorMsg, data } = resp;

        if (errorCode == 0) {
            this.interaction.showSuccess('已保存');
        }
        else {
            this.interaction.showError('保存失败');
        }

        /**
         * 无论是否保存成功，都重新加载一次
         */

        this.doReload();
    }

    /**
     * @param {UserStrategy} record 
     */
    start(record) {
        this.signal(Cm20FunctionCodes.auto.start, [record.id]);
    }

    /**
     * @param {UserStrategy} record 
     */
    stop(record) {
        this.signal(Cm20FunctionCodes.auto.stop, [record.id]);
    }

    startAll() {
        
        var strategies = this.typeds(this.tableObj.hasAnyRowsChecked ? this.tableObj.extractCheckedRecords() : this.tableObj.extractAllRecords());
        this.signal(Cm20FunctionCodes.auto.start, strategies.length == 0 ? [0] : strategies.map(x => x.id));
    }

    stopAll() {
        
        var strategies = this.typeds(this.tableObj.hasAnyRowsChecked ? this.tableObj.extractCheckedRecords() : this.tableObj.extractAllRecords());
        this.signal(Cm20FunctionCodes.auto.stop, strategies.length == 0 ? [0] : strategies.map(x => x.id));
    }

    /**
     * @param {Number} cmd 
     * @param {Array<String>} ids 
     */
    signal(cmd, ids) {

        ids.forEach(id => {
            this.renderProcess.send(this.systemEvent.transmitMsg2TradingServer, cmd, id.toString());
        });

        setTimeout(() => { this.doReload(); }, 1000);
        this.interaction.showSuccess('操作请求已发送');
    }

    refresh() {

        this.interaction.showSuccess('刷新请求已发出');
        this.doReload();
    }

    doReload() {

        this.states.hasChecked = false;
        this.requests();
    }

    async loadPools() {

        var resp = await repo20Cm.queryPools();
        var { errorCode, errorMsg, data } = resp;

        if (errorCode == 0 && data instanceof Array) {
            this.pools.refill(data.map(x => ({ id: x.id, name: x.ticketPoolName })));
        }
        else {

            this.pools.clear();
            this.interaction.showError('票池查询出错: ' + errorMsg);
        }
    }

    async loadSettings() {

        var resp = await repo20Cm.queryAutoSettings();
        var { errorCode, errorMsg, data } = resp;

        if (errorCode == 0 && data instanceof Array) {
            this.settings.refill(data.map(x => ({ id: x.id, name: x.autoStrikeBoardSettingName })));
        }
        else {

            this.settings.clear();
            this.interaction.showError('设置查询出错: ' + errorMsg);
        }
    }

    async requestStras() {

        var resp = await repo20Cm.queryBindings();
        var { errorCode, errorMsg, data } = resp;

        if (errorCode == 0 && data instanceof Array) {
            this.tableObj.refill(data.map(item => new UserStrategy(false, item)));
        }
        else {

            this.tableObj.clear();
            this.interaction.showError('策略查询出错: ' + errorMsg);
        }
    }
    
    /**
     * @param {HTMLElement} $container 
     */
    build($container) {

        super.build($container);
        this.createApp();
    }
};