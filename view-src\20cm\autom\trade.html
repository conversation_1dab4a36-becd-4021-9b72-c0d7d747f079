<div class="v20cm-auto">
	<table class="layout-table themed-bg" cellpadding="0" cellspacing="0">
		<colgroup>
			<col width="500" />
			<col width="500" />
			<col width="auto" />
		</colgroup>
		<tbody>
			<tr class="row-header">
				<td colspan="3">
					<div class="header">
						<!-- content here -->
					</div>
				</td>
			</tr>
			<tr class="row-actions">
				<td>
					<div class="monitor-title typical-header">10% 股票</div>
				</td>
				<td>
					<div class="monitor-title typical-header">20% 股票</div>
				</td>
				<td>
					<div class="type-tabs">
						<!-- content here -->
					</div>
				</td>
			</tr>
			<tr class="row-main">
				<td colspan="3">
					<div class="main-view-root s-full-size">

						<div class="buy10 auto-buy-box themed-bg s-full-height">
							<!-- content here -->
						</div>
	
						<div class="buy20 auto-buy-box themed-bg s-full-height">
							<!-- content here -->
						</div>
	
						<div class="data-wall s-full-size">
	
							<div class="entrust-views">
								<!-- content here -->
							</div>
							
							<div class="splitter-line"></div>
	
							<div class="corner-area">
								
								<div class="corner-tabs">
									<!-- content here -->
								</div>
								
								<div class="corner-views s-full-height">
									<!-- content here -->
								</div>
	
							</div>
	
						</div>
	
					</div>
				</td>
			</tr>
		</tbody>
	</table>
	<div class="footer-row">
		<div class="footer-row-inner">
			<span v-for="(item, item_idx) in stockAccounts" :key="item_idx" class="summary-item">
				<template>

					<label class="prop-name">{{ item.accountName }}:</label>

					<el-tooltip placement="top" content="总可用">
						<label class="prop-value">{{ thousands(item.available) }}</label>
					</el-tooltip>

					<el-tooltip placement="top" content="总可融">
						<label class="prop-value s-pdl-10">/ {{ thousands(item.enableCreditBuy) }}</label>
					</el-tooltip>

				</template>
			</span>
		</div>
	</div>
</div>
