const { IView } = require('../../../component/iview');
const { TabList } = require('../../../component/tab-list');
const { Tab } = require('../../../component/tab');
const HeaderView = require('./components/header');
const AutoBuyView = require('./components/auto-buy');
const { Position } = require('../../../model/position');
const { AccountSimple, UnclosedCreditPosition, TaskObject, Setting2Pool, Entrance, TaskStatus } = require('../components/objects');
const { repoAccount } = require('../../../repository/account');
const { Cm20FunctionCodes } = require('../../../config/20cm');
const { NumberMixin } = require('../../../mixin/number');
const { Splitter } = require('../../../component/splitter');
const { repoCredit } = require('../../../repository/credit');

module.exports = class TradeView extends IView {

    constructor(view_name, is_standalone_window) {

        super(view_name, is_standalone_window, '涨停板策略交易');

        this.enames = {

            addUnits: 'add-units',
            setTasks: 'set-tasks',
            taskCreated: 'task-created',
            accountChange: 'account-change',
            strategyChange: 'strategy-change',
            nofocus: 'no-focused-unit',
            askPositions: 'ask-4-positions',
        };

        this.limitType = { ten_cm: 1, twenty_cm: 2 };
        this.stockAccounts = [new AccountSimple({})].slice(1);
    }

    /**
     * @param {HTMLElement} $template 
     */
    buildTree($template) {

        var $header = $template.querySelector('.header');
        var $mainView = $template.querySelector('.main-view-root');
        var $buy10 = $template.querySelector('.buy10');
        var $buy20 = $template.querySelector('.buy20');
        var $entrustTabs = $template.querySelector('.type-tabs');
        var $entrustViews = $template.querySelector('.entrust-views');
        var $splitter = $template.querySelector('.splitter-line');
        var $cornerTabs = $template.querySelector('.corner-tabs');
        var $cornerViews = $template.querySelector('.corner-views');

        this.$mainView = $mainView;

        /**
         * 顶部工具栏
         */
        var vheader = new HeaderView();
        vheader.loadBuild($header);
        vheader.registerEvent('open-auto-setting', this.openSetting.bind(this));
        vheader.registerEvent('open-auto-pool', this.openPool.bind(this));
        vheader.registerEvent('open-auto-stra', this.openStra.bind(this));
        vheader.registerEvent('open-auto-completed', this.openCompleted.bind(this));
        this.vheader = vheader;

        /**
         * 10%买入监控
         */
        var vbuy10 = new AutoBuyView();
        vbuy10.loadBuild($buy10);
        this.vbuy10 = vbuy10;

        /**
         * 20%买入监控
         */
        var vbuy20 = new AutoBuyView();
        vbuy20.loadBuild($buy20);
        this.vbuy20 = vbuy20;

        /**
         * 10% & 20% 自动买入监控
         */
        this.autos = [vbuy10, vbuy20];

        /**
         * 右侧列表与卖出单元，上下区域垂直布局
         */
        this.splitter = new Splitter('20cm-right-wall', $splitter, this.handleSpliting.bind(this), {

            previousMinHeight: 100, 
            nextMinHeight: 200,
        });

        setTimeout(() => {
            this.splitter.coordinate(0, this.splitter.$previous.offsetHeight - 10 + 123);
        }, 200);

        /**
         * 持仓与委托列表
         */
        var tabCtr = new TabList({

            allowCloseTab: false,
            embeded: true,
            lazyLoad: false,
            showToolkit: 'refresh, export',
            $navi: $entrustTabs,
            $content: $entrustViews,
            tabCreated: this.handleTabCreated.bind(this),
            tabFocused: this.handleTabFocused.bind(this),
        });

        tabCtr.openTab(false, '@20cm/components/position', '股票持仓');
        tabCtr.openTab(false, '@20cm/components/account', '账号信息');
        tabCtr.openTab(true, '@20cm/components/entrust', '委托列表');

        tabCtr.setFocus(tabCtr.tabs[0]);
        this.tabCtr = tabCtr;
        
        this.vpositionView = tabCtr.tabs[0].viewEngine;
        this.vpositionView.registerEvent('hit-position', this.handlePositionHit.bind(this));
        this.vpositionView.registerEvent('brocast-position-condition', this.handlePositionBrocastEvent.bind(this));
        this.vpositionView.registerEvent('required-positions', this.handlePositionFeedback.bind(this));
        this.vaccountView = tabCtr.tabs[1].viewEngine;
        this.ventrust = tabCtr.tabs[2].viewEngine;
        this.ventrust.registerEvent('summarized-sells', this.handleSummarizedSells.bind(this));

        /**
         * 右下方操作区域
         */
        var cnrTab = new TabList({

            allowCloseTab: false,
            embeded: true,
            $navi: $cornerTabs,
            $content: $cornerViews,
            lazyLoad: false,
            showToolkit: false,
        });

        cnrTab.openTab(false, '@20cm/components/boarding', '行情', { height: 470 });
        cnrTab.openTab(false, '@20cm/components/sell-list', '卖出监控', { height: 470 });

        this.cnrTab = cnrTab;
        this.vboarding = cnrTab.tabs[0].viewEngine;
        this.vsell = cnrTab.tabs[1].viewEngine;

        this.vsell.registerEvent(this.enames.askPositions, (stockCode) => {
            this.vpositionView.trigger(this.enames.askPositions, stockCode); 
        });

        this.vsell.registerEvent('ask-summarized-sells', () => { this.ventrust.trigger('ask-summarized-sells'); });
        this.vsell.registerEvent('ask-2-cancel-all-sells', (code, name) => { this.ventrust.trigger('ask-2-cancel-all-sells', code, name); });

        /**
         * 多个界面交叉事件
         */
        var viewList = [this.vbuy10, this.vbuy20, this.vsell];
        viewList.forEach(item => {

            item.registerEvent('unit-focused', (title, stockCode) => {
                
                viewList.forEach(item2 => {
                    title != item2.title && item2.trigger(this.enames.nofocus);
                });
                
                this.vboarding.trigger('set-as-instrument', stockCode);
            });

            item.registerEvent('unit-removed', (stockCode) => {
                this.vboarding && this.vboarding.trigger('test-instrument', stockCode);
            });
        });
    }

    /**
     * @param {Number} previous_height 
     * @param {Number} next_height 
     */
    handleSpliting(previous_height, next_height) {
        this.tabCtr.fireEventOnAllTabs('set-max-height', previous_height);
    }

    handlePositionHit(position) {

        let { instrument, instrumentName, closableVolume } = position;
        this.cnrTab.setFocus(this.cnrTab.tabs[1]);
        this.vsell.trigger(this.enames.addUnits, [{ code: instrument, name: instrumentName, closableVolume }]);
    }

    /**
     * @param {Array<String>} zeros 
     * @param {Array<{ instrument: String, closable: Number }>} closables 
     */
    handlePositionBrocastEvent(zeros, closables) {
        this.vsell.trigger('action-by-position', zeros, closables);
    }    

    /**
     * @param {Array<Position>} positions 
     */
    async handlePositionFeedback(positions) {

        var uncloseds = await this.requestUnclosedCredits(positions.map(x => x.accountId).distinct());
        this.vsell.trigger('required-positions', positions, uncloseds);
    }

    handleSummarizedSells(map) {
        this.vsell.trigger('summarized-sells', map);
    }

    /**
     * @param {Array<String>} accountIds 
     * @returns {Array<{ accountId: String, uncloseds: Array<UnclosedCreditPosition> }>}
     */
    async requestUnclosedCredits(accountIds) {

        if (accountIds.length == 0) {
            return [];
        }

        var types = {

            cash: { value: 0, label: '融资' },
            stock: { value: 1, label: '融券' },
            list: { value: 2, label: '标的' },
	        special: { value: 3, label: '专项' },
        };

        var subsists = {

            alive: { value: 0, label: '未了结' },
            done: { value: 1, label: '已了结' },
        };

        var results = [];

        for (let accountId of accountIds) {

            let resp = await repoCredit.getContractInfo({

                user_id: this.userInfo.userId,
                token: this.userInfo.token,

                compact_type: types.cash.value,
                compact_status: subsists.alive.value,
                fund_id: null,
                strategy_id: null,
                account_id: accountId,
                pageNo: 1,
                pageSize: 99999,
            });

            if (resp.errorCode == 0) {
                
                let records = resp.data.list;
                if (records instanceof Array && records.length > 0) {
                    results.push({ accountId, uncloseds: records.map(item => new UnclosedCreditPosition(item)) });
                }
            }
        }

        return results;
    }

    /**
     * @param {HTMLElement} $template 
     */
    buildFooterRow($template) {

        this.footerApp = new Vue({

            el: $template,
            mixins: [NumberMixin],
            data: {
                stockAccounts: this.stockAccounts,
            },
        });
    }

    /**
     * @param {Tab} tab 
     */
    handleTabCreated(tab) {
        setTimeout(() => { this.simulateWinSizeChange(); }, 100);
    }

    /**
     * @param {Tab} tab 
     */
    handleTabFocused(tab) {

        this.tabCtr.fireEventOnTab(tab, 'auto-fit');
        setTimeout(() => { this.simulateWinSizeChange(); }, 100);
    }

    openSetting() {
        
        if (this.settingView) {

            this.settingView.dialog.visible = true;
            this.settingView.reload();
            this.setWindowAsBlockedWaiting(true);
            return;
        }

        var SettingView = require('./setting');
        var dialog = new SettingView('@20cm/autom/setting', false);
        var $node = document.createElement('div');
        this.$container.appendChild($node);
        dialog.loadBuild($node, null, () => { this.setWindowAsBlockedWaiting(true); });
        this.settingView = dialog;
    }

    openPool() {

        if (this.poolView) {

            this.poolView.dialog.visible = true;
            this.setWindowAsBlockedWaiting(true);
            return;
        }

        var PoolView = require('./pool');
        var dialog = new PoolView('@20cm/autom/pool', false);
        var $node = document.createElement('div');
        this.$container.appendChild($node);
        dialog.loadBuild($node, null, () => { this.setWindowAsBlockedWaiting(true); });
        this.poolView = dialog;
    }

    openStra() {

        if (this.straView) {

            this.setWindowAsBlockedWaiting(true);
            this.straView.dialog.visible = true;
            this.straView.doReload();
            return;
        }

        var StraView = require('./stra');
        var dialog = new StraView('@20cm/autom/stra', false);
        var $node = document.createElement('div');
        this.$container.appendChild($node);
        dialog.loadBuild($node, null, () => { this.setWindowAsBlockedWaiting(true); });
        this.straView = dialog;
    }

    openCompleted() {
        
        if (this.historyView) {

            this.historyView.dialog.visible = true;
            this.historyView.requestTasks();
            this.setWindowAsBlockedWaiting(true);
            return;
        }

        var HistoryView = require('./history');
        var dialog = new HistoryView('@20cm/autom/history', false);
        var $node = document.createElement('div');
        this.$container.appendChild($node);
        dialog.loadBuild($node, null, () => { this.setWindowAsBlockedWaiting(true); });
        this.historyView = dialog;
    }

    async requestAccounts() {

        var resp = await repoAccount.getAccountDetailInfo({ identity_id: '', userId: this.userInfo.userId });
        if (resp.errorCode == 0) {

            let records = resp.data.list;
            let simples = AccountSimple.Convert(records);
            let stockAccounts = simples.filter(x => x.identityType == this.systemEnum.identityType.account.code
                                                 && x.assetType == this.systemEnum.assetsType.stock.code);

            this.stockAccounts.refill(stockAccounts);
            this.spreadAccounts();
        }
        else {
            this.interaction.showError('获取账号资金详情发生异常：' + resp.errorMsg);
        }
    }

    requestTasks() {

        /** 该请求，为了获取卖出策略（卖出策略仅包含在手动模式中） */
        this.renderProcess.send(this.systemEvent.transmitMsg2TradingServer, Cm20FunctionCodes.request.query, '0');
        /** 该请求，获取自动模式策略 */
        this.renderProcess.send(this.systemEvent.transmitMsg2TradingServer, Cm20FunctionCodes.request.query, '1');
    }

    spreadAccounts() {

        var data = this.helper.deepClone(this.stockAccounts);
        var acnts = this.stockAccounts;
        var summarized = {

            zky: acnts.map(x => x.available).sum(),
            zkr: acnts.map(x => x.enableCreditBuy).sum(),
            zzc: acnts.map(x => x.balance).sum(),
            djzj: acnts.map(x => (x.frozenMargin + x.frozenCommission)).sum(),
        };

        this.autos.forEach(v => v.trigger(this.enames.accountChange, data));
        this.vheader.trigger(this.enames.accountChange, summarized);
    }

    /**
     * @param {String} message 
     */
    log(message) {
        this.loggerTrading.info('ztlog:' + message);
    }

    /**
     * @param {TaskObject|Array<TaskObject>} data 
     */
    shape(data) {

        (data instanceof Array ? data : [data]).forEach(task => {
            task.xorders = TaskObject.CreateOrders(task.orderInfo);
        });
    }

    /**
     * @param {TaskObject} task 
     */
    handleTaskCreated(task) {

        this.shape(task);
        var dir = this.systemTrdEnum.tradingDirection;

        if (task.direction == dir.buy.code) {

            if (task.strikeBoardType == 1) {

                if (task.stockLimitType == this.limitType.ten_cm) {
                    this.vbuy10.trigger(this.enames.setTasks, [task]);
                }
                else if (task.stockLimitType == this.limitType.twenty_cm) {
                    this.vbuy20.trigger(this.enames.setTasks, [task]);
                }
            }
        }
        else {
            this.vsell.trigger(this.enames.taskCreated, task);
        }
    }

    /**
     * @param {Array<TaskObject>} records 
     */
    handleTasksReply(records) {

        var dir = this.systemTrdEnum.tradingDirection;
        var auto_buys = records.filter(item => item.direction == dir.buy.code && item.strikeBoardType == 1);
        /** 卖出仅包含在手动类型中 */
        var sells = records.filter(item => item.direction == dir.sell.code && item.strikeBoardType == 0 && !TaskObject.isUnexpected(item.strikeBoardStatus));

        if (auto_buys.length == 0 && sells.length == 0) {
            return;
        }

        var strucs = records.map(task => {

            let { id, instrument, instrumentName, direction, strikeBoardStatus } = task;
            return { id, instrument, instrumentName, direction, strikeBoardStatus };
        });

        this.log(`received auto-tasks reply: ${JSON.stringify(strucs)}`);

        var cm10s = auto_buys.filter(x => x.stockLimitType == this.limitType.ten_cm);
        var cm20s = auto_buys.filter(x => x.stockLimitType == this.limitType.twenty_cm);

        if (cm10s.length > 0) {
            this.vbuy10.trigger(this.enames.setTasks, cm10s);
        }

        if (cm20s.length > 0) {
            this.vbuy20.trigger(this.enames.setTasks, cm20s);
        }

        if (sells.length > 0) {
            this.vsell.trigger(this.enames.setTasks, sells, true);
        }
    }

    /**
     * @param {TaskObject} task 
     */
    handleTaskChanged(task) {

        this.shape(task);

        var dir = this.systemTrdEnum.tradingDirection;
        var struc = {

            id: task.id,
            instrument: task.instrument,
            instrumentName: task.instrumentName,
            direction: task.direction,
            strikeBoardStatus: task.strikeBoardStatus,
        };

        this.log(`received auto-task notify: ${JSON.stringify(struc)}`);
        
        if (task.direction == dir.buy.code) {
            
            if (task.strikeBoardType == 1) {

                if (task.stockLimitType == this.limitType.ten_cm) {
                    this.vbuy10.trigger(this.enames.setTasks, [task]);
                }
                else if (task.stockLimitType == this.limitType.twenty_cm) {
                    this.vbuy20.trigger(this.enames.setTasks, [task]);
                }
            }
        }
        else {
            this.vsell.trigger(this.enames.setTasks, [task], false);
        }
    }

    /**
     * @param {Setting2Pool} strategy 
     */
    handleStrategyStarted(strategy) {
        this.vheader.trigger(this.enames.strategyChange, strategy, 'started');
    }

    /**
     * @param {Setting2Pool} strategy 
     */
    handleStrategyStopped(strategy) {
        this.vheader.trigger(this.enames.strategyChange, strategy, 'stopped');
    }

    /**
     * @param {TaskObject} task 
     */
    handleTaskDeleted(task) {

        /**
         * 对于已删除的任务，通知到具体交易界面，由界面自行进行删除
         */
        this.handleTaskChanged(task);
    }

    listen2Events() {

        this.renderProcess.on(Cm20FunctionCodes.reply.queried + '', (event, { data, errorCode, errorMsg }) => {

            if (errorCode == 0) {

                this.shape(data);
                this.handleTasksReply(data);
            }
            else {
                this.interaction.showError('监控列表查询错误：' + errorMsg);
            }
        });

        var handleReply = (action, { data, errorCode, errorMsg }) => {

            if (errorCode == 0) {
                this.interaction.showSuccess(`${action}，已处理`);
            }
            else {

                console.error(action, { errorCode, errorMsg, data });
                this.interaction.showError(`${action}错误：${errorMsg}`);
            }
        };

        this.renderProcess.on(Cm20FunctionCodes.reply.started + '', (event, message) => { handleReply('启动', message); });
        this.renderProcess.on(Cm20FunctionCodes.reply.stopped + '', (event, message) => { handleReply('停止', message); });
        this.renderProcess.on(Cm20FunctionCodes.reply.deleted + '', (event, message) => { handleReply('删除', message); });
        this.renderProcess.on(Cm20FunctionCodes.reply.canceled + '', (event, message) => { handleReply('撤单', message); });

        this.renderProcess.on(Cm20FunctionCodes.notify.created + '', (event, task) => { this.handleTaskCreated(task); });
        this.renderProcess.on(Cm20FunctionCodes.notify.changed + '', (event, task) => { this.handleTaskChanged(task); });
        this.renderProcess.on(Cm20FunctionCodes.notify.deleted + '', (event, task) => { this.handleTaskDeleted(task); });

        this.renderProcess.on(Cm20FunctionCodes.auto.auto_created + '', (event, task) => { this.handleTaskCreated(task); });
        this.renderProcess.on(Cm20FunctionCodes.auto.auto_changed + '', (event, task) => { this.handleTaskChanged(task); });
        this.renderProcess.on(Cm20FunctionCodes.auto.auto_deleted + '', (event, task) => { this.handleTaskDeleted(task); });

        this.renderProcess.on(Cm20FunctionCodes.auto.strategy_started + '', (event, task) => { this.handleStrategyStarted(task); });
        this.renderProcess.on(Cm20FunctionCodes.auto.strategy_stopped + '', (event, task) => { this.handleStrategyStopped(task); });

        this.thisWindow.on('strike-board-setting-updated', (settings) => {
            this.readSettings();
        });

        this.lisen2WinSizeChange((width, height, isMaximized) => {

            let neth = height - (isMaximized ? 175 : 160);
            this.$mainView.style.height = neth + 'px';
            this.splitter.coordinate(0, this.splitter.$previous.offsetHeight + 123);
        });
    }

    async readSettings() {

        var settings = this.settings = await Entrance.readSetting();
        var ename = 'setting-updated';
        this.vbuy10.trigger(ename, settings);
        this.vbuy20.trigger(ename, settings);
        this.vsell.trigger(ename, settings);
        this.ventrust.trigger(ename, settings);
    }

    async chainedRequest() {

        await this.readSettings();
        this.requestAccounts();
        this.requestTasks();
        /** 账号数据定时刷新 */
        this.timer4AccountData = setInterval(() => { this.requestAccounts(); }, 1000 * 30);
    }

    // /**
    //  * 
    //  * @param {*} instrument 
    //  * @param {*} instrument_name 
    //  * @param {*} limit_type auto: 涨停板类型（1：10%涨停，2：20%涨停）
    //  * @param {*} task_id 
    //  * @param {*} task_status 
    //  */
    // mock(instrument, instrument_name, limit_type, task_id, task_status) {

    //     var t = (a, b) => { return this.helper.makeRandomNum(a, b, true); };
    //     var b = () => { return t(1, 100) % 2 == 0; };
    //     var v = () => { return t(10, 5000) * 100; };
    //     var r = () => { return t(0, 99); };
    //     var o = () => {

    //         let d = {};
    //         let end = t(1, 15);
    //         for (let idx = 1; idx <= end; idx++) {
    //             d[idx] = t(10, 500) * 100;
    //         }

    //         return d;
    //     };
        
    //     return {

    //         /** auto: 涨停板类型（1：10%涨停，2：20%涨停） */
    //         stockLimitType: limit_type,
    //         /** auto: 打板类型（0：人工打板，1：自动打板） */
    //         strikeBoardType: 1,

    //         boardStrategy: {

    //             strategyType: 20220101,
    //             strategyVolume: v(),
    //             strategyRate: r(),
    //             strategyDelayTime: t(1, 30) * 100,
    //             strategyMinVolume: v(),
    //         },

    //         cancelCondition: {
 
    //             beforeCancelOpen: b(),
    //             beforeTradeCancel: v(),
    //             cancelProtectedEnabled: b(),
    //             cancelProtectedTime: v(),
    //             cancelProtectedVolume: v(),
    //             customDownRate: r(),
    //             customDownRateOpen: b(),
    //             customDownRateTime: v(),
    //             downRate: r(),
    //             downRateOpen: b(),
    //             effectiveTradedAmount: v(),
    //             effectiveTradeAmount: undefined,
    //             extremeBack: v(),
    //             extremeBackOpen: b(),
    //             followCancel: v(),
    //             followCancelOpen: b(),
    //             hasCanceled: b(),
    //             hasSupplement: b(),
    //             lessLineupOrderVolume: v(),
    //             lessLineupOrderVolumeEnabled: b(),
    //             lineupOrderVolume: v(),
    //             lineupOrderVolumeOpen: b(),
    //             sellOrderVolume: v(),
    //             sellOrderVolumeOpen: b(),
    //             supplementEnabled: b(),
    //             supplementOrderVolume: v(),
    //             tradeProtectedEnabled: b(),
    //             tradeProtectedTime: v(),
    //         },

    //         cash: t(10000, 9999999),
    //         creditFlag: 0,
    //         direction: this.systemTrdEnum.tradingDirection.buy.code,
    //         id: 'id-' + task_id,
    //         instrument: instrument,
    //         instrumentName: instrument_name,
    //         limitPositionType: 0,

    //         orderInfo: {

    //             '涨停AAA': o(),
    //             '涨停BBB': o(),
    //             '涨停CCC': o(),
    //         },

    //         orderPrice: +(t(100, 9999) * 0.01).toFixed(2),
    //         positionPercent: r(),
    //         priceFollowType: 0,

    //         splitDetail: JSON.stringify({

    //             main: 9999,
    //             imbark: 3000,
    //             star: 1000,
    //             protect2: { first: 30, second: 70 },
    //             protect3: { third: 40, first: 30, second: 30 },
    //             random: false, 
    //         }),

    //         splitInterval: v(),
    //         splitType: 1,
    //         strikeBoardStatus: task_status,
    //         supplementOpen: b(),
    //         supplementVolume: v(),
    //         targetVolume: v(),
    //         taskId: 'task-id-' + task_id,
    //         ticketPoolId: 'pool-id-' + task_id,
    //         ticketPoolName: '太平鸟',
    //         usedMargin: v(),
    //         userId: this.userInfo.userId,
    //         userName: this.userInfo.userName,
    //     };
    // }

    // test() {

    //     this.handleTaskChanged(this.mock('SHSE.600036', '招商银行', 1, 1001, TaskStatus.ordered));
    //     this.handleTaskChanged(this.mock('SZSE.300059', '东方财富', 2, 2001, TaskStatus.ordered));
    // }

    /**
     * @param {HTMLElement} $container 
     */
    build($container) {

        super.build($container);
        this.buildTree($container.firstElementChild.querySelector('table'));
        this.buildFooterRow($container.firstElementChild.lastElementChild);
        this.chainedRequest();
        this.listen2Events();
        this.simulateWinSizeChange();
    }
};