const { IView } = require('../../component/iview');
const { Entrance } = require('./components/objects');
const fs = require('fs');

class BaseView extends IView {

    constructor(...args) {

        super(...args);
        this.ringtons = Entrance.makeRings();
    }

    play(rington, crington) {

        let matched = this.ringtons.find(x => x.code == rington);
        if (matched === undefined) {
            return;
        }

        let murl = !matched.isCustomized ? matched.mediaUrl : crington;
        if (!fs.existsSync(murl)) {
            murl = this.ringtons[0].mediaUrl;
        }

        if (this.$audio === undefined) {
            this.$audio = document.createElement('audio');
        }

        try {

            if (!this.$audio.paused) {
                this.$audio.pause();
            }
    
            clearTimeout(this.pauseTimer);
            this.$audio.src = murl;
            this.$audio.play();

            /**
             * have to pause anyway after some time > this is not a loving of music but concentrating on trading
             */
            this.pauseTimer = setTimeout(() => { this.$audio.pause(); }, 1000 * 10);
        }
        catch(ex) {
            console.error(ex);
        }
    }

    /**
     * @param {String} message 
     */
    log(message) {
        this.loggerTrading.info('ztlog:' + message);
    }

    build($container) {
        super.build($container);
    }
}

module.exports = { BaseView };