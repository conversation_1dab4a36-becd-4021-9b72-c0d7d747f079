<div class="bulletin-view s-full-height">

	<template>

		<div class="bulletin">
			<table>
				<colgroup>
					<col>
					<col width="65">
				</colgroup>
				<tr>
	
					<td class="s-ellipsis">
	
						<label class="prop-name">买1量：</label>
						<label class="prop-value">{{ typeof bulletin.myl == 'number' ? thousands(bulletin.myl) : '---' }}</label>
						<label class="prop-name">买1额：</label>
						<label class="prop-value">{{ typeof bulletin.myje == 'number' ? simplify(bulletin.myje) : '---' }}</label>
						<label class="prop-name">封单笔数：</label>
						<label class="prop-value">{{ typeof bulletin.fdbs == 'number' ? thousands(bulletin.fdbs) : '---' }}</label>
	
						<span style="display: inline-block; width: 100px;"></span>
	
						<label class="prop-name">资金(万)：</label>
						<label class="prop-value">{{ typeof summary.syzj == 'number' ? precisePrice(summary.syzj) : '---' }}</label>
						<label class="prop-name s-color-red">剩(手):</label>
						<label class="prop-value s-color-red">{{ typeof summary.sx == 'number' ? thousands(summary.sx) : '---' }}</label>
	
					</td>
	
					<td rowspan="2" class="s-right">
						<el-button type="danger" size="small" @click="cancel" style="font-weight: bold; font-size: 14px;">撤单</el-button>
					</td>
	
				</tr>
	
				<tr>
	
					<td class="s-ellipsis">
	
						<span v-for="aloc in entrusts" :key="aloc.accountId" class="each-entrust">
							<span class="aname">{{ aloc.accountName }}：</span>
							<span class="hands">
								<template v-if="aloc.volumes instanceof Array">
									<template v-if="aloc.volumes.length > 3">{{ aloc.volumes.slice(0, 3).join(', ') }}, ...</template>
									<template v-else>{{ aloc.volumes.join(', ') }}</template>
								</template>
								<template v-else>---</template>
							</span>
						</span>
	
					</td>
	
				</tr>
				
			</table>
		</div>
	
		<div class="main-part">
			
			<div class="boarding">
	
				<div class="boarding-title typical-header">
		
					<span>{{ states.instrumentName }}</span>
					<span v-if="typeof states.increaseRate == 'number'" class="prop-value" :class="states.colorClass">{{ precisePrice(states.increaseRate) }}%</span>
					<span class="prop-name s-pdl-20">涨停</span>
					<span class="prop-value s-color-red">{{ typeof prices.ceiling == 'number' ? precisePrice(prices.ceiling) : '---' }}</span>
					<span class="prop-name s-pdl-20">跌停</span>
					<span class="prop-value s-color-green">{{ typeof prices.floor == 'number' ? precisePrice(prices.floor) : '---' }}</span>
		
				</div>
		
				<div class="levels themed-box s-pdl-10">
					
					<div v-for="(item, item_idx) in levels" 
						:key="item_idx"
						:style="{
							'margin-top': item_idx == 0 ? ui.margin / 2 + 'px' : 0,
							'margin-bottom': item_idx == levels.length - 1 ? ui.margin / 2 + 'px' : 0,
						}">
		
						<div class="level-item">
		
							<div class="level-p-h">
		
								<span v-if="item.price == 0" class="level-price s-unselectable">--</span>
								<span v-else class="level-price s-ellipsis s-unselectable"
										:class="item.colorClass">{{ precisePrice(item.price) }}</span>
		
								<span v-if="item.hands == 0" class="level-hands s-unselectable">--</span>
								<span v-else class="level-hands s-ellipsis s-unselectable" 
										:class="item.colorClass">{{ simplyHands(item.hands) }}</span>
							</div>
	
							<div class="level-name s-pull-left">
								<template>{{ item.label }}</template>
							</div>
		
						</div>
		
						<div v-if="item_idx == 9" class="seperator" :style="{ margin: ui.margin + 'px 1px' }"></div>
							
					</div>
			
				</div>
		
				<div class="transactions s-pdl-10 themed-box">
					
					<div v-for="(item, item_idx) in transactions" 
						:key="item_idx" 
						:style="{ height: ui.transHeight + 'px', 'line-height': ui.transHeight + 'px' }"
						:class="isMinuteEnd(item.time, item_idx) ? 'new-minute-start' : ''"
						class="trans-item">
						
						<div class="trans-time s-pull-left">
							<template>{{ item.time == null ? '00:00:00 000' : formatTransTime(item.time) }}</template>
						</div>
		
						<div class="trans-p-h">
		
							<span class="trans-price s-ellipsis" :class="decidePriceColorClass(item.price)">
								<template>{{ precisePrice(item.price) }}</template>
							</span>
			
							<span class="trans-hands s-ellipsis" 
								:class="item.direction > 0 ? 's-color-red' : item.direction < 0 ? 's-color-green' : ''">
								<template>{{ simplyHands(item.volume) }}</template>
							</span>
						</div>
		
					</div>
		
				</div>
			
			</div>
	
			<div class="remote-queue themed-box">
	
				<div v-if="remotes.length == 0" style="display: table; height: 100%; width: 100%;">
					<div style="display: table-cell; text-align: center; vertical-align: middle;">无队列数据...</div>
				</div>
				
				<ul v-show="remotes.length > 0" class="infinite-list" v-infinite-scroll="() => { loadMore(); }">
					<li
						v-for="hands in remotes"
						:class="colorizeHands(hands)"
						class="infinite-list-item remote themed-right-border themed-bottom-border s-ellipsis">{{ hands }}</li>
				</ul>
			</div>
		
		</div>

	</template>

</div>