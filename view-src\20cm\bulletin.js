const { IView } = require('../../component/iview');
const { <PERSON>ickData, PriceLevel, TransactionItem } = require('../2021/model/message');
const { AccountEntrust, UserSetting } = require('./components/objects');
const { Cm20FunctionCodes } = require('../../config/20cm');
const { repoInstrument } = require('../../repository/instrument');
const { NumberMixin } = require('../../mixin/number');
const { DatetimeMixin } = require('../../mixin/date-time');

const ViewSetting = {

    /**
     * 首批加载的委托队列数量
     */
    firstScreenCount: 11 * 26,

    /**
     * 每次增量加载的委托数据
     */
    batchCount: 11 * 3,
};

/**
 * @returns {Array<PriceLevel>}
 */
function makeLevels(count) {

    var levels = [];

    for (let idx = count; idx >= 1; idx--) {
        levels.push(new PriceLevel(false, '卖' + idx, 0, 0));
    }

    for (let idx = 1; idx <= count; idx++) {
        levels.push(new PriceLevel(true, '买' + idx, 0, 0));
    }

    return levels;
}

/**
 * @returns {Array<TransactionItem}
 */
function makeTransactions() {
    return [];
}

/**
 * @returns {Array<AccountEntrust>}
 */
function makeEntrusts() {
    return [];
}

module.exports = class BulletinView extends IView {

    constructor() {

        super('@20cm/bulletin', true, '行情与委托队列');

        this.gsettings = {

            main: this.app.bigOrderOptions.main,
            others: this.app.bigOrderOptions.others,
        };

        /**
         * 档位总数
         */
        this.tlevel = 10;
        this.levels = makeLevels(this.tlevel);

        this.states = {

            taskId: null,
            instrument: null,
            instrumentName: null,
            increaseRate: null,
            colorClass: '',
            maxEntrustHands: 0,
        };

        this.ui = {

            margin: 5,
            transHeight: 18,
        };

        this.bulletin = {

            /** 买1量 */
            myl: null,
            /** 封单笔数 */
            fdbs: null,
            /** 买1金额 */
            myje: null,
            /** 封单均 */
            fdj: null,
        };
        
        this.summary = {

            /** 实用资金（万） */
            syzj: null,
            /** 笔数 */
            bs: null,
            /** 剩下 */
            sx: null,
        };

        this.prices = {

            yesterdayClose: null,
            ceiling: null,
            floor: null,
        };

        /** 成交队列，最新一笔成交记录 */
        this.trans = {

            /** 已进入的时间片，开始时间 */
            time: null,
            /** 汇总时间片内，总计买入数量 */
            buy: 0,
            /** 汇总时间片内，总计卖出数量 */
            sell: 0,
        };

        /**
         * 最新的成交序列包含数据条数
         */
        this.tcapacity = 200;
        this.latests = makeTransactions();
        this.transactions = makeTransactions();
        this.entrusts = makeEntrusts(); 
        /** 所有账号合并的委托手数字典（key：手数，value：true） */
        this.entrustsMap = {};
        /** 远端涨停价，排队队列（剩余量） */
        this.leftRemotes = [];
        /** 远端涨停价，排队队列（已展示量） */
        this.remotes = [];

        this.renderProcess.on('expired', (event) => { this.closeWin(); });
        this.renderProcess.on('set-as-instrument', (event, taskId, code, name) => { this.setAsInstrument(taskId, code, name); });
        this.renderProcess.on('set-as-entrusts', (event, syzj, entrusts) => { this.updateEntrusts(syzj, entrusts); });
        this.renderProcess.on('set-as-queue', (event, remotes) => { this.updateQueue(remotes); });
        this.renderProcess.on('set-as-settings', (event, settings) => { this.setAsSettings(settings); });

        this.thisWindow.on('move', () => { this.tellLocation(); });
        this.thisWindow.on('resize', () => { this.tellLocation(); });
    }

    closeWin() {

        this.subscribeTick(this.states.instrument, null);
        this.thisWindow.close();
    }

    /**
     * @param {UserSetting} settings 
     */
    setAsSettings(settings) {
        this.settings = settings;
    }

    createApp() {

        this.vapp = new Vue({

            el: this.$container.firstElementChild,
            data: {

                states: this.states,
                ui: this.ui,
                bulletin: this.bulletin,
                summary: this.summary,
                prices: this.prices,
                entrusts: this.entrusts,
                remotes: this.remotes,
                entrustsMap: this.entrustsMap,
                levels: this.levels,
                transactions: this.transactions,
            },
            mixins: [NumberMixin, DatetimeMixin],
            methods: this.helper.fakeVueInsMethod(this, [

                this.cancel,
                this.simplyHands,
                this.decidePriceColorClass,
                this.precisePrice,
                this.isMinuteEnd,
                this.formatTransTime,
                this.loadMore,
                this.simplify,
                this.colorizeHands,
            ]),
        });

        this.vapp.$nextTick(() => {

            var $main = this.vapp.$el.querySelector('.main-part');
            var $boarding = $main.querySelector('.boarding');
            var $queue = $main.querySelector('.remote-queue');
            var $trans = $main.querySelector('.transactions');
            // var width = this.thisWindow.getSize()[0];
            // var boardingWidth = parseInt(width / 3);
            var boardingWidth = 266;

            $main.style.paddingRight = boardingWidth + 'px';
            $boarding.style.width = boardingWidth + 'px';
            $boarding.style.marginRight = -boardingWidth + 'px';

            this.$queue = $queue;
            this.$trans = $trans;

            this.fillEmptyTransactions();
        });
    }

    /**
     * 将价格格式化为适合的精度
     * @param {Number} price 
     */
    decidePriceColorClass(price) {

        if (price == 0 || typeof price != 'number') {
            return '';
        }
        
        let yc = this.prices.yesterdayClose;
        if (yc == 0 || yc == null) {
            return '';
        }
        else {
            return price > yc ? 's-color-red' : price < yc ? 's-color-green' : '';
        }
    }

    /**
     * 将价格格式化为适合的精度
     * @param {Number} price 
     */
    precisePrice(price) {
        return typeof price == 'number' ? price.toFixed(2) : price;
    }

    /**
     * @param {Number|String} time 时间，格式为 hmmssfff000 整形数值
     */
    isMinuteEnd(time, trans_idx) {
        
        let trans = this.transactions;

        if (trans_idx <= 0 || trans_idx == trans.length - 1) {
            return false;
        }
        
        let trans2 = trans[trans_idx + 1];
        if (!trans2) {
            return false;
        }
        
        let time2 = trans2.time;
        if (!time || !time2) {
            return false;
        }

        let stime = time.toString();
        let stime2 = time2.toString();

        if (stime.length == 11) {
            stime = '0' + stime;
        }

        if (stime2.length == 11) {
            stime2 = '0' + stime2;
        }
        
        return stime.substr(2, 2) != stime2.substr(2, 2);
    }

    /**
     * @param {Number|String} time 时间，格式为 hmmssfff000 整形数值
     */
    formatTransTime(time) {
        
        let stime = time.toString();
        if (stime.length == 11) {
            stime = '0' + stime;
        }

        return `${stime.substr(0, 2)}:${stime.substr(2, 2)}:${stime.substr(4, 2)} ${stime.substr(6, 3)}`;
    }

    setAsInstrument(taskId, instrument, instrumentName) {

        var last = this.states.instrument;

        /**
         * 合约未变更，无需订阅
         */
        if (instrument == last) {
            return;
        }

        this.setWindowTitle(`${instrumentName} - ${instrument}`, false);
        this.resetProperties();
        this.resetLevels();
        this.fillEmptyTransactions();
        
        this.states.taskId = taskId;
        this.states.instrument = instrument;
        this.states.instrumentName = instrumentName;
        this.subscribeTick(last, instrument);
        this.requestLimitedPrice(instrument, instrumentName);
    }

    resetProperties() {

        var states = this.states;
        var prices = this.prices;

        states.instrument = null;
        states.instrumentName = null;
        states.increaseRate = null;
        states.colorClass = null;

        prices.yesterdayClose = null;
        prices.ceiling = null;
        prices.floor = null;
    }

    resetLevels() {

        /**
         * 合约产生变化时，首先将档位显示重置
         */
        this.levels.forEach(level => { level.update(0, 0, 0); });
    }

    /**
     * @param {*} last 上个合约
     * @param {*} current 当前合约
     */
    subscribeTick(last, current) {

        /**
         * 首次合约信息变更时，启动监听
         */

        if (this.hasListened2TickChange === undefined) {

            /**
             * 是否已开启TICK数据监听
             */
            this.hasListened2TickChange = true;

            /**
             * 监听订阅回执
             */
            this.standardListen(this.serverEvent.subscribeTickReceived, (...args) => {
                this.handleTickChange(true, ...args);
            });

            /**
             * 监听TICK数据持续推送
             */
            this.standardListen(this.serverEvent.tickPriceChanged, (...args) => {
                this.handleTickChange(false, ...args);
            });
        }

        var tty = this.systemTrdEnum.tickType;

        if (last) {

            this.standardSend(this.systemEvent.unsubscribeTick, [last], tty.tick);
            this.standardSend(this.systemEvent.unsubscribeTick, [last], tty.transaction);
            this.standardSend(this.systemEvent.unsubscribeTick, [last], tty.orderQueue);
        }

        if (current) {

            this.standardSend(this.systemEvent.subscribeTick, [current], tty.tick);
            this.standardSend(this.systemEvent.subscribeTick, [current], tty.transaction);
            this.standardSend(this.systemEvent.subscribeTick, [current], tty.orderQueue);
        }
    }

    async requestLimitedPrice(instrument, instrumentName) {

        var resp = await repoInstrument.queryPrice(instrument);
        var { errorCode, errorMsg, data } = resp;
        var { assetType, preClosePrice, lastPrice, lowerLimitPrice, optionType, priceTick, strikePrice, upperLimitPrice, volumeMultiple } = data || {};
        var prices = this.prices;

        if (errorCode == 0 && data && upperLimitPrice > 0) {

            prices.yesterdayClose = preClosePrice;
            prices.ceiling = upperLimitPrice;
            prices.floor = lowerLimitPrice;
        }
        else {

            prices.yesterdayClose = 0;
            prices.ceiling = 9999;
            prices.floor = 0;
            this.interaction.showError(`${instrumentName}，涨跌停价格未获得：${errorCode}/${errorMsg}`);
        }
    }

    /**
     * @param {*} myl 买1量
     * @param {*} myje 买1金额
     * @param {*} fdj 封单均
     */
    updateBulle(myl, myje, fdj) {

        this.bulletin.myl = myl;
        this.bulletin.myje = myje;
        this.bulletin.fdj = fdj;
    }

    /**
     * @param {*} value 封单笔数
     */
    updateFdbs(value) {
        this.bulletin.fdbs = value;
    }

    /**
     * @param {Array<AccountEntrust>} entrusts 
     */
    updateEntrusts(syzj, entrusts) {

        this.summary.syzj = syzj;
        this.entrusts.refill(entrusts);
        this.helper.clearHash(this.entrustsMap);

        var max = 0;
        entrusts.forEach(item => {

            if (item.volumes.length > 0) {

                item.volumes.forEach(val => { this.entrustsMap[val] = true; });
                max = Math.max(max, item.volumes.max(x => x));
            }
        });

        this.states.maxEntrustHands = max;
    }

    /**
     * @param {Array<Number>} remotes 远端挂单
     */
    updateQueue(remotes) {

        this.updateFdbs(remotes.length);
        this.refreshSummary(remotes);
    }

    /**
     * @param {Array<Number>} remotes 远端挂单
     */
    refreshSummary(remotes) {
        
        var left = 0;
        var reachedFirstMatch = false;
        var hands = [];

        for (let idx = 0; idx < remotes.length; idx++) {

            // 买入单位都是手不会有散股，除了盘口第一位
            let volume = Math.ceil(remotes[idx] * 0.01);

            if (!reachedFirstMatch) {

                if (volume == this.states.maxEntrustHands) {
                    reachedFirstMatch = true;
                }

                if (!reachedFirstMatch) {
                    left += volume;
                }
            }

            hands.push(volume);
        }
        
        if (hands.length <= ViewSetting.firstScreenCount) {

            this.leftRemotes.clear();
            this.remotes.refill(hands);
        }
        else {
            
            this.remotes.refill(hands.splice(0, Math.max(ViewSetting.firstScreenCount, this.remotes.length)));
            this.leftRemotes.refill(hands);
        }

        this.summary.sx = reachedFirstMatch ? left : null;
    }

    /**
     * 处理TICK数据变化
     * @param {*} isReceipt 是否为订阅回执
     * @param {*} instrument 该TICK所属合约
     * @param {*} tickType 该TICK数据类型
     * @param {*} tick TICK数据本身
     */
    handleTickChange(isReceipt, instrument, tickType, tick) {

        if (instrument != this.states.instrument) {
            return;
        }
        
        var tty = this.systemTrdEnum.tickType;

        if (tickType == tty.tick) {
            
            var tickd = new TickData(tick);
            var change = (tickd.latest / tickd.preclose - 1) * 100;
            var buy1 = tickd.buys[0];
            var buy1Hands = buy1.hands;
            var buy1Price = buy1.price;

            this.states.increaseRate = change;
            this.states.colorClass = change > 0 ? 's-color-red' : change < 0 ? 's-color-green' : '';

            this.updateLevels(tickd);
            this.updateBulle(buy1Hands * 0.01, buy1Hands * buy1Price, null);
        }
        else if (tickType == tty.transaction && !isReceipt) {

            let now = new Date().getTime();
            let ms = 3000;
            let ti = new TransactionItem(tick);
            let vbuy = ti.direction > 0 ? ti.volume : 0;
            let vsell = ti.direction < 0 ? ti.volume : 0;

            let limited = this.decideCapacity();
            let visibles = this.transactions;
            let mores = this.latests;
            let ts = this.trans;
            let is_as_new = ts.time == null || now - ts.time >= ms;

            if (is_as_new) {

                while (visibles.length >= limited) { visibles.shift(); }
                visibles.push(ti);

                while (mores.length >= this.tcapacity) { mores.shift(); }
                mores.push(ti);

                ts.time = now;
                ts.buy = vbuy;
                ts.sell = vsell;
            }
            else {

                ts.buy += vbuy;
                ts.sell += vsell;
            }

            let latest = visibles[visibles.length - 1];
            latest.time = ti.time;
            latest.price = ti.price;
            latest.volume = ts.buy + ts.sell;
            latest.direction = ts.buy > ts.sell ? 1 : ts.buy < ts.sell ? -1 : 0;
        }
        else if (tickType == tty.orderQueue) {

            if (tick instanceof Array) {
                this.updateQueue(tick);
            }
        }
    }

    /**
     * @param {String} message 
     */
    log(message) {
        this.loggerTrading.info('ztlog:' + message);
    }

    confirm(isRequired, message, callback) {

        if (isRequired) {
            
            this.interaction.showConfirm({

                title: '操作确认',
                message: message,
                confirmed: () => {
                    callback();
                },
            });
        }
        else {
            callback();
        }
    }

    cancel() {

        let { taskId, instrument, instrumentName } = this.states;
        let task = () => {

            this.log(`to cancel a task from stand-along monitor window, stock/${instrument}/${instrumentName}`);
            this.renderProcess.send(this.systemEvent.transmitMsg2TradingServer, Cm20FunctionCodes.request.cancel, { id: taskId });
        };
        
        if (this.settings) {

            this.confirm(this.settings.prompt.mcancel, `${instrumentName}，撤销买入？`, () => {
                task();
            });
        }
        else {
            task();
        }
    }

    /**
     * 简化手数显示
     * @param {Number} volume 
     */
    simplyHands(volume) {
        return volume;
    }

    /**
     * @param {TickData} tick 
     */
    updateLevels(tick) {

        var tl = this.tlevel;
        for (let idx = 0; idx < tl; idx++) {

            let sdata = tick.sells[idx];
            this.levels[tl - 1 - idx].update(sdata.price, tick.preclose, Math.ceil(sdata.hands * 0.01), tick.sells[0].price);
        }

        for (let idx = 0; idx < tl; idx++) {

            let bdata = tick.buys[idx];
            let price = bdata.price > 0 ? bdata.price : tick.buys[0].price;
            this.levels[idx + tl].update(bdata.price, tick.preclose, Math.ceil(bdata.hands * 0.01), tick.buys[0].price);
        }
    }

    addTrans(many) {

        var bigger = this.latests;
        var smaller = this.transactions;

        if (bigger.length <= smaller.length) {
            return;
        }
        
        var start = bigger.length - smaller.length - many;
        var end = bigger.length - smaller.length;
        var elders = bigger.slice(Math.max(0, start), end);

        while (elders.length > 0) {
            this.transactions.unshift(elders.pop());
        }
    }

    reduceTrans(many) {
        this.transactions.splice(0, many);
    }

    decideCapacity() {

        if (this.$trans == undefined) {
            return 5;
        }

        var border = 1;
        return parseInt((this.$trans.offsetHeight - border) / this.ui.transHeight);
    }

    fillEmptyTransactions() {
        
        var capacity = this.decideCapacity();
        var trans = [];
        for (let idx = 1; idx <= capacity; idx++) {
            trans.push(new TransactionItem({ direction: 0, time: null, price: 0, volume: 0 }));
        }

        this.transactions.refill(trans);
        this.latests.refill(trans);

        this.trans.time = null;
        this.trans.buy = 0;
        this.trans.sell = 0;
    }

    simplify(amount) {
        return this.helper.simplifyAmount(amount);
    }

    colorizeHands(hands) {

        if (this.entrustsMap[hands] === true) {
            return 'highlighted';
        }
        
        var ins = this.states.instrument;
        if (ins.indexOf('SHSE.60') == 0 || ins.indexOf('SZSE.00') == 0) {
            return hands >= this.gsettings.main ? 'large-scale' : '';
        }
        else {
            return hands >= this.gsettings.others ? 'large-scale' : '';
        }
    }

    loadMore() {

        if (this.leftRemotes.length > 0) {
            this.remotes.merge(this.leftRemotes.splice(0, ViewSetting.batchCount));
        }
    }

    handleReconnect() {
        this.subscribeTick(undefined, this.states.instrument);
    }

    tellLocation() {

        if (!this.states.taskId) {
            return;
        }

        var pos = this.thisWindow.getPosition();
        var size = this.thisWindow.getSize();
        this.thisWindow.webContents.emit('location-change', this.states.taskId, ...pos, ...size);
    }

    tellReady() {
        this.thisWindow.webContents.emit('is-ready');
    }

    handleWinSizeChange() {
        
        var awidth = 45;
        var aheight = 24;
        var qwidth = this.$queue.offsetWidth;
        var qheight = this.$queue.offsetHeight;
        var maxVisibleCount = Math.floor(qwidth / awidth) * Math.floor(qheight / aheight);
        
        while (this.remotes.length < maxVisibleCount && this.leftRemotes.length > 0) {
            this.loadMore();
        }

        /**
         * 增减交易队列里的条目数量
         */

        var capacity = this.decideCapacity();
        var trans = this.transactions;

        if (trans.length < capacity) {
            this.addTrans(capacity - trans.length);
        }
        else if (trans.length > capacity) {
            this.reduceTrans(trans.length - capacity);
        }
    }

    build($container) {

        super.build($container);
        this.createApp();
        this.renderProcess.on(this.systemEvent.tradingServerReestablished, () => { this.handleReconnect(); });
        this.tellReady();
        this.tellLocation();
        this.lisen2WinSizeChange(this.handleWinSizeChange.bind(this), 20);
    }
};