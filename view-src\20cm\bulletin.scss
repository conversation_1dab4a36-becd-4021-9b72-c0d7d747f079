.win-title {

	font-size: 16px;
	font-weight: bold;
}

.bulletin-view {

	box-sizing: border-box;
	padding-top: 64px;
}

.bulletin {

	margin-top: -64px;
	height: 64px;
	padding: 0;

	table {

		table-layout: fixed;
		box-sizing: border-box;
		width: 100%;

		tr {

			height: 31px;
			border-bottom: 1px solid #0C1016;
		}

		td {

			padding: 0 10px;
			&:not(:last-child) {
				border-right: 1px solid #0C1016;
			}
		}
	}

	.each-entrust {

		padding-right: 20px;

		.aname {
			display: inline-block;
		}

		.hands {
			display: inline-block;
		}
	}

	.prop-value {
		padding-right: 20px;
	}
}

.main-part {

	box-sizing: border-box;
	width: 100%;
	height: 100%;
	padding-right: 266px;
}

.boarding {

	float: right;
	margin-right: -266px;
	height: 100%;
	box-sizing: border-box;
	padding: 323px 5px 5px 5px;
	overflow: hidden;

	.boarding-title {

		margin-top: -323px;
		height: 28px;
		line-height: 28px;
		padding-left: 6px;
		background-color: #1A212B;
		border-bottom: 1px solid #0C1016;
		box-sizing: border-box;
		overflow: hidden;
	}

	.levels {

		box-sizing: border-box;
		height: 295px;

		.level-item {

			box-sizing: border-box;
			height: 14px;
			padding-left: 30px;

			.level-name {

				float: left;
				margin-left: -30px;
				width: 30px;
				height: 100%;
			}

			.level-p-h {

				float: right;
				box-sizing: border-box;
				width: 100%;
				height: 100%;

				> * {

					display: block;
					float: left;
					box-sizing: border-box;
					text-align: right;
				}
			}
	
			.level-price {
				width: 50%;
			}
	
			.level-hands {

				width: 50%;
				padding-right: 10px;
			}
		}
	
		.seperator {

			height: 1px;
			background-color: #606b7b;
		}
	}

	.transactions {

		box-sizing: border-box;
		height: 100%;
		border-top: 1px solid #606b7b;
		overflow-y: auto;

		.trans-item {

			box-sizing: border-box;
			padding-left: 75px;

			&.new-minute-start {
				border-bottom: 1px dotted white;
			}

			.trans-time {
				
				width: 75px;
				margin-left: -75px;
			}

			.trans-p-h {

				box-sizing: border-box;
				height: 100%;

				> * {

					display: block;
					float: left;
					box-sizing: border-box;
					text-align: right;
				}
			}
	
			.trans-price {
				width: 50%;
			}
	
			.trans-hands {

				width: 50%;
				padding-right: 10px;
			}
		}
	}
}

.remote-queue {

	height: 100%;
	overflow-y: auto;
	box-sizing: border-box;
	border-right: 1px solid #606b7b;
	
	.infinite-list {

		margin: 0;
		padding: 0;
		height: 100%;
		overflow: auto;
	}

	.remote {

		display: inline-block;
		box-sizing: border-box;
		width: 45px;
		height: 24px;
		line-height: 24px;
		padding-left: 3px;
		cursor: default;

		&.highlighted {
			background-color: #942C2CFF;
		}

		&.large-scale {
			background-color: #d28181;
		}
	}
}