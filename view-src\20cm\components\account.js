const { BaseView } = require('../base-view');
const { SmartTable } = require('../../../libs/table/smart-table');
const { repoAccount } = require('../../../repository/account');
const { AccountSimple } = require('../components/objects');

module.exports = class PositionView extends BaseView {

    constructor() {

        super('@20cm/components/account', false, '账号信息');
        this.registerEvent('set-max-height', this.setMaxHeight.bind(this));
        this.registerEvent('auto-fit', () => { this.tableObj.fitColumnWidth(); });
    }

    setMaxHeight(height) {

        if (this.tableObj) {
            this.tableObj.setMaxHeight(height - 35);
        }
    }

    identify(record) {
        return record.id;
    }

    createTable() {
        
        var $table = this.$container.querySelector('.account-records');
        this.tableObj = new SmartTable($table, this.identify, this, {

            tableName: 'smt-20account',
            displayName: this.title,
        });

        this.tableObj.setMaxHeight(200);
        this.tableObj.setPageSize(99999);
    }

    async requestAccounts() {

        var resp = await repoAccount.getAccountDetailInfo({ identity_id: '', userId: this.userInfo.userId });
        if (resp.errorCode == 0) {

            let records = resp.data.list;
            let simples = AccountSimple.Convert(records);
            let accounts = simples.filter(x => x.identityType == this.systemEnum.identityType.account.code
                                            && x.assetType == this.systemEnum.assetsType.stock.code);

            accounts.forEach(acnt => {

                acnt.positionPercent = acnt.marketValue / (acnt.balance || 1);
                acnt.availablePositionPercent = 1 - acnt.positionPercent;
            });

            this.tableObj.refill(accounts);
        }
        else {
            this.interaction.showError('获取账号资金详情发生异常：' + resp.errorMsg);
        }
    }

    refresh() {

        this.interaction.showSuccess('刷新请求已发出');
        this.requestAccounts();
    }

    arrangeRefresh() {

        setInterval(async () => {
            
            if (this.isBackgroundRefresh) {
                return;
            }

            try {

                this.isBackgroundRefresh = true;
                await this.requestAccounts();
            }
            catch(ex) {
                console.error(ex);
            }
            finally {
                this.isBackgroundRefresh = false;
            }

        }, 1000 * 30);
    }

    exportSome() {

        var date = new Date().format('yyyyMMdd');
        var time = new Date().format('hhmmss');
        this.tableObj.exportAllRecords(`${this.title}-${this.userInfo.userName}-${date}-${time}`);
    }

    build($container) {

        super.build($container);
        this.createTable();
        this.requestAccounts();
        this.arrangeRefresh();
    }
};