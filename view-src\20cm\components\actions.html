<div class="actions-inter typical-header">

	<label class="action-name">{{ title }}</label>

	<span class="operations s-pdl-5">

		<el-autocomplete
			ref="kw"
			placeholder="输入代码或名称"
			v-model="keywords"
			:fetch-suggestions="suggest"
			@keydown.native="handleInput"
			@clear="clear"
			@select="select"
			prefix-icon="iconfont icon-sousuo"
			class="search-keywords"
			style="width: 155px;"
			clearable>

			<template slot-scope="{ item: ins }">
				<span class="item-name">{{ ins.instrumentName }} </span>
				<span class="item-code">[{{ ins.instrument }}]</span>
			</template>
			
		</el-autocomplete>

	</span>
	
</div>
