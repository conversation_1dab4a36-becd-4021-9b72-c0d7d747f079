const { BaseView } = require('../base-view');
const { <PERSON><PERSON><PERSON><PERSON><PERSON>, OneInstrument } = require('../../../libs/helper-biz');

module.exports = class ActionsView extends BaseView {

    constructor() {
        super('@20cm/components/actions', false);
    }

    createApp() {

        this.data = {

            title: '买入监控',
            keywords: null,
        };

        this.vapp = new Vue({

            el: this.$container.firstElementChild.firstElementChild,
            data: this.data,
            methods: this.helper.fakeVueInsMethod(this, [

                this.suggest,
                this.handleInput, 
                this.select,
                this.clear,
            ]),
        });

        this.vapp.$nextTick(() => {
            this.$search = this.vapp.$refs.kw.$el.querySelector('input');
        });
    }

    handleInput() {

        if (event.keyCode == 8) {

            event.returnValue = false;
            this.data.keywords = null;
            this.clear();
        }
        else if (typeof this.data.keywords == 'string' && this.data.keywords.trim().length == 0) {
            this.clear();
        }
    }

    clear() {
        this.data.keywords = null;
    }

    addStocks(stocks) {
        this.trigger('add-stocks', stocks);
    }

    /**
     * @param {OneInstrument} selected 
     */
    select(selected) {

        var { instrument, instrumentName } = selected;
        this.data.keywords = `${instrumentName}-${instrument}`;
        this.log(`manual select stock/${instrument}/${instrumentName}`);
        this.addStocks([selected]);
        this.clear();

        /**
         * 延时目的：避免因为auto complete待选项，因为敲击回车选中，引起回车快捷事件
         */
        setTimeout(() => { this.$search.blur(); }, 100);
    }

    /**
     * @param {String} keywords 
     * @param {Function} callback 
     */
    suggest(keywords, callback) {

        if (typeof keywords != 'string' || keywords.trim().length < 1) {

            callback([]);
            return;
        }

        let matches = BizHelper.filterInstruments(this.systemEnum.assetsType.stock.code, keywords);
        if (matches.length == 1) {

            callback([]);
            this.select(matches[0]);
            return;
        }

        callback(matches);
    }

    listen2Events() {
        this.registerEvent('focus-on-search', () => { this.$search && this.$search.focus(); });
    }
    
    build($container) {

        super.build($container);
        this.createApp();
        this.listen2Events();
    }
};