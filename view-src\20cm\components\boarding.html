<div class="s-full-height">
	
	<div class="boarding s-full-height">

		<div class="boarding-title typical-header" style="background-color: #1A212B; border-bottom: 1px solid #0C1016; box-sizing: border-box;">

			<span v-if="states.instrument">{{ states.instrumentName }}（{{ states.instrument }}）</span>
			<span v-else>---</span>
			<span class="prop-value" :class="states.colorClass">{{ typeof states.increaseRate == 'number' ? precisePrice(states.increaseRate) + '%' : '---' }}</span>
			<span class="prop-name s-pdl-20">涨停</span>
			<span class="prop-value s-color-red">{{ typeof states.prices.ceiling == 'number' ? precisePrice(states.prices.ceiling) : '---' }}</span>
			<span class="prop-name s-pdl-20">跌停</span>
			<span class="prop-value s-color-green">{{ typeof states.prices.floor == 'number' ? precisePrice(states.prices.floor) : '---' }}</span>

		</div>

		<div class="levels themed-box s-pdl-10">
			
			<div v-for="(item, item_idx) in levels" 
				:key="item_idx"
				:style="{
					'margin-top': item_idx == 0 ? states.ui.margin / 2 + 'px' : 0,
					'margin-bottom': item_idx == levels.length - 1 ? states.ui.margin / 2 + 'px' : 0,
				}">

				<div class="level-item">

					<div class="level-name s-pull-left">
						<template>{{ item.label }}</template>
					</div>

					<div class="level-p-h">

						<span v-if="item.price == 0" class="level-price s-unselectable">--</span>
						<span v-else class="level-price s-ellipsis s-unselectable"
								:class="item.colorClass">{{ precisePrice(item.price) }}</span>

						<span v-if="item.hands == 0" class="level-hands s-unselectable">--</span>
						<span v-else class="level-hands s-ellipsis s-unselectable" 
								:class="item.colorClass">{{ simplyHands(item.hands) }}</span>
					</div>

				</div>

				<div v-if="item_idx == 9" 
					class="seperator"
					:style="{ margin: states.ui.margin + 'px 1px' }"></div>
					
			</div>
	
		</div>

		<div class="transactions s-pdl-10 themed-box themed-left-border">
			
			<div v-for="(item, item_idx) in transactions" 
				:key="item_idx" 
				:class="isMinuteEnd(item.time, item_idx) ? 'new-minute-start' : ''"
				class="trans-item">
				
				<div class="trans-time s-pull-left">
					<template>{{ item.time == null ? '00:00:00 000' : formatTransTime(item.time) }}</template>
				</div>

				<div class="trans-p-h">

					<span class="trans-price s-ellipsis" :class="decidePriceColorClass(item.price)">
						<template>{{ precisePrice(item.price) }}</template>
					</span>
	
					<span class="trans-hands s-ellipsis" 
						:class="item.direction > 0 ? 's-color-red' : item.direction < 0 ? 's-color-green' : ''">
						<template>{{ simplyHands(item.volume) }}</template>
					</span>
				</div>

			</div>

		</div>
	
	</div>
	
</div>