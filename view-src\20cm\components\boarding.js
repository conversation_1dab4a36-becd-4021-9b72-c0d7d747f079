const { IView } = require('../../../component/iview');
const { BizHelper } = require('../../../libs/helper-biz');
const { TickData, PriceLevel, TransactionItem } = require('../../2021/model/message');
const { NumberMixin } = require('../../../mixin/number');
const { DatetimeMixin } = require('../../../mixin/date-time');
const { repoInstrument } = require('../../../repository/instrument');

/**
 * @returns {Array<PriceLevel>}
 */
 function makeLevels(count) {

    var levels = [];

    for (let idx = count; idx >= 1; idx--) {
        levels.push(new PriceLevel(false, '卖' + idx, 0, 0));
    }

    for (let idx = 1; idx <= count; idx++) {
        levels.push(new PriceLevel(true, '买' + idx, 0, 0));
    }

    return levels;
}

/**
 * @returns {Array<TransactionItem}
 */
function makeTransactions() {
    return [];
}

module.exports = class BoardingView extends IView {

    constructor() {

        super('@20cm/components/boarding', false);

        /**
         * 档位总数
         */
        this.tlevel = 10;
        this.levels = makeLevels(this.tlevel);

        this.states = {

            instrument: null,
            instrumentName: null,
            increaseRate: null,
            colorClass: '',

            prices: {

                yesterdayClose: null,
                ceiling: null,
                floor: null,
            },
            
            ui: {

                margin: 5,
                levelRowHeight: 1,
            },
        };

        /** 成交队列，最新一笔成交记录 */
        this.trans = {

            /** 已进入的时间片，开始时间 */
            time: null,
            /** 汇总时间片内，总计买入数量 */
            buy: 0,
            /** 汇总时间片内，总计卖出数量 */
            sell: 0,
        };

        this.transactions = makeTransactions();
        this.registerEvent('set-as-instrument', this.setAsInstrument.bind(this));
        this.registerEvent('test-instrument', this.testInstrument.bind(this));
    }

    createApp() {

        new Vue({

            el: this.$container.firstElementChild.firstElementChild,
            data: {

                states: this.states,
                levels: this.levels,
                transactions: this.transactions,
            },
            mixins: [NumberMixin, DatetimeMixin],
            methods: this.helper.fakeVueInsMethod(this, [

                this.simplyHands,
                this.decidePriceColorClass,
                this.precisePrice,
                this.isMinuteEnd,
                this.formatTransTime,
            ]),
        });
    }

    /**
     * 将价格格式化为适合的精度
     * @param {Number} price 
     */
    decidePriceColorClass(price) {

        if (price == 0 || typeof price != 'number') {
            return '';
        }
        
        let yc = this.states.prices.yesterdayClose;
        if (yc == 0 || yc == null) {
            return '';
        }
        else {
            return price > yc ? 's-color-red' : price < yc ? 's-color-green' : '';
        }
    }

    /**
     * 将价格格式化为适合的精度
     * @param {Number} price 
     */
    precisePrice(price) {
        return typeof price == 'number' ? price.toFixed(2) : price;
    }

    /**
     * @param {Number|String} time 时间，格式为 hmmssfff000 整形数值
     */
    isMinuteEnd(time, trans_idx) {
        
        if (trans_idx <= 0 || trans_idx == this.transactions.length - 1) {
            return false;
        }
        
        let trans2 = this.transactions[trans_idx + 1];
        if (!trans2) {
            return false;
        }
        
        let time2 = trans2.time;
        if (!time || !time2) {
            return false;
        }

        let stime = time.toString();
        let stime2 = time2.toString();

        if (stime.length == 11) {
            stime = '0' + stime;
        }

        if (stime2.length == 11) {
            stime2 = '0' + stime2;
        }
        
        return stime.substr(2, 2) != stime2.substr(2, 2);
    }

    /**
     * @param {Number|String} time 时间，格式为 hmmssfff000 整形数值
     */
    formatTransTime(time) {
        
        let stime = time.toString();
        if (stime.length == 11) {
            stime = '0' + stime;
        }

        return `${stime.substr(0, 2)}:${stime.substr(2, 2)}:${stime.substr(4, 2)} ${stime.substr(6, 3)}`;
    }

    /**
     * 检测交易单元被删除时，合约是否为当前行情面板的合约
     * @param {String} stockCode
     */
    testInstrument(stockCode) {

        if (this.states.instrument != stockCode) {
            return;
        }

        this.subscribeTick(stockCode, null);
        this.resetProperties();
        this.resetLevels();
        this.fillEmptyTransactions();
    }

    /**
     * 设置为当前合约
     * @param {String} stockCode
     */
    setAsInstrument(stockCode) {

        var stocks = BizHelper.filterInstruments(this.systemEnum.assetsType.stock.code, stockCode, true);
        if (stocks.length == 0) {

            this.interaction.showError(`合约代码/${stockCode}，未找到`);
            return;
        }

        var matched = stocks[0];
        var states = this.states;
        var last = states.instrument;

        /**
         * 合约未变更，无需订阅
         */
        if (matched.instrument == last) {
            return;
        }

        this.resetProperties();
        this.resetLevels();
        this.fillEmptyTransactions();        
        
        states.instrument = matched.instrument;
        states.instrumentName = matched.instrumentName;
        this.subscribeTick(last, matched.instrument);
        this.requestLimitedPrice(matched.instrument, matched.instrumentName);
    }

    resetProperties() {

        var states = this.states;
        states.instrument = null;
        states.instrumentName = null;
        states.prices.yesterdayClose = null;
        states.prices.ceiling = null;
        states.prices.floor = null;
        states.increaseRate = null;
        states.colorClass = null;
    }

    resetLevels() {

        /**
         * 合约产生变化时，首先将档位显示重置
         */
        this.levels.forEach(level => { level.update(0, 0, 0); });
    }

    /**
     * @param {*} last 上个合约
     * @param {*} current 当前合约
     */
    subscribeTick(last, current) {

        /**
         * 首次合约信息变更时，启动监听
         */

        if (this.hasListened2TickChange === undefined) {

            /**
             * 是否已开启TICK数据监听
             */
            this.hasListened2TickChange = true;

            /**
             * 监听订阅回执
             */
            this.standardListen(this.serverEvent.subscribeTickReceived, (...args) => {
                this.handleTickChange(true, ...args);
            });

            /**
             * 监听TICK数据持续推送
             */
            this.standardListen(this.serverEvent.tickPriceChanged, (...args) => {
                this.handleTickChange(false, ...args);
            });
        }

        if (last) {

            this.standardSend(this.systemEvent.unsubscribeTick, [last], this.systemTrdEnum.tickType.tick);
            this.standardSend(this.systemEvent.unsubscribeTick, [last], this.systemTrdEnum.tickType.transaction);
        }

        if (current) {

            this.standardSend(this.systemEvent.subscribeTick, [current], this.systemTrdEnum.tickType.tick);
            this.standardSend(this.systemEvent.subscribeTick, [current], this.systemTrdEnum.tickType.transaction);
        }
    }

    async requestLimitedPrice(instrument, instrumentName) {

        var resp = await repoInstrument.queryPrice(instrument);
        var { errorCode, errorMsg, data } = resp;
        var { assetType, preClosePrice, lastPrice, lowerLimitPrice, optionType, priceTick, strikePrice, upperLimitPrice, volumeMultiple } = data || {};

        if (errorCode == 0 && data && upperLimitPrice > 0) {

            this.states.prices.yesterdayClose = preClosePrice;
            this.states.prices.ceiling = upperLimitPrice;
            this.states.prices.floor = lowerLimitPrice;
        }
        else {

            this.states.prices.yesterdayClose = 0;
            this.states.prices.ceiling = 9999;
            this.states.prices.floor = 0;
            this.interaction.showError(`${instrumentName}，涨跌停价格未获得：${errorCode}/${errorMsg}`);
        }
    }

    /**
     * 处理TICK数据变化
     * @param {*} isReceipt 是否为订阅回执
     * @param {*} instrument 该TICK所属合约
     * @param {*} tickType 该TICK数据类型
     * @param {*} tick TICK数据本身
     */
    handleTickChange(isReceipt, instrument, tickType, tick) {

        if (instrument != this.states.instrument) {
            return;
        }
        else if (tickType == this.systemTrdEnum.tickType.tick) {
            
            var tickd = new TickData(tick);
            this.updateLevels(tickd);
            this.states.increaseRate = (tickd.latest / tickd.preclose - 1) * 100;
            this.states.colorClass = this.states.increaseRate > 0 ? 's-color-red' : this.states.increaseRate < 0 ? 's-color-green' : '';
        }
        else if (tickType == this.systemTrdEnum.tickType.transaction && !isReceipt) {

            let now = new Date().getTime();
            let ms = 3000;
            let ti = new TransactionItem(tick);
            let vbuy = ti.direction > 0 ? ti.volume : 0;
            let vsell = ti.direction < 0 ? ti.volume : 0;

            let list = this.transactions;
            let ts = this.trans;
            let is_as_new = ts.time == null || now - ts.time >= ms;

            if (is_as_new) {

                list.shift();
                list.push(ti);
                ts.time = now;
                ts.buy = vbuy;
                ts.sell = vsell;
            }
            else {

                ts.buy += vbuy;
                ts.sell += vsell;
            }

            let latest = list[list.length - 1];
            latest.time = ti.time;
            latest.price = ti.price;
            latest.volume = ts.buy + ts.sell;
            latest.direction = ts.buy > ts.sell ? 1 : ts.buy < ts.sell ? -1 : 0;
        }
    }

    /**
     * 简化手数显示
     * @param {Number} volume 
     */
    simplyHands(volume) {
        
        return volume;

        // if (volume <= 999999) {
        //     return volume;
        // }
        // else if (volume >= 1000000 && volume <= 99999999) {
        //     return (volume / 10000).toFixed(1) + '万';
        // }
        // else {
        //     return (volume / 100000000).toFixed(1) + '亿';
        // }
    }

    /**
     * @param {TickData} tick 
     */
    updateLevels(tick) {

        for (let idx = 0; idx < this.tlevel; idx++) {

            let sdata = tick.sells[idx];
            this.levels[this.tlevel - 1 - idx].update(sdata.price, tick.preclose, Math.ceil(sdata.hands * 0.01), tick.sells[0].price);
        }

        for (let idx = 0; idx < this.tlevel; idx++) {

            let bdata = tick.buys[idx];
            let price = bdata.price > 0 ? bdata.price : tick.buys[0].price;
            this.levels[idx + this.tlevel].update(bdata.price, tick.preclose, Math.ceil(bdata.hands * 0.01), tick.buys[0].price);
        }
    }

    fillEmptyTransactions() {
        
        var trans = [];
        for (let idx = 1; idx <= this.tlevel * 2; idx++) {
            trans.push(new TransactionItem({ direction: 0, time: null, price: 0, volume: 0 }));
        }
        this.transactions.refill(trans);
    }

    handleReconnect() {
        this.subscribeTick(undefined, this.states.instrument);
    }

    build($container, { height }) {

        super.build($container);
        this.states.ui.levelRowHeight = (height - this.states.ui.margin * 3 - 30) / this.tlevel / 2;
        this.createApp();
        this.fillEmptyTransactions();
        this.renderProcess.on(this.systemEvent.tradingServerReestablished, () => { this.handleReconnect(); });
    }
};