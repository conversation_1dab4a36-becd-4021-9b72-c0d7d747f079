<div class="buys-inter">

	<div v-for="(unit, unit_idx) in units" 
		:key="unit_idx" 
		class="identity-bought-unit stock-unit typical-content"
		:class="isFocused(unit) ? 'focused' : ''"
		@click="setAsCurrent(unit)">

		<div class="title typical-header">
			<span>{{ unit.stock.name }} ({{ unit.stock.code }})</span>
			<span class="s-pull-right">

				<el-tooltip placement="bottom-end">

					<div slot="content">
						<div v-for="item in unit.paramb" :key="item.label" style="line-height: 22px;">
							<label class="prop-name" style="display: inline-block; width: 100px; text-align: right;">{{ item.label }}：</label>
							<label v-if="item.isPercent" class="prop-value">{{ item.value || '---' }}</label>
							<label v-else class="prop-value">{{ typeof item.value == 'number' ? thousands(item.value) : '---' }}</label>
						</div>
					</div>
					
					<span class="s-pdl-10 s-pdr-10 shine-color s-hover-underline s-fs-14">
						<label class="prop-name">数据</label>
						<label class="prop-value">{{ typeof unit.paramb.bl.value == 'number' ? thousands(unit.paramb.bl.value) : '---' }}</label>
					</span>

				</el-tooltip>

				<el-button type="primary" size="small" @click="popupEntrust(unit)" class="s-mgl-5 s-mgr-5" style="position: relative; top: -2px;">全息</el-button>
				<el-button type="danger" size="small" @click="cancel(unit)" class="s-mgr-5" style="position: relative; top: -1px; font-weight: bold; font-size: 14px;">撤单</el-button>
				<button class="collapser-btn s-mgl-5 s-opacity-7 s-opacity-hover" @click="unit.expanded = !unit.expanded;" style="position: relative; top: -1px;">
					<i :class="'el-icon-arrow-' + (unit.expanded ? 'up' : 'down')"></i>
				</button>
			</span>
		</div>

		<div class="unit-body">
	
			<div class="ctr-row" v-show="unit.expanded">
	
				<span 
					v-for="(thres, thres_idx) in unit.threses" 
					:key="thres_idx" 
					class="ctr-thres" 
					:class="'col-' + (thres_idx % 2 > 0 ? 'second' : 'first') + ' thres-option-' + thres.checkedProp"
					:style="{ width: (thres_idx == 0 ? 38 : thres_idx == 1 ? 38 : thres_idx == 2 ? 38 : 60) + '%' }">
	
					<el-checkbox 
						v-model="thres.isOn" 
						class="choice-check" 
						:class="(thres.members[0].label.length == 4 && thres_idx % 2 > 0 ? 'shorter' : '') + (thres.isOn ? '' : ' unchecked')"
						@change="handleSomeChange(unit, '1-2-01:' + thres.checkedProp, thres.isOn)">
						{{ thres.members[0].label }}
					</el-checkbox>
	
					<span v-for="(memb, memb_idx) in thres.members" :key="memb_idx">
					
						<el-input-number
							v-model="memb.value" 
							@change="handleSomeChange(unit, '1-2-02:' + thres.checkedProp + ':' + memb.prop, memb.value)"
							:min="memb.min"
							:max="memb.max"
							:step="memb.step"
							:controls="memb.hasButton"
							controls-position="right" 
							size="mini"
							class="medium-input s-mgl-10"></el-input-number>

						<label class="input-unit s-pdl-5">{{ memb.unit }}</label>

					</span>

				</span>
	
			</div>

			<div class="themed-bg" style="height: 1px;"></div>
	
			<div class="ctr-row row-entrust s-ellipsis" v-show="unit.expanded">

				<div class="summary">
					<label class="prop-name">资金(万)：</label>
					<label class="prop-value">{{ typeof unit.summary.syzj == 'number' ? precisePrice(unit.summary.syzj) : '---' }}</label>
					<br>
					<!-- <label class="prop-name">笔：</label>
					<label class="prop-value">{{ typeof unit.summary.bs == 'number' ? thousands(unit.summary.bs) : '---' }}</label>
					<br> -->
					<label class="prop-name s-fs-20 s-color-red">剩(手):</label>
					<label class="prop-value s-fs-20 s-color-red">{{ typeof unit.summary.sx == 'number' ? thousands(unit.summary.sx) : '---' }}</label>
					<br>
				</div>

				<div class="entrusts" v-if="unit.entrusts.length > 0">
					<span v-for="aloc in unit.entrusts" :key="aloc.accountId" class="each-entrust">
						<span class="aname s-ellipsis">{{ aloc.accountName }}</span>
						<span style="position: relative; top: -6px;">: </span>
						<span class="hands s-ellipsis">
							<template v-if="aloc.volumes instanceof Array">
								<template v-if="aloc.volumes.length > 3">
									<el-tooltip placement="right" :content="aloc.volumes.join(', ')">
										<span>{{ aloc.volumes.slice(0, 3).join(', ') }}...</span>
									</el-tooltip>
								</template>
								<template v-else>{{ aloc.volumes.join(', ') }}</template>
							</template>
							<template v-else>---</template>
						</span>
					</span>
				</div>

			</div>

			<div class="bulletin themed-box s-fs-16">
			
				<label class="prop-name">买1量</label>
				<label class="prop-value">{{ typeof unit.bulletin.myl == 'number' ? thousands(unit.bulletin.myl) : '---' }}</label>
				<label class="prop-name">买1额</label>
				<label class="prop-value">{{ typeof unit.bulletin.myje == 'number' ? simplify(unit.bulletin.myje) : '---' }}</label>
				<label class="prop-name">封单笔数</label>
				<label class="prop-value">{{ typeof unit.bulletin.fdbs == 'number' ? thousands(unit.bulletin.fdbs) : '---' }}</label>
	
			</div>
	
			<div class="remote-queue themed-box themed-top-border themed-left-border">

				<ul class="infinite-list" v-infinite-scroll="() => { load(unit); }">
					<li
						v-for="hands in unit.remotes"
						:class="colorizeHands(unit, hands)"
						class="infinite-list-item remote themed-right-border themed-bottom-border s-ellipsis">{{ hands }}</li>
				</ul>
			</div>

		</div>

	</div>
</div>
<template v-if="units.length == 0">
	<div class="no-data-notice">
		<div class="displaying">
			<i class="el-icon-moon"></i> 没有已买股票
		</div>
	</div>
</template>
