const { BaseView } = require('../base-view');
const { NumberMixin } = require('../../../mixin/number');
const { TickData } = require('../../2021/model/message');
const { Entrance, TaskStatus, UserSetting, TradeThreshold, TaskObject, AccountEntrust } = require('./objects');
const { PopGovener } = require('./pop-governer');
const { Cm20FunctionCodes } = require('../../../config/20cm');
const { repo20Cm } = require('../../../repository/20cm');

const ViewSetting = {

    /**
     * 首批加载的委托队列数量
     */
    firstScreenCount: 60,

    /**
     * 每次增量加载的委托数据
     */
    batchCount: 30,
};

/**
 * @returns {Array<AccountEntrust>}
 */
function makeEntrusts() {
    return [];
}

class BoughtUnit {

    constructor(stockCode, stockName, taskId) {

        this.stock = {
            
            code: stockCode,
            name: stockName,
        };

        /** 是否展开 */
        this.expanded = true;
        /** 任务ID */
        this.taskId = taskId;
        this.bulletin = {

            /** 买1量 */
            myl: null,
            /** 封单笔数 */
            fdbs: null,
            /** 买1金额 */
            myje: null,
            /** 封单均 */
            fdj: null,
        };

        this.threses = Entrance.makeBoughtThresholds();
        this.supplement = {

            isOn: false,
            value: 0,
        };

        this.entrusts = makeEntrusts();

        this.summary = {

            /** 实用资金（万） */
            syzj: null,
            /** 笔数 */
            bs: null,
            /** 剩下 */
            sx: null,
        };

        this.paramb = Entrance.makeParamb();

        /**
         * 远端涨停价排队队列
         */
        this.leftRemotes = [];
        this.remotes = [];

        /**
         * 所有账号合并的委托手数字典（key：手数，value：true）
         */
        this.entrustsMap = {};
        this.maxEntrustHands = 0;
    }

    removeCentralThrs() {
        this.threses.remove(x => x instanceof TradeThreshold && x.isCentralizedOnly);
    }

    /**
     * @param {*} myl 买1量
     * @param {*} myje 买1金额
     * @param {*} fdj 封单均
     */
    updateBulle(myl, myje, fdj) {

        this.bulletin.myl = myl;
        this.bulletin.myje = myje;
        this.bulletin.fdj = fdj;
    }

    /**
     * @param {*} value 封单笔数
     */
    updateFdbs(value) {
        this.bulletin.fdbs = value;
    }

    /**
     * @param {Array<Number} remotes 远端挂单
     */
    refreshSummary(remotes) {

        var left = 0;
        var reachedFirstMatch = false;
        var hands = [];

        for (let idx = 0; idx < remotes.length; idx++) {

            // 买入单位都是手不会有散股，除了盘口第一位
            let volume = Math.ceil(remotes[idx] * 0.01);

            if (!reachedFirstMatch) {

                if (volume == this.maxEntrustHands) {
                    reachedFirstMatch = true;
                }

                if (!reachedFirstMatch) {
                    left += volume;
                }
            }

            hands.push(volume);
        }
        
        if (hands.length <= ViewSetting.firstScreenCount) {

            this.leftRemotes = [];
            this.remotes = hands;
        }
        else {
            
            this.remotes = hands.splice(0, Math.max(ViewSetting.firstScreenCount, this.remotes.length));
            this.leftRemotes = hands;
        }

        this.summary.sx = reachedFirstMatch ? left : null;
    }

    loadMore() {

        if (this.leftRemotes.length > 0) {
            this.remotes.merge(this.leftRemotes.splice(0, ViewSetting.batchCount));
        }
    }

    /**
     * @param {Array<Number>} remotes 远端挂单
     */
    updateQueue(remotes) {

        this.updateFdbs(remotes.length);
        this.refreshSummary(remotes);
    }

    /**
     * @param {Array<AccountEntrust>} updates 
     */
    updateEntrusts(updates) {

        this.entrusts.refill(updates);
        var map = {};
        var max = 0;

        updates.forEach(item => {

            if (item.volumes.length > 0) {

                item.volumes.forEach(val => { map[val] = true; });
                max = Math.max(max, item.volumes.max(x => x));
            }
        });

        this.entrustsMap = map;
        this.maxEntrustHands = max;
    }

    /**
     * @param {TaskObject} task 
     */
    attachTask(task) {
        this.task = task;
    }
}

module.exports = class BoughtListView extends BaseView {

    constructor() {

        super('@20cm/components/bought-list', false, '已买清单');

        this.gsettings = {

            main: this.app.bigOrderOptions.main,
            others: this.app.bigOrderOptions.others,
        };

        /**
         * @returns {Array<BoughtUnit>}
         */
        function makeUnits() {
            return [];
        }

        this.popper = new PopGovener(this);
        this.units = makeUnits();
        this.unitsMap = {};
        this.states = {

            focused: this.units.length > 0 ? this.units[0] : null,
        };

        this.registerEvent('setting-updated', (settings) => { this.setAsSettings(settings); });
        this.registerEvent('no-focused-unit', () => { this.clearCurrent(); });
        this.registerEvent('set-tasks', (tasks, isReply) => { this.handleTaskChange(tasks, isReply); });
        this.registerEvent('get-into-continious-trade-time', () => { this.setAsContiniousTrade(); });
    }

    setAsContiniousTrade() {

        if (this.isContiniousRange == undefined) {
            
            this.isContiniousRange = true;
            this.units.forEach(unit => { unit.removeCentralThrs(); });
        }
    }

    /**
     * @param {UserSetting} settings 
     */
    setAsSettings(settings) {
        
        this.settings = settings;
        this.popper.updateSettings(settings);
    }

    createApp() {

        this.vapp = new Vue({

            el: this.$container.firstElementChild,
            data: {

                units: this.units,
                states: this.states,
            },
            mixins: [NumberMixin],
            methods: this.helper.fakeVueInsMethod(this, [

                this.isFocused,
                this.setAsCurrent,
                this.cancel,
                this.popupEntrust,
                this.simplify,
                this.handleSomeChange,
                this.precisePrice,
                this.load,
                this.colorizeHands,
            ]),
        });
    }

    /**
     * @param {BoughtUnit} unit 
     */
    colorizeHands(unit, hands) {

        if (unit.entrustsMap[hands] === true) {
            return 'highlighted';
        }
        
        var ins = unit.stock.code;
        if (ins.indexOf('SHSE.60') == 0 || ins.indexOf('SZSE.00') == 0) {
            return hands >= this.gsettings.main ? 'large-scale' : '';
        }
        else {
            return hands >= this.gsettings.others ? 'large-scale' : '';
        }
    }

    /**
     * @param {Array<TaskObject>} tasks
     * @param {Boolean} isReply
     */
    handleTaskChange(tasks, isReply) {

        tasks.forEach(task => {

            let status = task.strikeBoardStatus;

            if (status == TaskStatus.stopped || status == TaskStatus.supplemented) {

                let matched = this.units.find(x => x.taskId == task.id);
                if (matched) {
                    this.ring4Canceled(matched);
                }
            }

            if (TaskObject.isUnexpected(status) || TaskObject.isAlive(status) || TaskObject.isRunning(status)) {

                this.units.remove(x => x instanceof BoughtUnit && x.taskId == task.id);
                delete this.unitsMap[task.id];
                this.unsubscribeTick(task.instrument);
                this.popper.close(task.id);
                this.log(`unit removed from bought list, task id/${task.id}, task status/${status}, stock/${task.instrument}/${task.instrumentName}`);

                /**
                 * todo21: 可能解决了缺陷：添加一个合约 》启动 》已买 》完成结束 》重新添加相同合约 》动态实时提交的参数为上一次的参数痕迹 
                 */
                if (this.states.focused && this.states.focused.taskId === task.id) {

                    this.clearCurrent();
                    this.trigger('unit-removed', task.instrument);
                }

                return;
            }
            else if (!TaskObject.isOrdered(status)) {

                console.error('unexpected task status = ' + status);
                return;
            }

            let unit = this.units.find(x => x.taskId == task.id);
            if (unit === undefined) {

                /**
                 * 作为一个新的监控
                 */
                unit = new BoughtUnit(task.instrument, task.instrumentName, task.id);
                if (this.isContiniousRange) {
                    unit.removeCentralThrs();
                }
                
                unit.attachTask(task);
                this.units.push(unit);
                this.unitsMap[task.id] = unit;
                this.subscribeTick(unit.stock.code);
                this.ring4Bought(unit);
                this.requestParamb(unit);
                this.log(`unit added to bought list, task id/${task.id}, task status/${status}, stock/${task.instrument}/${task.instrumentName}`);
            }
            
            let ccd = task.cancelCondition;
            unit.threses.forEach(thr => {

                thr.isOn = ccd[thr.checkedProp] || false;
                thr.members.forEach(memb => { memb.value = ccd[memb.prop]; });
            });

            unit.supplement.isOn = !!task.supplementOpen;
            unit.supplement.value = task.supplementVolume;

            //usedMargin是元，转换为万 除 10000再乘100，简化为除100
            unit.summary.syzj = Math.round(task.usedMargin / 100) / 100;
            unit.updateEntrusts(task.xorders);
            this.popper.updateEntrust(task.id, unit.summary.syzj, task.xorders);
        });
    }

    /**
     * @param {BoughtUnit} unit 
     */
    isCheckedOk(unit) {
        
        var message = null;

        if (unit.threses.some(x => x.members.some(y => typeof y.value != 'number'))) {
            message = '撤单条件中，有参数值缺失（请输入数字，勿留空）';
        }

        if (message) {
            this.interaction.showError(message);
        }

        return !message;
    }

    /**
     * @param {BoughtUnit} unit 
     */
    submit(unit) {

        var ccd = {};
        unit.threses.forEach(thr => {

            ccd[thr.checkedProp] = thr.isOn || false;
            thr.members.forEach(memb => { ccd[memb.prop] = memb.value; });
        });

        var it = unit.task;
        var task = new TaskObject({

            id: it.id,
            userId: this.userInfo.userId,
            userName: this.userInfo.userName,
            instrument: it.instrument,
            instrumentName: null,
            direction: it.direction,
            priceFollowType: it.priceFollowType,
            orderPrice: it.orderPrice,
            limitPositionType: it.limitPositionType,
            strikeBoardStatus: it.strikeBoardStatus,
            supplementVolume: unit.supplement.value,
            supplementOpen: unit.supplement.isOn,
            splitInterval: it.splitInterval,
            splitType: it.splitType,
            splitDetail: this.settings.spliting,
            boardStrategy: this.helper.deepClone(it.boardStrategy),
            cancelCondition: ccd,
            cash: it.cash,
            positionPercent: it.positionPercent,
            creditFlag: it.creditFlag,
        });

        this.renderProcess.send(this.systemEvent.transmitMsg2TradingServer, Cm20FunctionCodes.request.modify, task);
        this.log(`submit the hot change of a bought unit, task = ${JSON.stringify(task)}`);
        return true;
    }

    /**
     * @param {BoughtUnit} unit 
     */
    handleSomeChange(unit, trigger, value) {
        
        if (!this.isCheckedOk(unit)) {
            return;
        }

        this.log(`manual change in bought unit: ${unit.taskId}/${unit.stock.code}/${trigger}/${value}`);
        this.submit(unit);
        this.interaction.showSuccess(`${unit.stock.name}，监控参数变动，已提交。`);
    }

    /**
     * @param {BoughtUnit} unit 
     */
    ring4Canceled(unit) {

        if (this.settings) {

            let rt = this.settings.rington;
            this.play(rt.canceled, rt.customized ? rt.customized.canceled : undefined);
        }
    }

    /**
     * @param {BoughtUnit} unit 
     */
    ring4Bought(unit) {

        if (this.settings) {

            let rt = this.settings.rington;
            this.play(rt.entrusted, rt.customized ? rt.customized.entrusted : undefined);
        }
    }

    percentagize(rate) {
        return typeof rate == 'number' ? (rate * 100).toFixed(2) + '%' : rate;
    }

    /**
     * @param {BoughtUnit} unit 
     */
    async requestParamb(unit) {

        var stock = unit.stock.code;
        var resp = await repo20Cm.queryParamb(stock);
        var { err, errMsg, data } = resp;
        var bizdata = data[stock];
        
        if (err != 0 || !this.helper.isJson(bizdata)) {

            for (let key in unit.paramb) {
                unit.paramb[key].value = null;
            }

            return;
        }

        var { 

            trading_day, 
            b_amount, 
            b_minute_amount,
            sealing_plate_rate, 
            disposable_sealing_plate_rate, 
            high_open_rate, 
            high_open_premium_rate, 
            avg_yield_rate,
            peak_yield_rate,
            avg_seal_plate_volume,
            board_volume,
        } = bizdata;

        var paramb = unit.paramb;
        paramb.fbgl.value = this.percentagize(sealing_plate_rate);
        paramb.ycxfbgl.value = this.percentagize(disposable_sealing_plate_rate);
        paramb.gkgl.value = this.percentagize(high_open_rate);
        paramb.cryjl.value = this.percentagize(high_open_premium_rate);
        paramb.jjsylj.value = this.percentagize(avg_yield_rate);
        paramb.zgsylj.value = this.percentagize(peak_yield_rate);
        paramb.zdfblj.value = avg_seal_plate_volume;
        paramb.bscjl.value = board_volume;
        paramb.bl.value = b_amount;

        if (this.hasStartedParambUpdate == undefined) {

            this.hasStartedParambUpdate = true;
            setInterval(() => {
                this.units.forEach(unit => {
                    if (typeof unit.paramb.bl.value != 'number') {
                        this.requestParamb(unit);
                    }
                });
            }, 1000 * 60 * 1);
        }
    }

    subscribeTick(stockCode) {

        if (this.hasListened2TickChange === undefined) {

            this.hasListened2TickChange = true;
            this.standardListen(this.serverEvent.subscribeTickReceived, (...args) => { this.handleTickChange(true, ...args); });
            this.standardListen(this.serverEvent.tickPriceChanged, (...args) => { this.handleTickChange(false, ...args); });
        }

        this.standardSend(this.systemEvent.subscribeTick, [stockCode], this.systemTrdEnum.tickType.tick);
        this.standardSend(this.systemEvent.subscribeTick, [stockCode], this.systemTrdEnum.tickType.orderQueue);
    }

    unsubscribeTick(stockCode) {

        if (this.units.some(x => x.stock.code == stockCode)) {
            return;
        }

        this.standardSend(this.systemEvent.unsubscribeTick, [stockCode], this.systemTrdEnum.tickType.tick);
        this.standardSend(this.systemEvent.unsubscribeTick, [stockCode], this.systemTrdEnum.tickType.orderQueue);
    }

    /**
     * 将价格格式化为适合的精度
     * @param {Number} price 
     */
    precisePrice(price) {
        return typeof price == 'number' ? price.toFixed(2) : price;
    }

    /**
     * @param {BoughtUnit} unit 
     */
    load(unit) {
        unit.loadMore();
    }

    /**
     * 处理TICK数据变化
     * @param {*} isReceipt 是否为订阅回执
     * @param {*} instrument 该TICK所属合约
     * @param {*} tickType 该TICK数据类型
     * @param {*} tick TICK数据本身
     */
    handleTickChange(isReceipt, instrument, tickType, tick) {

        if (tickType != this.systemTrdEnum.tickType.tick && tickType != this.systemTrdEnum.tickType.orderQueue) {
            return;
        }

        var targets = this.units.filter(x => x.stock.code == instrument);
        if (targets.length == 0) {
            return;
        }

        if (tickType == this.systemTrdEnum.tickType.tick) {

            targets.forEach(target => {

                let tickd = new TickData(tick);
                let buy1 = tickd.buys[0];
                let volume = buy1.hands;
                target.updateBulle(volume * 0.01, volume * buy1.price, null);
            });
        }
        else if (tickType == this.systemTrdEnum.tickType.orderQueue) {

            if (tick instanceof Array) {

                targets.forEach(target => {
                    target.updateQueue(tick);
                });
            }
        }
    }

    /**
     * @param {BoughtUnit} unit 
     */
    setAsCurrent(unit) {

        if (this.isFocused(unit)) {
            return;
        }

        this.states.focused = unit;
        this.log(`focus on an unit in bought list, stock/${unit.stock.code}/${unit.stock.name}`);
        this.trigger('unit-focused', this.title, unit.stock.code);
    }

    clearCurrent() {
        this.states.focused = null;
    }

    /**
     * @param {BoughtUnit} unit
     */
    isFocused(unit) {
        return unit === this.states.focused;
    }

    confirm(isRequired, message, callback) {

        if (isRequired) {
            
            this.interaction.showConfirm({

                title: '操作确认',
                message: message,
                confirmed: () => {
                    callback();
                },
            });
        }
        else {
            callback();
        }
    }

    /**
     * @param {BoughtUnit} unit
     */
    cancel(unit) {
        
        this.confirm(this.settings.prompt.mcancel, `${unit.stock.name}，撤销买入？`, () => {

            this.log(`to cancel strategy ordering on a bought unit, stock/${unit.stock.code}/${unit.stock.name}`);
            this.renderProcess.send(this.systemEvent.transmitMsg2TradingServer, Cm20FunctionCodes.request.cancel, { id: unit.taskId });
        });
    }

    /**
     * @param {BoughtUnit} unit
     */
    popupEntrust(unit) {
        this.popper.open(unit.taskId, unit.stock.code, unit.stock.name);
    }

    simplify(amount) {
        return this.helper.simplifyAmount(amount);
    }

    handleReconnect() {

        this.units.forEach(item => {

            this.log(`to resub tick in bought view while re-connected, stock/${item.stock.code}`);
            this.subscribeTick(item.stock.code);
        });
    }

    build($container) {

        super.build($container);
        this.createApp();
        this.renderProcess.on(this.systemEvent.tradingServerReestablished, () => { this.handleReconnect(); });
    }
};