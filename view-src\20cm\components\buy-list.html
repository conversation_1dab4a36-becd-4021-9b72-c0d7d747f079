<div class="buys-inter">

	<div v-for="(unit, unit_idx) in units" 
		:key="unit_idx"
		class="identity-buy-unit stock-unit typical-content"
		:class="isFocused(unit) ? 'focused' : ''"
		@click="setAsCurrent(unit)">

		<div class="title typical-header">
			<span>{{ unit.stock.name }} ({{ unit.stock.code }})</span>
			<label v-if="unit.isCreditStock" class="s-color-red s-pdl-10">融</label>
			<span class="s-pull-right">
				<label v-if="unit.isSupplemented" class="s-color-red s-pdr-10">补单监控</label>
				<el-button v-if="unit.isRunning" type="primary" size="small" @click="stop(unit)">停止</el-button>
				<template v-else>
					<el-button type="danger" size="small" @click="start(unit)" style="position: relative; top: -2px;">启动</el-button>
					<el-button type="info" size="small" @click.stop="remove(unit)" style="position: relative; top: -2px;">删除</el-button>
				</template>
				<button class="collapser-btn s-mgl-5 s-opacity-7 s-opacity-hover" @click="unit.expanded = !unit.expanded;" style="position: relative; top: -1px;">
					<i :class="unit.expanded ? 'el-icon-arrow-up' : 'el-icon-arrow-down'"></i>
				</button>
			</span>
		</div>

		<div class="bulletin themed-box" v-show="unit.expanded">

			<label class="prop-name">总卖量</label>
			<label class="prop-value">{{ typeof unit.bulletin.zml == 'number' ? thousands(unit.bulletin.zml) : '---' }}</label>
			<label class="prop-name">涨停预埋量</label>
			<label class="prop-value">{{ typeof unit.bulletin.ztymd == 'number' ? thousands(unit.bulletin.ztymd) : '---' }}</label>
			<label class="prop-name">封单笔数</label>
			<label class="prop-value">{{ typeof unit.bulletin.fdbs == 'number' ? thousands(unit.bulletin.fdbs) : '---' }}</label>
			<label class="prop-name" style="display: none;">封单均</label>
			<label class="prop-value" style="display: none;">{{ typeof unit.bulletin.fdj == 'number' ? thousands(unit.bulletin.fdj) : '---' }}</label>
			
			<el-tooltip placement="bottom-end">
				<div slot="content">
					<div v-for="item in unit.paramb" :key="item.label" style="line-height: 22px;">
						<label class="prop-name" style="display: inline-block; width: 100px; text-align: right;">{{ item.label }}：</label>
						<label v-if="item.isPercent" class="prop-value">{{ item.value || '---' }}</label>
						<label v-else class="prop-value">{{ typeof item.value == 'number' ? thousands(item.value) : '---' }}</label>
					</div>
				</div>
				<span class="s-pull-right themed-left-border s-pdl-10 s-pdr-10 shine-color s-hover-underline">
					<label class="prop-name">数据</label>
					<label class="prop-value">{{ typeof unit.paramb.bl.value == 'number' ? thousands(unit.paramb.bl.value) : '---' }}</label>
				</span>
			</el-tooltip>

		</div>

		<div class="unit-body" v-show="unit.expanded" style="position: relative; padding-bottom: 0;">
	
			<div class="ctr-row">
	
				<label class="shine-color">策略</label>
	
				<el-select
					v-model="unit.skey"
					@change="handleStrategyChange(unit, '1-1-01', unit.skey)"
					class="s-mgl-15"
					style="width: 175px;">
					<el-option-group v-for="group in strategyGroups" :key="group.name" :label="group.name">
						<el-option v-for="(stra, stra_idx) in group.strategies" :key="stra_idx" :value="stra.skey" :label="stra.name"></el-option>
					</el-option-group>
				</el-select>

				<el-button type="primary" size="small" @click="openFixedPriceUnit(unit)" class="s-pull-right" style="width: 60px;">定价买入</el-button>
	
			</div>
	
			<div class="ctr-row" style="padding: 2px 0px 0px 40px;">
	
				<span v-for="(dyna, dyna_idx) in unit.dynamics" class="ctr-dynamic" :class="dyna.applicable ? '' : 'disabled-ctr'">
					<label class="input-name s-pdr-5">{{ dyna.label }}</label>
					<el-input-number v-model="dyna.value"
									:disabled="!dyna.applicable"
									:min="dyna.min"
									:max="dyna.max"
									:step="dyna.step"
									:controls="false" 
									class="short-input" 
									@change="handleSomeChange(unit, '1-1-02:' + dyna.prop, dyna.value)"
									clearable></el-input-number>
					<label class="input-unit s-pdl-5">{{ showProperParamUnit(unit, dyna) }}</label>
				</span>
	
			</div>
	
			<div class="ctr-row">
	
				<label class="shine-color">撤单条件</label>

				<a @click="unit.innerExpanded = !unit.innerExpanded;">
					<i class="shine-color" :class="unit.innerExpanded ? 'el-icon-arrow-up' : 'el-icon-arrow-right'"></i>
				</a>

				<!-- <template v-if="unit.innerExpanded">
					<span
						class="themed-bg" 
						style="display: inline-block; height: 1px; width: 333px; position: relative; top: -2px; left: 5px;"></span>
				</template> -->

			</div>
	
			<div class="ctr-row" v-show="unit.innerExpanded" style="padding-top: 0;">
	
				<span 
					v-for="(thres, thres_idx) in unit.threses" 
					:key="thres_idx" 
					class="ctr-thres" 
					:class="'col-' + (thres_idx % 2 > 0 ? 'second' : 'first') + ' thres-option-' + thres.checkedProp"
					:style="{ width: (thres_idx == 0 ? 50 : thres_idx == 1 ? 50 : thres_idx == 2 ? 100 : 50) + '%' }">
	
					<el-checkbox 
						v-model="thres.isOn"
						@change="handleSomeChange(unit, '1-1-03:' + thres.checkedProp, thres.isOn)"
						class="choice-check"
						:class="(thres.members[0].label.length == 4 && thres_idx % 2 > 0 ? 'shorter' : '') + (thres.isOn ? '' : ' unchecked')">
						{{ thres.members[0].label }}</el-checkbox>

					<span v-for="(memb, memb_idx) in thres.members" :key="memb_idx">
						
						<el-input-number
							v-model="memb.value" 
							@change="handleSomeChange(unit, '1-1-04:' + thres.checkedProp + ':' + memb.prop, memb.value)"
							:min="memb.min"
							:max="memb.max"
							:step="memb.step"
							:controls="memb.hasButton"
							controls-position="right" 
							size="mini"
							class="medium-input s-mgl-10"></el-input-number>

						<label class="input-unit s-pdl-5">{{ memb.unit }}</label>

					</span>

				</span>
	
			</div>

			<div class="themed-bg" style="height: 1px; margin-top: 3px;"></div>
	
			<div class="ctr-row row-allocation s-ellipsis">

				<span class="allocations">

					<template v-if="accounts.length > 0">

						<span v-for="(acnt, acnt_idx) in accounts" :key="acnt_idx" class="each-aloc">
							{{ acnt.accountName }}:
							{{ allocate(unit, acnt) }}
							手
						</span>

					</template>

					<span v-else class="each-aloc">可用账号数 = 0</span>

				</span>

			</div>
	
			<div class="ctr-row">
				<label class="s-color-red">总可用：{{ typeof unit.zky == 'number' ? thousands(unit.zky) : '---' }} 万</label>
				<label class="s-color-red s-pdl-20">总可融：{{ typeof unit.zkr == 'number' ? thousands(unit.zkr) : '---' }} 万</label>
				<span class="s-pull-right" style="margin-top: -20px;">
					<label class="s-pdr-5">发单间隙</label>
					<el-input-number 
						placeholder="毫秒"
						v-model="unit.manual.fdjx"
						@change="handleSomeChange(unit, '1-1-07', unit.manual.fdjx)"
						:min="0" 
						:controls="false" 
						class="short-input" clearable></el-input-number>
				</span>
			</div>

			<div class="ctr-row" style="position: relative; padding-bottom: 0;">

				<div class="credit-option">
					<label class="s-pdr-5">优先融资</label>
					<el-checkbox :disabled="!unit.isCreditStock || !hasAnyCreditAccount" 
									v-model="unit.isCreditStock && unit.credit.creditBuy"
									@change="handleCreditChange(unit, '1-1-08', unit.credit.creditBuy)"></el-checkbox>
				</div>
	
				<span class="choice-box">
	
					<el-radio-group v-model="unit.position.percentage">
	
						<el-radio 
							v-for="(pct, pct_idx) in percentages"
							:key="pct_idx" 
							:label="pct.code"
							:class="'percent-choice-' + pct_idx"
							@change="handlePercentChange(unit, '1-1-09', unit.position.percentage)">{{ pct.mean }}</el-radio>
	
					</el-radio-group>

					<span class="abs-amount" :class="!isByAmount(unit) ? 'disabled-ctr' : ''">

						<el-input-number 
							v-model="unit.position.amount" 
							:disabled="!isByAmount(unit)" 
							@change="handleSomeChange(unit, '1-1-10', unit.position.amount)"
							:min="0"
							:step="0.01"
							:controls="false" 
							class="short-input" clearable></el-input-number>

						<label class="s-pdl-5">万</label>

					</span>
	
					<span class="user-position" :class="!isByCustomized(unit) ? 'disabled-ctr' : ''">

						<el-input-number 
							v-model="unit.position.customized" 
							:disabled="!isByCustomized(unit)" 
							@change="handleSomeChange(unit, '1-1-11', unit.position.customized)"
							:min="1" 
							:controls="false" 
							class="micro-input" clearable></el-input-number>

						<label class="s-pdl-5">仓</label>
					</span>
	
				</span>
	
			</div>

			<div class="row-user-price">
				<span class="s-pull-right" style="position: relative; top: 5px;">

					<label class="s-pdr-5">指定价格</label>

					<el-tooltip placement="bottom">

						<span slot="content">

							<span class="s-color-green">
								跌停 = 
								<a @click="setAsPrice(unit, unit.floor)" class="s-cp s-hover-underline">{{typeof unit.floor == 'number' ? unit.floor : '---' }}</a>
							</span>
							
							<span class="s-pdl-10">
								, 现价 = 
								<a @click="setAsPrice(unit, unit.latest)" class="s-cp s-hover-underline">{{ typeof unit.latest == 'number' ? unit.latest : '---' }}</a>
							</span>

							<span class="s-pdl-10 s-color-red">
								, 涨停 = 
								<a @click="setAsPrice(unit, unit.ceiling)" class="s-cp s-hover-underline">{{ typeof unit.ceiling == 'number' ? unit.ceiling : '---' }}</a>
							</span>

						</span>

						<el-input-number 
							v-model="unit.price"
							@change="handleSomeChange(unit, '1-1-12', unit.price)"
							:id="makePriceCtrId(unit)"
							:min="unit.floor" 
							:max="unit.ceiling"
							:step="0.01" 
							:controls="false" 
							class="short-input" clearable></el-input-number>

					</el-tooltip>
				</span>
			</div>

			<div class="row-protect">
				<span class="s-pull-right" style="position: relative; top: -8px;">
					<label class="s-pdr-5">分拆保护</label>
					<el-select v-model="unit.manual.protect" 
								@change="handleSomeChange(unit, '1-1-13', unit.manual.protect)"
								class="short-input">
						<el-option v-for="(item, item_idx) in protections" :key="item_idx" :value="item.code" :label="item.mean"></el-option>
					</el-select>
				</span>
			</div>

			<el-button type="danger" size="small" class="ctr-order-btn s-pull-right" @click="mbuy(unit)" style="width: 62px;">手动买</el-button>

		</div>

	</div>
</div>
<template v-if="units.length == 0">
	<div class="no-data-notice">
		<div class="displaying">
			<i class="el-icon-moon"></i> 请添加交易监控
		</div>
	</div>
</template>