const { BaseView } = require('../base-view');
const { FixedPriceGovener } = require('./fixed-price-governer');
const { NumberMixin } = require('../../../mixin/number');
const { TickData } = require('../../2021/model/message');
const {
    TaskStatus, 
    Entrance,
    StrategyParamNames,
    BuyUnit,
    Strategy, 
    StrategyItem, 
    DynamicParam,
    TaskObject, 
    Definition, 
    UserSetting, 
    AccountSimple,

} = require('./objects');

const { Cm20FunctionCodes } = require('../../../config/20cm');
const { repoInstrument } = require('../../../repository/instrument');
const { repo20Cm } = require('../../../repository/20cm');
const { BizHelper, OneInstrument } = require('../../../libs/helper-biz');

/**
 * @returns {Array<AccountSimple>}
 */
function makeAllocates() {
    return [];
}

/**
 * @returns {Array<BuyUnit>}
 */
function makeUnits() {
    return [];
}

/**
 * @returns {Array<Strategy>}
 */
function makeStrategies() {
    return [];
}

/**
 * @returns {Array<StrategyItem>}
 */
function makeStrategyItems() {
    return [];
}

module.exports = class BuyListView extends BaseView {

    constructor() {

        super('@20cm/components/buy-list', false, '买入监控');

        this.consts = {

            powering: 10000,

            /**
             * 最大可融资额度，向下折扣（确保额度使用安全性）
             */
            safeDiscount: 0.9,
        };

        /** 定价买入策略 */
        this.fixedPriceStrategies = Entrance.makeFixedPriceStrategies().map(x => x.code);

        /** 原始策略 */
        this.strategies = makeStrategies();
        /** 快捷策略 */
        this.specializeds = makeStrategyItems();
        /** 普通策略 */
        this.normals = makeStrategyItems();
        this.percentages = Entrance.makePercentages();
        this.protections = [

            new Definition(1, 1),
            new Definition(2, 2),
            new Definition(3, 3),
        ];

        this.accounts = makeAllocates();
        this.units = makeUnits();
        this.unitsMap = {};
        this.states = {

            focused: this.units.length > 0 ? this.units[0] : null,
        };

        this.fixedpop = new FixedPriceGovener(this);

        this.registerEvent('setting-updated', (settings) => { this.setAsSettings(settings); });
        this.registerEvent('add-units', (stocks) => { this.addUnits(stocks); });
        this.registerEvent('no-focused-unit', () => { this.clearCurrent(); });
        this.registerEvent('shortcut-key-pressed', (stroke) => { this.handleStrokePress(stroke); });
        this.registerEvent('account-change', (accounts) => { this.handleAccountChange(accounts); });
        this.registerEvent('task-created', (task) => { this.handleTaskCreated(task); });
        this.registerEvent('set-tasks', (tasks, isReply) => { this.group2HandleTasks(tasks, isReply); });
        this.registerEvent('start-monitor', () => { this.start2Monitor(); });
        this.registerEvent('get-into-continious-trade-time', () => { this.setAsContiniousTrade(); });
    }

    /**
     * @param {BuyUnit} unit 
     */
    chooseKey(unit) {
        return this.helper.isNotNone(unit.taskId) ? unit.taskId : unit.stock.code;
    }

    setAsContiniousTrade() {

        if (this.isContiniousRange == undefined) {
            
            this.isContiniousRange = true;
            this.units.forEach(unit => { unit.removeCentralThrs(); });
        }
    }

    start2Monitor() {

        var target = this.states.focused;
        if (!target || target.isRunning) {
            return;
        }
        
        this.log(`[Enter] is press to start the unit: task id/${target.taskId}, instrument/${target.stock.code}`);
        this.start(target);
    }

    /**
     * @param {UserSetting} settings 
     */
    setAsSettings(settings) {

        this.settings = settings;
        this.lazySet();
        this.loadStrategies();
        this.fixedpop.updateSettings(settings);
    }

    lazySet() {

        this.units.forEach(unit => {

            if (this.helper.isNone(unit.position.percentage)) {

                let { percentage, amount, customized } = this.settings.position;
                unit.updatePosition(percentage, amount, customized);
            }
        });
    }

    /**
     * @param {String} stroke 
     */
    handleStrokePress(stroke) {

        var target = this.states.focused;

        if (!target) {
            return;
        }
        else if (target && !target.expanded) {
            return;
        }

        var shortcut = this.settings.buyShortcuts.find(x => x.stroke == stroke.toUpperCase() || x.stroke == stroke.toLowerCase());
        if (!shortcut) {
            return;
        }

        let matched = this.specializeds.find(x => x.stroke == stroke);
        if (matched) {

            this.log(`buy list: pressed shortcut key/${stroke}, and selected strategy/${JSON.stringify(matched)}`);
            target.skey = matched.skey;
            this.handleStrategyChange(target, '1-1-14:' + stroke, target.skey);
        }
    }

    /**
     * @param {Array<AccountSimple} updates 
     */
    handleAccountChange(updates) {

        let total1 = updates.map(x => x.available).sum();
        let total2 = updates.map(x => x.enableCreditBuy).sum();
        this.accounts.refill(updates);
        this.units.forEach(item => {

            item.updateZky(total1);
            item.updateZkr(total2);
        });
    }

    loadStrategies() {

        var strategies = Entrance.makeBuyStrategies();
        var shortcuts = this.settings.buyShortcuts;
        this.strategies.refill(strategies);
        this.specializeds.clear();

        /**
         * 该种写法，规避因为发生过策略删除，导致旧的配置数据引起报错
         */
        shortcuts.forEach(x => {

            let matched = strategies.find(y => y.code == x.strategy);
            if (matched == undefined) {
                return;
            }

            let sitem = new StrategyItem({
                
                key: `${x.stroke}-${x.strategy}`,
                stroke: x.stroke,
                strategy: x.strategy, 
                name: `${x.stroke} - ${matched.mean}`,
            });

            this.specializeds.push(sitem);
        });

        this.normals.refill(strategies.filter(x => !shortcuts.some(y => y.strategy == x.code)).map(x => {

            return new StrategyItem({
                
                key: x.code,
                stroke: undefined,
                strategy: x.code,
                name: x.mean,
            });
        }));
    }

    createApp() {

        this.vapp = new Vue({

            el: this.$container.firstElementChild,
            data: {

                accounts: this.accounts,
                strategyGroups: [

                    { name: '快捷策略', strategies: this.specializeds },
                    { name: '普通策略', strategies: this.normals },
                ],

                percentages: this.percentages,
                protections: this.protections,
                units: this.units,
                states: this.states,
            },
            computed: {

                hasAnyCreditAccount: () => {
                    return this.hasAnyCreditAccount();
                }
            },
            mixins: [NumberMixin],
            methods: this.helper.fakeVueInsMethod(this, [

                this.makePriceCtrId,
                this.isFocused,
                this.setAsCurrent,
                this.setAsPrice,
                this.allocate,
                this.start,
                this.stop,
                this.mbuy,
                this.remove,
                this.isByAmount,
                this.isByCustomized,
                this.handleStrategyChange,
                this.handlePercentChange,
                this.handleSomeChange,
                this.handleCreditChange,
                this.showProperParamUnit,
                this.openFixedPriceUnit,
            ]),
        });
    }

    /**
     * @param {Array<OneInstrument>} stocks
     */
    addUnits(stocks) {

        if (!(stocks instanceof Array) || stocks.length == 0) {
            return;
        }
        
        stocks.forEach(item => {

            let { instrument, instrumentName, creditBuy } = item;

            /**
             * 人工添加时，不允许加入已经存在的股票
             */
            if (this.units.some(x => x.stock.code == instrument)) {

                this.interaction.showError(`${instrument}，股票已存在`);
                return;
            }

            var unit = new BuyUnit(false, instrument, instrumentName, null);
            unit.setCreditBuyFlag(creditBuy);

            if (this.isContiniousRange) {
                unit.removeCentralThrs();
            }

            if (this.settings) {

                let settings = this.settings;

                /**
                 * 更新持仓默认选择
                 */

                let { percentage, amount, customized } = settings.position;
                unit.updatePosition(percentage, amount, customized);
                unit.innerExpanded = !!settings.toggleShow;
                unit.credit.creditBuy = !!settings.credit.creditBuy && this.hasAnyCreditAccount();

                /**
                 * 更新撤单条件阈值
                 */

                unit.threses.forEach(thr => {

                    let matched = UserSetting.ReadThreadhold(settings.threshold, thr.checkedProp);
                    if (matched == undefined) {
                        return;
                    }

                    thr.isOn = !!matched.checked;
                    thr.members.forEach(memb => { memb.value = matched.values[memb.prop]; });
                });

                let { defaultSeq } = settings.buyOptions;
                let specias = this.specializeds;
                if (specias.length > 0 && typeof defaultSeq == 'number' && defaultSeq >= 1 && defaultSeq <= specias.length) {

                    unit.skey = specias[parseInt(defaultSeq) - 1].skey;
                    this.alignDynamicState(unit);
                }

                /**
                 * 更新补单委托量
                 */

                unit.supplement.checked = !!settings.supplement.checked;
                unit.supplement.value = settings.supplement.value || 0;
            }

            /**
             * 填充总可用
             */
            if (this.accounts.length > 0) {

                unit.updateZky(this.accounts.map(x => x.available).sum());
                unit.updateZkr(this.accounts.map(x => x.enableCreditBuy).sum());
            }

            this.units.unshift(unit);
            this.unitsMap[this.chooseKey(unit)] = unit;
            
            /**
             * 订阅标准行情
             */
            this.subscribeTick(unit.stock.code);
            this.requestLimitedPrice(unit);
            this.requestParamb(unit);
            this.log(`added new unit to buy list, stock/${instrument}/${instrumentName}`);
        });

        /**
         * 设置默认选中
         */

        if (this.units.length > 0) {
            this.setAsCurrent(this.units[0]);
        }
    }

    isFixedPriceStrategy(strategy) {
        return this.fixedPriceStrategies.some(x => x == strategy);
    }

    /**
     * @param {TaskObject} task
     */
    handleTaskCreated(task) {

        /**
         * 定价买入策略启动
         */

        if (this.isFixedPriceStrategy(task.boardStrategy.strategyType)) {

            this.fixedpop.handleTaskCreated(task);
            return;
        }

        let matched = this.units.find(x => x.taskId == task.id);
        if (!matched) {
            matched = this.units.find(x => x.stock.code == task.instrument && this.helper.isNone(x.taskId));
        }

        if (!matched) {

            console.error(task);
            return;
        }
        
        this.log(`a new unit in buy list has been started, with replied task id/${task.id}`);
        matched.taskId = task.id;
        delete this.unitsMap[matched.stock.code];
        this.unitsMap[this.chooseKey(matched)] = matched;
        matched.updateRunning(TaskObject.isRunning(task.strikeBoardStatus));
        matched.updateSupplemented(TaskObject.isSupplemented(task.strikeBoardStatus));
    }

    /**
     * @param {Array<TaskObject>} tasks
     * @param {Boolean} isReply
     */
    group2HandleTasks(tasks, isReply) {

        var fixeds = [];
        var normals = [];

        tasks.forEach(task => {

            if (this.isFixedPriceStrategy(task.boardStrategy.strategyType)) {
                fixeds.push(task);
            }
            else {
                normals.push(task);
            }
        });

        if (fixeds.length > 0) {

            fixeds.forEach((item, idx) => {
                setTimeout(() => {
                    this.fixedpop.handleTaskChange(item, isReply); 
                }, 1000 * idx); });
        }
        
        if (normals.length > 0) {
            this.handleTaskChange(normals, isReply);
        }
    }

    /**
     * @param {Array<TaskObject>} tasks
     * @param {Boolean} isReply
     */
    handleTaskChange(tasks, isReply) {
        
        tasks.forEach(task => {

            let status = task.strikeBoardStatus;

            if (TaskObject.isUnexpected(status) || TaskObject.isOrdered(status)) {

                this.units.remove(x => x instanceof BuyUnit && x.taskId == task.id);
                delete this.unitsMap[task.id];
                this.unsubscribeTick(task.instrument);
                this.log(`unit removed from buy list, task id/${task.id}, task status/${status}, stock/${task.instrument}/${task.instrumentName}`);

                /**
                 * todo21: 可能解决了缺陷：添加一个合约 》启动 》已买 》完成结束 》重新添加相同合约 》动态实时提交的参数为上一次的参数痕迹 
                 */
                if (this.states.focused && this.states.focused.taskId === task.id) {

                    this.clearCurrent();
                    this.trigger('unit-removed', task.instrument);
                }

                return;
            }
            else if (!TaskObject.isAlive(status)) {

                console.error('unexpected task status = ' + status);
                return;
            }

            let isNew = false;
            let unit = this.units.find(x => x.taskId == task.id);

            if (unit === undefined) {

                isNew = true;

                /**
                 * 作为一个新的监控
                 */
                unit = new BuyUnit(false, task.instrument, task.instrumentName, task.id);
                let stockInfo = BizHelper.pick(task.instrument);
                unit.setCreditBuyFlag(stockInfo.creditBuy);

                if (this.isContiniousRange) {
                    unit.removeCentralThrs();
                }
                
                if (this.settings) {
                    unit.innerExpanded = !!this.settings.toggleShow;
                }

                this.units.unshift(unit);
                this.unitsMap[this.chooseKey(unit)] = unit;
                this.subscribeTick(unit.stock.code);
                this.log(`unit added to buy list, task id/${task.id}, task status/${status}, stock/${task.instrument}/${task.instrumentName}`);
            }

            unit.updateRunning(TaskObject.isRunning(status));
            unit.updateSupplemented(TaskObject.isSupplemented(status));
            unit.updatePrice(task.orderPrice);

            let strategyId = task.boardStrategy.strategyType;
            let selected = this.specializeds.find(x => x.strategy == strategyId);
            if (!selected) {
                selected = this.normals.find(x => x.strategy == strategyId);
            }
            
            if (selected) {
                unit.skey = selected.skey;
            }
            
            unit.supplement.checked = task.supplementOpen;
            unit.supplement.value = task.supplementVolume;
            unit.manual.fdjx = task.splitInterval;
            unit.manual.protect = task.splitType;
            unit.credit.creditBuy = task.creditFlag == true;
            
            let ref2Position = unit.position;
            ref2Position.percentage = task.limitPositionType;

            if (task.cash > 0) {
                ref2Position.amount = Number((task.cash / this.consts.powering).toFixed(2));
            }

            if (task.positionPercent > 0) {
                ref2Position.customized = task.positionPercent;
            }

            let ccd = task.cancelCondition;
            unit.threses.forEach(thr => {

                thr.isOn = ccd[thr.checkedProp] || false;
                thr.members.forEach(memb => { memb.value = ccd[memb.prop]; });
            });

            /**
             * 填充总可用
             */
            if (this.accounts.length > 0) {

                unit.updateZky(this.accounts.map(x => x.available).sum());
                unit.updateZkr(this.accounts.map(x => x.enableCreditBuy).sum());
            }

            if (isNew) {

                this.alignDynamicState(unit, true);
                this.requestLimitedPrice(unit);
                this.requestParamb(unit);
            }

            unit.dynamics.forEach(item => {

                if (item.applicable) {

                    let efficiency = this.retrieveEffi(selected, item.prop);
                    item.value = this.helper.safeDevide(task.boardStrategy[item.prop], efficiency);
                }
            });
        });

        /**
         * 设置默认选中
         */

        if (!this.states.focused && this.units.length > 0) {
            this.setAsCurrent(this.units[0]);
        }
    }

    /**
     * @param {BuyUnit} unit 
     */
    async requestLimitedPrice(unit) {

        var resp = await repoInstrument.queryPrice(unit.stock.code);
        var { errorCode, errorMsg, data } = resp;
        var { assetType, preClosePrice, lastPrice, lowerLimitPrice, optionType, priceTick, strikePrice, upperLimitPrice, volumeMultiple } = data || {};

        if (errorCode == 0 && data && upperLimitPrice > 0) {

            unit.updateLimits(lowerLimitPrice, upperLimitPrice);
            unit.updateLatest(lastPrice);
        }
    }

    percentagize(rate) {
        return typeof rate == 'number' ? (rate * 100).toFixed(2) + '%' : rate;
    }

    /**
     * @param {BuyUnit} unit 
     */
    async requestParamb(unit) {

        var stock = unit.stock.code;
        var resp = await repo20Cm.queryParamb(stock);
        var { err, errMsg, data } = resp;
        var bizdata = data[stock];
        
        if (err != 0 || !this.helper.isJson(bizdata)) {

            for (let key in unit.paramb) {
                unit.paramb[key].value = null;
            }

            return;
        }

        var { 

            trading_day, 
            b_amount, 
            b_minute_amount,
            sealing_plate_rate, 
            disposable_sealing_plate_rate, 
            high_open_rate, 
            high_open_premium_rate, 
            avg_yield_rate,
            peak_yield_rate,
            avg_seal_plate_volume,
            board_volume,
        } = bizdata;

        var paramb = unit.paramb;
        paramb.fbgl.value = this.percentagize(sealing_plate_rate);
        paramb.ycxfbgl.value = this.percentagize(disposable_sealing_plate_rate);
        paramb.gkgl.value = this.percentagize(high_open_rate);
        paramb.cryjl.value = this.percentagize(high_open_premium_rate);
        paramb.jjsylj.value = this.percentagize(avg_yield_rate);
        paramb.zgsylj.value = this.percentagize(peak_yield_rate);
        paramb.zdfblj.value = avg_seal_plate_volume;
        paramb.bscjl.value = board_volume;
        paramb.bl.value = b_amount;

        if (this.hasStartedParambUpdate == undefined) {

            this.hasStartedParambUpdate = true;
            setInterval(() => {
                this.units.forEach(unit => {
                    if (typeof unit.paramb.bl.value != 'number') {
                        this.requestParamb(unit);
                    }
                }); 
            }, 1000 * 60 * 1);
        }
    }

    subscribeTick(stockCode) {

        if (this.hasListened2TickChange === undefined) {

            this.hasListened2TickChange = true;
            this.standardListen(this.serverEvent.subscribeTickReceived, (...args) => { this.handleTickChange(true, ...args); });
            this.standardListen(this.serverEvent.tickPriceChanged, (...args) => { this.handleTickChange(false, ...args); });
        }

        this.standardSend(this.systemEvent.subscribeTick, [stockCode], this.systemTrdEnum.tickType.tick);
        this.standardSend(this.systemEvent.subscribeTick, [stockCode], this.systemTrdEnum.tickType.orderQueue);
    }

    unsubscribeTick(stockCode) {

        if (this.units.some(x => x.stock.code == stockCode)) {
            return;
        }

        this.standardSend(this.systemEvent.unsubscribeTick, [stockCode], this.systemTrdEnum.tickType.tick);
        this.standardSend(this.systemEvent.unsubscribeTick, [stockCode], this.systemTrdEnum.tickType.orderQueue);
    }

    /**
     * 处理TICK数据变化
     * @param {*} isReceipt 是否为订阅回执
     * @param {*} instrument 该TICK所属合约
     * @param {*} tickType 该TICK数据类型
     * @param {*} tick TICK数据本身
     */
    handleTickChange(isReceipt, instrument, tickType, tick) {

        var isTick = tickType == this.systemTrdEnum.tickType.tick;
        var isYmd = tickType == this.systemTrdEnum.tickType.ztymd;
        var isQueue = tickType == this.systemTrdEnum.tickType.orderQueue;

        if (!isTick && !isYmd && !isQueue) {
            return;
        }

        var targets = this.units.filter(x => x.stock.code == instrument);
        if (targets.length == 0) {
            return;
        }

        if (isTick) {

            let tickd = new TickData(tick);
            targets.forEach(target => { target.updateLatest(tickd.latest); });
        }
        else if (isYmd) {

            let tacticQuote = tick[instrument];
            let { totalSell, limitBuy } = tacticQuote;
            let vzml = this.helper.safeDevide(totalSell || 0, 100);
            let vymd = this.helper.safeDevide(limitBuy || 0, 100);

            targets.forEach(target => {

                target.updateZml(vzml);
                target.updateYmd(vymd);
            });
        }
        else if (isQueue && tick instanceof Array) {
            
            let targets = this.units.filter(x => x.stock.code == instrument);
            if (targets.length > 0) {
                targets.forEach(target => { target.updateFdbs(tick.length); });
            }
        }
    }

    hasAnyCreditAccount() {
        return this.accounts.some(x => !!x.credit);
    }

    /**
     * @param {BuyUnit} unit 
     */
    setAsCurrent(unit) {

        if (this.isFocused(unit)) {
            return;
        }

        this.states.focused = unit;
        this.log(`focus on an unit in buy list, stock/${unit.stock.code}/${unit.stock.name}`);
        this.trigger('unit-focused', this.title, unit.stock.code);
    }

    clearCurrent() {
        this.states.focused = null;
    }

    /**
     * @param {BuyUnit} unit 
     */
    isFocused(unit) {
        return unit === this.states.focused;
    }

    /**
     * @param {BuyUnit} unit 
     */
    makePriceCtrId(unit) {
        return 'price-of-buy-unit-' + this.chooseKey(unit);
    }

    /**
     * @param {BuyUnit} unit 
     * @param {AccountSimple} acnt 
     */
    allocate(unit, acnt) {
       
        let canuse = acnt.available + (unit.isCreditStock && unit.credit.creditBuy ? acnt.enableCreditBuy : 0);
        let cando = unit.price > 0 && canuse > 0;

        if (!cando) {
            return '---';
        }

        /**
         * 该账号最终，实际占用资金
         */
        let taken = 0;
        let upos = unit.position;

        if (this.isByAmount(unit)) {

            let t1 = this.accounts.map(x => x.available).sum(x => x);
            let t2 = this.accounts.map(x => x.enableCreditBuy).sum(x => x) * this.consts.safeDiscount;
            let tcash = t1 + t2;
            let can = Math.min(tcash, upos.amount > 0 ? upos.amount * this.consts.powering : 0);
            taken = can * canuse / (tcash || 1);
        }
        else if (this.isByCustomized(unit)) {
            taken = canuse / (upos.customized >= 1 ? upos.customized : 1);
        }
        else {
            taken = canuse / (upos.percentage || 1);
        }

        return NumberMixin.methods.thousands(taken / unit.price * 0.01);
    }

    /**
     * @param {BuyUnit} unit 
     * @param {Number} price 
     */
    setAsPrice(unit, price) {
        
        if (typeof price == 'number') {

            this.log(`set buy-unit price to ${price}, task id/${unit.taskId}, stock/${unit.stock.code}/${unit.stock.name}`);
            unit.updatePrice(Number(price.toFixed(2)));
        }
    }

    /**
     * @param {BuyUnit} unit 
     */
    staticRemove(unit) {

        var units = this.units;
        var pos = units.indexOf(unit);
        if (pos < 0) {
            return;
        }

        units.splice(pos, 1);
        delete this.unitsMap[this.chooseKey(unit)];
        this.unsubscribeTick(unit.stock.code);

        if (!this.isFocused(unit)) {
            return;
        }

        this.trigger('unit-removed', unit.stock.code);

        if (units.length == 0) {
            this.clearCurrent();
        }
        else {
            this.setAsCurrent(units[Math.min(pos, units.length - 1)]);
        }
    }

    /**
     * @param {BuyUnit} unit 
     */
    remove(unit) {

        this.confirm(false, `${unit.stock.name}，删除监控？`, () => {

            let hasId = this.helper.isNotNone(unit.taskId);

            if (hasId) {

                this.log(`to stop running a buy, task id/${unit.taskId}, stock/${unit.stock.code}/${unit.stock.name}`);
                this.renderProcess.send(this.systemEvent.transmitMsg2TradingServer, Cm20FunctionCodes.request.delete, { id: unit.taskId });
            }
            else {

                this.log(`to delete a fresh new buy unit, stock/${unit.stock.code}/${unit.stock.name}`);
                this.staticRemove(unit);
            }
        });
    }

    /**
     * @param {StrategyItem} straItem 
     * @param {String} paramName 
     */
    retrieveEffi(straItem, paramName) {

        var strag = this.strategies.find(x => straItem && x.code == straItem.strategy);
        var efficiency = 1;

        if (strag) {

            let meta = strag.params.find(x => x.prop == paramName);
            efficiency = meta ? meta.coefficiency : 1;
        }

        return efficiency;
    }

    /**
     * @param {BuyUnit} unit 
     * @param {Boolean} isManual 
     */
    isCheckedOk(unit, isManual) {
        
        var message = null;
        var isProgram = !isManual;
        var ref2Position = unit.position;

        if (isProgram && this.helper.isNone(unit.skey)) {
            message = '请选择，策略';
        }
        else if (isProgram && unit.dynamics.some(x => x.applicable && typeof x.value != 'number')) {
            message = '策略参数缺失';
        }
        else if (isProgram && unit.threses.some(x => x.members.some(y => typeof y.value != 'number'))) {
            message = '撤单条件中，有参数值缺失';
        }
        else if (this.isByAmount(unit) && typeof ref2Position.amount != 'number') {
            message = '金额值，参数值缺失';
        }
        else if (this.isByCustomized(unit) && typeof ref2Position.customized != 'number') {
            message = '自定义仓位，参数值缺失';
        }
        else if (isProgram && typeof unit.manual.fdjx != 'number') {
            message = '发单间隙，参数值缺失';
        }
        else if (isProgram && this.helper.isNone(unit.manual.protect)) {
            message = '请选择，分拆保护';
        }
        else if (typeof unit.price != 'number' || unit.price <= 0) {
            message = '指定价格，参数值缺失';
        }

        if (message) {
            this.interaction.showError(message);
        }

        return !message;
    }

    /**
     * @param {BuyUnit} unit 
     */
    start(unit) {

        if (!this.isCheckedOk(unit, false)) {
            return;
        }

        this.confirm(false, `${unit.stock.name}，开始监控？`, () => {

            if (this.execStart(unit, false)) {
                this.interaction.showSuccess(`${unit.stock.name}，启动买入监控`);
            }
        });
    }

    /**
     * @param {BuyUnit} unit 
     * @param {Boolean} isHotChange 是否为运行状态下，对任务因子的修改
     * @param {Boolean} isMannualBuy 是否为人工买入
     */
    execStart(unit, isHotChange, isMannualBuy = false) {

        var byAmount = this.isByAmount(unit);
        var random = this.settings.random;
        var split = this.settings.spliting;
        
        var sdetail = {

            random: random.random,

            mainMin: random.mainMin,
            mainMax: random.mainMax,
            main: split.main,

            imbarkMin: random.imbarkMin,
            imbarkMax: random.imbarkMax,
            imbark: split.imbark,

            starMin: random.starMin,
            starMax: random.starMax,
            star: split.star,

            protect2: split.protect2,
            protect3: split.protect3,
        };

        var ccd = {};
        unit.threses.forEach(thr => {

            ccd[thr.checkedProp] = thr.isOn || false;
            thr.members.forEach(memb => { ccd[memb.prop] = memb.value; });
        });

        ccd['supplementTime'] = this.settings.supplement.time;

        var selected = this.getSelectedStrategy(unit);
        var strategyObj = {
            [StrategyParamNames.strategy]: selected ? selected.strategy : null,
        };

        unit.dynamics.forEach(item => {

            if (!item.applicable) {
                return;
            }

            let efficiency = this.retrieveEffi(selected, item.prop);
            strategyObj[item.prop] = this.helper.safeMul(item.value, efficiency);
        });

        var ref2Position = unit.position;
        var task = new TaskObject({

            id: unit.taskId || null,
            userId: this.userInfo.userId,
            userName: this.userInfo.userName,
            instrument: unit.stock.code,
            instrumentName: null,
            direction: this.systemTrdEnum.tradingDirection.buy.code,
            priceFollowType: 0,
            orderPrice: unit.price,
            strikeBoardStatus: TaskStatus.created,
            supplementVolume: unit.supplement.value,
            supplementOpen: unit.supplement.checked,
            splitInterval: unit.manual.fdjx,
            splitType: unit.manual.protect,
            splitDetail: sdetail,
            boardStrategy: strategyObj,
            cancelCondition: ccd,
            
            limitPositionType: ref2Position.percentage,
            cash: byAmount ? ref2Position.amount * this.consts.powering : 0,
            positionPercent: byAmount ? 0 : ref2Position.customized,
            creditFlag: unit.credit.creditBuy,
        });

        var rqcode = Cm20FunctionCodes.request;
        var cmdCode = isMannualBuy ? rqcode.mannualBuy: isHotChange ? rqcode.modify : rqcode.start;

        this.renderProcess.send(this.systemEvent.transmitMsg2TradingServer, cmdCode, task);
        this.log(`to start/modify a buying unit, manual mode/${isMannualBuy}, hot change/${isHotChange}, task = ${JSON.stringify(task)}`);
        return true;
    }

    /**
     * @param {BuyUnit} unit 
     */
    stop(unit) {

        this.confirm(false, `${unit.stock.name}，停止监控？`, () => {

            this.log(`to stop a buying unit, task id/${unit.taskId}, stock/${unit.stock.code}/${unit.stock.name}`);
            this.renderProcess.send(this.systemEvent.transmitMsg2TradingServer, Cm20FunctionCodes.request.stop, { id: unit.taskId });
        });
    }

    /**
     * @param {BuyUnit} unit 
     */
    mbuy(unit) {

        if (this.accounts.length == 0) {
            return this.interaction.showError('没有可用账号，用于买入');
        }
        else if (!this.isCheckedOk(unit, true)) {
            return;
        }

        var unfocusButton = () => {

            try {

                /**
                 * 规避获得焦点的 “手动买入” 按钮，继续补货回车按键
                 */
                var $priceInput = document.getElementById(this.makePriceCtrId(unit)).querySelector('input');
                $priceInput.focus();
                $priceInput.blur();
            }
            catch(ex) {
                console.error(ex);
            }
        };

        this.confirm(this.settings.prompt.mbuy, `${unit.stock.name}，手动买入？`, () => {

            unfocusButton();

            if (this.execStart(unit, false, true)) {
                this.interaction.showSuccess(`${unit.stock.name}，启动手动买入`);
            }
        },
        () => {            
            unfocusButton();
        });
    }

    /**
     * @param {BuyUnit} unit 
     * @param {Number} total 账号总可用资金
     * @param {AccountSimple} account 
     * @param {Number} price 买入价格
     */
    trimVolume(unit, total, account, price) {
        
        var available = account.available;
        if (typeof available != 'number' || available <= 0) {
            return 0;
        }

        var ref2Position = unit.position;
        var canUse = 0;

        if (this.isByAmount(unit)) {
            canUse = (available / total) * ref2Position.amount * this.consts.powering;
        }
        else if (this.isByCustomized(unit)) {
            canUse = available / ref2Position.customized;
        }
        else {
            canUse = available / ref2Position.percentage;
        }

        if (canUse > 0) {

            /**
             * 除去千3的手续费
             */
            canUse -= (3 / 1000);
        }

        return Math.floor(canUse / price / 100) * 100;
    }

    /**
     * @param {BuyUnit} unit 
     */
    execBuy(unit) {
        
        var dict = this.systemTrdEnum;
        var price = unit.price;
        var orders = [];
        var accountNameMap = {};
        var totalAvailable = this.accounts.map(x => x.available).sum();

        this.accounts.forEach(account => {

            let volume = this.trimVolume(unit, totalAvailable, account, price);
            if (volume == 0) {
                return;
            }

            accountNameMap[account.accountId] = account.accountName;
            orders.push({

                strategyId: account.fundId,
                accountId: account.accountId,
                userId: this.userInfo.userId,
                price: price,
                volume: volume,
                instrument: unit.stock.code,
                priceType: dict.pricingType.fixedPrice.code,
                bsFlag: dict.tradingDirection.buy.code,
                businessFlag: 0,
                positionEffect: 0,
                customId: '20cm-buy-' + this.helper.makeToken(),
                orderTime: null,
                hedgeFlag: dict.hedgeFlag.Speculate.code,
            });
        });

        if (orders.length == 0 && this.accounts.length > 0) {
            return this.interaction.showError(`当前账号数 = ${this.accounts.length}，没有任何账号达到买入100股最低限额。`);
        }

        var mentions = [

            ['股票', unit.stock.name],
            ['方向', '买入', 's-color-red'],
            ['价格', price],
            ['账号摊派', orders.map(x => `${accountNameMap[x.accountId]}/${x.volume}`).join('，')],
        ];

        var message = mentions.map(item => `<div><span>${item[0]}：</span><span class="${item[2] || ''}">${item[1]}</span></div>`).join('');
        this.confirm(this.settings.prompt.mbuy, message, () => {

            this.log(`manual buy orders are prepared, task id/${unit.taskId}, orders = ${JSON.stringify(orders)}`);
            this.sendOut(orders);
            this.interaction.showSuccess('订单已发送，数量 = ' + orders.length);
        });
    }

    /**
     * @param {Array} orders 
     */
    sendOut(orders) {

        var send = (ord) => {
            this.renderProcess.send(this.systemEvent.transmitMsg2TradingServer, this.serverFunction.sendOrder, ord);
        }

        if (!this.settings) {

            orders.forEach(ord => {
                send(ord); 
            });
        }
        else {
            
            var { main, imbark, star } = this.settings.spliting;
            main *= 100;
            imbark *= 100;
            star *= 100;

            orders.forEach(ord => {
                
                let instrument = ord.instrument;
                let volume = ord.volume;
                let max = instrument.indexOf('.68') > 0 ? star : instrument.indexOf('.30') ? imbark : main;

                if (volume <= max) {
                    send(ord);
                }
                else {

                    let left = volume;
                    let rounds = [];
                    while(left > max) {

                        rounds.push(max);
                        left -= max;
                    }
                    if (left > 0) {
                        rounds.push(left);
                    }

                    rounds.forEach(vol => {

                        let each = this.helper.deepClone(ord);
                        each.volume = vol;
                        each.customId = '20cm-buy-' + this.helper.makeToken();
                        send(each);
                    });
                }
            });
        }
    }

    confirm(isRequired, message, confirmed_callback, canceled_callback) {

        if (isRequired) {
            
            this.interaction.showConfirm({

                title: '操作确认',
                message: message,
                confirmed: () => {
                    confirmed_callback();
                },
                canceled: () => {
                    canceled_callback();
                },
            });
        }
        else {
            confirmed_callback();
        }
    }

    /**
     * @param {BuyUnit} unit 
     */
    getSelectedPosition(unit) {
        return this.percentages.find(x => x.code == unit.position.percentage);
    }

    /**
     * @param {BuyUnit} unit 
     */
    isByAmount(unit) {

        let selected = this.getSelectedPosition(unit);
        return selected && selected.isByAmount;
    }

    /**
     * @param {BuyUnit} unit 
     */
    isByCustomized(unit) {

        let selected = this.getSelectedPosition(unit);
        return selected && selected.isByCustomized;
    }

    /**
     * @param {BuyUnit} unit 
     */
    getSelectedStrategy(unit) {

        var target = this.specializeds.find(x => x.skey == unit.skey);
        if (!target) {
            target = this.normals.find(x => x.skey == unit.skey);
        }

        return target;
    }

    /**
     * @param {BuyUnit} unit 
     */
    openFixedPriceUnit(unit) {

        var stockInfo = BizHelper.pick(unit.stock.code);
        if (stockInfo) {
            this.fixedpop.open(stockInfo);
        }
    }

    /**
     * @param {BuyUnit} unit 
     * @param {DynamicParam} dynamic 
     */
    showProperParamUnit(unit, dynamic) {

        var selected = this.getSelectedStrategy(unit);
        if (!selected) {
            return dynamic.unit;
        }

        var strategy = this.strategies.find(x => x.code == selected.strategy);
        var paramInfo = strategy.params.find(x => x.prop == dynamic.prop);
        return paramInfo ? paramInfo.unit : dynamic.unit;
    }

    /**
     * @param {BuyUnit} unit 
     */
    handleStrategyChange(unit, trigger, value) {

        this.alignDynamicState(unit);
        this.handleSomeChange(unit, trigger, value);
    }

    /**
     * @param {BuyUnit} unit 
     * @param {Boolean} isByServerSideChange 
     */
    alignDynamicState(unit, isByServerSideChange) {

        var selected = this.getSelectedStrategy(unit);
        var hasSelected = !!selected;
        var strategy = hasSelected ? this.strategies.find(x => x.code == selected.strategy) : undefined;
        var shortcut = hasSelected ? this.settings.buyShortcuts.find(x => x.strategy == selected.strategy && x.stroke == selected.stroke) : undefined;

        unit.dynamics.forEach(dnm => {

            let metaInfo = hasSelected ? strategy.params.find(x => x.prop == dnm.prop) : undefined;

            if (metaInfo) {

                dnm.applicable = true;
                dnm.max = metaInfo.max;
                dnm.min = metaInfo.min;
                dnm.step = metaInfo.step;
                dnm.value = null;

                if (!shortcut) {
                    return;
                }

                let propVal = shortcut.data[dnm.prop];
                if (typeof propVal != 'number') {
                    return;
                }

                if (isByServerSideChange) {
                    
                    let efficiency = this.retrieveEffi(selected, dnm.prop);
                    dnm.value = this.helper.safeDevide(propVal, efficiency);
                }
                else {
                    dnm.value = propVal;
                }
            }
            else {

                dnm.applicable = false;
                dnm.max = 999999999;
                dnm.min = 0;
                dnm.step = 1;
                dnm.value = null;
            }
        });
    }

    /**
     * @param {BuyUnit} unit 
     */
    handlePercentChange(unit, trigger, value) {
        this.handleSomeChange(unit, trigger, value);
    }

    /**
     * @param {BuyUnit} unit 
     */
    handleSomeChange(unit, trigger, value) {
        
        if (!unit.isRunning) {
            return;
        }

        this.log(`manual change in buy unit: ${unit.taskId}/${unit.stock.code}/${trigger}/${value}`);

        if (this.execStart(unit, true)) {

            this.log(`submit the hot change of a buy unit`);
            this.interaction.showSuccess(`${unit.stock.name}，监控参数变动，已提交。`);
        }
    }

    /**
     * @param {BuyUnit} unit 
     */
    handleCreditChange(unit, trigger, value) {
        this.handleSomeChange(unit, trigger, value);
    }

    handleReconnect() {

        this.units.forEach(item => {

            this.log(`to resub tick in buy view while re-connected, stock/${item.stock.code}`);
            this.subscribeTick(item.stock.code);
        });
    }

    build($container) {

        super.build($container);
        this.createApp();
        this.renderProcess.on(this.systemEvent.tradingServerReestablished, () => { this.handleReconnect(); });
    }
};