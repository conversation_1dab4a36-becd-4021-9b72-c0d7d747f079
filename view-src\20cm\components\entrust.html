<div class="s-full-height">
	<div class="entrust-options">
		<el-checkbox v-model="toptions.checked" @change="handleTOptionChange">仅显示可撤</el-checkbox>
	</div>
	<div class="entrust-records">
		<table>
			<tr>
				<th label="股票代码" 
					min-width="100" 
					prop="instrument" sortable searchable overflowt></th>

				<th label="股票名称" 
					min-width="80" 
					prop="instrumentName" sortable searchable overflowt></th>

				<th label="账号" 
					min-width="80" 
					prop="accountName" sortable searchable overflowt></th>

				<th label="委托数量" 
					min-width="80" 
					prop="volumeOriginal" 
					align="right" sortable overflowt thousands-int></th>

				<th label="报单编号" 
					min-width="130" 
					prop="exchangeOrderId" sortable searchable overflowt></th>

				<th label="委托价格" 
					min-width="80" 
					prop="orderPrice" 
					align="right" sortable overflowt ></th>

				<th label="方向" 
					min-width="70" 
					prop="direction" 
					align="right" 
					formatter="formatDirection" 
					export-formatter="formatDirectionText" sortable overflowt></th>

				<th label="交易方式" 
					min-width="100" 
					prop="businessFlag"
					formatter="formatBusinessFlag" 
					export-formatter="formatBusinessFlag" sortable></th>

				<th label="状态" 
					min-width="70" 
					prop="orderStatus" 
					watch="orderStatus,errorMsg" 
					align="right"
					watch="orderStatus, errorMsg"
					formatter="formatOrderStatus"
					export-formatter="formatOrderStatusText" sortable overflowt></th>

				<th label="成交" 
					min-width="80" 
					prop="tradedVolume" 
					align="right" sortable overflowt thousands-int></th>

				<th label="委托时间"
					fixed-width="70" 
					prop="orderTime"
					formatter="formatTime" sortable></th>

				<th label="成交时间"
					fixed-width="70" 
					prop="tradeTime"
					formatter="formatTime" sortable></th>

				<th label="操作"
					fixed="right"
					prop="isCompleted"
					fixed-width="60"
					formatter="formatActions"
					exportable="false">

				</th>

			</tr>
		</table>
	</div>

	<div class="stock-tabs">

		<template v-if="stocks.length > 0">

			<a 
				v-for="(item, item_idx) in stocks"
				:key="item_idx" 
				@click="cancel(item)"
				:style="{ width: 100 / stocks.length + '%' }"
				:class="item.direction > 0 ? 'buy' : 'sell'"
				class="tab-item s-ellipsis s-opacity-8 s-opacity-hover">{{ item.name }}</a>

		</template>

		<!-- <template v-else>
			<div class="s-center s-full-height">无可撤委托</div>
		</template> -->

	</div>

</div>