const { BaseView } = require('../base-view');
const { SmartTable } = require('../../../libs/table/smart-table');
const { Order } = require('../../../model/order');
const { ModelConverter } = require('../../../model/model-converter');
const { repoOrder } = require('../../../repository/order');
const { ColumnCommonFunc } = require('../../../libs/table/column-common-func');
const { UserSetting } = require('./objects');

class StockTab {

    constructor(code, name, direction) {

        this.code = code;
        this.name = name;
        this.direction = direction;
    }
}

/**
 * @returns {Array<StockTab}
 */
function makeTabs() {
    return [];
}

/**
 * 一个新的订单，首次发生成交（买入方向）时，需要进行提示（仅提示1次），该map存储数据为：订单id/true，标识为已提示
 */
const GNotifyingMap = {};

module.exports = class EntrustView extends BaseView {

    constructor() {

        super('@20cm/components/entrust', false, '委托列表');
        this.registerEvent('set-max-height', this.setMaxHeight.bind(this));
        this.registerEvent('setting-updated', (settings) => { this.setAsSettings(settings); });
        this.registerEvent('ask-summarized-sells', () => { this.summarizeAndReportSell(); });
        this.registerEvent('ask-summarized-buys', () => { this.summarizeAndReportBuy(); });
        this.registerEvent('ask-2-cancel-all-sells', (code, name) => { this.cancelSells(code, name); });
        this.registerEvent('auto-fit', () => { this.tableObj.fitColumnWidth(); });

        /**
         * 单票多订单，快速撤单TAB按钮
         */
        this.stockTabs = makeTabs();
    }

    get notifyingMap() {
        return GNotifyingMap;
    }

    setMaxHeight(height) {

        if (this.tableObj) {
            this.tableObj.setMaxHeight(height - 68);
        }
    }

    /**
     * @param {UserSetting} settings 
     */
    setAsSettings(settings) {
        this.settings = settings;
    }

    /**
     * @param {Order} record 
     */
    identify(record) {
        return record.id;
    }

    /**
     * @returns {Array<Order}
     */
    typedAsOrders(records) {
        return records;
    }

    createTable() {
        
        this.tableObj = new SmartTable(this.$table, this.identify, this, {

            tableName: 'smt-20entr',
            displayName: this.title,
        });

        this.tableObj.setPageSize(99999);
        this.tableObj.setMaxHeight(200);
    }

    createOptionsApp() {
        
        this.toptions = { checked: true };
        this.optionApp = new Vue({

            el: this.$options,
            data: {
                toptions: this.toptions,
            },
            methods: this.helper.fakeVueInsMethod(this, [this.handleTOptionChange]),
        });
    }

    /**
     * @param {Order} record
     */
    testRecords(record) {
        return !this.toptions.checked || this.toptions.checked && !record.isCompleted;
    }

    handleTOptionChange() {
        this.tableObj.customFilter((record) => { return this.testRecords(record); });
    }

    createTabsApp() {
        
        this.tabApp = new Vue({

            el: this.$tabs,
            data: {
                stocks: this.stockTabs,
            },
            methods: this.helper.fakeVueInsMethod(this, [this.cancel]),
        });
    }

    confirm(isRequired, message, callback) {

        if (isRequired) {
            
            this.interaction.showConfirm({

                title: '操作确认',
                message: message,
                confirmed: () => {
                    callback();
                },
            });
        }
        else {
            callback();
        }
    }

    cancelSells(code, name) {

        var tab = this.stockTabs.find(x => x.code == code && x.direction < 0);
        if (tab) {
            this.cancel(tab);
        }
        else {
            this.interaction.showMessage(`${name}/${code}，没有进行中的卖单`);
        }
    }

    /**
     * @param {StockTab} tab 
     */
    cancel(tab) {

        this.confirm(this.settings.prompt.mcancel, `${tab.name}，撤销${tab.direction > 0 ? '买入' : '卖出'}？`, () => {

            this.log(`to cancel orders by clicking a tab/${JSON.stringify(tab)}`);
            this.exeCancel(tab);
        });
    }

    /**
     * @param {StockTab} tab 
     */
    exeCancel(tab) {

        var orders = this.typedAsOrders(this.tableObj.extractAllRecords());
        var matches = orders.filter(x => x.instrument == tab.code && x.direction == tab.direction && !x.isCompleted);
        if (matches.length == 0) {

            this.log(`no matched orders for tab/${JSON.stringify(tab)}`);
            this.interaction.showError(`${tab.name}（${tab.direction > 0 ? '买入' : '卖出'}），委托数据可能已过期。`);
            return;
        }

        this.log(`to cancel all matched orders for tab/${JSON.stringify(tab)}, orders = ${JSON.stringify(orders)}`);
        matches.forEach(item => {
            this.renderProcess.send(this.systemEvent.transmitMsg2TradingServer, this.serverFunction.cancelOrder, { orderId: item.id });
        });

        this.interaction.showSuccess(`${tab.name}，撤单请求已发出，数量 = ${matches.length}`);
    }

    /**
     * 单一撤单
     * @param {Order} order 
     */
    cancelSingle(order) {

        this.confirm(this.settings.prompt.mcancel, '是否撤销该委托？', () => {

            this.log(`to cancel an order = ${JSON.stringify(order)}`);
            this.renderProcess.send(this.systemEvent.transmitMsg2TradingServer, this.serverFunction.cancelOrder, { orderId: order.id });
            this.interaction.showSuccess(`${order.instrumentName}，撤单请求已发出`);
        });
    }

    /**
     * @param {Order} record
     */
    formatActions(record) {
        return record.isCompleted ? '' : '<a class="s-cp s-hover-underline shine-color" event.onclick="cancelSingle">撤单</a>';
    }

    async requestOrders() {

        var resp = await repoOrder.batchMemQuery({ trade_user_id: this.userInfo.userId });
        if (resp.errorCode != 0) {

            this.interaction.showError(`订单查询错误：${resp.errorCode}/${resp.errorMsg}`);
            return;
        }

        var records = resp.data;
        /** 首行数据，为标题栏 */
        var titles = records.shift();
        var orders = ModelConverter.formalizeOrders(titles, records);
        this.tableObj.refill(orders);
        this.handleTOptionChange();
        this.aggregateTabs(orders.filter(x => !x.isCompleted));
        this.summarizeAndReportSell();
        this.mapNotifieds(orders);
    }
    
    /**
     * @param {Array<Order>} orders 
     */
    aggregateTabs(orders) {

        var map = {};
        var tabs = [];

        orders.forEach(ord => {

            let key = `${ord.instrument}/${ord.direction}`;

            if (map[key] === undefined) {

                map[key] = true;
                tabs.push(new StockTab(ord.instrument, ord.instrumentName, ord.direction));
            }
        });

        this.stockTabs.refill(tabs);
    }

    /**
     * @param {Array<Order>} orders
     */
    mapNotifieds(orders) {

        var stses = this.systemEnum.orderStatus;
        orders.forEach(order => {

            let tradedAll = order.orderStatus == stses.traded.code;
            let tradedPartial = order.orderStatus == stses.partialTraded.code;

            if (order.parentOrderId && (tradedAll || tradedPartial)) {
                this.notifyingMap[order.id] = true;
            }
        });
    }

    /**
     * @param {Order} order
     */
    ask2Notify(order) {

        var stses = this.systemEnum.orderStatus;
        var dirs = this.systemTrdEnum.tradingDirection;
        var tradedAll = order.orderStatus == stses.traded.code;
        var tradedPartial = order.orderStatus == stses.partialTraded.code;
        var isBuy = order.direction == dirs.buy.code;
        var orderId = order.id;
        var pid = order.parentOrderId;
        var isRequired = pid && (tradedAll || tradedPartial) && this.notifyingMap[pid] === undefined;

        if (!isRequired) {
            return;
        }

        this.notifyingMap[pid] = true;
        let directionLabel = isBuy ? '买入' : '卖出';
        this.interaction.notify({

            title: `${directionLabel}提示`,
            type: 'success',
            position: 'bottom-right',
            message: `${order.instrument}/${order.instrumentName}，已${isBuy ? '买入' : '卖出'}${order.tradedVolume}`,
        });

        if (this.settings) {

            let rt = this.settings.rington;
            let rington = isBuy ? rt.bought : rt.sold;
            let crington = rt.customized ? (isBuy ? rt.customized.bought : rt.customized.sold) : undefined;
            this.play(rington, crington);
        }
    }

    /**
     * @param {*} struc
     */
    handleOrderChange(struc) {

        var order = new Order(struc);
        var is4Me = order.userId == this.userInfo.userId;
        var isAdjustPos = order.adjustFlag;

        if (!is4Me || isAdjustPos) {
            return;
        }

        this.log(`received an order change notify: ${JSON.stringify(struc)}`);
        this.tableObj.putRow(order);

        if (this.toptions.checked && order.isCompleted) {
            this.handleTOptionChange();
        }

        var orders = this.typedAsOrders(this.tableObj.extractAllRecords());
        this.aggregateTabs(orders.filter(x => !x.isCompleted));
        this.summarizeAndReportSell();
        this.ask2Notify(order);
    }

    summarizeEntrusts(otherDirection, isByTaskId) {

        var map = {};
        var ors = this.systemEnum.orderStatus;
        var orders = this.typedAsOrders(this.tableObj.extractAllRecords());
        
        orders.forEach(item => {

            if (item.direction == otherDirection 
                || item.orderStatus == ors.canceled.code 
                || item.orderStatus == ors.invalid.code) {
                return;
            }
            
            let identifier = isByTaskId ? item.parentOrderId : item.instrument;
            let matched = map[identifier];
            if (matched === undefined) {
                matched = map[identifier] = { traded: 0, original: 0 };
            }

            matched.traded += item.tradedVolume;
            matched.original += item.volumeOriginal;
        });

        return map;
    }

    summarizeAndReportSell() {

        var map = this.summarizeEntrusts(this.systemTrdEnum.tradingDirection.buy.code, false);
        return this.trigger('summarized-sells', map);
    }

    summarizeAndReportBuy() {

        var map = this.summarizeEntrusts(this.systemTrdEnum.tradingDirection.sell.code, true);
        return this.trigger('summarized-buys', map);
    }

    listen2Change() {
        this.standardListen(this.serverEvent.orderChanged, this.handleOrderChange.bind(this));
    }

    subChange() {
        this.standardSend(this.systemEvent.subscribeAccountChange, null, [this.serverEvent.orderChanged]);
    }

    unsubChange() {
        this.standardSend(this.systemEvent.unsubscribeAccountChange, null, [this.serverEvent.orderChanged]);
    }

    dispose() {
        this.unsubChange();
    }

    refresh() {
        
        this.interaction.showSuccess('刷新请求已发出');
        this.requestOrders();
    }

    exportSome() {

        var date = new Date().format('yyyyMMdd');
        var time = new Date().format('hhmmss');
        this.tableObj.exportAllRecords(`${this.title}-${this.userInfo.userName}-${date}-${time}`);
    }

    build($container) {

        super.build($container);
        this.$table = this.$container.querySelector('.entrust-records');
        this.$options = this.$container.querySelector('.entrust-options');
        this.$tabs = this.$container.querySelector('.stock-tabs');
        this.helper.extend(this, ColumnCommonFunc);
        this.createTable();
        this.createOptionsApp();
        this.createTabsApp();
        this.listen2Change();
        this.subChange();
        this.requestOrders();
        this.renderProcess.on(this.systemEvent.tradingServerReestablished, () => { this.subChange(); });
    }
};