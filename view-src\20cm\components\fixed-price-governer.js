const { BrowserWindow } = require('@electron/remote');
const BuyListView = require('./buy-list');
const { TaskObject, TaskStatus } = require('./objects');
const { BizHelper, OneInstrument } = require('../../../libs/helper-biz');

class WindowInfo {

    constructor({ windowId, taskId, instrument, instrumentName }) {

        this.windowId = windowId;
        this.taskId = taskId;
        this.instrument = instrument;
        this.instrumentName = instrumentName;
    }

    setAsTaskId(taskId) {
        this.taskId = taskId;
    }
}

class FixedPriceGovener {

    get settings() {
        return this.sourceView.settings;
    }

    /**
     * @param {BuyListView} sourceView 
     */
    constructor(sourceView) {

        this.renderProcess = sourceView.renderProcess;
        this.helper = sourceView.helper;
        this.systemEvent = sourceView.systemEvent;
        this.interaction = sourceView.interaction;
        sourceView.registerEvent('summarized-buys', (map) => { this.distributeSummarizedBuys(map); });
        
        this.sourceView = sourceView;
        
        /**
         * 买入交易单元，打开定价买入窗口，映射表
         * key：instrument；
         * value：窗口对象引用
         */
        this.mapper = {};

        /**
         * 窗口位置信息
         * key：instrument；
         * value：窗口位置信息
         */
        this.slocationMap = {};
    }

    log(message) {
        this.sourceView.log(message);
    }

    /**
     * read/write a window's location & size info
     * @param {*} instrument 
     * @returns {{ x, y, width, height }} 
     */
    _locate(instrument, x, y, width, height) {
        
        if (typeof x == 'number') {
            this.slocationMap[instrument] = { x, y, width, height };
        }
        else {
            return this.slocationMap[instrument];
        }
    }

    /**
     * @param {*} identifier
     * @returns {WindowInfo}
     */
    _seek(identifier) {
        return this.mapper[identifier];
    }

    /**
     * @param {*} identifier
     * @param {WindowInfo} winInfo 
     */
    _tie(identifier, winInfo) {
        this.mapper[identifier] = winInfo;
    }

    /**
     * @param {*} identifier
     */
    _untie(identifier) {
        delete this.mapper[identifier];
    }

    /**
     * @param {TaskObject} task
     */
    handleTaskCreated(task) {

        var matched = this._seek(task.instrument);
        if (matched == undefined) {

            console.error('a fixed-price buy task is created, but the window has disappeared', task);
            return;
        }
        
        this.log(`a fixed-price task has been started, with replied task id/${task.id}`);

        /**
         * 启动成功后，写入task id到对应的窗口信息
         */
        matched.setAsTaskId(task.id);

        var winObj = BrowserWindow.fromId(matched.windowId);
        if (winObj && !winObj.isDestroyed()) {
            winObj.webContents.send('task-started', task);
        }
    }

    isTaskFinished(status) {
        return status == TaskStatus.finished;
    }

    isTaskDeleted(status) {
        return status == TaskStatus.deleted;
    }

    /**
     * @param {TaskObject} task
     * @param {Boolean} isReply
     */
    handleTaskChange(task, isReply) {

        var { instrument, instrumentName, strikeBoardStatus } = task;

        if (this.isTaskDeleted(strikeBoardStatus) || this.isTaskFinished(strikeBoardStatus)) {

            this.closeTaskWin(instrument);
            return;
        }
        
        var matched = this._seek(instrument);
        if (matched) {

            matched.setAsTaskId(task.id);
            this.updateTask(matched.windowId, task);
        }
        else {

            /**
             * 软件启动时，为线上已有的策略，创建对应窗口
             */

            var stockInfo = BizHelper.pick(instrument);
            this.open(stockInfo, (windowId) => {

                let matched2 = this._seek(instrument);
                matched2.setAsTaskId(task.id);

                /**
                 * 为即时创建的窗口，更新任务具体数据
                 */

                this.updateTask(windowId, task);
            });
        }
    }

    updateTask(windowId, task) {

        var winObj = BrowserWindow.fromId(windowId);
        if (winObj && !winObj.isDestroyed()) {
            winObj.webContents.send('task-changed', task);
        }
    }

    ask4SummarizedBuys() {
        this.sourceView.trigger('ask-summarized-buys');
    }

    distributeSummarizedBuys(map) {

        if (this.lastMap && this.lastMap == JSON.stringify(map)) {
            return;
        }

        /**
         * 上一次的最新数据（用以辅助判断数据是否产生过变化）
         */
        this.lastMap = JSON.stringify(map);

        /**
         * 遍历通知所有窗口
         */

        for (let identifier in this.mapper) {

            let winfo = this._seek(identifier);
            let { windowId, instrument, taskId } = winfo;

            let winObj = BrowserWindow.fromId(windowId);
            if (!winObj || winObj.isDestroyed()) {
                continue;
            }

            let matched = map[taskId];
            if (matched == undefined) {
                matched = { traded: 0, original: 0 };
            }

            winObj.webContents.send('summarized-buys', matched);
        }
    }

    /**
     * @param {OneInstrument} stockInfo 
     */
    setWinProperties(stockInfo, windowId, windowCreationCallback) {

        var { instrument, instrumentName } = stockInfo;
        this._tie(instrument, new WindowInfo({ windowId, instrument, instrumentName }));
        var winRef = BrowserWindow.fromId(windowId);

        winRef.webContents.on('is-ready', () => {
            
            let isOpenExisting = typeof windowCreationCallback == 'function';
            let supportCreditTrading = this.sourceView.settings && !!this.sourceView.settings.credit.creditBuy && this.sourceView.hasAnyCreditAccount();
            winRef.webContents.send('set-as-instrument', supportCreditTrading, stockInfo, isOpenExisting);
            winRef.webContents.send('set-as-settings', this.settings);
            setTimeout(() => { this.ask4SummarizedBuys(); }, 1000);
            
            if (isOpenExisting) {
                windowCreationCallback(windowId);
            }
        });

        winRef.on('closed', () => {

            winRef = null;
            this._untie(instrument);
            this.log(`a fixed-price buy window is closed/${instrument}/${instrumentName}`);
        });

        winRef.webContents.on('location-change', (...args) => { this._locate(...args); });
    }

    /**
     * @param {OneInstrument} stockInfo
     */
    open(stockInfo, windowCreationCallback) {

        var { instrument, instrumentName } = stockInfo;

        if (this._xtimer == undefined) {
            this._xtimer = setInterval(() => { this.ask4SummarizedBuys(); }, 1000 * 11);
        }

        var matched = this._seek(instrument);

        /**
         * 窗口已经存在，呼出 & 聚焦
         */

        if (matched) {

            let winObj = BrowserWindow.fromId(matched.windowId);
            if (winObj && !winObj.isDestroyed()) {
                winObj.focus();
            }
            else {
                this.interaction.showError(`${instrument}/${instrumentName}，窗口已被异常销毁，或引用关系错误`);
            }

            return;
        }

        /**
         * 打开一个新窗口
         */

        this.log(`to open a fixed-price buy window/${instrument}/${instrumentName}`);
        this.renderProcess.once(this.systemEvent.huntWinTabViewFromRender, (event, windowId) => {
            this.setWinProperties(stockInfo, windowId, windowCreationCallback);
        });

        var woptions = {
            
            width: 540,
            height: 225,
            maximizable: false,
            resizable: false,
            closable: false,
            highlight: true,
        };

        var locat = this._locate(instrument);
        if (locat) {

            var { x, y, width, height } = locat;
            woptions.x = x;
            woptions.y = y;
            woptions.width = width;
            woptions.height = height;
        }

        this.renderProcess.send(this.systemEvent.huntWinTabViewFromRender, '@20cm/fixedp', `${instrumentName}/${instrument}`, woptions);
    }

    updateSettings(settings) {
        
        var instruments = this.helper.dictKey2Array(this.mapper);
        instruments.forEach(eachIns => {

            let matched = this._seek(eachIns);
            if (matched == undefined) {
                return;
            }

            var winObj = BrowserWindow.fromId(matched.windowId);
            if (!winObj || winObj.isDestroyed()) {
                return;
            }

            winObj.webContents.send('set-as-settings', settings);
        });
    }

    closeTaskWin(instrument) {
        
        var matched = this._seek(instrument);
        if (matched == undefined) {
            return;
        }

        this._untie(instrument);

        var winObj = BrowserWindow.fromId(matched.windowId);
        if (winObj && !winObj.isDestroyed()) {
            winObj.webContents.send('task-expired');
        }
    }
}

module.exports = { FixedPriceGovener };