<div class="header-inter" style="background-color: #0C1016;">

	<el-button size="mini" @click="openSetting">
		<i class="el-icon-setting"></i> 设置</el-button>

	<el-button size="mini" @click="openCompleted">
		<i class="el-icon-setting"></i> 查看历史策略</el-button>
		
	<span class="monitor s-pull-right">
		<label class="prop-name">总可用</label>
		<label class="prop-value">{{ typeof zky == 'number' ? thousandsDecimal(zky) : '--' }}</label>
		<label class="prop-name">总可融</label>
		<label class="prop-value">{{ typeof zkr == 'number' ? thousandsDecimal(zkr) : '--' }}</label>
		<label class="prop-name">总资产</label>
		<label class="prop-value">{{ typeof zzc == 'number' ? thousandsDecimal(zzc) : '--' }}</label>
		<label class="prop-name">冻结资金</label>
		<label class="prop-value">{{ typeof djzj == 'number' ? thousandsDecimal(djzj) : '--' }}</label>
	</span>
	
</div>
