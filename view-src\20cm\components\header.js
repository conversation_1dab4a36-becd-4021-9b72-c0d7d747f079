const { IView } = require('../../../component/iview');
const NumberMixin = require('../../../mixin/number').NumberMixin;

module.exports = class HeaderView extends IView {

    constructor() {

        super('@20cm/components/header', false);
        this.registerEvent('account-change', (args) => { this.updateSummary(args); });
    }

    updateSummary({ zky, zkr, zzc, djzj }) {

        this.data.zky = zky;
        this.data.zkr = zkr;
        this.data.zzc = zzc;
        this.data.djzj = djzj;
    }

    createApp() {

        this.data = {

            zky: null,
            zkr: null,
            zzc: null,
            djzj: null,
        };

        this.vapp = new Vue({

            el: this.$container.firstElementChild.firstElementChild,
            data: this.data,
            mixins: [NumberMixin],
            methods: {
                
                openSetting: () => {
                    this.trigger('open-setting');
                },

                openCompleted: () => {
                    this.trigger('open-completed');
                },
            }
        });
    }

    build($container) {

        super.build($container);
        this.createApp();
    }
};