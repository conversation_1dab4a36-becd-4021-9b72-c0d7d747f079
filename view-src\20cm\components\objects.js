const { helper } = require('../../../libs/helper');
const { repo20Cm } = require('../../../repository/20cm');
const { TransactionItem } = require('../../2021/model/message');

class LabelValue {

    constructor(label, value, isPercent) {

        this.label = label;
        this.value = value;
        this.isPercent = isPercent;
    }
}

class Definition {

    constructor(code, mean) {

        this.code = code;
        this.mean = mean;
    }
}

class DynamicParam {

    constructor(prop, struc) {

        this.applicable = struc.applicable != false;
        this.prop = prop;
        this.label = struc.label;
        this.value = struc.value;
        this.unit = struc.unit;
        this.min = 0;
        this.max = 9999999999;
        this.step = 1;
    }
}

class Percentage extends Definition {

    constructor(code, mean, isByAmount, isByCustomized) {

        super(code, mean);
        this.isByAmount = !!isByAmount;
        this.isByCustomized = !!isByCustomized;
    }
}

class StrategyParam {

    constructor(struc = { prop, unit, min, max, step, coefficiency }) {
        
        this.prop = struc.prop;
        this.unit = struc.unit;
        this.min = struc.min || 0;
        this.step = struc.step || 1;
        this.max = struc.max || 999999999;
        this.coefficiency = struc.coefficiency || 1;
    }
}

/**
 * 基础策略
 */
class Strategy extends Definition {

    /**
     * @param {Array<StrategyParam>} params 
     */
    constructor(code, mean, params) {

        super(code, mean);
        /** 策略附带参数列表 */
        this.params = params instanceof Array ? params : [];
    }
}

class ThresholdDef {

    /**
     * @param {String} checkedProp 
     * @param {Array<{ prop: String, label: String, unit: String, hasButton: Boolean, min: Number, max: Number, step: Number }>} members 
     */
    constructor(checkedProp, members) {

        this.checkedProp = checkedProp;
        this.members = members;
    }
}

const ThresholdConfigMeta = {

    checkedProp: '',
    isOn: false,
    fields: [{ prop: '', label: '', unit: '', min: 0, max: 0, step: 0, hasButton: false, value: null }],
    isCentralizedOnly: false,
};

class TradeThreshold {

    constructor(struc = ThresholdConfigMeta) {

        this.checkedProp = struc.checkedProp;
        this.isOn = !!struc.isOn;
        this.members = struc.fields.map(fi => {

            return {

                prop: fi.prop,
                label: fi.label,
                unit: fi.unit,
                hasButton: fi.hasButton,
                
                min: fi.min || 0,
                max: fi.max || 999999999,
                step: fi.step || 1,

                value: fi.value || 0,
            };
        });

        /** 是否仅集中竞价适配参数 */
        this.isCentralizedOnly = !!struc.isCentralizedOnly;
    }
}

class ShortcutConfig {

    /**
     * @param {String} stroke 
     * @param {*} strategy 
     * @param {*} data 
     */
    constructor(stroke, strategy, data) {

        this.stroke = stroke;
        this.strategy = strategy;
        this.data = data || {};
    }

    static CreateNew() {
        return new ShortcutConfig(null, null);
    }
}

class ZtRington extends Definition {

    constructor(code, mean, mediaUrl) {
        
        super(code, mean);
        this.mediaUrl = mediaUrl;
        this.isCustomized = !mediaUrl;
    }
}

class ThresholdState {

    /**
     * @param {Boolean} checked 该阈值是否被勾选
     * @param {Object} values 阈值JSON对象
     */
    constructor(checked, values) {

        /** 该阈值是否被勾选 */
        this.checked = checked;
        /** 阈值JSON对象 */
        this.values = values;
    }
}

class UserSetting {

    /**
     * 读取阈值对象里，某个属性值
     * @param {Object} states 阈值对象
     * @param {String} propName 属性名称
     * @returns {ThresholdState}
     */
    static ReadThreadhold(states, propName) {
        return states[propName];
    }

    constructor(percentage, followedPrice) {

        this.position = {

            percentage: percentage,
            amount: 5000,
            customized: 5,
        };

        this.credit = {
            creditBuy: false,
        };

        var { dyfdl, xjbl, jzhl, xmwt, jhjjc, jzgc, zdyxjbl } = ThresholdDefMap;
        this.threshold = {

            [dyfdl.checkedProp]: new ThresholdState(false, { [dyfdl.members[0].prop]: 10000 }),
            [jzgc.checkedProp]: new ThresholdState(false, { [jzgc.members[0].prop]: 5000 }),
            // [xjbl.checkedProp]: new ThresholdState(false, { [xjbl.members[0].prop]: 50 }),
            // [jzhl.checkedProp]: new ThresholdState(false, { [jzhl.members[0].prop]: 50 }),
            [xmwt.checkedProp]: new ThresholdState(false, { [xmwt.members[0].prop]: 8000 }),
            [jhjjc.checkedProp]: new ThresholdState(false, { [jhjjc.members[0].prop]: 20000 }),
            [zdyxjbl.checkedProp]: new ThresholdState(false, { [zdyxjbl.members[0].prop]: 1, [zdyxjbl.members[1].prop]: 500 }),
        };

        this.supplement = { checked: false, value: 25000, time: 1000 };

        this.random = {

            random: false,
            mainMin: null,
            mainMax: null,
            imbarkMin: null,
            imbarkMax: null,
            starMin: null,
            starMax: null,
        };

        this.spliting = {

            main: 9999,
            imbark: 3000,
            star: 1000,
            protect2: { first: 30, second: 70 },
            protect3: { first: 30, second: 30, third: 40 },
        };

        this.prompt = {
            
            mbuy: true,
            msell: false,
            mcancel: false,
            floating: false,
        };

        this.toggleShow = false;
        this.followedPrice = followedPrice;
        this.limitedPos = 1;
        this.buyOptions = { defaultSeq: 1 };
        this.buyShortcuts = makeDefaultBuyShortcuts();
        this.sellOptions = { defaultSeq: 1 };
        this.sellShortcuts = makeDefaultSellShortcuts();

        this.ztrington = {

            entrusted: null,
            bought: null,
            canceled: null,
            sold: null,

            customized: {

                entrusted: null,
                bought: null,
                canceled: null,
                sold: null,
            },
        };
    }
}

class UserBasicSetting {

    constructor({ a1, a2, a3, b1, b2, c1, c2 }) {

        /** 过去涨幅天数 */
        this.a1 = a1;
        /** 过去天数内涨幅 */
        this.a2 = a2;
        /** 过去未涨停天数 */
        this.a3 = a3;
        /** 流通市值最大值 */
        this.b1 = b1;
        /** 流通市值最大值 */
        this.b2 = b2;
        /** 当天振幅 */
        this.c1 = c1;
        /** 收盘涨幅 */
        this.c2 = c2;
    }
}

class UserAutoSetting {

    /**
     * 读取阈值对象里，某个属性值
     * @param {Object} states 阈值对象
     * @param {String} propName 属性名称
     * @returns {ThresholdState}
     */
    static ReadThreadhold(states, propName) {
        return UserSetting.ReadThreadhold(states, propName);
    }

    constructor({ 
        a1, a2, 
        b1, b2, 
        c1, c2, c3, c4, c5, 
        d1, d2, d3, 
        e1, e2, 
        f1, f2, f3, f4, 
        g1, g2, g3, g4, 
        h1, h2, h3, 
        i1, i2, 
        j1, j2, 
        k1, k2, k3, k4, 
        l1, l2, l3,
        m1, m2, m3,
    }) {

        this.a1 = a1 !== false;
        this.a2 = a2;

        this.b1 = b1 !== false;
        this.b2 = b2;
        
        this.c1 = c1 !== false;
        this.c2 = c2;
        this.c3 = c3;
        this.c4 = c4 !== false;
        this.c5 = c5;
        
        this.d1 = d1 !== false;
        this.d2 = d2;
        this.d3 = d3;
        
        this.e1 = e1;
        this.e2 = e2 !== true;
        
        this.f1 = f1 !== false;
        this.f2 = f2;
        this.f3 = f3 !== false;
        this.f4 = f4;
        
        this.g1 = g1;
        this.g2 = g2;
        this.g3 = g3 !== false;
        this.g4 = g4;
        
        this.h1 = h1 !== false;
        this.h2 = h2;
        this.h3 = h3;

        this.i1 = i1 !== true;
        this.i2 = i2;

        this.j1 = j1 !== true;
        this.j2 = j2;

        this.k1 = k1 !== true;
        this.k2 = k2;
        this.k3 = k3;
        this.k4 = k4;

        this.l1 = l1 !== true;
        this.l2 = l2;
        this.l3 = l3;

        this.m1 = !!m1;
        this.m2 = m2;
        this.m3 = m3;
    }
}

class TaskObject {

    constructor(struc) {

        this.id = struc.id;
        this.userId = struc.userId;
        this.userName = struc.userName;
        this.instrument = struc.instrument;
        this.instrumentName = struc.instrumentName;
        this.direction = struc.direction;
        this.priceFollowType = struc.priceFollowType;
        this.orderPrice = struc.orderPrice;
        /** 账号委托数据 */
        this.xorders = [{ accountId: null, accountName: null, volumes: [0] }].splice(1);
        this.limitPositionType = struc.limitPositionType;
        /** auto: 涨停板类型（1：10%涨停，2：20%涨停） */
        this.stockLimitType = struc.stockLimitType;
        /** auto: 打板类型（0：人工打板，1：自动打板） */
        this.strikeBoardType = struc.strikeBoardType;
        this.strikeBoardStatus = struc.strikeBoardStatus;
        this.targetVolume = struc.targetVolume;
        this.supplementTime = struc.supplementTime;
        this.supplementVolume = struc.supplementVolume;
        this.supplementOpen = struc.supplementOpen;
        this.splitInterval = struc.splitInterval;
        this.splitType = struc.splitType;
        this.usedMargin = struc.usedMargin;

        this.splitDetail = {

            main: undefined,
            imbark: undefined,
            protect2: { first: undefined, second: undefined },
            protect3: { first: undefined, second: undefined, third: undefined },
        };

        var sd = struc.splitDetail;
        var sd2 = helper.isJson(sd) ? sd : typeof sd == 'string' && sd.length > 0 ? JSON.parse(sd) : {};
        for (let key in sd2) {
            this.splitDetail[key] = sd2[key];
        }

        this.boardStrategy = {
            
            strategyType: null,
            strategyVolume: null,
            strategyRate: null,
            strategyDelayTime: null,

            // auto: 最低策略数量
            strategyMinVolume: null,
        };

        var bds = struc.boardStrategy;
        for (let key in bds) {
            this.boardStrategy[key] = bds[key];
        }

        this.cancelCondition = {

            afterLimitTickCount: undefined,
            afterLimitTickEnabled: false,
            beforeTradeCancel: undefined,
            beforeCancelOpen: false,
            downRate: undefined,
            downRateOpen: false,
            extremeBack: undefined,
            extremeBackOpen: false,
            lineupOrderVolume: undefined,
            lineupOrderVolumeOpen: false,
            sellOrderVolume: undefined,
            sellOrderVolumeOpen: false,
            customDownRate: undefined,
            customDownRateTime: undefined,
            customDownRateOpen: false,

            // auto: 撤单保护手数
            cancelProtectedVolume: undefined,
            // auto: 撤单保护时间（单位：分钟）
            cancelProtectedTime: undefined,
            // auto: 撤单保护是否启用
            cancelProtectedEnabled: false,

            // auto: 成交保护时间（单位：分钟）
            tradeProtectedTime: undefined,
            // auto: 成交保护是否启用
            tradeProtectedEnabled: false,
            
            // auto: 有效成交额
            effectiveTradedAmount: undefined,
            // auto: 增加补单数量
            supplementOrderVolume: undefined,
            // auto: 补单是否启用
            supplementEnabled: false,
            // auto: 精准跟撤
            followCancel: undefined,
            // auto: 精准跟撤是否启用
            followCancelOpen: false,
            // auto: 是否发生过补单
            hasSupplement: false,
            // auto: 是否已执行撤单
            hasCanceled: false,
        };

        var ccd = struc.cancelCondition;
        for (let key in ccd) {
            this.cancelCondition[key] = ccd[key];
        }

        this.cash = struc.cash;
        this.positionPercent = struc.positionPercent;
        this.creditFlag = struc.creditFlag;

        // 以下，为自动监控有关字段
        
        this.taskId = struc.taskId;
        this.ticketPoolId = struc.ticketPoolId;
        this.ticketPoolName = struc.ticketPoolName;

        // 以上，为自动监控有关字段
    }

    static CreateOrders(account_map) {

        if (!account_map) {
            return [];
        }

        var orders = [];

        for (let account_name in account_map) {

            let order_map = account_map[account_name];
            let volumes = [];

            for (let order_id in order_map) {
                volumes.push(Math.ceil(order_map[order_id] * 0.01));
            }

            orders.push({ accountId: null, accountName: account_name, volumes });
        }

        return orders;
    }

    /**
     * 是否非预期的状态
     */
    static isUnexpected(status) {
        return status == TaskStatus.deleted || status == TaskStatus.finished;
    }

    /**
     * 是否处于已激活（未启动）状态
     */
    static isAlive(status) {
        return status == TaskStatus.created || status == TaskStatus.stopped || this.isRunning(status);
    }

    /**
     * 是否处于已激活（运行中，但尚未产生委托）状态
     */
    static isRunning(status) {
        return status == TaskStatus.started || status == TaskStatus.supplemented;
    }

    /**
     * 是否处于补单状态
     */
    static isSupplemented(status) {
        return status == TaskStatus.supplemented;
    }

    /**
     * 是否处于已停止状态
     */
    static isStopped(status) {
        return status == TaskStatus.stopped;
    }

    /**
     * 是否处于已下单状态
     */
    static isOrdered(status) {
        return status == TaskStatus.ordered;
    }
}

class AccountEntrust {

    /**
     * @param {*} accountId 
     * @param {*} accountName 
     * @param {Array<Number>} volumes 
     */
    constructor(accountId, accountName, volumes) {

        this.accountId = accountId;
        this.accountName = accountName;
        this.volumes = volumes instanceof Array ? volumes : [];
    }
}

/**
 * 用在交易单元界面中，供用户选择的，策略结构
 */
class StrategyItem {

    constructor({ key, stroke, strategy, name }) {

        /** 唯一识别代码 */
        this.skey = key;
        /** 快捷设置 */
        this.stroke = stroke;
        /** 策略数据项ID */
        this.strategy = strategy;
        /** 用于显示的策略名称 */
        this.name = name;
    }
}

const TaskStatus = {

    created: 0,
    started: 1, // 运行中，未发生过下单（买入监控 + S）
    ordered: 2, // 已发生下单，且未成交完成（已买清单）
    supplemented: 3, // 补单运行中（买入监控 + S）
    stopped: 4, // 已停止（买入监控 + S）
    deleted: 5, // 忽略
    finished: 6, // 忽略
};

const Digits = ['1', '2', '3', '4', '5', '6', '7', '8', '9', '0'];
const FunctionKeys = ['F1', 'F2', 'F3', 'F4', 'F5', 'F6', 'F7', 'F8', 'F9', 'F10', 'F11', 'F12'];
const KeyStrokes4Buy = Digits.concat(FunctionKeys);
const KeyStrokes4Sell = ['a', 'b', 'c', 'd', 'e', 'f', 'g', 'h', 'i', 'j', 'k', 'l', 'm', 'n', 'o', 'p', 'q', 'r', 's', 't', 'u', 'v', 'w', 'x', 'y', 'z'];
const ThresholdDefMap = {

    dyfdl: new ThresholdDef('lineupOrderVolumeOpen', [{ prop: 'lineupOrderVolume', label: '低于封单量', unit: '手', hasButton: false }]),
    xjbl: new ThresholdDef('downRateOpen', [{ prop: 'downRate', label: '下降比例', unit: '%', min: 1, max: 100, hasButton: false }]),
    jzhl: new ThresholdDef('extremeBackOpen', [{ prop: 'extremeBack', label: '极值回落', unit: '%', min: 1, max: 100, hasButton: false }]),
    xmwt: new ThresholdDef('sellOrderVolumeOpen', [{ prop: 'sellOrderVolume', label: '现卖委托', unit: '手', hasButton: false }]),
    jhjjc: new ThresholdDef('beforeCancelOpen', [{ prop: 'beforeTradeCancel', label: '集合竞价撤', unit: '手', hasButton: false }]),
    jzgc: new ThresholdDef('followCancelOpen', [{ prop: 'followCancel', label: '精准跟撤', unit: '手', hasButton: false }]),
    zdyxjbl: new ThresholdDef('customDownRateOpen', [

        { prop: 'customDownRate', label: '自定义下降比例', unit: '%', min: 1, max: 100, hasButton: false },
        { prop: 'customDownRateTime', label: '作用时间', unit: '毫秒', min: 500, max: 3000, step: 200, hasButton: false },
    ]),
};

const StrategyParamNames = {

    strategy: 'strategyType',
    volume: 'strategyVolume',
    rate: 'strategyRate',
    delay: 'strategyDelayTime',
};

const StrategyMap = {

    b01: { id: 1, name: '第N笔涨停的单子延时' },
    b02: { id: 2, name: '涨停时，第N笔卖出' },
    b03: { id: 4, name: '跳扫' },
    b04: { id: 5, name: '买一量' },
    b05: { id: 6, name: '集合-买二量' },

    s01: { id: 101, name: '定时定量' },
    s02: { id: 102, name: '定时定比例' },
    s03: { id: 103, name: '低买一量' },
    s04: { id: 104, name: '委卖跟单' },
    s05: { id: 105, name: '封单下降比例' },
    s06: { id: 106, name: '极速卖出' },
};

/**
 * @returns {Array<ShortcutConfig>}
 */
function makeDefaultBuyShortcuts() {
    
    var names = StrategyParamNames;
    return helper.deepClone([

        new ShortcutConfig(Digits[0], StrategyMap.b01.id, { [names.volume]: 1, [names.delay]: 0 }),
        new ShortcutConfig(Digits[1], StrategyMap.b01.id, { [names.volume]: 2, [names.delay]: 3000 }),
        new ShortcutConfig(Digits[2], StrategyMap.b02.id, { [names.volume]: 1 }),
        new ShortcutConfig(Digits[3], StrategyMap.b03.id, { [names.rate]: 50 }),
        new ShortcutConfig(Digits[4], StrategyMap.b04.id, { [names.volume]: 100 }),
        new ShortcutConfig(Digits[5], StrategyMap.b05.id, { [names.volume]: 200 }),
        new ShortcutConfig(Digits[6], StrategyMap.b01.id, { [names.volume]: 1 }),
        new ShortcutConfig(Digits[7], StrategyMap.b02.id),
    ]);
}

/**
 * @returns {Array<ShortcutConfig>}
 */
function makeDefaultSellShortcuts() {
    
	var names = StrategyParamNames;

    return helper.deepClone([

        new ShortcutConfig(KeyStrokes4Sell[0], StrategyMap.s01.id, { [names.volume]: 3000, [names.delay]: 3000 }),
        new ShortcutConfig(KeyStrokes4Sell[1], StrategyMap.s02.id, { [names.delay]: 3000, [names.rate]: 50 }),
        new ShortcutConfig(KeyStrokes4Sell[2], StrategyMap.s06.id, { [names.volume]: 5000, [names.delay]: 500 }),
        new ShortcutConfig(KeyStrokes4Sell[3], StrategyMap.s03.id, { [names.volume]: 10000 }),
        new ShortcutConfig(KeyStrokes4Sell[4], StrategyMap.s04.id, { [names.volume]: 8000 }),
        new ShortcutConfig(KeyStrokes4Sell[5], StrategyMap.s05.id, { [names.rate]: 50 }),
    ]);
}

const Entrance = {

    defaultsSettings: function () {

        var percentages = this.makePercentages();
        var prices = this.makeFollowPrices();
        return new UserSetting(percentages[1].code, prices[0].code);
    },

    defaultsBuyDynamicParams: function() {

        var names = StrategyParamNames;

        return [
    
            new DynamicParam(names.volume, { applicable: false, label: '量', value: null, unit: '手' }),
            new DynamicParam(names.rate, { applicable: false, label: '比例', value: null, unit: '%' }),
            new DynamicParam(names.delay, { applicable: false, label: '延时', value: null, unit: '毫秒' }),
        ];
    },

    defaultsSellDynamicParams: function() {

        var names = StrategyParamNames;

        return [
    
            new DynamicParam(names.volume, { applicable: false, label: '量', value: null, unit: '股' }),
            new DynamicParam(names.rate, { applicable: false, label: '比例', value: null, unit: '%' }),
            new DynamicParam(names.delay, { applicable: false, label: '延时', value: null, unit: '毫秒' }),
        ];
    },
    
    makeHistoryThresholds: function() {
    
        var { dyfdl, xjbl, jzhl, xmwt, jzgc, zdyxjbl, jhjjc } = ThresholdDefMap;
        
        return [
    
            new TradeThreshold({ checkedProp: dyfdl.checkedProp, fields: dyfdl.members }),
            new TradeThreshold({ checkedProp: jzgc.checkedProp, fields: jzgc.members }),
            // new TradeThreshold({ checkedProp: xjbl.checkedProp, fields: xjbl.members }),
            // new TradeThreshold({ checkedProp: jzhl.checkedProp, fields: jzhl.members }),
            new TradeThreshold({ checkedProp: xmwt.checkedProp, fields: xmwt.members }),
            new TradeThreshold({ checkedProp: zdyxjbl.checkedProp, fields: zdyxjbl.members }),
            new TradeThreshold({ checkedProp: jhjjc.checkedProp, fields: jhjjc.members, isCentralizedOnly: true }),
        ];
    },
    
    makeBuyThresholds: function() {
        
        var { jzgc } = ThresholdDefMap;
        var threses = this.makeHistoryThresholds();
        threses.remove(thr => thr instanceof TradeThreshold && thr.checkedProp == jzgc.checkedProp);
        return threses;
    },
    
    makeBoughtThresholds: function() {
    
        var { dyfdl, xjbl, jzhl, xmwt, jzgc, zdyxjbl, jhjjc } = ThresholdDefMap;
        
        return [
    
            new TradeThreshold({ checkedProp: dyfdl.checkedProp, fields: dyfdl.members }),
            new TradeThreshold({ checkedProp: jzgc.checkedProp, fields: jzgc.members }),
            // new TradeThreshold({ checkedProp: xjbl.checkedProp, fields: xjbl.members }),    
            // new TradeThreshold({ checkedProp: jzhl.checkedProp, fields: jzhl.members }),
            new TradeThreshold({ checkedProp: xmwt.checkedProp, fields: xmwt.members }),
            new TradeThreshold({ checkedProp: zdyxjbl.checkedProp, fields: zdyxjbl.members }),
            new TradeThreshold({ checkedProp: jhjjc.checkedProp, fields: jhjjc.members, isCentralizedOnly: true }),
        ];
    },

    makePercentages: function() {

        return [

            new Percentage(1, '全仓'),
            new Percentage(2, '1/2仓'),
            new Percentage(3, '1/3仓'),
            new Percentage(4, '1/4仓'),
            new Percentage(5, '按金额', true, false),
            new Percentage(6, '按仓位1/', false, true),
        ];
    },

    makeBuyStrategies() {

        var strategies = [];        
        var names = StrategyParamNames;
    
        strategies.push(new Strategy(StrategyMap.b01.id, StrategyMap.b01.name, [
            
            new StrategyParam({ prop: names.volume, unit: '笔' }),
            new StrategyParam({ prop: names.delay, unit: '毫秒' }),
        ]));
        
        strategies.push(new Strategy(StrategyMap.b02.id, StrategyMap.b02.name, [new StrategyParam({ prop: names.volume, unit: '笔' })]));
        strategies.push(new Strategy(StrategyMap.b03.id, StrategyMap.b03.name, [new StrategyParam({ prop: names.rate, unit: '%', min: 1, max: 100 })]));
        strategies.push(new Strategy(StrategyMap.b04.id, StrategyMap.b04.name, [new StrategyParam({ prop: names.volume, unit: '手', coefficiency: 1 })]));
        strategies.push(new Strategy(StrategyMap.b05.id, StrategyMap.b05.name, [new StrategyParam({ prop: names.volume, unit: '手', coefficiency: 1 })]));

        return strategies;
    },

    makeSellStrategies() {

        var strategies = [];
        var names = StrategyParamNames;
    
        strategies.push(new Strategy(StrategyMap.s01.id, StrategyMap.s01.name, [
            
            new StrategyParam({ prop: names.delay, unit: '毫秒', min: 3000 }),
            new StrategyParam({ prop: names.volume, unit: '股' }),
        ]));
        
        strategies.push(new Strategy(StrategyMap.s02.id, StrategyMap.s02.name, [

            new StrategyParam({ prop: names.delay, unit: '毫秒', min: 3000 }),
            new StrategyParam({ prop: names.rate, unit: '%', min: 1, max: 100 }),
        ]));

        strategies.push(new Strategy(StrategyMap.s03.id, StrategyMap.s03.name, [new StrategyParam({ prop: names.volume, unit: '手' })]));
        strategies.push(new Strategy(StrategyMap.s04.id, StrategyMap.s04.name, [new StrategyParam({ prop: names.volume, unit: '手' })]));
        strategies.push(new Strategy(StrategyMap.s05.id, StrategyMap.s05.name, [new StrategyParam({ prop: names.rate, unit: '%', min: 1, max: 100 })]));
        strategies.push(new Strategy(StrategyMap.s06.id, StrategyMap.s06.name, [

            new StrategyParam({ prop: names.delay, unit: '毫秒', max: 2999 }),
            new StrategyParam({ prop: names.volume, unit: '股' }),
        ]));

        return strategies;
    },

    makePlainSellStrategies() {
        return this.makeSellStrategies().map(x => ({ code: x.code, mean: x.mean }));
    },

    makeFixedPriceStrategies() {

        return [

            { code: 98, mean: '定时定量', isByVolume: true },
            { code: 99, mean: '定时定比例', isByRate: true },
        ];
    },

    makeFollowPrices() {

        var levels = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10];
        return levels.map(x => new Definition(x, `买${x}价`));
    },

    makeLimitedPoses() {

        var list = [];
        for (let idx = 1; idx <= 10; idx++) {
            list.push(new Definition(idx, '1/' + idx));
        }

        return list;
    },

    makeParamb() {

        return {

            fbgl: new LabelValue('封板概率', null, true),
            ycxfbgl: new LabelValue('一次性封板概率', null, true),
            gkgl: new LabelValue('高开概率', null, true),
            cryjl: new LabelValue('次日溢价率', null, true),
            jjsylj: new LabelValue('均价收益率均', null, true),
            zgsylj: new LabelValue('最高收益率均', null, true),
            zdfblj: new LabelValue('最大封板量均', null),
            bscjl: new LabelValue('板上成交量', null),
            bl: new LabelValue('B量', null),
        };
    },

    makeRings() {

        var path = require('path');
        return [

            new ZtRington(1, '提示音1', path.join(__dirname, '../../../asset/rington/01.wav')),
            new ZtRington(2, '提示音2', path.join(__dirname, '../../../asset/rington/02.wav')),
            new ZtRington(3, '提示音3', path.join(__dirname, '../../../asset/rington/03.wav')),
            new ZtRington(4, '提示音4', path.join(__dirname, '../../../asset/rington/04.wav')),
            new ZtRington(99, '自定义'),
        ];
    },

    readSetting: async function () {
        
        var defaults = this.defaultsSettings();
        var resp = await repo20Cm.querySetting();
        var content = (resp.data || {}).settings;

        if (typeof content == 'string' && content.length > 0) {

            try {

                let latest = JSON.parse(content);
                if (helper.isJson(latest)) {
                    defaults = this.integrity(defaults, latest);
                }
            }
            catch(ex) {
                return null;
            }
        }
        
        return defaults;
    },

    /**
     * @param {UserSetting} defaultSettings 
     * @param {UserSetting} latest 
     * @returns 
     */
    integrity: function (defaults, latest) {

        for (let keyName in defaults) {

            if (latest[keyName] === undefined) {
                latest[keyName] = defaults[keyName];
            }
        }

        return latest;
    },

    saveSetting: async function (settings) {
        return await repo20Cm.setting(JSON.stringify(settings));
    },

    deleteSetting: async function() {
        return await repo20Cm.deleteSetting();
    },
};

class UnclosedCreditPosition {

    constructor(struc) {
        
        this.accountId = struc.accountId;
        this.accountName = struc.accountName;
        this.compactStatus = struc.compactStatus;
        this.compactType = struc.compactType;
        this.expireDate = struc.expireDate;
        this.fundId = struc.fundId;
        this.fundName = struc.fundName;
        this.id = struc.id;
        this.identityId = struc.identityId;
        this.instrument = struc.instrument;
        this.instrumentName = struc.instrumentName;
        this.occurDate = struc.occurDate;
        this.openAmount = struc.openAmount;
        this.openVolume = struc.openVolume;
        this.orderId = struc.orderId;
        this.strategyName = struc.strategyName;
        this.tradeAmount = struc.tradeAmount;
        /** 待了结融资买入仓位 */
        this.volume = struc.volume;
    }
}

class AccountSimple {

    constructor(struc) {
        
        this.id = struc.id;
        this.assetType = struc.assetType;
        this.available = struc.available;
        this.balance = struc.balance;
        this.accountId = struc.identityId;
        this.accountName = struc.identityName;
        this.identityType = struc.identityType;
        this.fundId = struc.fundId;
        this.fundName = struc.fundName;
        this.frozenCommission = struc.frozenCommission;
        this.frozenMargin = struc.frozenMargin;
        this.marketValue = struc.marketValue;
        /** 是否为信用账号 */
        this.credit = struc.credit;
        /** 为信用账号时，可用融资额度 */
        this.enableCreditBuy = struc.enableCreditBuy || 0;

        // this.closeProfit = struc.closeProfit;
        // this.commission = struc.commission;
        // this.connectCount = struc.connectCount;
        // this.connectionStatus = struc.connectionStatus;
        // this.diffBalance = struc.diffBalance;
        this.financeAccount = struc.financeAccount;
        // this.frozenCommission = struc.frozenCommission;
        // this.frozenMargin = struc.frozenMargin;
        // this.fundShare = struc.fundShare;
        // this.inMoney = struc.inMoney;
        // this.loanBuyBalance = struc.loanBuyBalance;
        // this.loanSellBalance = struc.loanSellBalance;
        // this.loanSellQuota = struc.loanSellQuota;
        // this.margin = struc.margin;
        // this.nav = struc.nav;
        // this.navRealTime = struc.navRealTime;
        // this.outMoney = struc.outMoney;
        // this.positionProfit = struc.positionProfit;
        // this.preBalance = struc.preBalance;
        // this.risePercent = struc.risePercent;
        // this.status = struc.status;
        // this.strategyId = struc.strategyId;
        // this.tradingDay = struc.tradingDay;
        // this.withdrawQuota = struc.withdrawQuota;
    }

    /**
     * @param {Array} records 
     * @returns {Array<AccountSimple>}
     */
    static Convert(records) {
        return records instanceof Array ? records.map(x => new AccountSimple(x)) : records ? [new AccountSimple(records)] : [];
    }
}

class BuyUnit {

    constructor(isHistory, stockCode, stockName, taskId) {

        this.stock = {
            
            code: stockCode,
            name: stockName,
        };

        /** 是否展开 */
        this.expanded = true;
        /** 内部撤单条件是否展开 */
        this.innerExpanded = false;
        /** 任务ID */
        this.taskId = taskId;

        this.bulletin = {

            /** 总卖量 */
            zml: null,
            /** 涨停预埋单 */
            ztymd: null,
            /** 封单笔数 */
            fdbs: null,
            /** 封单均 */
            fdj: null,
            /** 封板 */
            fb: null,
        };

        this.paramb = Entrance.makeParamb();

        /** 选择的策略 */
        this.skey = null;
        this.isRunning = false;
        this.isSupplemented = false;
        this.dynamics = Entrance.defaultsBuyDynamicParams();
        this.threses = isHistory ? Entrance.makeHistoryThresholds() : Entrance.makeBuyThresholds();

        /** 总可用 */
        this.zky = null;
        /** 总可融 */
        this.zkr = null;

        /** 补单策略 */
        this.supplement = {

            checked: false,
            value: null,
        };

        /** 仓位比例设置 */
        this.position = {

            /** 仓位选择 */
            percentage: null,
            /** 金额值（按金额时） */
            amount: null,
            /** 自定义仓比值 */
            customized: null,
        };

        /** 融资选项设置 */
        this.credit = {

            /** 是否使用融资 */
            creditBuy: false,
        };

        /** 纯人工非预设参数 */
        this.manual = {

            /** 发单间隙 */
            fdjx: 20,
            /** 分拆保护 */
            protect: 1,
        };

        /** 指定价格 */
        this.price = null;
        /** 现价 */
        this.latest = null;
        /** 跌停 */
        this.floor = 0;
        /** 涨停 */
        this.ceiling = 99999;
    }

    /**
     * 设置当前合约，是否是可融资买入标的
     * @param {Boolean} isCreditStock 是否可融资买入
     */
    setCreditBuyFlag(isCreditStock) {

        /**
         * 是否可融资买入
         */
        this.isCreditStock = (isCreditStock == 1 || isCreditStock == true);
    }

    removeCentralThrs() {
        this.threses.remove(x => x instanceof TradeThreshold && x.isCentralizedOnly);
    }

    /**
     * @param {Number} zml 总卖量
     */
    updateZml(zml) {
        this.bulletin.zml = zml;
    }

    /**
     * @param {Number} value 涨停预埋单
     */
    updateYmd(value) {
        this.bulletin.ztymd = value;
    }

    /**
     * @param {Number} value 封单笔数
     */
    updateFdbs(value) {
        this.bulletin.fdbs = value;
    }

    /**
     * @param {*} choice 选择仓位比例
     * @param {*} amount 金额（按金额时）
     * @param {*} customized 自定义仓位比例
     */
    updatePosition(choice, amount, customized) {

        var ref = this.position;
        ref.percentage = choice;
        ref.amount = amount;
        ref.customized = customized;
    }

    /**
     * @param {Number} update 
     */
    updateZky(update) {
        this.zky = update * 0.0001;
    }

    /**
     * @param {Number} update 
     */
    updateZkr(update) {
        this.zkr = update * 0.0001;
    }

    /**
     * @param {Number} latest 
     */
    updateLatest(latest) {
        this.latest = typeof latest == 'number' ? Number(latest.toFixed(2)) : latest;
    }

    /**
     * @param {Number} price 
     */
    updatePrice(price) {
        this.price = typeof price == 'number' ? Number(price.toFixed(2)) : price;
    }

    /**
     * @param {Number} floor 
     * @param {Number} ceiling 
     */
    updateLimits(floor, ceiling) {

        this.floor = typeof floor == 'number' ? Number(floor.toFixed(2)) : floor;
        this.ceiling = typeof ceiling == 'number' ? Number(ceiling.toFixed(2)) : ceiling;

        if (this.price == 0 || typeof this.price != 'number') {
            this.updatePrice(this.ceiling);
        }
    }

    updateRunning(update) {
        this.isRunning = update;
    }

    updateSupplemented(update) {
        this.isSupplemented = update;
    }
}

const ViewSetting = {

    /**
     * 首批加载的委托队列数量
     */
    firstScreenCount: 60,

    /**
     * 每次增量加载的委托数据
     */
    batchCount: 30,
};

class AutoBuyUnit {

    /**
     * @param {TaskObject} task
     */
    constructor(task) {

        this.stock = {
            
            code: task.instrument,
            name: task.instrumentName,
        };

        /** 是否展开 */
        this.expanded = true;
        /** 买1 */
        this.buy1 = { volume: 0, amount: 0, fdbs: 0 };
        /** 任务ID */
        this.taskId = task.id;
        this.ticketPoolId = task.ticketPoolId
        this.ticketPoolName = task.ticketPoolName;
        this.paramb = Entrance.makeParamb();

        var aref = task.boardStrategy;
        if (aref == undefined) {
            aref = {};
        }
        
        this.scale = {

            fdcs: aref.strategyVolume,
            syss: 0,
        };

        this.conditions = {

            cancel: { checked: false },
            trade: { checked: false },
            zdy: { checked: false, percent: 1, time: 500 },
            jzcd: { checked: false, hands: 0 },
            dyfd: { checked: false, hands: 10000 },
            bd: { checked: false, hands: 25000 },
        };

        this.available = 0;

        /** 成交队列，最新一笔成交记录 */
        this.trans = {

            /** 已进入的时间片，开始时间 */
            time: null,
            /** 汇总时间片内，总计买入数量 */
            buy: 0,
            /** 汇总时间片内，总计卖出数量 */
            sell: 0,
        };

        /** 成交队列 */
        this.transactions = [new TransactionItem({})].slice(1);
        this.emptyTrans();

        /** 委托队列 */
        this.entrusts = [{ accountId: null, accountName: null, volumes: [0] }].splice(1);        
        this.isEntrustCollapsed = false;

        /** 远端涨停价排队队列（未展示部分）*/
        this.leftRemotes = [];
        /** 远端涨停价排队队列（已展示部分）*/
        this.remotes = [];
 
        /** 所有账号合并的委托手数字典（key：手数，value：true）*/
        this.entrustsMap = {};
        this.maxEntrustHands = 0;
    }

    /**
     * @param {TaskObject} task 
     */
    setAsTask(task) {
        this.task = task;
    }

    emptyTrans() {
        
        var trans = [];
        for (let idx = 1; idx <= 10; idx++) {
            trans.push(new TransactionItem({ direction: 0, time: null, price: 0, volume: 0 }));
        }

        this.transactions.refill(trans);
        this.resetTrans();
    }

    resetTrans() {
        
        this.trans.time = null;
        this.trans.buy = 0;
        this.trans.sell = 0;
    }

    /**
     * @param {*} volume 买1量
     * @param {*} amount 买1金额
     * @param {*} fdbs 封单笔数
     */
    updateBuy1(volume, amount, fdbs) {

        this.buy1.volume = volume;
        this.buy1.amount = amount;
    }

    /**
     * @param {Array<Number>} remotes 远端挂单
     */
    updateQueue(remotes) {

        this.buy1.fdbs = remotes.length;
        this.refreshSummary(remotes);
    }

    /**
     * @param {Array<AccountEntrust>} updates 
     */
    updateEntrusts(updates) {

        this.entrusts.refill(updates);
        var map = {};
        var max = 0;

        updates.forEach(item => {

            if (item.volumes.length > 0) {

                item.volumes.forEach(val => { map[val] = true; });
                max = Math.max(max, item.volumes.max(x => x));
            }
        });

        this.entrustsMap = map;
        this.maxEntrustHands = max;
    }

    loadMore() {

        if (this.leftRemotes.length > 0) {
            this.remotes.merge(this.leftRemotes.splice(0, ViewSetting.batchCount));
        }
    }

    /**
     * @param {Array<Number} remotes 远端挂单
     */
    refreshSummary(remotes) {

        var left = 0;
        var reachedFirstMatch = false;
        var hands = [];

        for (let idx = 0; idx < remotes.length; idx++) {

            // 买入单位都是手不会有散股，除了盘口第一位
            let volume = Math.ceil(remotes[idx] * 0.01);

            if (!reachedFirstMatch) {

                if (volume == this.maxEntrustHands) {
                    reachedFirstMatch = true;
                }

                if (!reachedFirstMatch) {
                    left += volume;
                }
            }

            hands.push(volume);
        }
        
        if (hands.length <= ViewSetting.firstScreenCount) {

            this.leftRemotes = [];
            this.remotes = hands;
        }
        else {
            
            this.remotes = hands.splice(0, Math.max(ViewSetting.firstScreenCount, this.remotes.length));
            this.leftRemotes = hands;
        }

        this.scale.syss = reachedFirstMatch ? left : null;
    }
}

class Setting2Pool {

    constructor(struc) {

        this.amount = struc.amount;
        this.autoStrikeBoardSettingId = struc.autoStrikeBoardSettingId;
        this.autoStrikeBoardSettingName = struc.autoStrikeBoardSettingName;
        this.autoStrikeBoardStatus = struc.autoStrikeBoardStatus;
        this.endTime = struc.endTime;
        this.id = struc.id;
        this.instrumentCancelCount = struc.instrumentCancelCount;
        this.instrumentOrderCount = struc.instrumentOrderCount;
        this.instrumentTradeCount = struc.instrumentTradeCount;
        this.orderAmount = struc.orderAmount;
        this.sequence = struc.sequence;
        this.startTime = struc.startTime;
        this.stockLimitType = struc.stockLimitType;
        this.ticketPoolId = struc.ticketPoolId;
        this.ticketPoolName = struc.ticketPoolName;
        this.tradedAmount = struc.tradedAmount;
    }
}

module.exports = {

    DynamicParam,
    TradeThreshold,
    Definition,
    Percentage,
    StrategyParam,
    Strategy,
    ShortcutConfig,
    UserSetting,
    UserBasicSetting,
    UserAutoSetting,
    AccountSimple,
    UnclosedCreditPosition,
    TaskObject,
    AccountEntrust,
    StrategyItem,
    BuyUnit,
    AutoBuyUnit,
    Setting2Pool,

    Digits,
    FunctionKeys,
    KeyStrokes4Buy,
    KeyStrokes4Sell,
    StrategyParamNames,
    TaskStatus,
    Entrance,
};