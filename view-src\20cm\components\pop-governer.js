const { <PERSON><PERSON>erWindow } = require('@electron/remote');
const BoughtListView = require('./bought-list');

class WindowInfo {

    constructor({ windowId, taskId, instrument, instrumentName }) {

        this.windowId = windowId;
        this.taskId = taskId;
        this.instrument = instrument;
        this.instrumentName = instrumentName;
    }
}

class PopGovener {

    get units() {
        return this.boughtView.units;
    }

    /**
     * @param {BoughtListView} hostView 
     */
    constructor(hostView) {

        this.boughtView = hostView;
        this.renderProcess = hostView.renderProcess;
        this.helper = hostView.helper;
        this.systemEvent = hostView.systemEvent;
        this.interaction = hostView.interaction;
        this.mapper = {};
        this.slocationMap = {};
    }

    log(message) {
        this.boughtView.log(message);
    }

    /**
     * read/write a window's location & size info
     * @param {*} taskId 
     * @returns {{ x, y, width, height }} 
     */
    _locate(taskId, x, y, width, height) {
        
        if (typeof x == 'number') {
            this.slocationMap[taskId] = { x, y, width, height };
        }
        else {
            return this.slocationMap[taskId];
        }
    }

    /**
     * @param {*} taskId 
     * @returns {WindowInfo}
     */
    _seek(taskId) {
        return this.mapper[taskId];
    }

    /**
     * @param {*} taskId 
     * @param {WindowInfo} winInfo 
     */
    _tie(taskId, winInfo) {
        this.mapper[taskId] = winInfo;
    }

    /**
     * @param {*} taskId 
     */
    _untie(taskId) {
        delete this.mapper[taskId];
    }

    open(taskId, instrument, instrumentName) {

        this.log(`to pop out bought window/${taskId}/${instrument}/${instrumentName}`);

        var matched = this._seek(taskId);
        if (matched) {

            let winObj = BrowserWindow.fromId(matched.windowId);
            if (winObj && !winObj.isDestroyed()) {

                winObj.focus();
                return;
            }
        }

        this.renderProcess.once(this.systemEvent.huntWinTabViewFromRender, (event, windowId) => {

            this._tie(taskId, new WindowInfo({ windowId, taskId, instrument, instrumentName }));
            var winRef = BrowserWindow.fromId(windowId);

            winRef.on('closed', () => {

                winRef = null; 
                this._untie(taskId);
                this.log(`pop-out bought window is closed/${taskId}/${instrument}/${instrumentName}`);
            });

            winRef.webContents.on('is-ready', () => {

                let source = this.units.find(x => x.taskId == taskId);
                if (!source) {
                    return this.interaction.showError('与主窗口，关联丢失，建议关闭后重开！');
                }
                
                let queue = source.remotes.concat(source.leftRemotes);
                winRef.webContents.send('set-as-instrument', taskId, instrument, instrumentName);
                winRef.webContents.send('set-as-entrusts', source.summary.syzj, source.entrusts);
                winRef.webContents.send('set-as-queue', queue.map(x => 100 * x));
                winRef.webContents.send('set-as-settings', this.boughtView.settings);
            });

            winRef.webContents.on('location-change', (...args) => { this._locate(...args); });
        });

        var woptions = {
            
            minWidth: 500,
            minHeight: 775,
            width: 800,
            height: 775,
            minimizable: false,
            maximizable: false,
            highlight: true,
        };

        var locat = this._locate(taskId);
        if (locat) {

            var { x, y, width, height } = locat;
            woptions.x = x;
            woptions.y = y;
            woptions.width = width;
            woptions.height = height;
        }

        this.renderProcess.send(this.systemEvent.huntWinTabViewFromRender, '@20cm/bulletin', `${instrumentName}/${instrument}`, woptions);
    }

    updateSettings(settings) {
        
        var taskIds = this.helper.dictKey2Array(this.mapper);
        taskIds.forEach(taskId => {

            let matched = this._seek(taskId);
            if (matched == undefined) {
                return;
            }

            var winObj = BrowserWindow.fromId(matched.windowId);
            if (!winObj || winObj.isDestroyed()) {
                return;
            }

            winObj.webContents.send('set-as-settings', settings);
        });
    }

    updateEntrust(taskId, syzj, orders) {

        var matched = this._seek(taskId);
        if (matched == undefined) {
            return;
        }
        
        var winObj = BrowserWindow.fromId(matched.windowId);
        if (!winObj || winObj.isDestroyed()) {
            return;
        }

        winObj.webContents.send('set-as-entrusts', syzj, orders);
    }

    close(taskId) {
        
        var matched = this._seek(taskId);
        if (matched == undefined) {
            return;
        }

        this._untie(taskId);

        var winObj = BrowserWindow.fromId(matched.windowId);
        if (winObj && !winObj.isDestroyed()) {
            winObj.webContents.send('expired');
        }
    }
}

module.exports = { PopGovener };