const { BaseView } = require('../base-view');
const { SmartTable } = require('../../../libs/table/smart-table');
const { Position } = require('../../../model/position');
const { ColumnCommonFunc } = require('../../../libs/table/column-common-func');
const { ModelConverter } = require('../../../model/model-converter');
const { repoPosition } = require('../../../repository/position');

/**
 * @returns {Array<Position>}
 */
function makePositions() {
    return [];
}

class AggregatedPosition {

    /**
     * @param {Position} struc 
     */
    constructor(struc, weightedPrice) {

        this.instrument = struc.instrument;
        this.instrumentName = struc.instrumentName;
        this.weightedPrice = weightedPrice;

        this.totalPosition = struc.totalPosition;
        this.closableVolume = struc.closableVolume;
        this.marketValue = struc.marketValue;
        this.yesterdayPosition = struc.yesterdayPosition;
        this.frozenVolume = struc.frozenVolume;
        this.profit = struc.profit;
        this.lastPrice = 0;
    }
}

module.exports = class PositionView extends BaseView {

    constructor() {

        super('@20cm/components/position', false, '持仓');
        this.positions = makePositions();
    }

    replyWithPositions(stockCode) {

        var positions = this.positions.filter(x => x.instrument == stockCode);
        this.trigger('required-positions', positions);
    }

    setMaxHeight(height) {

        if (this.tableObj) {
            this.tableObj.setMaxHeight(height - 35);
        }
    }

    /**
     * @param {AggregatedPosition} record 
     */
    identify(record) {
        return record.instrument;
    }

    /**
     * @param {AggregatedPosition} record 
     */
    handleRowPunch(record) {

        var { instrument, instrumentName, totalPosition, yesterdayPosition, frozenVolume, closableVolume } = record;
        
        if (closableVolume <= 0) {
            
            this.log(`double click a position record, but it has no closables, id/${record.id}`);
            this.interaction.showError(`${instrumentName}，无可平仓位`);
            return;
        }

        this.log(`double click a position record, id/${record.id}, stock/${instrument}/${instrumentName}`);
        this.trigger('hit-position', { instrument, instrumentName, totalPosition, yesterdayPosition, frozenVolume, closableVolume });
    }

    createTable() {
        
        this.helper.extend(this, ColumnCommonFunc);
        var $table = this.$container.querySelector('.position-records');
        this.tableObj = new SmartTable($table, this.identify, this, {

            tableName: 'smt-20pos',
            displayName: this.title,
            rowDbClicked: this.handleRowPunch.bind(this),
        });

        this.tableObj.setMaxHeight(200);
        this.tableObj.setPageSize(99999);
    }

    async requestPositions() {

        var resp = await repoPosition.batchMemQuery({ trade_user_id: this.userInfo.userId });
        if (resp.errorCode != 0) {

            this.interaction.showError(`持仓查询错误：${resp.errorCode}/${resp.errorMsg}`);
            return;
        }

        var records = resp.data;
        /** 首行数据，为标题栏 */
        var titles = records.shift();
        var positions = ModelConverter.formalizePositions(titles, records);
        this.positions.refill(positions);
        
        var aggregateds = [];
        var map = {};

        positions.forEach(item => {
            
            let matched = map[item.instrument];
            if (matched === undefined) {

                let sames = positions.filter(x => x.instrument == item.instrument);
                let tpos = sames.map(x => x.totalPosition).sum();
                let wprice = tpos == 0 ? 0 : sames.map(x => x.totalPosition * x.avgPrice / tpos).sum();
                let agg = new AggregatedPosition(item, wprice);
                aggregateds.push(agg);
                map[item.instrument] = agg;
            }
            else if (matched instanceof AggregatedPosition) {

                matched.totalPosition += item.totalPosition;
                matched.closableVolume += item.closableVolume;
                matched.marketValue += item.marketValue;
                matched.yesterdayPosition += item.yesterdayPosition;
                matched.frozenVolume += item.frozenVolume;
                matched.profit += item.profit;
            }
        });

        this.tableObj.refill(aggregateds);
    }

    refresh() {

        this.interaction.showSuccess('刷新请求已发出');
        this.requestPositions();
    }

    /**
     * @returns {Array<AggregatedPosition>}
     */
    get aggregatedPositions() {
        return this.tableObj.extractAllRecords();
    }

    arrangeRefresh() {

        setInterval(async () => {
            
            if (this.isBackgroundRefresh) {
                return;
            }

            try {

                this.isBackgroundRefresh = true;
                await this.requestPositions();
                var empties = this.aggregatedPositions.filter(x => x.yesterdayPosition == 0 && x.frozenVolume == 0);
                var closables = this.aggregatedPositions.filter(x => x.closableVolume > 0).map(x => ({ instrument: x.instrument, closable: x.closableVolume }));
                this.brocastCondition(empties.map(x => x.instrument), closables);
            }
            catch(ex) {
                console.error(ex);
            }
            finally {
                this.isBackgroundRefresh = false;
            }

        }, 1000 * 5);
    }

    brocastCondition(empties, closables) {
        this.trigger('brocast-position-condition', empties, closables);
    }

    exportSome() {

        var date = new Date().format('yyyyMMdd');
        var time = new Date().format('hhmmss');
        this.tableObj.exportAllRecords(`${this.title}-${this.userInfo.userName}-${date}-${time}`);
    }

    listen2Events() {

        this.registerEvent('set-max-height', this.setMaxHeight.bind(this));
        this.registerEvent('ask-4-positions', this.replyWithPositions.bind(this));
        this.registerEvent('auto-fit', () => { this.tableObj.fitColumnWidth(); });
    }

    build($container) {

        super.build($container);
        this.createTable();
        this.listen2Events();
        this.requestPositions();
        this.arrangeRefresh();
    }
};