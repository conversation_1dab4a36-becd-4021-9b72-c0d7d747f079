<div class="sells themed-box">

	<div v-for="(unit, unit_idx) in units" 
		:key="unit_idx"
		class="identity-sell-unit sell-unit typical-content"
		:class="isFocused(unit) ? 'focused' : ''"
		@click="setAsCurrent(unit)">

		<div class="title typical-header themed-bottom-border">

			<span class="summary s-pull-right s-full-height">

				<table>
					<colgroup>
						<col width="70">
						<col width="auto">
					</colgroup>
					<tr>
						<td>已成交量</td>
						<td class="s-text-right">
							{{ typeof unit.summary.tradedVolume == 'number' ? thousands(unit.summary.tradedVolume) : '---' }}
						</td>
					</tr>
					<tr>
						<td>挂单数</td>
						<td class="s-text-right">
							{{ typeof unit.summary.entrustedVolume == 'number' ? thousands(unit.summary.entrustedVolume) : '---' }}
						</td>
					</tr>
				</table>

			</span>

			<span style="font-size: 16px; font-weight: bold;">
				{{ unit.stock.name }} ({{ unit.stock.code }})
			</span>

			<span class="info shine-color s-pdl-10">
				<label>竞量比</label>
				<label>{{ typeof unit.summary.jlb == 'number' ? (unit.summary.jlb + '%') : '---' }}</label>
			</span>

		</div>

		<div class="unit-body">

			<div class="right-part">

				<el-button type="success" size="small" @click="unit.isManualMode = !unit.isManualMode">
					{{ unit.isManualMode ? '自动卖' : '手动卖' }}
				</el-button>
				<el-button v-if="!unit.isRunning" type="primary" size="small" @click="remove(unit)">删</el-button>
				<el-button type="primary" size="small" @click="cancelAll(unit)">全撤</el-button>
				<el-button type="success" size="small" @click="floorSell(unit)">一键跌停卖</el-button>
				<el-button type="danger" size="small" @click="ceilingSell(unit)">一键涨停卖</el-button>

			</div>

			<div class="left-part themed-right-border">

				<div class="info shine-color themed-bottom-border">
					{{ unit.isManualMode ? '手动卖' : '自动卖' }}
				</div>
				
				<div class="ctr-row">

					<span class="ctr-label">{{ unit.isManualMode ? '价格' : '跟价' }}</span>
					<el-select v-model="unit.boundary.followed" 
								@change="handleSomeChange(unit, '1-3-01', unit.boundary.followed)" 
								class="medium-input s-mgl-5 s-mgr-20">
						<el-option v-for="item in priceLevels" :key="item.code" :value="item.code" :label="item.mean"></el-option>
					</el-select>
	
					<span class="ctr-label">限价</span>

					<template v-if="unit.isManualMode">

						<el-tooltip placement="bottom">

							<span slot="content">
	
								<span class="s-color-green">
									跌停 = 
									<a @click="setAsManualPrice(unit, unit.limits.floor)" class="s-cp s-hover-underline">
										{{ typeof unit.limits.floor == 'number' ? unit.limits.floor : '---' }}</a>
								</span>
	
								<span class="s-pdl-10 s-color-red">
									涨停 = 
									<a @click="setAsManualPrice(unit, unit.limits.ceiling)" class="s-cp s-hover-underline">
										{{ typeof unit.limits.ceiling == 'number' ? unit.limits.ceiling : '---' }}</a>
								</span>
	
							</span>
	
							<el-input-number 
								v-model="unit.manual.price"
								:min="unit.limits.floor"
								:max="unit.limits.ceiling"
								:step="0.01"
								:controls="false"
								size="mini"
								class="medium-input s-mgl-5 s-mgr-20"></el-input-number>
	
						</el-tooltip>

					</template>

					<template v-else>

						<el-tooltip placement="bottom">

							<span slot="content">
	
								<span class="s-color-green">
									跌停 = 
									<a @click="setAsPrice(unit, unit.limits.floor)" class="s-cp s-hover-underline">{{ typeof unit.limits.floor == 'number' ? unit.limits.floor : '---' }}</a>
								</span>
	
								<span class="s-pdl-10 s-color-red">
									涨停 = 
									<a @click="setAsPrice(unit, unit.limits.ceiling)" class="s-cp s-hover-underline">{{ typeof unit.limits.ceiling == 'number' ? unit.limits.ceiling : '---' }}</a>
								</span>
	
							</span>
	
							<el-input-number
								v-model="unit.boundary.price"
								@change="handleSomeChange(unit, '1-3-02', unit.boundary.price)"
								:min="unit.limits.floor"
								:max="unit.limits.ceiling"
								:step="0.01"
								:controls="false"
								size="mini"
								class="medium-input s-mgl-5 s-mgr-20"></el-input-number>
	
						</el-tooltip>

					</template>
	
					<span class="ctr-label">限仓</span>
					<el-select v-model="unit.boundary.pos" 
								@change="handleSomeChange(unit, '1-3-03', unit.boundary.pos)"
								class="medium-input s-mgl-5">
						<el-option v-for="item in limits" :key="item.code" :value="item.code" :label="item.mean"></el-option>
					</el-select>
		
				</div>

				<template v-if="unit.isManualMode">

					<div class="ctr-row">
						<span class="ctr-label">最大可卖：</span>
						<a class="s-cp s-hover-underline" @click="setAsVolume(unit, unit.maxSellable)">{{ typeof unit.maxSellable == 'number' ? thousands(unit.maxSellable) : 'N/A' }}</a> 股
					</div>

					<div class="ctr-row">
					
						<span class="ctr-label" style="padding-left: 30px;">数量</span>

						<el-input-number 
							v-model="unit.manual.volume" 
							@change="handleVolumeChange(unit, '1-3-04', unit.manual.volume)"
							:min="0" 
							:max="typeof unit.maxSellable == 'number' ? unit.maxSellable : 999999999" 
							:controls="false" 
							size="mini" 
							class="medium-input s-mgl-5"></el-input-number>

						<span class="ctr-unit s-pdl-5">股</span>

						<span class="ctr-label s-pdl-20">仓位比例</span>

						<el-input-number 
							v-model="unit.manual.percent" 
							@change="handlePercentChange(unit, '1-3-05', unit.manual.percent)"

							:min="1" 
							:max="100" 
							:controls="false" 
							size="mini" 
							class="medium-input s-mgl-5"></el-input-number>

						<span class="ctr-unit s-pdl-5">%</span>

					</div>

					<div class="ctr-row s-center">
						<el-button type="success" size="small" @click="msell(unit)">卖出</el-button>
						<el-button type="warning" size="small" @click="clearWithBuy2(unit)">买二 清仓</el-button>
					</div>
					
				</template>

				<template v-else>

					<div class="ctr-row" style="margin-top: -6px;">

						<span class="ctr-label">策略</span>

						<el-select
							v-model="unit.skey"
							@change="handleStrategyChange(unit, '1-3-06', unit.skey)"
							class="s-mgl-5"
							style="width: 194px;">

							<el-option-group v-for="group in strategyGroups" :key="group.name" :label="group.name">
								<el-option v-for="(stra, stra_idx) in group.strategies" :key="stra_idx" :value="stra.skey" :label="stra.name"></el-option>
							</el-option-group>

						</el-select>

					</div>

					<div class="ctr-row" style="margin-top: -3px; padding-left: 30px;">
	
						<span v-for="(dyna, dyna_idx) in unit.dynamics" class="ctr-dynamic" :class="dyna.applicable ? '' : 'disabled-ctr'">
							<label class="input-name s-pdr-5">{{ dyna.label }}</label>
							<el-input-number 
								v-model="dyna.value"
								:disabled="!dyna.applicable"
								@change="handleSomeChange(unit, '1-3-07:' + dyna.prop, dyna.value)"
								:min="dyna.min"
								:max="dyna.max"
								:step="dyna.step"
								:controls="false" class="medium-input" clearable></el-input-number>
							<label class="input-unit s-pdl-5">{{ showProperParamUnit(unit, dyna) }}</label>
						</span>
			
					</div>

					<div class="ctr-row s-center">
						<el-button v-if="unit.isRunning" type="danger" size="small" @click="stop(unit)">暂停</el-button>
						<el-button v-else type="success" size="small" @click="start(unit)">启动</el-button>
					</div>

				</template>

			</div>

		</div>

	</div>

	<template v-if="units.length == 0">
		<div class="no-data-notice">
			<div class="displaying">
				<i class="el-icon-moon"></i> 请双击持仓记录，添加卖出监控
			</div>
		</div>
	</template>

</div>