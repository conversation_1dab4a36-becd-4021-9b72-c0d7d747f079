const { BaseView } = require('../base-view');
const { NumberMixin } = require('../../../mixin/number');
const { TickData } = require('../../2021/model/message');
const { Position } = require('../../../model/position');
const { Entrance, TaskStatus, StrategyParamNames, UnclosedCreditPosition, Strategy, StrategyItem, DynamicParam, TaskObject, Definition, UserSetting } = require('./objects');
const { Cm20FunctionCodes } = require('../../../config/20cm');
const { repoInstrument } = require('../../../repository/instrument');
const { repo20Cm } = require('../../../repository/20cm');

/**
 * @returns {TickData}
 */
function makeTickd() {
    return null;
}

class SellUnit {

    constructor(stockCode, stockName, taskId) {

        this.stock = {
            
            code: stockCode,
            name: stockName,
        };

        /** 任务ID */
        this.taskId = taskId;

        /**
         * 委托情况
         */
        this.summary = {

            /** 竞量比 */
            jlb: null,
            /** 已成交量 */
            tradedVolume: null,
            /** 挂单数 */
            entrustedVolume: null,
        };

        /**
         * 下单边界值
         */
        this.boundary = {

            /** 跟价 */
            followed: null,
            /** 限价 */
            price: null, 
            /** 限仓 */
            pos: null,
        };

        /**
         * 涨跌停价格
         */
        this.limits = {

            floor: null,
            ceiling: null,
        };

        /** 选择的策略 */
        this.skey = null;
        this.isRunning = false;
        this.dynamics = Entrance.defaultsSellDynamicParams();

        /** 是否为人工模式 */
        this.isManualMode = false;
        /** 最大可卖 */
        this.maxSellable = null;
        /** 人工卖出参数 */
        this.manual = {

            price: null,
            /** 数量 */
            volume: null,
            /** 预期占仓位总量的百分比 */
            percent: null,
        };

        this.tickd = makeTickd();
    }

    hasNotSetPrice() {
        return typeof this.boundary.price != 'number' || this.boundary.price <= 0;
    }

    hasNotSetManualPrice() {
        return typeof this.manual.price != 'number' || this.manual.price <= 0;
    }

    /**
     * @param {*} followed 跟价
     * @param {*} price 限价
     * @param {*} pos 限仓
     */
    updateBoundary(followed, price, pos) {

        this.boundary.followed = followed;
        this.boundary.pos = pos;
        this.updatePrice(price);
    }

    /**
     * @param {*} price 限价
     */
    updatePrice(price) {

        if (price > 0) {
            this.boundary.price = price;
        }
    }

    /**
     * @param {*} price 限价
     */
    updateManualPrice(price) {

        if (price > 0) {
            this.manual.price = price;
        }
    }

    /**
     * @param {*} floor
     * @param {*} ceiling
     */
    updateLimits(floor, ceiling) {

        this.limits.floor = floor;
        this.limits.ceiling = ceiling;
    }

    /**
     * @param {*} tradedVolume 已成交量
     * @param {*} entrustedVolume 挂单数
     */
    updateSummary(tradedVolume, entrustedVolume) {

        this.summary.tradedVolume = tradedVolume;
        this.summary.entrustedVolume = entrustedVolume;
    }

    /**
     * @param {Boolean} update 
     */
    updateRunning(update) {
        this.isRunning = update;
    }
}

/**
 * @returns {Array<SellUnit>}
*/
function makeUnits() {
    return [];
}

function makePriceLevels() {

    var levels = [];

    for (let seq = 1; seq <= 10; seq++) {
        if (seq != 0) {
            levels.push(new Definition(seq, '买' + seq));
        }
    }

    return levels;
}

function makeLimits() {

    var limits = [];
    for (let seq = 1; seq <= 10; seq++) {
        limits.push(new Definition(seq, '1/' + seq));
    }
    return limits;
}

/**
 * @returns {Array<Strategy>}
 */
function makeStrategies() {
    return [];
}

/**
 * @returns {Array<StrategyItem>}
 */
function makeStrategyItems() {
    return [];
}

module.exports = class SellListView extends BaseView {

    constructor() {

        super('@20cm/components/sell-list', false, '卖出监控');

        /** 原始策略 */
        this.strategies = makeStrategies();
        /** 快捷策略 */
        this.specializeds = makeStrategyItems();
        /** 普通策略 */
        this.normals = makeStrategyItems();

        this.priceLevels = makePriceLevels();
        this.limits = makeLimits();
        this.units = makeUnits();
        this.unitsMap = {};
        this.states = {
            focused: this.units.length > 0 ? this.units[0] : null,
        };

        this.registerEvent('setting-updated', (settings) => { this.setAsSettings(settings); });
        this.registerEvent('add-units', (stocks) => { this.addUnits(stocks); });
        this.registerEvent('no-focused-unit', () => { this.clearCurrent(); });
        this.registerEvent('shortcut-key-pressed', (stroke) => { this.handleStrokePress(stroke); });
        this.registerEvent('task-created', (task) => { this.handleTaskCreated(task); });
        this.registerEvent('task-error', (task) => { this.handleTaskError(task); });
        this.registerEvent('set-tasks', (tasks, isReply) => { this.handleTaskChange(tasks, isReply); });
        this.registerEvent('summarized-sells', (map) => { this.handleSummarizedSell(map); });
        this.registerEvent('action-by-position', (zeros, closables) => { this.doActionByPositions(zeros, closables); });
    }

    /**
     * @param {UserSetting} settings 
     */
    setAsSettings(settings) {
        
        this.settings = settings;
        this.loadStrategies();
    }

    /**
     * @param {String} stroke 
     */
    handleStrokePress(stroke) {

        var target = this.states.focused;

        if (!target) {
            return;
        }
        else if (target.isManualMode) {
            return;
        }

        var shortcut = this.settings.sellShortcuts.find(x => x.stroke == stroke.toUpperCase() || x.stroke == stroke.toLowerCase());
        if (!shortcut) {
            return;
        }

        let matched = this.specializeds.find(x => x.stroke == stroke);
        if (matched) {

            this.log(`sell list: pressed shortcut key/${stroke}, and selected strategy/${JSON.stringify(matched)}`);
            target.skey = matched.skey;
            this.handleStrategyChange(target, '1-3-08:' + stroke, target.skey);
        }
    }

    /**
     * @param {TaskObject} task
     */
    handleTaskCreated(task) {
        this.handleTaskChange([task], false);
    }

    /**
     * @param {TaskObject} task
     */
    handleTaskError(task) {

        let matched = this.units.find(x => x.stock.code == task.instrument);
        let key_content = `task id/${task.id}, instrument/${task.instrument}/${task.instrumentName}`;

        if (matched) {

            this.log(`an error task has been received by sell list, and would rewrite the existing unit, ${key_content}`);

            /**
             * 发生状态报错的任务，在客户端的形态2种可能：
             * 1. 尚未建立映射（未具有id，通过instrument进行反查）； 
             * 2. 已建立映射（已具有id）
             */
            matched.taskId = task.id;

            /**
             * 借用task change通道，对相匹配的交易单元，各项参数进行重写
             */
            this.handleTaskChange([task]);
        }
        else {

            console.error(task);
            this.log(`an error task is passed to sell list, but found no matched unit, ${key_content}`);
        }
    }

    /**
     * @param {TaskObject} task
     */
    handleTaskError(task) {

        let matched = this.units.find(x => x.stock.code == task.instrument);
        let key_content = `task id/${task.id}, instrument/${task.instrument}/${task.instrumentName}`;

        if (matched) {

            this.log(`an error task has been received by sell list, and would rewrite the existing unit, ${key_content}`);

            /**
             * 发生状态报错的任务，在客户端的形态2种可能：
             * 1. 尚未建立映射（未具有id，通过instrument进行反查）； 
             * 2. 已建立映射（已具有id）
             */
            matched.taskId = task.id;

            /**
             * 借用task change通道，对相匹配的交易单元，各项参数进行重写
             */
            this.handleTaskChange([task]);
        }
        else {

            console.error(task);
            this.log(`an error task is passed to sell list, but found no matched unit, ${key_content}`);
        }
    }

    /**
     * @param {Array<TaskObject>} tasks
     * @param {Boolean} isReply
     */
    handleTaskChange(tasks, isReply) {

        var hasNew = false;
        tasks.forEach(task => {

            let status = task.strikeBoardStatus;

            if (TaskObject.isUnexpected(status)) {

                this.units.remove(x => x instanceof SellUnit && x.stock.code == task.instrument);
                this.unsubscribeTick(task.instrument);
                this.log(`unit removed from sell list, task id/${task.id}, task status/${status}, stock/${task.instrument}/${task.instrumentName}`);

                /**
                 * todo21: 可能解决了缺陷：添加一个合约 》启动 》已买 》完成结束 》重新添加相同合约 》动态实时提交的参数为上一次的参数痕迹 
                 */
                if (this.states.focused && this.states.focused.stock.code === task.instrument) {

                    this.clearCurrent();
                    this.trigger('unit-removed', task.instrument);
                }

                return;
            }
            else if (!TaskObject.isAlive(status)) {

                console.error('unexpected task status = ' + status);
                return;
            }

            let unit = this.units.find(x => x.stock.code == task.instrument);
            if (unit === undefined) {

                /**
                 * 作为一个新的监控
                 */
                unit = new SellUnit(task.instrument, task.instrumentName, task.id);
                this.units.unshift(unit);
                this.unitsMap[unit.stock.code] = unit;
                this.subscribeTick(unit.stock.code);
                this.requestLimitedPrice(unit);
                this.requestJLRatio(unit);
                this.log(`unit added to sell list, task id/${task.id}, task status/${status}, stock/${task.instrument}/${task.instrumentName}`);
                hasNew = true;
            }
            
            unit.taskId = task.id;
            unit.updateRunning(TaskObject.isRunning(status));
            unit.updatePrice(task.orderPrice);
            unit.updateManualPrice(task.orderPrice);

            let strategyId = task.boardStrategy.strategyType;
            let selected = this.specializeds.find(x => x.strategy == strategyId);
            if (!selected) {
                selected = this.normals.find(x => x.strategy == strategyId);
            }
            
            if (selected) {

                unit.skey = selected.skey;
                this.alignDynamicState(unit);
            }

            unit.dynamics.forEach(item => {

                if (item.applicable) {
                    item.value = task.boardStrategy[item.prop];
                }
            });
            
            unit.updateBoundary(task.priceFollowType, task.orderPrice, task.limitPositionType);
        });

        if (hasNew) {
            this.ask4SummarizedSells();
        }
    }

    handleSummarizedSell(map) {

        this.units.forEach(item => {

            let matched = map[item.stock.code];
            if (matched) {

                let { traded, original } = matched;
                item.updateSummary(traded, original);
            }
            else {
                item.updateSummary(0, 0);
            }
        });
    }

    /**
     * @param {Array<String>} zeros 
     * @param {Array<{ instrument: String, closable: Number }>} closables 
     */
    doActionByPositions(zeros, closables) {

        if (zeros.length > 0) {

            this.log(`to programatically delete sell units/${zeros.join()}`);
            zeros.forEach(stcode => {

                let matched = this.units.find(x => x.stock.code == stcode);
                if (matched) {
                    this.remove(matched);
                }
            });
        }

        /**
         * 备注：避免周期性地从持仓汇总数据，程序化主动修改掉【数量】和【比例】，造成用户困扰
         */

        // if (closables.length > 0) {
            
        //     closables.forEach(clos => {

        //         if (clos.closable > 0) {

        //             let matched = this.units.find(x => x.stock.code == clos.instrument);
        //             if (matched && matched.isManualMode) {
        //                 matched.manual.percent = Math.ceil(100 * Math.min(1, matched.manual.volume / clos.closable));
        //             }
        //         }
        //     });
        // }
    }

    loadStrategies() {

        var strategies = Entrance.makeSellStrategies();
        var shortcuts = this.settings.sellShortcuts;
        this.strategies.refill(strategies);

        this.specializeds.refill(shortcuts.map(x => {

            return new StrategyItem({
                
                key: `${x.stroke}-${x.strategy}`,
                stroke: x.stroke,
                strategy: x.strategy, 
                name: `${x.stroke} - ${strategies.find(y => y.code == x.strategy).mean}`,
            });
        }));

        this.normals.refill(strategies.filter(x => !shortcuts.some(y => y.strategy == x.code)).map(x => {

            return new StrategyItem({
                
                key: x.code,
                stroke: undefined,
                strategy: x.code,
                name: x.mean,
            });
        }));
    }

    createApp() {

        this.vapp = new Vue({

            el: this.$container.firstElementChild,
            data: {

                strategyGroups: [

                    { name: '快捷策略', strategies: this.specializeds },
                    { name: '普通策略', strategies: this.normals },
                ],

                priceLevels: this.priceLevels,
                limits: this.limits,
                units: this.units,
                states: this.states,
            },
            mixins: [NumberMixin],
            methods: this.helper.fakeVueInsMethod(this, [

                this.isFocused,
                this.setAsCurrent,
                this.setAsPrice,
                this.cancelAll,
                this.remove,
                this.msell,
                this.clearWithBuy2,
                this.floorSell,
                this.ceilingSell,
                this.simplify,
                this.start,
                this.stop,
                this.setAsVolume,
                this.setAsManualPrice,
                this.handleStrategyChange,
                this.handleSomeChange,
                this.handlePercentChange,
                this.handleVolumeChange,
                this.showProperParamUnit,
            ]),
        });
    }

    /**
     * @param {Array<{ code: String, name: String, closableVolume: Number }>} stocks
     */
    addUnits(stocks) {

        if (!(stocks instanceof Array) || stocks.length == 0) {
            return;
        }

        var hasAdded = false;

        stocks.forEach(item => {

            if (this.units.some(x => x.stock.code == item.code)) {

                this.interaction.showError(`${item.code}，股票已存在`);
                this.setAsCurrent(this.units.find(x => x.stock.code == item.code));
                return;
            }

            var unit = new SellUnit(item.code, item.name, null);
            unit.maxSellable = item.closableVolume;

            if (this.settings) {

                let { defaultSeq } = this.settings.sellOptions;
                let specias = this.specializeds;
                if (specias.length > 0 && typeof defaultSeq == 'number' && defaultSeq >= 1 && defaultSeq <= specias.length) {

                    unit.skey = specias[parseInt(defaultSeq) - 1].skey;
                    this.alignDynamicState(unit);
                }

                unit.updateBoundary(this.settings.followedPrice, 0, this.settings.limitedPos);
            }
            else {
                unit.updateBoundary(this.priceLevels[0].code, 0, this.limits[0].code);
            }

            this.units.unshift(unit);
            this.unitsMap[unit.stock.code] = unit;

            /**
             * 订阅标准行情
             */
            this.subscribeTick(unit.stock.code);
            this.requestLimitedPrice(unit);
            this.requestJLRatio(unit);
            this.log(`added new unit to sell list, stock/${item.code}/${item.name}`);
            hasAdded = true;
        });

        if (this.units.length > 0) {
            this.setAsCurrent(this.units[0]);
        }

        if (hasAdded) {
            this.ask4SummarizedSells();
        }
    }

    ask4SummarizedSells() {
        this.trigger('ask-summarized-sells');
    }

    /**
     * @param {SellUnit} unit 
     */
    setAsCurrent(unit) {

        if (this.isFocused(unit)) {
            return;
        }

        this.states.focused = unit;
        this.log(`focus on an unit in sell list, stock/${unit.stock.code}/${unit.stock.name}`);
        this.trigger('unit-focused', this.title, unit.stock.code);
    }

    /**
     * @param {SellUnit} unit 
     * @param {Number} price 
     */
    setAsPrice(unit, price) {

        if (typeof price == 'number') {

            this.log(`set sell-unit price to ${price}, task id/${unit.taskId}, stock/${unit.stock.code}/${unit.stock.name}`);
            unit.updatePrice(Number(price.toFixed(2)));
        }
    }

    /**
     * @param {SellUnit} unit 
     * @param {Number} price 
     */
    setAsManualPrice(unit, price) {

        if (typeof price == 'number') {

            this.log(`set sell-unit manual price to ${price}, task id/${unit.taskId}, stock/${unit.stock.code}/${unit.stock.name}`);
            unit.updateManualPrice(Number(price.toFixed(2)));
        }
    }

    clearCurrent() {
        this.states.focused = null;
    }

    /**
     * @param {SellUnit} unit
     */
    isFocused(unit) {
        return unit === this.states.focused;
    }

    confirm(isRequired, message, callback) {

        if (isRequired) {
            
            this.interaction.showConfirm({

                title: '操作确认',
                message: message,
                confirmed: () => {
                    callback();
                },
            });
        }
        else {
            callback();
        }
    }

    /**
     * @param {SellUnit} unit 
     */
    staticRemove(unit) {

        var units = this.units;
        var pos = units.indexOf(unit);
        if (pos < 0) {
            return;
        }

        units.splice(pos, 1);
        delete this.unitsMap[unit.stock.code];
        this.unsubscribeTick(unit.stock.code);

        if (!this.isFocused(unit)) {
            return;
        }

        this.trigger('unit-removed', unit.stock.code);

        if (units.length == 0) {
            this.clearCurrent();
        }
        else {
            this.setAsCurrent(units[Math.min(pos, units.length - 1)]);
        }
    }

    /**
     * @param {SellUnit} unit 
     */
    remove(unit) {
        
        this.confirm(false, `${unit.stock.name}，删除监控？`, () => {

            let hasId = this.helper.isNotNone(unit.taskId);

            if (hasId) {

                this.log(`to stop running a sell, task id/${unit.taskId}, stock/${unit.stock.code}/${unit.stock.name}`);
                this.cancelAll(unit);
                this.renderProcess.send(this.systemEvent.transmitMsg2TradingServer, Cm20FunctionCodes.request.delete, { id: unit.taskId });
            }
            else {

                this.log(`to delete a fresh new sell unit, stock/${unit.stock.code}/${unit.stock.name}`);
                this.staticRemove(unit);
            }
        });
    }

    /**
     * @param {SellUnit} unit 
     */
    cancelAll(unit) {

        this.log(`to cancel all sell-direction entrusts from a sell unit, task id/${unit.taskId}, stock/${unit.stock.code}/${unit.stock.name}`);
        this.trigger('ask-2-cancel-all-sells', unit.stock.code, unit.stock.name);
    }

    /**
     * @param {SellUnit} unit 
     * @param {Number|undefined} price 
     */
    msell(unit, price, isFixPrice = true, isClear = false) {
        
        if (!this.isCheckedOk(unit)) {
            return;
        }

        this.aboutUnit = unit;
        this.aboutPrice = price || unit.manual.price;
        this.isAboutFixPrice = isFixPrice;
        this.isAboutClear = isClear;
        this.unregisterEvent('required-positions');
        this.registerEvent('required-positions', (positions, uncloseds) => { this.execSell(positions, uncloseds); }, true);
        this.log(`to manual sell on a sell unit buy some button, price/${this.aboutPrice}, fixed price/${isFixPrice}, clear/${isClear}, stock/${unit.stock.code}/${unit.stock.name}`);
        this.trigger('ask-4-positions', unit.stock.code);
    }

    trimVolume(volume) {

        var step = 100;

        if (isNaN(volume) || volume <= 0) {
            return 0;
        }
        else {
            return step * Math.floor(volume / step);
        }
    }

    /**
     * @param {Array<Position>} positions 
     * @param {Array<{ accountId: String, uncloseds: Array<UnclosedCreditPosition> }>} creditPoses 
     */
    execSell(positions, creditPoses) {

        var unit = this.aboutUnit;
        var expected = this.isByVolume(unit) ? unit.manual.volume : 0;
        var price = this.aboutPrice.toFixed(2);
        var dict = this.systemTrdEnum;
        var total = positions.map(x => x.closableVolume).sum();
        var maxCanSell = this.isAboutClear ? total : total / unit.boundary.pos;
        var tickd = unit.tickd;

        if (!tickd && (this.isByPercent(unit) || !this.isAboutFixPrice)) {

            this.interaction.showError(`${unit.stock.name}，实时行情数据缺失，无法启动卖出`);
            return;
        }

        if (total <= 0) {

            this.interaction.showError(`${unit.stock.name}，已无可卖持仓`);
            return;
        }
        
        if(this.isAboutClear) {
            expected = maxCanSell;
        }
        else if (this.isByPercent(unit)) {
            expected = total * unit.manual.percent / 100;
        }

        if (!this.isAboutFixPrice) {
            price = Math.max(price, tickd.buys[unit.boundary.followed - 1].price.toFixed(2));
        }

        if (expected > maxCanSell) {
            expected = maxCanSell;
        }

        var theInstrument = unit.stock.code;
        var orders = [];
        var accountNameMap = {};
        /** 是否人工指定了，卖出全部的数量 */
        var isSellAllVolume = expected == total;

        positions.forEach(pos => {

            let { accountId, accountName, fundId, closableVolume } = pos;
            if (closableVolume <= 0) {
                return;
            }

            let decided = isSellAllVolume ? closableVolume : this.trimVolume(expected * closableVolume / total);
            if (decided == 0 && closableVolume <= 100) {
                decided = closableVolume;
            }
            else if (decided > closableVolume) {
                decided = closableVolume;
            }

            if (decided <= 0) {
                return;
            }

            /**
             * 账号ID与名称对应关系表
             */
            accountNameMap[accountId] = accountName;

            /**
             * 获取其融资负债情况
             */
            let cpos = creditPoses.find(x => x.accountId == accountId);
            let uncloseds = !cpos ? [] : cpos.uncloseds;
            let cinstru = uncloseds.find(x => x.instrument == theInstrument);
            let notSet = null;
            let basic = {

                accountId: accountId,
                volume: notSet,
                customId: notSet,

                strategyId: fundId,
                userId: this.userInfo.userId,
                price: price,
                instrument: theInstrument,
                priceType: dict.pricingType.fixedPrice.code,
                bsFlag: dict.tradingDirection.sell.code,
                businessFlag: notSet,
                positionEffect: 0,
                orderTime: null,
                hedgeFlag: dict.hedgeFlag.Speculate.code,
            };

            /**
             * 排除已经消耗完毕的，融资买入仓位
             */
            if (cinstru && cinstru.volume <= 0) {

                uncloseds.remove(x => x === cinstru);
                cinstru = undefined;
            }

            /**
             * 形成订单
             */

            if (cinstru == undefined) {

                basic.businessFlag = 0;
                basic.volume = decided;
                basic.customId = '20cm-sell-' + this.helper.makeToken();
                orders.push(basic);
            }
            else {

                /**
                 * 尚未了结的融资仓位
                 */
                let cvolume = cinstru.volume;

                /**
                 * 尚不足偿还所有的融资仓位，全部作为偿还卖出
                 */
                if (decided <= cvolume) {

                    basic.businessFlag = this.systemTrdEnum.businessFlag.close.code;
                    basic.volume = decided;
                    basic.customId = '20cm-sell-close-return-' + this.helper.makeToken();
                    orders.push(basic);
                }
                else {

                    let first = Object.assign({}, basic);
                    first.businessFlag = this.systemTrdEnum.businessFlag.close.code;
                    first.volume = cvolume;
                    first.customId = '20cm-sell-close-return-' + this.helper.makeToken();
                    orders.push(first);

                    let second = Object.assign({}, basic);
                    second.businessFlag = 0;
                    second.volume = decided - cvolume;
                    second.customId = '20cm-sell-' + this.helper.makeToken();
                    orders.push(second);
                }
            }
        });

        var actual = orders.map(x => x.volume).sum();
        var amount = actual * price;
        var mentions = [

            ['股票', unit.stock.name],
            ['方向', '卖出', 's-color-green'],
            ['价格', price],
            ['期望数量', expected.thousands()],
            ['实际数量', actual.thousands()],
            ['金额', amount.thousands()],
            ['账号摊派', orders.map(item => `${accountNameMap[item.accountId]}/${item.volume}`).join('，')],
        ];

        if (actual <= 0) {

            this.interaction.showError(`${unit.stock.name}，按指定条件无可平持仓！`);
            return;
        }
        
        var message = mentions.map(item => `<div><span>${item[0]}：</span><span class="${item[2] || ''}">${item[1]}</span></div>`).join('');
        this.confirm(this.settings.prompt.msell, message, () => {

            this.log(`manual sell orders are prepared, task id/${unit.taskId}, orders = ${JSON.stringify(orders)}`);
            console.log(orders);
            this.sendOut(orders);
            this.interaction.showSuccess('订单已发送，数量 = ' + orders.length);
        });
    }

    /**
     * @param {Array} orders 
     */
    sendOut(orders) {

        var execSend = (data) => {
            this.renderProcess.send(this.systemEvent.transmitMsg2TradingServer, this.serverFunction.sendOrder, data);
        }

        if (!this.settings) {
            
            orders.forEach(order => { execSend(order); });
            return;
        }

        var { main, imbark, star } = this.settings.spliting;
        var { random, mainMin, mainMax, imbarkMin, imbarkMax, starMin, starMax } = this.settings.random;

        orders.forEach(order => {
            
            let instrument = order.instrument;
            let total = order.volume;
            let is_star = instrument.indexOf('.68') > 0;
            let is_imbark = instrument.indexOf('.30') > 0;
            let maxHands = random ? (is_star ? starMax : is_imbark ? imbarkMax : mainMax) : (is_star ? star : is_imbark ? imbark : main);
            let maxShare = maxHands * 100;
            let minHands = Math.max(1, random ? (is_star ? starMin : is_imbark ? imbarkMin : mainMin) : 1);

            /**
             * 一次性全部打完
             */
            
            if (total <= maxShare) {

                execSend(order);
                return;
            }
            
            let left = total;
            let rounds = [];

            while (left > maxShare) {

                let rshare = random ? this.helper.makeRandomNum(minHands + 1, maxHands - 1, true) * 100 : maxShare;
                rounds.push(rshare);
                left -= rshare;
            }
            
            if (left > 0) {
                rounds.push(left);
            }

            rounds.forEach(vol => {

                let each = this.helper.deepClone(order);
                each.volume = vol;
                each.customId = '20cm-sell-' + this.helper.makeToken();
                execSend(each);
            });
        });
    }

    /**
     * @param {SellUnit} unit 
     */
    floorSell(unit) {

        if (this.hasTick(unit)) {
            this.msell(unit, unit.limits.floor, true, true);
        }
    }

    /**
     * @param {SellUnit} unit 
     */
    ceilingSell(unit) {

        if (this.hasTick(unit)) {
            this.msell(unit, unit.limits.ceiling, true, true);
        }
    }

    /**
     * @param {SellUnit} unit 
     */
    clearWithBuy2(unit) {

        if (this.hasTick(unit)) {

            let buys = unit.tickd.buys;
            if (buys.length < 2 || !(buys[1].price > 0)) {
                return this.interaction.showError('无买二价格');
            }

            this.msell(unit, buys[1].price, true, true);
        }
    }

    /**
     * @param {SellUnit} unit 
     */
    hasTick(unit) {
        
        if (!unit.tickd) {

            this.interaction.showError(`${unit.stock.name}，未获得行情，无法快捷交易。`);
            return false;
        }

        return true;
    }

    /**
     * @param {SellUnit} unit 
     */
    isByVolume(unit) {
        return typeof unit.manual.volume == 'number' && unit.manual.volume > 0;
    }

    /**
     * @param {SellUnit} unit 
     */
    isByPercent(unit) {

        let percent = unit.manual.percent;
        return !this.isByVolume(unit) && typeof percent == 'number' && percent >= 1 && percent <= 100;
    }

    /**
     * @param {SellUnit} unit
     */
    isCheckedOk(unit) {
        
        var message = null;
        var isManual = unit.isManualMode;
        var isProgram = !isManual;

        if (isManual) {

            if (!this.isByVolume(unit) && !this.isByPercent(unit)) {
                message = '委托数量（或买入档位比例），参数值缺失';
            }
        }
        else {

            if (this.helper.isNone(unit.skey)) {
                message = '请选择，策略';
            }
            else if (unit.dynamics.some(x => x.applicable && typeof x.value != 'number')) {
                message = '策略参数缺失';
            }
        }
        
        if (!message) {

            if (this.helper.isNone(unit.boundary.followed)) {
                message = '请选择跟盘价';
            }
            else if (this.helper.isNone(unit.boundary.pos)) {
                message = '请选择限仓比例';
            }
            else if (isProgram && typeof unit.boundary.price != 'number') {
                message = '启动限价，参数值缺失';
            }
            else if (isManual && typeof unit.manual.price != 'number') {
                message = '手动限价，参数值缺失';
            }
        }
        
        if (message) {
            this.interaction.showError(message);
        }

        return !message;
    }

    /**
     * @param {SellUnit} unit 
     */
    start(unit) {
        
        if (!this.isCheckedOk(unit)) {
            return;
        }

        this.confirm(false, `${unit.stock.name}，开始监控？`, () => {
            this.execStart(unit);
        });
    }

    /**
     * @param {SellUnit} unit 
     * @param {Boolean} isHotChange 是否为运行状态下，对任务因子的修改
     */
    execStart(unit, isHotChange = false) {

        if (this.units.filter(x => x.stock.code == unit.stock.code).length >= 2) {

            this.interaction.showError(`${unit.stock.name}，股票已存在，无法启动2个`);
            return;
        }

        var random = this.settings.random;
        var split = this.settings.spliting;
        var sdetail = {

            random: random.random,

            mainMin: random.mainMin,
            mainMax: random.mainMax,
            main: split.main,

            imbarkMin: random.imbarkMin,
            imbarkMax: random.imbarkMax,
            imbark: split.imbark,

            starMin: random.starMin,
            starMax: random.starMax,
            star: split.star,
        };

        var selected = this.getSelectedStrategy(unit);
        var strategyObj = {
            [StrategyParamNames.strategy]: selected ? selected.strategy : null,
        };

        unit.dynamics.forEach(item => {

            if (item.applicable) {
                strategyObj[item.prop] = item.value;
            }
        });

        var defaultsNum = 0;
        var task = new TaskObject({

            id: unit.taskId || null,
            userId: this.userInfo.userId,
            userName: this.userInfo.userName,
            instrument: unit.stock.code,
            instrumentName: null,
            direction: this.systemTrdEnum.tradingDirection.sell.code,
            priceFollowType: unit.boundary.followed,
            orderPrice: unit.boundary.price,
            limitPositionType: unit.boundary.pos,
            strikeBoardStatus: TaskStatus.created,
            supplementVolume: defaultsNum,
            supplementOpen: true,
            splitInterval: defaultsNum,
            splitType: defaultsNum,
            splitDetail: sdetail,
            boardStrategy: strategyObj,
            cancelCondition: null,
            cash: defaultsNum,
            positionPercent: unit.boundary.pos,
            creditFlag: false,
        });

        var cmdCode = isHotChange ? Cm20FunctionCodes.request.modify : Cm20FunctionCodes.request.start;
        this.renderProcess.send(this.systemEvent.transmitMsg2TradingServer, cmdCode, task);
        this.log(`to start/modify a sell unit, hot change/${isHotChange}, task = ${JSON.stringify(task)}`);
        return true;
    }

    /**
     * @param {SellUnit} unit 
     */
    stop(unit) {
        
        this.confirm(false, `${unit.stock.name}，停止监控？`, () => {

            this.log(`to stop a sell unit, task id/${unit.taskId}, stock/${unit.stock.code}/${unit.stock.name}`);
            this.renderProcess.send(this.systemEvent.transmitMsg2TradingServer, Cm20FunctionCodes.request.stop, { id: unit.taskId });
        });
    }

    /**
     * @param {SellUnit} unit 
     */
    setAsVolume(unit, volume) {

        if (typeof volume == 'number' && volume > 0) {

            this.log(`sell unit: to set a number as volume, volume/${volume}, task id/${unit.taskId}, stock/${unit.stock.code}/${unit.stock.name}`);
            unit.manual.volume = volume;
        }
    }

    simplify(amount) {
        return this.helper.simplifyAmount(amount);
    }

    /**
     * @param {SellUnit} unit 
     */
    getSelectedStrategy(unit) {

        var target = this.specializeds.find(x => x.skey == unit.skey);
        if (!target) {
            target = this.normals.find(x => x.skey == unit.skey);
        }

        return target;
    }

    /**
     * @param {SellUnit} unit 
     */
    handleStrategyChange(unit, trigger, value) {
        
        this.alignDynamicState(unit);
        this.handleSomeChange(unit, trigger, value);
    }

    /**
     * @param {SellUnit} unit 
     */
    alignDynamicState(unit) {

        var selected = this.getSelectedStrategy(unit);
        var strategy = this.strategies.find(x => x.code == selected.strategy);
        var shortcut = this.settings.sellShortcuts.find(x => x.strategy == selected.strategy && x.stroke == selected.stroke);

        unit.dynamics.forEach(dnm => {

            let metaInfo = strategy.params.find(x => x.prop == dnm.prop);
            if (metaInfo) {

                dnm.applicable = true;
                dnm.max = metaInfo.max;
                dnm.min = metaInfo.min;
                dnm.step = metaInfo.step;
                dnm.value = shortcut ? (shortcut.data[dnm.prop] || null) : null;
            }
            else {

                dnm.applicable = false;
                dnm.max = 999999999;
                dnm.min = 0;
                dnm.step = 1;
                dnm.value = null;
            }
        });
    }

    /**
     * @param {SellUnit} unit 
     */
    handleSomeChange(unit, trigger, value) {

        if (unit.isManualMode || !unit.isRunning) {
            return;
        }
        
        this.log(`manual change in sell unit: ${unit.taskId}/${unit.stock.code}/${trigger}/${value}`);
        if (this.execStart(unit, true)) {
            this.interaction.showSuccess(`${unit.stock.name}，监控参数变动，已提交。`);
        }
    }

    /**
     * @param {SellUnit} unit 
     */
    handleVolumeChange(unit, trigger, value) {

        if (unit.isManualMode) {

            this.aboutUnit = unit;
            this.unregisterEvent('required-positions');
            this.registerEvent('required-positions', (positions) => { this.setPercentByVolumeChange(positions); }, true);
            this.trigger('ask-4-positions', unit.stock.code);
        } 
        else {
            this.handleSomeChange(unit, trigger, value);
        }
    }

    /**
     * @param {SellUnit} unit 
     * @param {DynamicParam} dynamic 
     */
    showProperParamUnit(unit, dynamic) {

        var selected = this.getSelectedStrategy(unit);
        if (!selected) {
            return dynamic.unit;
        }

        var strategy = this.strategies.find(x => x.code == selected.strategy);
        var paramInfo = strategy.params.find(x => x.prop == dynamic.prop);
        return paramInfo ? paramInfo.unit : dynamic.unit;
    }

    setPercentByVolumeChange(positions) {

        var unit = this.aboutUnit;
        var total = positions.map(x => x.closableVolume).sum();
        var percent = total > 0 ? unit.manual.volume / total : 0;
        unit.manual.percent = Math.ceil(percent * 100);
    }

    /**
     * @param {SellUnit} unit 
     */
    handlePercentChange(unit, trigger, value) {

        if (unit.isManualMode) {

            this.aboutUnit = unit;
            this.unregisterEvent('required-positions');
            this.registerEvent('required-positions', (positions) => { this.setVolumeByPercentChange(positions); }, true);
            this.trigger('ask-4-positions', unit.stock.code);
        } 
        else {
            this.handleSomeChange(unit, trigger, value);
        }
    }

    setVolumeByPercentChange(positions) {

        console.log(positions);
        var unit = this.aboutUnit;
        var percent = unit.manual.percent;
        var total = positions.map(x => x.closableVolume).sum();
        unit.manual.volume = percent >= 100 ? total : this.trimVolume(total * percent / 100);
    }

    /**
     * @param {SellUnit} unit 
     */
    async requestJLRatio(unit) {

        var stock = unit.stock.code;
        var resp = await repo20Cm.queryParamb(stock);
        var { err, errMsg, data } = resp;
        var bizdata = data[stock];
        
        if (err != 0 || !this.helper.isJson(bizdata)) {

            unit.summary.jlb = null;
            return;
        }

        unit.summary.jlb = bizdata.bid_than;

        if (this.hasStartedJLUpdate) {
            return;
        }
        
        this.hasStartedJLUpdate = true;
        setInterval(() => { this.units.forEach(unit => { this.requestJLRatio(unit); }); }, 1000 * 60 * 1);
    }

    subscribeTick(stockCode) {

        if (this.hasListened2TickChange === undefined) {

            this.hasListened2TickChange = true;
            this.standardListen(this.serverEvent.subscribeTickReceived, (...args) => { this.handleTickChange(true, ...args); });
            this.standardListen(this.serverEvent.tickPriceChanged, (...args) => { this.handleTickChange(false, ...args); });
        }

        this.standardSend(this.systemEvent.subscribeTick, [stockCode], this.systemTrdEnum.tickType.tick);
    }

    unsubscribeTick(stockCode) {

        if (this.units.some(x => x.stock.code == stockCode)) {
            return;
        }
        
        this.standardSend(this.systemEvent.unsubscribeTick, [stockCode], this.systemTrdEnum.tickType.tick);
    }

    /**
     * 处理TICK数据变化
     * @param {*} isReceipt 是否为订阅回执
     * @param {*} instrument 该TICK所属合约
     * @param {*} tickType 该TICK数据类型
     * @param {*} tick TICK数据本身
     */
    handleTickChange(isReceipt, instrument, tickType, tick) {

        var target = this.unitsMap[instrument];
        if (!(target instanceof SellUnit)) {
            return;
        }

        if (tickType == this.systemTrdEnum.tickType.tick) {
            target.tickd = new TickData(tick);
        }

        if (target.hasNotSetPrice() && target.tickd) {
            target.updatePrice(target.tickd.latest);
        }

        if (target.hasNotSetManualPrice() && target.tickd) {
            target.updateManualPrice(target.tickd.latest);
        }
    }

    /**
     * @param {SellUnit} unit 
     */
    async requestLimitedPrice(unit) {

        var { code, name } = unit.stock;
        var resp = await repoInstrument.queryPrice(code);
        var { errorCode, errorMsg, data } = resp;
        var { assetType, preClosePrice, lastPrice, lowerLimitPrice, optionType, priceTick, strikePrice, upperLimitPrice, volumeMultiple } = data || {};

        if (errorCode == 0 && data && upperLimitPrice > 0) {

            unit.updateLimits(lowerLimitPrice, upperLimitPrice);
            unit.updatePrice(lastPrice);
            unit.updateManualPrice(lastPrice);
        }
        else {

            unit.updateLimits(0, 9999);
            unit.updatePrice(null);
            unit.updateManualPrice(null);
        }
    }

    handleReconnect() {

        this.units.forEach(item => {

            this.log(`to resub tick in sell view while re-connected, stock/${item.stock.code}`);
            this.subscribeTick(item.stock.code);
        });
    }

    build($container) {

        super.build($container);
        this.createApp();
        this.renderProcess.on(this.systemEvent.tradingServerReestablished, () => { this.handleReconnect(); });
    }
};