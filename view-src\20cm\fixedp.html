<div class="fixedp-view s-full-height">

	<template>

		<label v-if="states.isCreditStock" class="label-credit-flag s-color-red">融</label>

		<div class="condition">

			<span class="ctr-volume">已成：{{ thousands(condition.traded) }} 股</span>
			<br>
			<span class="ctr-volume">挂单：{{ thousands(condition.original) }} 股</span>
			<br>
			<span class="ctr-volume">目标：{{ thousands(condition.target) }} 股</span>
	
		</div>
	
		<div class="ctr-row">
	
			<span class="ctr-label">跟盘价</span>
	
			<el-select v-model="formd.followed" :disabled="isRegistered" class="long-input s-mgl-5 s-mgr-20">
				<el-option v-for="item in priceLevels" :key="item.code" :value="item.code" :label="item.mean"></el-option>
			</el-select>
			
			<span class="ctr-label">限价</span>
	
			<el-tooltip placement="bottom">
	
				<span slot="content">
	
					<span class="s-color-green">
						跌停 = 
						<a @click="setAsPrice(limits.floor)" class="s-cp s-hover-underline">
							{{ typeof limits.floor == 'number' ? limits.floor : '---' }}</a>
					</span>
	
					<span class="s-pdl-10 s-color-red">
						涨停 = 
						<a @click="setAsPrice(limits.ceiling)" class="s-cp s-hover-underline">
							{{ typeof limits.ceiling == 'number' ? limits.ceiling : '---' }}</a>
					</span>
	
				</span>
	
				<el-input-number
					v-model="formd.price"
					:disabled="isRegistered"
					:step="0.01"
					:controls="false"
					size="mini"
					class="medium-input s-mgl-5 s-mgr-20"></el-input-number>
	
			</el-tooltip>
	
		</div>
	
		<div class="ctr-row">
	
			<el-select v-model="formd.strategy" :disabled="isRegistered" @change="handleStrategyChange" style="width: 175px; margin-left: -3px;">
				<el-option v-for="(stra, stra_idx) in strategies" :key="stra_idx" :value="stra.code" :label="stra.mean"></el-option>
			</el-select>
	
			<el-input-number 
				placeholder="毫秒"
				v-model="formd.delay"
				:disabled="isRegistered"
				:min="0"
				:controls="false"
				class="short-input s-mgl-10 s-mgr-10" clearable></el-input-number>
			
			<span class="ctr-label">毫秒</span>
	
			<template v-if="isVolumeStrategy">
	
				<el-input-number
					v-model="formd.volume"
					:disabled="isRegistered"
					:min="100"
					:step="100"
					:controls="false" 
					size="mini"
					class="medium-input s-mgl-10 s-mgr-10"></el-input-number>
	
				<span class="ctr-label">股</span>
	
			</template>
	
			<template v-else>
	
				<el-input-number
					v-model="formd.rate"
					:disabled="isRegistered"
					:min="1"
					:max="100"
					:step="1"
					:controls="false"
					size="mini"
					class="medium-input s-mgl-10 s-mgr-10"></el-input-number>
	
				<span class="ctr-label">%</span>
	
			</template>
	
		</div>
	
		<div class="ctr-row" style="position: relative;">

			<div class="credit-option">
				<label class="s-pdr-5">优先融资</label>
				<el-checkbox :disabled="isRegistered || !states.isCreditStock || !states.supportCreditTrading" 
									v-model="states.isCreditStock && credit.creditBuy"
									@change="handleCreditChange"></el-checkbox>
			</div>
	
			<span class="ctr-label s-pdr-5" style="position: relative; top: -23px;">买入仓位</span>
	
			<span class="choice-box">
		
				<el-radio-group v-model="position.percentage" :disabled="isRegistered">
	
					<el-radio 
						v-for="(pct, pct_idx) in percentages"
						:key="pct_idx" 
						:label="pct.code"
						:class="'percent-choice-' + pct_idx"
						@change="handlePercentChange">{{ pct.mean }}</el-radio>
	
				</el-radio-group>
	
				<span class="abs-amount" :class="!isByAmount() ? 'disabled-ctr' : ''">
	
					<el-input-number 
						v-model="position.amount" 
						:disabled="!isByAmount() || isRegistered"
						:min="0"
						:step="0.1"
						:controls="false"
						class="short-input" clearable></el-input-number>
	
					<label class="s-pdl-5">万</label>
	
				</span>
	
				<span class="user-position" :class="!isByCustomized() ? 'disabled-ctr' : ''">
	
					<el-input-number 
						v-model="position.customized" 
						:disabled="!isByCustomized() || isRegistered"
						:min="1"
						:step="1"
						:controls="false" 
						class="micro-input" clearable></el-input-number>
	
					<label class="s-pdl-5">仓</label>
				</span>
	
			</span>
	
		</div>
	
		<div class="button-row ctr-row s-pdt-10 s-center">
			
			<template v-if="states.isRunning">
				<el-button type="danger" size="small" @click="stop">暂停</el-button>
			</template>
	
			<template v-else>
	
				<el-button type="primary" size="small" @click="start">启动</el-button>
				<el-button type="danger" size="small" @click="truncate">删除</el-button>
	
			</template>
	
			<el-button v-if="isRegistered" type="info" size="small" @click.stop="cancelAll">撤未成</el-button>
			
		</div>
		
	</template>

</div>