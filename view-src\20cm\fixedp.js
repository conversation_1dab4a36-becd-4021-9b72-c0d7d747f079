const { IView } = require('../../component/iview');
const { Entrance, StrategyParamNames, TaskStatus, Definition, UserSetting, TaskObject } = require('./components/objects');
const { Cm20FunctionCodes } = require('../../config/20cm');
const { repoInstrument } = require('../../repository/instrument');
const { NumberMixin } = require('../../mixin/number');
const { DatetimeMixin } = require('../../mixin/date-time');
const { OneInstrument } = require('../../libs/helper-biz');

function makePriceLevels() {

    var levels = [];

    for (let seq = 1; seq <= 10; seq++) {
        if (seq != 0) {
            levels.push(new Definition(seq, '卖' + seq));
        }
    }

    return levels;
}

module.exports = class FixedpView extends IView {

    constructor() {

        super('@20cm/fixedp', true, '定价买入');

        this.priceLevels = makePriceLevels();
        this.strategies = Entrance.makeFixedPriceStrategies();
        this.percentages = Entrance.makePercentages();

        this.consts = {

            powering: 10000,
            delay: 3000,
            volume: 100,

            /**
             * 最大可融资额度，向下折扣（确保额度使用安全性）
             */
            safeDiscount: 0.9,
        };

        this.states = {

            taskId: null,
            instrument: null,
            instrumentName: null,
            isCreditStock: false,
            isRunning: false,
            supportCreditTrading: false,
        };

        this.limits = {

            yesterdayClose: null,
            ceiling: null,
            floor: null,
        };

        this.formd = {

            /** 跟价 */
            followed: this.priceLevels[0].code,
            /** 限价 */
            price: null,
            /** 选中策略 */
            strategy: this.strategies[0].code,
            /** 间隔时间（单位：毫秒） */
            delay: this.consts.delay,
            /** 数量（单位：股） */
            volume: 100,
            /** 数量（单位：%） */
            rate: 50,
        };

        /** 仓位比例设置 */
        this.position = {

            /** 仓位选择 */
            percentage: this.percentages[1].code,
            /** 金额值（按金额时） */
            amount: 0,
            /** 自定义仓比值 */
            customized: null,
        };

        /** 融资选项设置 */
        this.credit = {

            /** 是否使用融资 */
            creditBuy: false,
        };

        this.condition = {

            traded: 0,
            original: 0,
            target: 0,
        };

        this.renderProcess.on('set-as-instrument', (event, supportCreditTrading, stockInfo, isOpenExisting) => { this.setAsInstrument(supportCreditTrading, stockInfo, isOpenExisting); });
        this.renderProcess.on('set-as-settings', (event, settings) => { this.setAsSettings(settings); });
        this.renderProcess.on('task-started', (event, task) => { this.handleTaskStarted(task); });
        this.renderProcess.on('task-changed', (event, task) => { this.handleTaskChanged(task); });
        this.renderProcess.on('task-expired', (event) => { this.closeWin(); });
        this.renderProcess.on('summarized-buys', (event, summary) => { this.handleSummarizedBuy(summary); });

        this.thisWindow.on('move', () => { this.tellLocation(); });
        this.thisWindow.on('resize', () => { this.tellLocation(); });
    }

    closeWin() {
        this.thisWindow.close();
    }

    /**
     * @param {UserSetting} settings
     */
    setAsSettings(settings) {

        var isNewSetting = this.settings == undefined;
        this.settings = settings;

        if (isNewSetting) {

            this.position.percentage = settings.position.percentage;
            this.position.amount = settings.position.amount;
            this.position.customized = settings.position.customized;
            this.credit.creditBuy = !!settings.credit.creditBuy;
        }
    }

    createApp() {

        this.vapp = new Vue({

            el: this.$container.firstElementChild,
            data: {

                priceLevels: this.priceLevels,
                strategies: this.strategies,
                percentages: this.percentages,
                states: this.states,
                limits: this.limits,
                formd: this.formd,
                position: this.position,
                credit: this.credit,
                condition: this.condition,
            },
            mixins: [NumberMixin, DatetimeMixin],
            computed: {

                isVolumeStrategy: () => {
                    return this.isVolumeStrategy(); 
                },

                isRegistered: () => {
                    return this.helper.isNotNone(this.states.taskId);
                },
            },
            methods: this.helper.fakeVueInsMethod(this, [

                this.decidePriceColorClass,
                this.setAsPrice,
                this.precisePrice,
                this.handleStrategyChange,
                this.handlePercentChange,
                this.handleCreditChange,
                this.isByAmount,
                this.isByCustomized,
                this.start,
                this.stop,
                this.truncate,
                this.cancelAll,
            ]),
        });

        this.vapp.$nextTick(() => {
            //
        });
    }

    /**
     * @param {Number} price 
     */
    setAsPrice(price) {
        
        if (price > 0) {
            this.formd.price = Number(price.toFixed(2));
        }
    }

    isTaskRunning(status) {
        return status == TaskStatus.started || status == TaskStatus.ordered;
    }

    isTaskStopped(status) {
        return status == TaskStatus.stopped;
    }

    handleStrategyChange() {
        // todo
    }

    handlePercentChange() {
        // todo
    }

    handleCreditChange() {
        // todo
    }

    /**
     * @param {TaskObject} task
     */
    handleTaskStarted(task) {
        this.updateTask(task);
    }

    /**
     * @param {TaskObject} task
     */
    handleTaskChanged(task) {
        this.updateTask(task);
    }

    /**
     * @param {TaskObject} task
     */
    updateTask(task) {

        var {

            strikeBoardStatus, 
            priceFollowType, 
            orderPrice, 
            boardStrategy, 
            limitPositionType, 
            positionPercent, 
            cash,
            creditFlag,

        } = task;

        /**
         * 更新任务的状态
         */
        this.states.isRunning = this.isTaskRunning(strikeBoardStatus);
        this.states.taskId = task.id;

        /* 目标量 */
        this.condition.target = task.targetVolume;
        /* 是否启用了融资买入 */
        this.credit.creditBuy = creditFlag == true;

        var formd = this.formd;
        formd.followed = priceFollowType;
        formd.price = typeof orderPrice == 'number' ? Number(orderPrice.toFixed(2)) : orderPrice;
        formd.strategy = boardStrategy.strategyType;
        formd.delay = boardStrategy.strategyDelayTime;

        if (this.isVolumeStrategy()) {
            formd.volume = boardStrategy.strategyVolume;
        }

        if (this.isRateStrategy()) {
            formd.rate = boardStrategy.strategyRate;
        }

        var position = this.position;
        position.percentage = limitPositionType;

        if (cash > 0) {
            position.amount = Number((cash / this.consts.powering).toFixed(2));
        }
        
        if (positionPercent > 0) {
            position.customized = positionPercent;
        }
    }

    getSelectedPosition() {
        return this.percentages.find(x => x.code == this.position.percentage);
    }

    isVolumeStrategy() {
        return this.formd.strategy == this.strategies[0].code;
    }

    isRateStrategy() {
        return !this.isVolumeStrategy();
    }

    isByAmount() {

        var selected = this.getSelectedPosition();
        return selected && selected.isByAmount;
    }

    isByCustomized() {

        var selected = this.getSelectedPosition();
        return selected && selected.isByCustomized;
    }

    /**
     * 将价格格式化为适合的精度
     * @param {Number} price 
     */
    decidePriceColorClass(price) {

        if (price == 0 || typeof price != 'number') {
            return '';
        }
        
        let yc = this.limits.yesterdayClose;
        if (yc == 0 || yc == null) {
            return '';
        }
        else {
            return price > yc ? 's-color-red' : price < yc ? 's-color-green' : '';
        }
    }

    /**
     * 将价格格式化为适合的精度
     * @param {Number} price 
     */
    precisePrice(price) {
        return typeof price == 'number' ? price.toFixed(2) : price;
    }

    /**
     * @param {Boolean} supportCreditTrading
     * @param {OneInstrument} stockInfo
     * @param {Boolean} isOpenExisting 
     */
    setAsInstrument(supportCreditTrading, stockInfo, isOpenExisting) {

        var { instrument, instrumentName, creditBuy } = stockInfo;
        var last = this.states.instrument;

        /**
         * 合约未变更，无需订阅
         */
        if (instrument == last) {
            return;
        }

        this.setWindowTitle(instrumentName + ' - 定价买入', false);
        this.resetProperties();
        
        var states = this.states;
        states.supportCreditTrading = !!supportCreditTrading;
        states.instrument = instrument;
        states.instrumentName = instrumentName;
        states.isCreditStock = !!creditBuy;
        this.requestLimitedPrice(instrument, instrumentName, isOpenExisting);
    }

    resetProperties() {

        var states = this.states;
        states.instrument = null;
        states.instrumentName = null;
        states.isCreditStock = false;
    }

    async requestLimitedPrice(instrument, instrumentName, isOpenExisting) {

        var resp = await repoInstrument.queryPrice(instrument);
        var { errorCode, errorMsg, data } = resp;
        var { assetType, preClosePrice, lastPrice, lowerLimitPrice, optionType, priceTick, strikePrice, upperLimitPrice, volumeMultiple } = data || {};
        var limits = this.limits;
        var formd = this.formd;

        if (errorCode == 0 && data && upperLimitPrice > 0) {

            limits.yesterdayClose = preClosePrice;
            limits.ceiling = upperLimitPrice;
            limits.floor = lowerLimitPrice;
            
            if (!isOpenExisting) {
                formd.price = lastPrice;
            }
        }
        else {

            limits.yesterdayClose = 0;
            limits.ceiling = 9999;
            limits.floor = 0;
            
            if (!isOpenExisting) {
                formd.price = null;
            }

            this.interaction.showError(`${instrumentName}，涨跌停价格未获得：${errorCode}/${errorMsg}`);
        }
    }

    isCheckedOk() {
        
        var message = null;
        var formd = this.formd;
        var position = this.position;

        if (this.helper.isNone(formd.followed)) {
            message = '请选择跟盘价';
        }
        else if (typeof formd.price != 'number' || formd.price <= 0) {
            message = '限价，参数值无效';
        }
        else if (this.helper.isNone(formd.strategy)) {
            message = '请选择，策略';
        }
        else if (typeof formd.delay != 'number' || formd.delay < this.consts.delay) {
            message = `间隔时间，参数值无效（最小为${this.consts.delay}毫秒）`;
        }
        else if (this.isVolumeStrategy() && (typeof formd.volume != 'number' || formd.volume < this.consts.volume)) {
            message = `下单数量，参数值无效（最小为${this.consts.volume}股）`;
        }
        else if (this.isRateStrategy() && (typeof formd.rate != 'number' || formd.rate <= 0 || formd.rate > 100)) {
            message = '下单比例，参数值无效';
        }
        else if (this.helper.isNone(position.percentage)) {
            message = '请选择限仓比例';
        }
        else if (this.isByAmount() && (typeof position.amount != 'number' || position.amount <= 0)) {
            message = '金额值，参数值缺失';
        }
        else if (this.isByCustomized() && (typeof position.customized != 'number' || position.customized < 1)) {
            message = '自定义仓位，参数值缺失';
        }

        if (message) {
            this.interaction.showError(message);
        }

        return !message;
    }

    start() {

        if (!this.isCheckedOk()) {
            return;
        }

        this.confirm(false, `${this.states.instrumentName}，启动买入？`, () => {

            if (this.execStart()) {
                this.interaction.showSuccess(`${this.states.instrumentName}，已启动`);
            }
        });
    }

    execStart() {

        var split = this.settings.spliting;
        var sdetail = {

            main: split.main,
            imbark: split.imbark,
            star: split.star,
        };

        var formd = this.formd;
        var byAmount = this.isByAmount();
        var strategyObj = this.isVolumeStrategy() ? 
        {
            [StrategyParamNames.strategy]: formd.strategy,
            [StrategyParamNames.delay]: formd.delay,
            [StrategyParamNames.volume]: formd.volume,
        } :
        {
            [StrategyParamNames.strategy]: formd.strategy,
            [StrategyParamNames.delay]: formd.delay,
            [StrategyParamNames.rate]: formd.rate,
        };

        var states = this.states;
        var position = this.position;
        var task = new TaskObject({

            id: states.taskId || null,
            userId: this.userInfo.userId,
            userName: this.userInfo.userName,
            instrument: states.instrument,
            instrumentName: null,
            direction: this.systemTrdEnum.tradingDirection.buy.code,
            priceFollowType: formd.followed,
            orderPrice: formd.price,
            strikeBoardStatus: TaskStatus.created,
            supplementVolume: 0,
            supplementOpen: false,
            splitInterval: 0,
            splitType: 0,
            splitDetail: sdetail,
            boardStrategy: strategyObj,
            cancelCondition: {},

            limitPositionType: position.percentage,
            cash: byAmount ? position.amount * this.consts.powering : 0,
            positionPercent: byAmount ? 0 : position.customized,
            creditFlag: this.credit.creditBuy,
        });

        this.log(`to start a fixed-price buy, task = ${JSON.stringify(task)}`);
        this.renderProcess.send(this.systemEvent.transmitMsg2TradingServer, Cm20FunctionCodes.request.start, task);

        return true;
    }

    stop() {

        var { taskId, instrument, instrumentName } = this.states;
        this.confirm(false, `${instrumentName}，停止监控？`, () => {

            this.log(`to stop a fixed-price buy, task id/${taskId}, stock/${instrument}/${instrumentName}`);
            this.renderProcess.send(this.systemEvent.transmitMsg2TradingServer, Cm20FunctionCodes.request.stop, { id: taskId });
        });
    }

    truncate() {

        var { instrument, instrumentName, taskId } = this.states;
        this.confirm(false, `${instrumentName}，删除监控？`, () => {

            let hasId = this.helper.isNotNone(taskId);

            if (hasId) {

                this.log(`to truncate a fixed-price buy, task id/${taskId}, stock/${instrument}/${instrumentName}`);
                this.renderProcess.send(this.systemEvent.transmitMsg2TradingServer, Cm20FunctionCodes.request.delete, { id: taskId });
            }
            else {
                this.closeWin();
            }
        });
    }

    cancelAll() {

        var { taskId, instrument, instrumentName } = this.states;
        var task = () => {

            this.log(`to cancel a fixed-price buy, stock/${instrument}/${instrumentName}`);
            this.renderProcess.send(this.systemEvent.transmitMsg2TradingServer, Cm20FunctionCodes.request.cancel, { id: taskId });
        };
        
        if (this.settings) {

            this.confirm(this.settings.prompt.mcancel, `${instrumentName}，撤销未成？`, () => {
                task();
            });
        }
        else {
            task();
        }
    }

    handleSummarizedBuy(summary) {

        var { traded, original } = summary;
        this.condition.traded = traded;
        this.condition.original = original;
    }

    /**
     * @param {String} message 
     */
    log(message) {
        this.loggerTrading.info('ztlog:' + message);
    }

    confirm(isRequired, message, callback) {

        if (isRequired) {
            
            this.interaction.showConfirm({

                title: '操作确认',
                message: message,
                confirmed: () => { callback(); },
            });
        }
        else {
            callback();
        }
    }

    handleReconnect() {
        //
    }

    tellLocation() {

        var { taskId } = this.states;

        if (!taskId) {
            return;
        }

        var pos = this.thisWindow.getPosition();
        var size = this.thisWindow.getSize();
        this.thisWindow.webContents.emit('location-change', taskId, ...pos, ...size);
    }

    tellReady() {
        this.thisWindow.webContents.emit('is-ready');
    }

    handleWinSizeChange() {
        //
    }

    build($container) {

        super.build($container);
        this.createApp();
        this.renderProcess.on(this.systemEvent.tradingServerReestablished, () => { this.handleReconnect(); });
        this.tellReady();
        this.tellLocation();
        this.lisen2WinSizeChange(this.handleWinSizeChange.bind(this), 20);
    }
};