.win-title {

	margin-right: 50px;
	font-size: 16px;
	font-weight: bold;
}

.el-select-dropdown .el-scrollbar .el-select-dropdown__wrap {
	max-height: 160px !important;
}

.fixedp-view {

	box-sizing: border-box;
	padding: 10px 20px;

	.label-credit-flag {

		position: absolute;
		top: 8px;
		left: 200px;
		z-index: 999;
		font-size: 16px;
	}

	.condition {

		position: absolute;
		right: 10px;
		top: 40px;

		.ctr-volume {

			display: inline-block;
			width: 110px;
			line-height: 18px;
		}
	}

	.ctr-row {

		&:not(:first-child) {
			margin-top: 10px;
		}
	}

	input {
		text-align: left !important;
	}

	.el-input__inner {

		height: 24px;
		line-height: 24px;
		padding-left: 5px !important;
		padding-right: 5px !important;
	}

	.micro-input {

		width: 30px;
		border-radius: 2px;
	}

	.short-input {

		width: 55px;
		border-radius: 2px;
	}

	.medium-input {

		width: 70px;
		border-radius: 2px;
	}

	.long-input {

		width: 80px;
		border-radius: 2px;
	}

	.longest-input {

		width: 150px;
		border-radius: 2px;
	}

	.shine-color {
		color: #8CB5ED;
	}

	.disabled-ctr {
		
		> label {
			color: #8393AB;
		}
	}

	.credit-option {

		position: absolute;
		right: 60px;
		top: 2px;
		z-index: 1;
	}

	.choice-box {

		display: inline-block;
		width: 310px;

		.el-radio {

			margin-right: 32px;
			line-height: 24px;
		}

		.percent-choice-4 {
			margin-right: 99px;
		}

		.abs-amount {

			display: block;
			width: 80px;
    		margin-top: -23px;
			margin-left: 60px;
		}

		.user-position {
			
			display: block;
			width: 60px;
			margin-top: -25px;
			margin-left: 225px;
		}
	}

	.button-row {
		border-top: 1px solid #333;
	}
}