<div class="v20cm v20cm-history themed-bg-harder" v-show="dialog.visible">
	<el-dialog width="520px"
				title="策略历史"
				class="dialog-20cm-history"
				:visible="dialog.visible"
				:close-on-click-modal="false"
				:close-on-press-escape="false"
				:show-close="true"
				@close="close">

		<template>
			<div class="buy-histories">
				<div v-for="(unit, unit_idx) in units" 
					:key="unit_idx"
					class="identity-history-unit stock-unit"
					:class="isFocused(unit) ? 'focused' : ''"
					@click="setAsCurrent(unit)">
			
					<div class="title typical-header">
						<span>{{ unit.stock.name }} ({{ unit.stock.code }})</span>
					</div>

					<div class="unit-body" v-show="unit.expanded" style="padding-bottom: 0;">
				
						<div class="ctr-row s-mgt-5 s-mgb-5">
				
							<label class="shine-color">策略</label>
				
							<el-select :disabled="true" v-model="unit.skey" class="s-mgl-15" style="width: 175px;">
								<el-option-group v-for="group in strategyGroups" :key="group.name" :label="group.name">
									<el-option v-for="(stra, stra_idx) in group.strategies" :key="stra_idx" :value="stra.skey" :label="stra.name"></el-option>
								</el-option-group>
							</el-select>
				
						</div>
				
						<div class="ctr-row" style="padding: 2px 0px 0px 40px;">
				
							<span v-for="(dyna, dyna_idx) in unit.dynamics" class="ctr-dynamic" :class="dyna.applicable ? '' : 'disabled-ctr'">
								<label class="input-name s-pdr-5">{{ dyna.label }}</label>
								<el-input-number v-model="dyna.value"
												:disabled="true"
												:min="dyna.min"
												:max="dyna.max"
												:step="dyna.step"
												:controls="false" 
												class="short-input"
												clearable></el-input-number>
								<label class="input-unit s-pdl-5 s-pdr-20">{{ showProperParamUnit(unit, dyna) }}</label>
							</span>
				
						</div>
				
						<div class="ctr-row">
				
							<label class="shine-color">撤单条件</label>
			
							<a @click="unit.innerExpanded = !unit.innerExpanded;">
								<i class="shine-color" :class="unit.innerExpanded ? 'el-icon-arrow-up' : 'el-icon-arrow-right'"></i>
							</a>
			
							<!-- <template v-if="unit.innerExpanded">
								<span
									class="themed-bg" 
									style="display: inline-block; height: 1px; width: 333px; position: relative; top: -2px; left: 5px;"></span>
							</template> -->
			
						</div>
				
						<div class="ctr-row" v-show="unit.innerExpanded" style="padding-top: 0;">
				
							<span 
								v-for="(thres, thres_idx) in unit.threses" 
								:key="thres_idx" 
								class="ctr-thres" 
								:class="'col-' + (thres_idx % 2 > 0 ? 'second' : 'first') + ' thres-option-' + thres.checkedProp">
				
								<el-checkbox 
									:disabled="true"
									v-model="thres.isOn"
									class="choice-check"
									:class="(thres.members[0].label.length == 4 && thres_idx % 2 > 0 ? 'shorter' : '') + (thres.isOn ? '' : ' unchecked')">
									{{ thres.members[0].label }}</el-checkbox>

								<span v-for="(memb, memb_idx) in thres.members" :key="memb_idx">
						
									<el-input-number
										:disabled="true"
										v-model="memb.value"
										:min="0"
										:step="1"
										:controls="memb.hasButton"
										controls-position="right" 
										size="mini"
										class="medium-input s-mgl-10"></el-input-number>
			
									<label class="input-unit s-pdl-5">{{ memb.unit }}</label>
									
								</span>
			
							</span>
				
						</div>
			
						<div class="themed-bg s-mgt-5" style="height: 1px;"></div>
				
						<div class="ctr-row">
							<span class="s-pull-right" style="margin-top: -25px; margin-right: 10px;">
								<label class="s-pdr-5">发单间隙</label>
								<el-input-number 
									:disabled="true"
									placeholder="毫秒"
									v-model="unit.manual.fdjx"
									:min="0" 
									:controls="false" 
									class="short-input" clearable></el-input-number>
							</span>
						</div>
				
						<div class="ctr-row" style="position: relative; padding-top: 10px; height: 60px; padding-bottom: 0;">
				
							<div class="history-credit-option">
								<label class="s-pdr-5">优先融资</label>
								<el-checkbox :disabled="true" v-model="unit.credit.creditBuy"></el-checkbox>
							</div>

							<span class="choice-box">
				
								<el-radio-group v-model="unit.position.percentage" :disabled="true">
				
									<el-radio 
										v-for="(pct, pct_idx) in percentages"
										:key="pct_idx" 
										:label="pct.code"
										:class="'percent-choice-' + pct_idx">{{ pct.mean }}</el-radio>
				
								</el-radio-group>
			
								<span class="abs-amount" :class="!isByAmount(unit) ? 'disabled-ctr' : ''">
			
									<el-input-number 
										:disabled="true"
										v-model="unit.position.amount" 
										:disabled="!isByAmount(unit)"
										:min="0"
										:step="0.01"
										:controls="false" 
										class="short-input" clearable></el-input-number>
			
									<label class="s-pdl-5">万</label>
			
								</span>
				
								<span class="user-position" :class="!isByCustomized(unit) ? 'disabled-ctr' : ''">
			
									<el-input-number 
										:disabled="true"
										v-model="unit.position.customized" 
										:disabled="!isByCustomized(unit)" 
										:min="1" 
										:controls="false" 
										class="micro-input" clearable></el-input-number>
			
									<label class="s-pdl-5">仓</label>
								</span>
				
							</span>
				
						</div>
			
						<div class="row-user-price">
							<span class="s-pull-right" style="position: relative; right: 10px; top: -30px;">
			
								<label class="s-pdr-5">指定价格</label>

								<el-input-number 
									:disabled="true"
									v-model="unit.price"
									:min="unit.floor" 
									:max="unit.ceiling"
									:step="0.01" 
									:controls="false" 
									class="short-input" clearable></el-input-number>

							</span>
						</div>
			
						<div class="row-protect">
							<span class="s-pull-right" style="position: relative; right: -98px; top: -60px;">
								<label class="s-pdr-5">分拆保护</label>
								<el-select :disabled="true" v-model="unit.manual.protect" class="short-input">
									<el-option v-for="(item, item_idx) in protections" :key="item_idx" :value="item.code" :label="item.mean"></el-option>
								</el-select>
							</span>
						</div>
			
					</div>
			
				</div>
			</div>
			<template v-if="units.length == 0">
				<div class="no-data-notice">
					<div class="displaying">
						<i class="el-icon-moon"></i> 没有已完成策略
					</div>
				</div>
			</template>
		</template>
	</el-dialog>
</div>
