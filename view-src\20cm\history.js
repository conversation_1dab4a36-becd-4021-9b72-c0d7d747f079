const { BaseView } = require('./base-view');
const { NumberMixin } = require('../../mixin/number');
const { 

    Entrance,
    TaskObject, 
    BuyUnit, 
    Strategy, 
    StrategyItem, 
    DynamicParam, 
    Definition, 
    UserSetting,
    
} = require('./components/objects');

const { repo20Cm } = require('../../repository/20cm');
const { repoInstrument } = require('../../repository/instrument');
const { Biz<PERSON>elper } = require('../../libs/helper-biz');

/**
 * @returns {Array<BuyUnit>}
 */
function makeUnits() {
    return [];
}

/**
 * @returns {Array<Strategy>}
 */
function makeStrategies() {
    return [];
}

/**
 * @returns {Array<StrategyItem>}
 */
function makeStrategyItems() {
    return [];
}

module.exports = class HistoryView extends BaseView {

    constructor(view_name, is_standalone_window) {

        super(view_name, is_standalone_window, '策略历史');

        this.dialog = {
            visible: true,
        };

        this.consts = {
            powering: 10000,
        };

        /** 原始策略 */
        this.strategies = makeStrategies();
        /** 快捷策略 */
        this.specializeds = makeStrategyItems();
        /** 普通策略 */
        this.normals = makeStrategyItems();
        this.percentages = Entrance.makePercentages();
        this.protections = [

            new Definition(1, 1),
            new Definition(2, 2),
            new Definition(3, 3),
        ];

        this.units = makeUnits();
        this.unitsMap = {};
        this.states = {

            focused: this.units.length > 0 ? this.units[0] : null,
        };
    }

    createApp() {

        this.historyApp = new Vue({

            el: this.$container.firstElementChild,
            data : {

                dialog: this.dialog,
                units: this.units,
                strategyGroups: [

                    { name: '快捷策略', strategies: this.specializeds },
                    { name: '普通策略', strategies: this.normals },
                ],

                percentages: this.percentages,
                protections: this.protections,
                states: this.states,
            },
            mixins: [NumberMixin],
            methods: this.helper.fakeVueInsMethod(this, [

                this.close,
                this.isFocused,
                this.setAsCurrent,
                this.isByAmount,
                this.isByCustomized,
                this.showProperParamUnit,
            ]),
        });
    }

    /**
     * @param {BuyUnit} unit 
     */
    chooseKey(unit) {
        return this.helper.isNotNone(unit.taskId) ? unit.taskId : unit.stock.code;
    }

    /**
     * @param {UserSetting} settings 
     */
    setAsSettings(settings) {

        this.settings = settings;
        this.loadStrategies();
    }

    loadStrategies() {

        var strategies = Entrance.makeBuyStrategies();
        var shortcuts = this.settings.buyShortcuts;
        this.strategies.refill(strategies);
        this.specializeds.clear();

        /**
         * 该种写法，规避因为发生过策略删除，导致旧的配置数据引起报错
         */
        shortcuts.forEach(x => {

            let matched = strategies.find(y => y.code == x.strategy);
            if (matched == undefined) {
                return;
            }

            let sitem = new StrategyItem({
                
                key: `${x.stroke}-${x.strategy}`,
                stroke: x.stroke,
                strategy: x.strategy, 
                name: `${x.stroke} - ${matched.mean}`,
            });

            this.specializeds.push(sitem);
        });

        this.normals.refill(strategies.filter(x => !shortcuts.some(y => y.strategy == x.code)).map(x => {

            return new StrategyItem({
                
                key: x.code,
                stroke: undefined,
                strategy: x.code,
                name: x.mean,
            });
        }));
    }

    /**
     * @param {BuyUnit} unit 
     */
    async requestLimitedPrice(unit) {

        var resp = await repoInstrument.queryPrice(unit.stock.code);
        var { errorCode, errorMsg, data } = resp;
        var { assetType, preClosePrice, lastPrice, lowerLimitPrice, optionType, priceTick, strikePrice, upperLimitPrice, volumeMultiple } = data || {};

        if (errorCode == 0 && data && upperLimitPrice > 0) {

            unit.updateLimits(lowerLimitPrice, upperLimitPrice);
            unit.updateLatest(lastPrice);
        }
    }

    /**
     * @param {BuyUnit} unit 
     */
    getSelectedPosition(unit) {
        return this.percentages.find(x => x.code == unit.position.percentage);
    }

    close() {

        this.dialog.visible = false;
        this.setWindowAsBlockedWaiting(false);
    }

    /**
     * @param {BuyUnit} unit 
     */
    isFocused(unit) {
        return unit === this.states.focused;
    }

    /**
     * @param {BuyUnit} unit 
     */
    setAsCurrent(unit) {

        if (this.isFocused(unit)) {
            return;
        }

        this.states.focused = unit;
    }

    /**
     * @param {BuyUnit} unit 
     */
    isByAmount(unit) {

        let selected = this.getSelectedPosition(unit);
        return selected && selected.isByAmount;
    }

    /**
     * @param {BuyUnit} unit 
     */
    isByCustomized(unit) {

        let selected = this.getSelectedPosition(unit);
        return selected && selected.isByCustomized;
    }

    /**
     * @param {BuyUnit} unit 
     */
    getSelectedStrategy(unit) {

        var target = this.specializeds.find(x => x.skey == unit.skey);
        if (!target) {
            target = this.normals.find(x => x.skey == unit.skey);
        }

        return target;
    }

    /**
     * @param {BuyUnit} unit 
     * @param {DynamicParam} dynamic 
     */
    showProperParamUnit(unit, dynamic) {

        var selected = this.getSelectedStrategy(unit);
        if (!selected) {
            return dynamic.unit;
        }

        var strategy = this.strategies.find(x => x.code == selected.strategy);
        var paramInfo = strategy.params.find(x => x.prop == dynamic.prop);
        return paramInfo ? paramInfo.unit : dynamic.unit;
    }

    /**
     * @param {StrategyItem} straItem 
     * @param {String} paramName 
     */
    retrieveEffi(straItem, paramName) {

        var strag = this.strategies.find(x => straItem && x.code == straItem.strategy);
        var efficiency = 1;

        if (strag) {

            let meta = strag.params.find(x => x.prop == paramName);
            efficiency = meta ? meta.coefficiency : 1;
        }

        return efficiency;
    }

    /**
     * @param {BuyUnit} unit 
     * @param {TaskObject} task 
     */
    alignDynamicState(unit, task) {

        var selected = this.getSelectedStrategy(unit);
        var hasSelected = !!selected;
        var strategy = hasSelected ? this.strategies.find(x => x.code == selected.strategy) : undefined;

        unit.dynamics.forEach(dnm => {

            let propName = dnm.prop;
            let metaInfo = hasSelected ? strategy.params.find(x => x.prop == propName) : undefined;
            
            if (metaInfo) {

                dnm.applicable = true;
                dnm.max = metaInfo.max;
                dnm.min = metaInfo.min;
                dnm.step = metaInfo.step;
                dnm.value = null;

                let propVal = task.boardStrategy[propName];
                let efficiency = this.retrieveEffi(selected, propName);
                dnm.value = this.helper.safeDevide(propVal, efficiency);
            }
            else {

                dnm.applicable = false;
                dnm.max = 999999999;
                dnm.min = 0;
                dnm.step = 1;
                dnm.value = null;
            }
        });
    }
    
    /**
     * @param {Array<TaskObject>} tasks
     */
    fillTasks(tasks) {

        this.units.clear();

        tasks.forEach(task => {

            let status = task.strikeBoardStatus;
            let unit = new BuyUnit(true, task.instrument, task.instrumentName, task.id);
            let stockInfo = BizHelper.pick(task.instrument);
            unit.setCreditBuyFlag(stockInfo.creditBuy);

            this.units.push(unit);
            this.unitsMap[this.chooseKey(unit)] = unit;

            unit.updateRunning(TaskObject.isRunning(status));
            unit.updateSupplemented(TaskObject.isSupplemented(status));
            unit.updatePrice(task.orderPrice);

            let strategyId = task.boardStrategy.strategyType;
            let selected = this.specializeds.find(x => x.strategy == strategyId);
            if (!selected) {
                selected = this.normals.find(x => x.strategy == strategyId);
            }
            
            if (selected) {
                unit.skey = selected.skey;
            }
            
            unit.supplement.checked = task.supplementOpen;
            unit.supplement.value = task.supplementVolume;
            unit.manual.fdjx = task.splitInterval;
            unit.manual.protect = task.splitType;
            unit.credit.creditBuy = task.creditFlag == true;
            unit.position.percentage = task.limitPositionType;

            if (task.cash > 0) {
                unit.position.amount = Number((task.cash / this.consts.powering).toFixed(2));
            }

            if (task.positionPercent > 0) {
                unit.position.customized = task.positionPercent;
            }            

            let ccd = task.cancelCondition;
            unit.threses.forEach(thr => {

                thr.isOn = ccd[thr.checkedProp] || false;
                thr.members.forEach(memb => { memb.value = ccd[memb.prop]; });
            });
            
            this.alignDynamicState(unit, task);
            this.requestLimitedPrice(unit);
        });
    }

    async requestTasks() {

        var resp = await repo20Cm.queryFinishedTasks();
        var { errorCode, errorMsg, data } = resp;

        if (errorCode != 0) {
            return this.interaction.showError(`已完成策略，查询错误：${errorMsg}`);
        }

        var tasks = data || [];
        this.fillTasks(tasks);
        this.setAsCurrent(this.units[0]);
    }

    /**
     * @param {HTMLElement} $container 
     */
    build($container) {

        super.build($container);
        this.createApp();
        this.requestTasks();
    }
};