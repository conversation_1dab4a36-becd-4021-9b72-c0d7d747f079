<div class="v20cm v20cm-setting themed-bg-harder" v-show="dialog.visible">
	<el-dialog width="1100px"
				title="交易默认设置"
				class="dialog-20cm-setting"
				:visible="dialog.visible"
				:close-on-click-modal="false"
				:close-on-press-escape="false"
				:show-close="true"
				@close="close">

		<template>
			<table class="layout-table s-full-size" cellpadding="0" cellspacing="0">
				<colgroup>
					<col width="45%" />
					<col width="55%" />
				</colgroup>
				<tbody>
					<tr class="d-row-body">
						<td class="cell-of-panel cell-main-left">
							<div class="group-panel s-full-height">
		
								<div class="unit-title">买策略</div>
		
								<div class="unit-panel s-pdt-5 s-pdb-5" style="position: relative;">
									
									<div class="credit-option">
										<label class="s-pdr-5">优先融资</label>
										<el-checkbox v-model="credit.creditBuy"></el-checkbox>
									</div>

									<div class="ctr-row">
		
										<span class="choice-box">
			
											<el-radio-group v-model="position.percentage">
							
												<el-radio
													v-for="(pct, pct_idx) in percentages"
													:key="pct_idx" 
													:label="pct.code"
													:class="'percent-choice-' + pct_idx">{{ pct.mean }}</el-radio>
							
											</el-radio-group>
						
											<span class="abs-amount">
												<el-input-number v-model="position.amount" :min="0" :controls="false" class="short-input"></el-input-number>
												<label class="s-pdl-5">万</label>
											</span>
							
											<span class="user-position">
												<el-input-number v-model="position.customized" :min="1" :controls="false" class="micro-input"></el-input-number>
												<label class="s-pdl-5">仓</label>
											</span>
							
										</span>
		
									</div>
		
								</div>
		
								<div class="unit-panel">
									
									<div class="plain-title" style="margin-left: -23px;">
										<span class="shine-color">默认撤单策略</span>
										<el-checkbox class="s-mgl-10" v-model="toggler.show">以下选项默认展开</el-checkbox>
									</div>
		
									<div class="ctr-row">
			
										<span v-for="(thres, thres_idx) in threses" 
											  :key="thres_idx" 
											  class="ctr-thres" 
											  :class="'col-' + (thres_idx % 2 > 0 ? 'second' : 'first') + ' thres-option-' + thres.checkedProp"
											  :style="{ width: (thres_idx == 0 ? 50 : thres_idx == 1 ? 50 : thres_idx == 2 ? 100 : 50) + '%' }">
							
											<el-checkbox v-model="thres.isOn" 
														class="choice-check" 
														:class="thres.members[0].label.length == 4 && thres_idx % 2 > 0 ? 'shorter' : ''">
														{{ thres.members[0].label }}</el-checkbox>

											<span v-for="(memb, memb_idx) in thres.members" :key="memb_idx">
						
												<el-input-number
													v-model="memb.value"
													:min="memb.min"
													:max="memb.max"
													:step="memb.step"
													:controls="memb.hasButton"
													controls-position="right" 
													size="mini"
													class="medium-input s-mgl-10"></el-input-number>
						
												<label class="thres-unit s-pdl-5">{{ memb.unit }}</label>
												
											</span>
						
										</span>
							
									</div>
		
								</div>

								<div class="unit-panel shortcut-panel bottom-bordered" style="padding-left: 12px;">
		
									<div class="plain-title">
										<label class="prop-name shine-color">快捷策略</label>
										<el-tooltip content="添加策略" placement="right">
											<i class="el-icon-plus s-mgl-5" @click="addShortcutConfig4Buy"></i>
										</el-tooltip>
										<label class="prop-name shine-color s-mgl-20">默认选中第</label>
										<el-input v-model.number="buyOptions.defaultSeq" class="short-input s-mgl-5 s-mgr-5"></el-input>
										<label class="prop-name shine-color">个</label>
									</div>
									
									<div class="shortcut-items">
	
										<div v-for="(scc, scc_idx) in buyShortcuts" :key="scc_idx" class="ctr-row">
		
											<el-input v-model="sequencialPickBuy(scc_idx)" :disabled="true" class="short-input"></el-input>

											<el-select v-model="scc.strategy" class="longest-input s-mgl-5" placeholder="请选择策略" clearable>
												<el-option v-for="strategy in buyStrategies" :key="strategy.code" :label="strategy.mean" :value="strategy.code"></el-option>
											</el-select>

											<template v-if="scc.strategy">
												<span v-for="(item, item_idx) in mapBuyParams(scc.strategy)" :key="item_idx">
													<el-input-number v-model="scc.data[item.prop]" :min="item.min" :max="item.max" :step="item.step" :controls="false" class="short-input s-mgl-5"></el-input-number>
													<label class="prop-name fixed-unit-label s-pdl-5">{{ item.unit }}</label>
												</span>
											</template>
											
											<span class="shortcut-remover s-pull-right s-color-red">
												<el-tooltip content="删除" placement="right">
													<i class="el-icon-remove" @click="removeBuyShortcutConfig(scc_idx)"></i>
												</el-tooltip>
											</span>
			
										</div>
	
									</div>
									
								</div>
		
							</div>
						</td>
						<td class="cell-of-panel cell-main-right">
							<div class="group-panel s-full-height">
								<div class="unit-title">分单</div>
								<div class="unit-panel bottom-bordered">
									
									<div class="ctr-row s-pdt-10">

										<label class="prop-name s-pdr-5">
											<span>随机拆单</span>
											<el-checkbox class="s-mgl-5" v-model="random.random"></el-checkbox>
										</label>

										<label class="prop-name s-pdr-5">主板</label>
										<el-input-number placeholder="最小手" v-model="random.mainMin" :min="boundary.mins.main" :max="boundary.maxes.main" :controls="false" class="short-input"></el-input-number>
										<el-input-number placeholder="最大手" v-model="random.mainMax" :min="boundary.mins.main" :max="boundary.maxes.main" :controls="false" class="short-input s-mgl-5"></el-input-number>
		
										<label class="prop-name s-pdl-5 s-pdr-5">创业板</label>
										<el-input-number placeholder="最小手" v-model="random.imbarkMin" :min="boundary.mins.imbark" :max="boundary.maxes.imbark" :controls="false" class="short-input"></el-input-number>
										<el-input-number placeholder="最大手" v-model="random.imbarkMax" :min="boundary.mins.imbark" :max="boundary.maxes.imbark" :controls="false" class="short-input s-mgl-5"></el-input-number>
		
										<label class="prop-name s-pdl-5 s-pdr-5">科创板</label>
										<el-input-number placeholder="最小手" v-model="random.starMin" :min="boundary.mins.star" :max="boundary.maxes.star" :controls="false" class="short-input"></el-input-number>
										<el-input-number placeholder="最大手" v-model="random.starMax" :min="boundary.mins.star" :max="boundary.maxes.star" :controls="false" class="short-input s-mgl-5"></el-input-number>
		
									</div>
									
									<div class="ctr-row s-pdt-5">
		
										<label class="prop-name s-pdr-5">主板</label>
										<el-input-number v-model="spliting.main" :min="boundary.mins.main" :max="boundary.maxes.main" :controls="false" class="short-input"></el-input-number>
										<label class="prop-name s-pdl-5">手</label>
		
										<label class="prop-name s-pdr-5">创业板</label>
										<el-input-number v-model="spliting.imbark" :min="boundary.mins.imbark" :max="boundary.maxes.imbark" :controls="false" class="short-input"></el-input-number>
										<label class="prop-name s-pdl-5">手</label>
		
										<label class="prop-name s-pdr-5">科创板</label>
										<el-input-number v-model="spliting.star" :min="boundary.mins.star" :max="boundary.maxes.star" :controls="false" class="short-input"></el-input-number>
										<label class="prop-name s-pdl-5">手</label>
		
									</div>
									
									<div class="ctr-row s-pdt-5">
		
										<label class="prop-name s-pdr-5">分拆保护2</label>
										<el-input-number v-model="spliting.protect2.first" :min="1" :max="99" :step="1" :controls="false" class="short-input"></el-input-number>
										<el-input-number v-model="spliting.protect2.second" :min="1" :max="99" :step="1" :controls="false" class="short-input s-mgl-5"></el-input-number>
										<label class="prop-name s-pdl-5">%</label>
		
									</div>
		
									<div class="ctr-row s-pdt-5 s-pdb-5">
		
										<label class="prop-name s-pdr-5">分拆保护3</label>
										<el-input-number v-model="spliting.protect3.first" :min="1" :max="99" :step="1" :controls="false" class="short-input"></el-input-number>
										<el-input-number v-model="spliting.protect3.second" :min="1" :max="99" :step="1" :controls="false" class="short-input s-mgl-5"></el-input-number>
										<el-input-number v-model="spliting.protect3.third" :min="1" :max="99" :step="1" :controls="false" class="short-input s-mgl-5"></el-input-number>
										<label class="prop-name s-pdl-5">%</label>
		
									</div>
		
								</div>
		
								<div class="unit-between"></div>
								<div class="unit-title">弹框</div>
		
								<div class="unit-panel bottom-bordered">
									<div class="ctr-row s-pdt-10 s-pdb-10">
										<el-checkbox class="s-mgr-20" v-model="prompt.mbuy">手动买入</el-checkbox>
										<el-checkbox class="s-mgr-20" v-model="prompt.msell">手动卖出</el-checkbox>
										<el-checkbox class="s-mgr-20" v-model="prompt.mcancel">手动撤单</el-checkbox>
										<el-checkbox class="s-mgr-20" v-model="prompt.floating">展示浮板</el-checkbox>
									</div>
								</div>
		
								<div class="unit-between"></div>
								<div class="unit-title">卖策略</div>
		
								<div class="unit-panel" style="padding-left: 12px;">
									<div class="ctr-row s-pdt-10 s-pdb-10">
										<label class="prop-name s-pdr-5">跟盘价</label>
										<el-select v-model="others.followedPrice" class="long-input s-mgr-30" placeholder="请选择" clearable>
											<el-option v-for="item in followPrices" :key="item.code" :label="item.mean" :value="item.code"></el-option>
										</el-select>
										<label class="prop-name s-pdr-5">限仓</label>
										<el-select v-model="others.limitedPos" class="long-input" placeholder="请选择" filterable clearable>
											<el-option v-for="item in limits" :key="item.code" :label="item.mean" :value="item.code"></el-option>
										</el-select>
									</div>
								</div>
		
								<div class="unit-panel shortcut-panel bottom-bordered" style="padding-left: 12px;">
		
									<div class="plain-title">
										<label class="prop-name shine-color">快捷策略</label>
										<el-tooltip content="添加策略" placement="right">
											<i class="el-icon-plus s-mgl-5" @click="addShortcutConfig4Sell"></i>
										</el-tooltip>
										<label class="prop-name shine-color s-mgl-20">默认选中第</label>
										<el-input v-model.number="sellOptions.defaultSeq" class="short-input s-mgl-5 s-mgr-5"></el-input>
										<label class="prop-name shine-color">个</label>
									</div>
									
									<div class="shortcut-items">
	
										<div v-for="(scc, scc_idx) in sellShortcuts" :key="scc_idx" class="ctr-row">
		
											<el-input v-model="sequencialPickSell(scc_idx)" :disabled="true" class="short-input"></el-input>

											<el-select v-model="scc.strategy" class="longest-input s-mgl-5" placeholder="请选择策略" clearable>
												<el-option v-for="strategy in sellStrategies" :key="strategy.code" :label="strategy.mean" :value="strategy.code"></el-option>
											</el-select>

											<template v-if="scc.strategy">
												<span v-for="(item, item_idx) in mapSellParams(scc.strategy)" :key="item_idx">
													<el-input-number v-model="scc.data[item.prop]" :min="item.min" :max="item.max" :step="item.step" :controls="false" class="short-input s-mgl-5"></el-input-number>
													<label class="prop-name fixed-unit-label s-pdl-5">{{ item.unit }}</label>
												</span>
											</template>
											
											<span class="shortcut-remover s-pull-right s-color-red">
												<el-tooltip content="删除" placement="right">
													<i class="el-icon-remove" @click="removeSellShortcutConfig(scc_idx)"></i>
												</el-tooltip>
											</span>
			
										</div>
	
									</div>
									
								</div>
		
							</div>
						</td>
					</tr>
					<tr class="d-row-ring">
						<td colspan="2" class="cell-of-panel cell-ring">
							<div class="group-panel s-full-height">
								<div class="unit-title">提示音</div>
								<div class="unit-panel bottom-bordered s-pdt-10 s-pdb-10">
									<div class="ctr-row">
										
										<span class="rington-item s-mgr-30">
											<label class="prop-name s-pdr-5">监控买入</label>
											<el-select v-model="rington.entrusted" class="rington-input" placeholder="请选择提示音" clearable>
												<el-option v-for="item in (rington.entrusted ? keepRingtonSelf(rington.entrusted) : otherRingtons)" :key="item.code" :label="item.mean" :value="item.code"></el-option>
											</el-select>
											<el-tooltip v-if="rington.entrusted" placement="top" content="试听">
												<i class="el-icon-video-play s-mgl-5" @click="play(rington.entrusted, rington.customized.entrusted)"></i>
											</el-tooltip>
											<a class="custom-media s-ellipsis" @click="chooseRington('entrusted')" :style="{ visibility: isCustomizedRington(rington.entrusted)}">
												<i class="el-icon-star-on"></i>
												{{ rington.customized.entrusted ? showPlainName(rington.customized.entrusted) : '[请选择铃音]' }}
											</a>
										</span>
		
										<span class="rington-item s-mgr-30">
											<label class="prop-name s-pdr-5">买入成交</label>
											<el-select v-model="rington.bought" class="rington-input" placeholder="请选择提示音" clearable>
												<el-option v-for="item in (rington.bought ? keepRingtonSelf(rington.bought) : otherRingtons)" :key="item.code" :label="item.mean" :value="item.code"></el-option>
											</el-select>
											<el-tooltip v-if="rington.bought" placement="top" content="试听">
												<i class="el-icon-video-play s-mgl-5" @click="play(rington.bought, rington.customized.bought)"></i>
											</el-tooltip>
											<a class="custom-media s-ellipsis" @click="chooseRington('bought')" :style="{ visibility: isCustomizedRington(rington.bought)}">
												<i class="el-icon-star-on"></i>
												{{ rington.customized.bought ? showPlainName(rington.customized.bought) : '[请选择铃音]' }}
											</a>
										</span>
		
										<span class="rington-item s-mgr-30">
											<label class="prop-name s-pdr-5">买入撤单</label>
											<el-select v-model="rington.canceled" class="rington-input" placeholder="请选择提示音" clearable>
												<el-option v-for="item in (rington.canceled ? keepRingtonSelf(rington.canceled) : otherRingtons)" :key="item.code" :label="item.mean" :value="item.code"></el-option>
											</el-select>
											<el-tooltip v-if="rington.canceled" placement="top" content="试听">
												<i class="el-icon-video-play s-mgl-5" @click="play(rington.canceled, rington.customized.canceled)"></i>
											</el-tooltip>
											<a class="custom-media s-ellipsis" @click="chooseRington('canceled')" :style="{ visibility: isCustomizedRington(rington.canceled)}">
												<i class="el-icon-star-on"></i>
												{{ rington.customized.canceled ? showPlainName(rington.customized.canceled) : '[请选择铃音]' }}
											</a>
										</span>
		
										<span class="rington-item">
											<label class="prop-name s-pdr-5">卖出成交</label>
											<el-select v-model="rington.sold" class="rington-input" placeholder="请选择提示音" clearable>
												<el-option v-for="item in (rington.sold ? keepRingtonSelf(rington.sold) : otherRingtons)" :key="item.code" :label="item.mean" :value="item.code"></el-option>
											</el-select>
											<el-tooltip v-if="rington.sold" placement="top" content="试听">
												<i class="el-icon-video-play s-mgl-5" @click="play(rington.sold, rington.customized.sold)"></i>
											</el-tooltip>
											<a class="custom-media s-ellipsis" @click="chooseRington('sold')" :style="{ visibility: isCustomizedRington(rington.sold)}">
												<i class="el-icon-star-on"></i>
												{{ rington.customized.sold ? showPlainName(rington.customized.sold) : '[请选择铃音]' }}
											</a>
										</span>
		
									</div>
								</div>
							</div>
						</td>
					</tr>
					<tr class="d-row-footer">
						<td colspan="2" class="cell-of-panel cell-footer">
							<el-button type="primary" size="small" @click="checkAndSave">保存</el-button>
							<el-button type="success" size="small" @click="reset">重置为默认</el-button>
							<el-button type="info" size="small" @click="cancel">取消</el-button>
						</td>
					</tr>
				</tbody>
			</table>
		</template>	

	</el-dialog>
	
</div>
