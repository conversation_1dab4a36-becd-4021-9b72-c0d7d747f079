const { BaseView } = require('./base-view');
const { NumberMixin } = require('../../mixin/number');
const { Entrance, KeyStrokes4Buy, KeyStrokes4Sell, ShortcutConfig, UserSetting } = require('./components/objects');

/**
 * @returns {Array<ShortcutConfig>}
 */
function makeShortcutConfigs() {
    return [];
}

module.exports = class SettingView extends BaseView {

    constructor(view_name, is_standalone_window) {

        super(view_name, is_standalone_window, '功能设置');

        this.percentages = Entrance.makePercentages();
        this.position = {

            percentage: null,
            amount: null,
            customized: null,
        };

        this.credit = {
            creditBuy: false,
        };

        this.others = {

            supplement: { checked: false, value: null, time: 1000 },
            followedPrice: null,
            limitedPos: null,
        };

        this.buyOptions = { defaultSeq: 1 };
        this.buyStrokes = [];
        this.buyStrokes.refill(this.helper.deepClone(KeyStrokes4Buy));
        this.buyShortcuts = makeShortcutConfigs();
        this.buyStrategies = Entrance.makeBuyStrategies();

        this.sellOptions = { defaultSeq: 1 };
        this.sellStrokes = [];
        this.sellStrokes.refill(this.helper.deepClone(KeyStrokes4Sell));
        this.sellShortcuts = makeShortcutConfigs();
        this.sellStrategies = Entrance.makeSellStrategies();

        this.boundary = {

            mins : {

                main: 0,
                imbark: 0,
                star: 0,
            },

            maxes : {

                main: 9999,
                imbark: 3000,
                star: 1000,
            },
        };

        this.random = {

            random: false,
            mainMin: null,
            mainMax: null,
            imbarkMin: null,
            imbarkMax: null,
            starMin: null,
            starMax: null,
        };

        this.spliting = {

            main: null,
            imbark: null,
            star: null,
            protect2: { first: null, second: null },
            protect3: { first: null, second: null, third: null },
        };

        this.prompt = {
            
            mbuy: true,
            msell: false,
            mcancel: false,
            floating: false,
        };

        this.rington = {

            entrusted: null,
            bought: null,
            canceled: null,
            sold: null,
            
            customized: {

                entrusted: null,
                bought: null,
                canceled: null,
                sold: null,
            },
        };

        this.followPrices = Entrance.makeFollowPrices();
        this.limits = Entrance.makeLimitedPoses();
        this.ringtons = Entrance.makeRings();

        this.dialog = {
            visible: true,
        };

        this.toggler = { show: true };
        this.threses = Entrance.makeBuyThresholds();
    }

    createApp() {

        this.settingApp = new Vue({

            el: this.$container.firstElementChild,
            data : {

                dialog: this.dialog,
                percentages: this.percentages,
                position: this.position,
                credit: this.credit,
                toggler: this.toggler,
                threses: this.threses,
                others: this.others,

                buyOptions: this.buyOptions,
                buyStrokes: this.buyStrokes,
                buyShortcuts: this.buyShortcuts,
                buyStrategies: this.buyStrategies,

                sellOptions: this.sellOptions,
                sellStrokes: this.sellStrokes,
                sellShortcuts: this.sellShortcuts,
                sellStrategies: this.sellStrategies,

                boundary: this.boundary,
                random: this.random,
                spliting: this.spliting,
                prompt: this.prompt,
                followPrices: this.followPrices,
                limits: this.limits,
                rington: this.rington,
                allRingtons: this.ringtons,
            },
            mixins: [NumberMixin],
            computed: {

                otherRingtons: () => {

                    let rt = this.rington;
                    return this.ringtons.filter(x => x.isCustomized || ![rt.entrusted, rt.bought, rt.canceled, rt.sold].some(y => y == x.code));
                },
            },
            methods: this.helper.fakeVueInsMethod(this, [

                this.close,
                this.addShortcutConfig4Buy,
                this.addShortcutConfig4Sell,
                this.mapBuyParams,
                this.mapSellParams,
                this.removeBuyShortcutConfig,
                this.removeSellShortcutConfig,
                this.keepRingtonSelf,
                this.chooseRington,
                this.isCustomizedRington,
                this.showPlainName,
                this.play,
                this.checkAndSave,
                this.reset,
                this.cancel,
                this.sequencialPickBuy,
                this.sequencialPickSell,
            ]),
        });
    }

    sequencialPickBuy(idx) {
        return this.buyStrokes[idx];
    }

    sequencialPickSell(idx) {
        return this.sellStrokes[idx];
    }

    close() {

        this.dialog.visible = false;
        this.setWindowAsBlockedWaiting(false);
    }

    addShortcutConfig4Buy() {

        this.buyShortcuts.push(ShortcutConfig.CreateNew());
        this.interaction.showMessage('已加入到末尾');
    }

    addShortcutConfig4Sell() {

        this.sellShortcuts.push(ShortcutConfig.CreateNew());
        this.interaction.showMessage('已加入到末尾');
    }

    mapBuyParams(strategyCode) {

        var matched = this.buyStrategies.find(x => x.code == strategyCode);
        return matched.params;
    }

    mapSellParams(strategyCode) {

        var matched = this.sellStrategies.find(x => x.code == strategyCode);
        return matched.params;
    }

    removeBuyShortcutConfig(index) {
        this.buyShortcuts.splice(index, 1);
    }

    removeSellShortcutConfig(index) {
        this.sellShortcuts.splice(index, 1);
    }

    keepRingtonSelf(rington) {
        return this.ringtons.filter(x => x.code == rington || x.isCustomized);
    }

    chooseRington(which) {
        
        const { dialog } = require('@electron/remote');
        const paths = dialog.showOpenDialogSync(this.thisWindow, {
            
            title: '选择提示音',
            filters: [{ name: '音频文件', extensions: ['mp3', 'wav'] }],

        });

        this.handleRingtonFile(which, paths);
    }

    handleRingtonFile(which, paths) {

        if (!paths) {
            return;
        }

        var fpath = paths[0];
        var customized = this.rington.customized;

        switch (which) {

            case 'entrusted': customized.entrusted = fpath; break;
            case 'bought': customized.bought = fpath; break;
            case 'canceled': customized.canceled = fpath; break;
            case 'sold': customized.sold = fpath; break;
        }
    }

    isCustomizedRington(rington) {
        return rington == this.ringtons.find(x => x.isCustomized).code ? 'visible' : 'hidden';
    }

    showPlainName(crington) {

        if (typeof crington != 'string') {
            return crington;
        }

        let levels = crington.replace(/\\/g, '/').split('/');
        return levels[levels.length - 1];
    }

    validate() {

        var result = { isOk: true, message: null };
        var random = this.random;
        var spliting = this.spliting;

        if (this.helper.isNone(this.position.percentage) || !Number.isInteger(this.position.amount) || !Number.isInteger(this.position.customized)) {

            result.isOk = false;
            result.message = '仓位比例，设置有误';
        }
        else if (this.threses.some(x => x.members.some(y => !Number.isInteger(y.value)))) {

            result.isOk = false;
            result.message = '撤单策略，设置有误';
        }
        else if (!Number.isInteger(this.others.supplement.value)) {

            result.isOk = false;
            result.message = '补单策略：委托量，设置有误';
        }
        else if (this.others.supplement.time < 10 || this.others.supplement.time > 2999) {

            result.isOk = false;
            result.message = '补单策略：观测时间，设置有误';
        }
        else if (random.random) {

            if (!Number.isInteger(random.mainMin) 
                || !Number.isInteger(random.mainMax)
                || !Number.isInteger(random.imbarkMin)
                || !Number.isInteger(random.imbarkMax)
                || !Number.isInteger(random.starMin)
                || !Number.isInteger(random.starMax)) {

                result.isOk = false;
                result.message = '主板/创业板/科创板，随机拆单，设置有误';
            }
        }
        else if (!Number.isInteger(spliting.main) || !Number.isInteger(spliting.imbark) || !Number.isInteger(spliting.star)) {

            result.isOk = false;
            result.message = '主板/创业板/科创板，手数，设置有误';
        }
        else if (!Number.isInteger(spliting.protect2.first) || !Number.isInteger(spliting.protect2.second)) {

            result.isOk = false;
            result.message = '分拆保护2，设置有误';
        }
        else if (spliting.protect2.first + spliting.protect2.second != 100) {

            result.isOk = false;
            result.message = '分拆保护2，合计不等于100%';
        }
        else if (!Number.isInteger(spliting.protect3.first) || !Number.isInteger(spliting.protect3.second)|| !Number.isInteger(spliting.protect3.third)) {

            result.isOk = false;
            result.message = '分拆保护3，设置有误';
        }
        else if (spliting.protect3.first + spliting.protect3.second + spliting.protect3.third != 100) {

            result.isOk = false;
            result.message = '分拆保护3，合计不等于100%';
        }

        return result;
    }

    getRevised() {

        var defaults = Entrance.defaultsSettings();
        this.helper.extend(defaults.position, this.position);
        this.helper.extend(defaults.credit, this.credit);

        this.threses.forEach(thr => {

            let matched = UserSetting.ReadThreadhold(defaults.threshold, thr.checkedProp);
            if (matched == undefined) {
                return;
            }

            matched.checked = thr.isOn;
            thr.members.forEach(memb => { matched.values[memb.prop] = memb.value; });
        });
        
        this.helper.extend(defaults.supplement, this.others.supplement);
        defaults.toggleShow = this.toggler.show;
        defaults.followedPrice = this.others.followedPrice;
        defaults.limitedPos = this.others.limitedPos;

        this.helper.extend(defaults.random, this.random);

        var spliting = this.spliting;
        var dsplitting = defaults.spliting;

        dsplitting.main = spliting.main;
        dsplitting.imbark = spliting.imbark;
        dsplitting.star = spliting.star;

        this.helper.extend(dsplitting.protect2, spliting.protect2);
        this.helper.extend(dsplitting.protect3, spliting.protect3);
        this.helper.extend(defaults.prompt, this.prompt);
        
        defaults.rington.entrusted = this.rington.entrusted;
        defaults.rington.bought = this.rington.bought;
        defaults.rington.canceled = this.rington.canceled;
        defaults.rington.sold = this.rington.sold;
        this.helper.extend(defaults.rington.customized, this.rington.customized);

        this.helper.extend(defaults.buyOptions, this.buyOptions);
        this.helper.extend(defaults.sellOptions, this.sellOptions);
        defaults.buyShortcuts.refill(this.helper.deepClone(this.buyShortcuts));
        defaults.sellShortcuts.refill(this.helper.deepClone(this.sellShortcuts));

        /**
         * 在数据提交前，将快捷键顺序，进行自然顺序重置
         */
        defaults.buyShortcuts.forEach((sht, index) => { sht.stroke = this.sequencialPickBuy(index); });
        defaults.sellShortcuts.forEach((sht, index) => { sht.stroke = this.sequencialPickSell(index); });
        
        return this.helper.deepClone(defaults);
    }

    async checkAndSave() {

        var result = this.validate();
        if (!result.isOk) {

            this.interaction.showError(result.message);
            return;
        }

        var revised = this.getRevised();
        var resp = await Entrance.saveSetting(revised);
        this.log(`to save settings = ${JSON.stringify(revised)}`);
        
        if (resp.errorCode != 0) {
                    
            this.interaction.showError(`保存失败：${resp.errorCode}/${resp.errorMsg}`);
            return;
        }

        this.dialog.visible = false;
        this.interaction.showSuccess('默认设置已保存');
        this.trigger('setting-updated');
    }

    reset() {

        this.interaction.showConfirm({

            title: '操作确认',
            message: '是否重置为默认设置 ？',
            confirmed: async () => {
                
                this.log(`to reset(delete) existing settings`);
                let resp = await Entrance.deleteSetting();
                if (resp.errorCode != 0) {

                    this.dialog.visible = false;
                    this.interaction.showError(`重置失败：${resp.errorCode}/${resp.errorMsg}`);
                    return;
                }

                this.dialog.visible = false;
                await this.loadSettingProfile();
                this.interaction.showSuccess('已重置，您可继续编辑，或关闭窗口。');
            },
        });
    }

    cancel() {
        this.dialog.visible = false;
    }

    async loadSettingProfile() {

        var userSettings = await Entrance.readSetting();
        this.helper.extend(this.position, userSettings.position);
        this.helper.extend(this.credit, userSettings.credit);

        this.threses.forEach(thr => {

            let matched = UserSetting.ReadThreadhold(userSettings.threshold, thr.checkedProp);
            if (matched == undefined) {
                return;
            }

            thr.isOn = matched.checked;
            thr.members.forEach(memb => { memb.value = matched.values[memb.prop]; });
        });
        
        this.helper.extend(this.others.supplement, userSettings.supplement);
        this.toggler.show = userSettings.toggleShow;
        this.others.followedPrice = userSettings.followedPrice;
        this.others.limitedPos = userSettings.limitedPos;

        this.helper.extend(this.random, userSettings.random);

        var spliting = this.spliting;
        var usplitting = userSettings.spliting;
        spliting.main = usplitting.main;
        spliting.imbark = usplitting.imbark;
        spliting.star = usplitting.star;

        this.helper.extend(spliting.protect2, usplitting.protect2);
        this.helper.extend(spliting.protect3, usplitting.protect3);
        this.helper.extend(this.prompt, userSettings.prompt);
        this.helper.extend(this.buyOptions, userSettings.buyOptions);
        this.helper.extend(this.sellOptions, userSettings.sellOptions);

        this.rington.entrusted = userSettings.rington.entrusted;
        this.rington.bought = userSettings.rington.bought;
        this.rington.canceled = userSettings.rington.canceled;
        this.rington.sold = userSettings.rington.sold;
        this.helper.extend(this.rington.customized, userSettings.rington.customized);

        /**
         * 解决废弃的快捷键 & 旧快捷配置数据，之间的不一致冲突导致的问题
         */
        this.buyShortcuts.refill(userSettings.buyShortcuts.filter(x => this.buyStrategies.some(y => x.strategy == y.code)));
        this.sellShortcuts.refill(userSettings.sellShortcuts.filter(x => this.sellStrategies.some(y => x.strategy == y.code)));
    }

    /**
     * @param {HTMLElement} $container 
     */
    build($container) {

        super.build($container);
        this.createApp();
        this.loadSettingProfile();
    }
};