.dialog-20cm-setting {
	
	.el-dialog__body {
		padding: 0;
	}
}

.v20cm-setting {

	width: 830px;
	height: 628px !important;
	border-radius: 4px;

	table td {
		box-sizing: border-box;
	}

	.cell-main-left {
		padding: 10px 5px 10px 10px !important;
	}

	.cell-main-right {
		padding: 10px 10px 10px 5px !important;
	}

	.d-row-ring {
		height: 65px;
	}

	.cell-of-panel {
		background-color: #324F80;
	}

	.group-panel {
		background-color: #283A56;
	}

	.unit-between {
		
		height: 10px;
		background-color: #324F80;
	}
	
	.unit-panel {
		
		padding-left: 35px;
		border-width: 1px 1px 0 1px;
		border-style: solid;
		border-color: #1A212B;
	}

	.bottom-bordered {
		border-bottom: 1px solid #1A212B;
	}

	.unit-title {

		height: 24px;
		line-height: 24px;
		padding-left: 12px;
		border-width: 1px 1px 0 1px;
		border-style: solid;
		border-color: #1A212B;
	}

	.plain-title {

		height: 28px;
		line-height: 28px;
	}

	.credit-option {

		position: absolute;
		right: 15px;
		top: 8px;
		z-index: 1;
	}

	.ctr-thres.col-first,
	.ctr-thres.col-second {
		padding-left: 0 !important;
	}

	.fixed-unit-label {

		display: inline-block;
		box-sizing: border-box;
	}

	.shortcut-panel .ctr-row {
		margin-bottom: 3px;
	}

	.shortcut-remover {

		margin-right: 5px;
		margin-top: 2px;
		font-size: 14px;
		color: #5C93EB;
	}

	.shortcut-items {

		max-height: 240px;
		overflow-y: auto;
	}

	.cell-ring {
		padding: 0 10px !important;
	}

	.rington-item {

		display: inline-block;

		.rington-input {
			width: 130px;
		}

		.custom-media {

			display: block;
			width: 150px;
			padding-left: 55px;
    		margin-top: 10px;
			cursor: pointer;

			&:hover {
				text-decoration: underline;
			}
		}
	}

	.d-row-footer {
		height: 57px;
	}

	.cell-footer {

		text-align: center;
		vertical-align: middle;

		button {

			margin-top: 17px;
			padding-left: 25px;
			padding-right: 25px;
		}
	}
}