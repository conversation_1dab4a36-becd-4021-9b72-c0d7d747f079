const { IView } = require('../../component/iview');
const { TabList } = require('../../component/tab-list');
const { Tab } = require('../../component/tab');
const HeaderView = require('./components/header');
const ActionsView = require('./components/actions');
const BuyListView = require('./components/buy-list');
const BoughtListView = require('./components/bought-list');
const { Position } = require('../../model/position');
const { Entrance, FunctionKeys, AccountSimple, UnclosedCreditPosition, TaskObject } = require('./components/objects');
const { repoAccount } = require('../../repository/account');
const { Cm20FunctionCodes } = require('../../config/20cm');
const { NumberMixin } = require('../../mixin/number');
const MouseTrap = require('mousetrap');
const { Splitter } = require('../../component/splitter');
const { repoCredit } = require('../../repository/credit');

/**
 * @returns {Array<AccountSimple>}
 */
function makeAccounts() {
    return [];
}

module.exports = class TradeView extends IView {

    constructor(view_name, is_standalone_window) {

        super(view_name, is_standalone_window, '涨停板策略交易');

        this.enames = {

            addUnits: 'add-units',
            setTasks: 'set-tasks',
            taskCreated: 'task-created',
            accountChange: 'account-change',
            shortcutKey: 'shortcut-key-pressed',
            nofocus: 'no-focused-unit',
            askPositions: 'ask-4-positions',
        };

        this.stockAccounts = makeAccounts();
    }

    /**
     * @param {Array} stocks
     */
    addStocks(stocks) {
        this.vbuying.trigger(this.enames.addUnits, stocks);
    }

    /**
     * @param {HTMLElement} $template 
     */
    buildTree($template) {

        var $header = $template.querySelector('.header');
        var $actions = $template.querySelector('.actions');
        var $mainView = $template.querySelector('.main-view-root');
        var $buys = $template.querySelector('.buys');
        var $boughts = $template.querySelector('.boughts');
        var $entrustTabs = $template.querySelector('.type-tabs');
        var $entrustViews = $template.querySelector('.entrust-views');
        var $splitter = $template.querySelector('.splitter-line');
        var $cornerTabs = $template.querySelector('.corner-tabs');
        var $cornerViews = $template.querySelector('.corner-views');

        this.$mainView = $mainView;

        /**
         * 顶部工具栏
         */
        var vheader = new HeaderView();
        vheader.loadBuild($header);
        vheader.registerEvent('open-setting', this.openSetting.bind(this));
        vheader.registerEvent('open-completed', this.openCompleted.bind(this));
        this.vheader = vheader;

        /**
         * 买入监控 ~ 工具栏
         */
        var vaction = new ActionsView();
        vaction.loadBuild($actions);
        vaction.registerEvent('add-stocks', this.addStocks.bind(this));
        this.vaction = vaction;

        /**
         * 买入监控
         */
        var vbuying = new BuyListView();
        vbuying.loadBuild($buys);
        this.$buys = $buys;
        this.vbuying = vbuying;

        /**
         * 已买清单
         */
        var vbought = new BoughtListView();
        vbought.loadBuild($boughts);
        this.$boughts = $boughts;
        this.vbought = vbought;

        /**
         * 右侧列表与卖出单元，上下区域垂直布局
         */
        this.splitter = new Splitter('20cm-right-wall', $splitter, this.handleSpliting.bind(this), {

            previousMinHeight: 100, 
            nextMinHeight: 200,
        });

        setTimeout(() => {
            this.splitter.coordinate(0, this.splitter.$previous.offsetHeight - 10 + 123);
        }, 200);

        /**
         * 持仓与委托列表
         */
        var tabCtr = new TabList({

            allowCloseTab: false,
            embeded: true,
            lazyLoad: false,
            showToolkit: 'refresh, export',
            $navi: $entrustTabs,
            $content: $entrustViews,
            tabCreated: this.handleTabCreated.bind(this),
            tabFocused: this.handleTabFocused.bind(this),
        });

        tabCtr.openTab(false, '@20cm/components/position', '股票持仓');
        tabCtr.openTab(false, '@20cm/components/account', '账号信息');
        tabCtr.openTab(true, '@20cm/components/entrust', '委托列表');

        this.tabCtr = tabCtr;
        this.tabCtr.setFocus(tabCtr.tabs[0]);
        
        this.vpositionView = tabCtr.tabs[0].viewEngine;
        this.vpositionView.registerEvent('hit-position', this.handlePositionHit.bind(this));
        this.vpositionView.registerEvent('brocast-position-condition', this.handlePositionBrocastEvent.bind(this));
        this.vpositionView.registerEvent('required-positions', this.handlePositionFeedback.bind(this));
        this.vaccountView = tabCtr.tabs[1].viewEngine;
        this.ventrust = tabCtr.tabs[2].viewEngine;
        this.ventrust.registerEvent('summarized-buys', this.handleSummarizedBuys.bind(this));
        this.ventrust.registerEvent('summarized-sells', this.handleSummarizedSells.bind(this));

        /**
         * 右下方操作区域
         */
        var cnrTab = new TabList({

            allowCloseTab: false,
            embeded: true,
            $navi: $cornerTabs,
            $content: $cornerViews,
            lazyLoad: false,
            showToolkit: false,
        });

        cnrTab.openTab(false, '@20cm/components/boarding', '行情', { height: 470 });
        cnrTab.openTab(false, '@20cm/components/sell-list', '卖出监控', { height: 470 });

        this.cnrTab = cnrTab;
        this.vboarding = cnrTab.tabs[0].viewEngine;
        this.vsell = cnrTab.tabs[1].viewEngine;

        this.vsell.registerEvent(this.enames.askPositions, (stockCode) => {
            this.vpositionView.trigger(this.enames.askPositions, stockCode); 
        });

        this.vbuying.registerEvent('ask-summarized-buys', () => { this.ventrust.trigger('ask-summarized-buys'); });
        this.vsell.registerEvent('ask-summarized-sells', () => { this.ventrust.trigger('ask-summarized-sells'); });
        this.vsell.registerEvent('ask-2-cancel-all-sells', (code, name) => { this.ventrust.trigger('ask-2-cancel-all-sells', code, name); });

        /**
         * 多个界面交叉事件
         */
        var viewList = [this.vbuying, this.vbought, this.vsell];
        viewList.forEach(item => {

            item.registerEvent('unit-focused', (title, stockCode) => {
                
                viewList.forEach(item2 => {
                    title != item2.title && item2.trigger(this.enames.nofocus);
                });
                
                this.vboarding.trigger('set-as-instrument', stockCode);
            });

            item.registerEvent('unit-removed', (stockCode) => {
                this.vboarding && this.vboarding.trigger('test-instrument', stockCode);
            });
        });

        this.multipleViews = viewList;
    }

    /**
     * @param {Number} previous_height 
     * @param {Number} next_height 
     */
    handleSpliting(previous_height, next_height) {
        this.tabCtr.fireEventOnAllTabs('set-max-height', previous_height);
    }

    handlePositionHit(position) {

        let { instrument, instrumentName, closableVolume } = position;
        this.cnrTab.setFocus(this.cnrTab.tabs[1]);
        this.vsell.trigger(this.enames.addUnits, [{ code: instrument, name: instrumentName, closableVolume }]);
    }

    /**
     * @param {Array<String>} zeros 
     * @param {Array<{ instrument: String, closable: Number }>} closables 
     */
    handlePositionBrocastEvent(zeros, closables) {
        this.vsell.trigger('action-by-position', zeros, closables);
    }    

    /**
     * @param {Array<Position>} positions 
     */
    async handlePositionFeedback(positions) {

        var uncloseds = await this.requestUnclosedCredits(positions.map(x => x.accountId).distinct());
        this.vsell.trigger('required-positions', positions, uncloseds);
    }

    handleSummarizedBuys(map) {
        this.vbuying.trigger('summarized-buys', map);
    }

    handleSummarizedSells(map) {
        this.vsell.trigger('summarized-sells', map);
    }

    /**
     * @param {Array<String>} accountIds 
     * @returns {Array<{ accountId: String, uncloseds: Array<UnclosedCreditPosition> }>}
     */
    async requestUnclosedCredits(accountIds) {

        if (accountIds.length == 0) {
            return [];
        }

        var types = {

            cash: { value: 0, label: '融资' },
            stock: { value: 1, label: '融券' },
            list: { value: 2, label: '标的' },
	        special: { value: 3, label: '专项' },
        };

        var subsists = {

            alive: { value: 0, label: '未了结' },
            done: { value: 1, label: '已了结' },
        };

        var results = [];

        for (let accountId of accountIds) {

            let resp = await repoCredit.getContractInfo({

                user_id: this.userInfo.userId,
                token: this.userInfo.token,

                compact_type: types.cash.value,
                compact_status: subsists.alive.value,
                fund_id: null,
                strategy_id: null,
                account_id: accountId,
                pageNo: 1,
                pageSize: 99999,
            });

            if (resp.errorCode == 0) {
                
                let records = resp.data.list;
                if (records instanceof Array && records.length > 0) {
                    results.push({ accountId, uncloseds: records.map(item => new UnclosedCreditPosition(item)) });
                }
            }
        }

        return results;
    }

    /**
     * @param {HTMLElement} $template 
     */
    buildFooterRow($template) {

        this.footerApp = new Vue({

            el: $template,
            mixins: [NumberMixin],
            data: {
                stockAccounts: this.stockAccounts,
            },
        });
    }

    /**
     * @param {Tab} tab 
     */
    handleTabCreated(tab) {
        setTimeout(() => { this.simulateWinSizeChange(); }, 100);
    }

    /**
     * @param {Tab} tab 
     */
    handleTabFocused(tab) {

        this.tabCtr.fireEventOnTab(tab, 'auto-fit');
        setTimeout(() => { this.simulateWinSizeChange(); }, 100);
    }

    openSetting() {
        
        if (this.settingView) {

            this.settingView.dialog.visible = true;
            this.setWindowAsBlockedWaiting(true);
            return;
        }

        var SettingView = require('./setting');
        var dialog = new SettingView('@20cm/setting', false);
        dialog.registerEvent('setting-updated', () => { this.readSettings(true); });
        dialog.loadBuild(this.$container.lastElementChild, null, () => {
            this.setWindowAsBlockedWaiting(true);
        });
        this.settingView = dialog;
    }

    openCompleted() {
        
        if (this.historyView) {

            this.historyView.dialog.visible = true;
            this.historyView.setAsSettings(this.settings);
            this.historyView.requestTasks();
            this.setWindowAsBlockedWaiting(true);
            return;
        }

        var HistoryView = require('./history');
        var dialog = new HistoryView('@20cm/history', false);
        this.historyView = dialog;

        dialog.loadBuild(this.$container.lastElementChild, null, () => {

            this.historyView.setAsSettings(this.settings);
            this.setWindowAsBlockedWaiting(true);
        });
    }

    async readSettings(is_hot_change) {

        var settings = this.settings = await Entrance.readSetting();
        var ename = 'setting-updated';
        this.registerShortcuts();
        this.vbuying.trigger(ename, settings);
        this.vbought.trigger(ename, settings);
        this.vsell.trigger(ename, settings);
        this.ventrust.trigger(ename, settings);

        if (is_hot_change) {
            this.thisWindow.emit('strike-board-setting-updated', settings);
        }
    }

    registerShortcuts() {

        if (this.shortcutsLib === undefined) {

            var keymap = {};
            FunctionKeys.forEach((key, idx) => { keymap[112 + idx] = key; });
            Mousetrap.addKeycodes(keymap);
            this.shortcutsLib = Mousetrap;
        }

        var lib = this.shortcutsLib;
        lib.reset();

        /**
         * 对买入热键 & 卖出热键，合并到一起绑定（热键之间无交叉）
         */
        var allShortcuts = this.settings.buyShortcuts.concat(this.settings.sellShortcuts);
        allShortcuts.forEach(item => {
            lib.bind(item.stroke, (e, combo) => { this.handleStrokePress(e, combo); }, 'keyup');
        });

        /**
         * 窗口全局等待用户确认时，阻止所有快捷监听
         */
        lib.bind('enter', (e, combo) => { 

            if (this.isWindowBlockedByConfirm) {
                return;
            }

            this.vbuying.trigger('start-monitor');
        }, 'keyup');
    }

    handleStrokePress(e, combo) {

        if (this.isWindowBlockedByConfirm) {
            return;
        }

        this.multipleViews.forEach(item => {
            item.trigger(this.enames.shortcutKey, event.key);
        });
    }

    async requestAccounts() {

        var resp = await repoAccount.getAccountDetailInfo({ identity_id: '', userId: this.userInfo.userId });
        if (resp.errorCode == 0) {

            let records = resp.data.list;
            let simples = AccountSimple.Convert(records);
            let stockAccounts = simples.filter(x => x.identityType == this.systemEnum.identityType.account.code
                                                 && x.assetType == this.systemEnum.assetsType.stock.code);

            this.stockAccounts.refill(stockAccounts);
            this.spreadAccounts();
        }
        else {
            this.interaction.showError('获取账号资金详情发生异常：' + resp.errorMsg);
        }
    }

    requestTasks() {
        this.renderProcess.send(this.systemEvent.transmitMsg2TradingServer, Cm20FunctionCodes.request.query, '0');
    }

    spreadAccounts() {

        var data = this.helper.deepClone(this.stockAccounts);
        var acnts = this.stockAccounts;
        var summarized = {

            zky: acnts.map(x => x.available).sum(),
            zkr: acnts.map(x => x.enableCreditBuy).sum(),
            zzc: acnts.map(x => x.balance).sum(),
            djzj: acnts.map(x => (x.frozenMargin + x.frozenCommission)).sum(),
        };

        this.vbuying.trigger(this.enames.accountChange, data);
        this.vheader.trigger(this.enames.accountChange, summarized);
    }

    /**
     * @param {String} message 
     */
    log(message) {
        this.loggerTrading.info('ztlog:' + message);
    }

    /**
     * @param {TaskObject|Array<TaskObject>} data
     */
    shape(data) {

        (data instanceof Array ? data : [data]).forEach(task => {
            task.xorders = TaskObject.CreateOrders(task.orderInfo);
        });
    }

    /**
     * @param {TaskObject} task 
     */
    handleTaskCreated(task) {

        this.shape(task);
        var dir = this.systemTrdEnum.tradingDirection;

        if (task.direction == dir.buy.code) {
            this.vbuying.trigger(this.enames.taskCreated, task);
        }
        else {
            this.vsell.trigger(this.enames.taskCreated, task);
        }
    }

    /**
     * @param {Array<TaskObject>} records 
     */
    handleTasksReply(records) {

        var tasks = records.filter(item => item.strikeBoardType == 0);
        if (tasks.length == 0) {
            return;
        }

        var strucs = tasks.map(task => {

            let { id, instrument, instrumentName, direction, strikeBoardStatus } = task;
            return { id, instrument, instrumentName, direction, strikeBoardStatus };
        });

        this.log(`received tasks reply: ${JSON.stringify(strucs)}`);

        /**
         * 回执里包含的全量任务，需排除（因意外数据带来的）已过期（已结束）任务
         */
        var dir = this.systemTrdEnum.tradingDirection;
        var buys = tasks.filter(x => x.direction == dir.buy.code && !TaskObject.isUnexpected(x.strikeBoardStatus));
        var sells = tasks.filter(x => x.direction == dir.sell.code && !TaskObject.isUnexpected(x.strikeBoardStatus));
        this.vbuying.trigger(this.enames.setTasks, buys, true);
        this.vbought.trigger(this.enames.setTasks, buys, true);
        this.vsell.trigger(this.enames.setTasks, sells, true);
    }

    /**
     * @param {TaskObject} task 
     */
    handleTaskChanged(task) {

        this.shape(task);

        var dir = this.systemTrdEnum.tradingDirection;
        var struc = {

            id: task.id,
            instrument: task.instrument,
            instrumentName: task.instrumentName,
            direction: task.direction,
            strikeBoardStatus: task.strikeBoardStatus,
        };

        this.log(`received task notify: ${JSON.stringify(struc)}`);
        
        if (task.direction == dir.buy.code) {

            this.vbuying.trigger(this.enames.setTasks, [task], false);
            this.vbought.trigger(this.enames.setTasks, [task], false);
        }
        else {
            this.vsell.trigger(this.enames.setTasks, [task], false);
        }
    }

    /**
     * @param {TaskObject} task 
     */
    handleTaskDeleted(task) {

        /**
         * 对于已删除的任务，通知到具体交易界面，由界面自行进行删除
         */
        this.handleTaskChanged(task);
    }

    /**
     * @param {TaskObject} task 
     */
    handleTaskErrorReply(errorCode, errorMsg, task) {

        let msg = `server replied with the latest task for operation error: error_code/${errorCode}, error_msg/${errorMsg} task/${JSON.stringify(task)}`;
        console.error(msg);
        this.log(msg);

        let is_a_task = this.helper.isNotNone(task.id);
        if (!is_a_task) {
            return;
        }

        var dir = this.systemTrdEnum.tradingDirection;
        if (task.direction == dir.buy.code) {
            return;
        }

        let order_map = task.orderInfo
        if (this.helper.isJson(order_map)) {
            task.orderInfo = TaskObject.CreateOrders(order_map);
        }
        
        this.vsell.trigger('task-error', task);
    }

    checkTime() {

        if (!this.multipleViews) {
            return;
        }
        
        var time = new Date().format('hhmmss');
        if (time >= '092500' && time <= '180000') {
            
            clearInterval(this.cktimer);
            this.multipleViews.forEach(item => {
                item.trigger('get-into-continious-trade-time');
            });
        }
    }

    listen2Events() {

        this.renderProcess.on(Cm20FunctionCodes.reply.queried + '', (event, { data, errorCode, errorMsg }) => {

            if (errorCode == 0) {

                console.log('queried tasks', data);
                this.shape(data);
                this.handleTasksReply(data); 
            }
            else {
                this.interaction.showError('买卖监控列表查询错误：' + errorMsg);
            }
        });

        var handleReply = (action, { data, errorCode, errorMsg }) => {

            if (errorCode == 0) {
                this.interaction.showSuccess(`${action}，已处理`);
            }
            else {

                console.error(action, { errorCode, errorMsg, data });
                this.interaction.showError(`${action}错误：${errorCode}/${errorMsg}`);

                /**
                 * 1. 出现报错时，客户端发起对一个任务的修改（参数、状态等改变），服务器检测到两端的任务状态不一致；
                 * 2. data 为异常状态回执，服务器端返回的，任务当前的结构快照；
                 */
                this.handleTaskErrorReply(errorCode, errorMsg, data);
            }
        };

        this.renderProcess.on(Cm20FunctionCodes.reply.started + '', (event, message) => { handleReply('启动', message); });
        this.renderProcess.on(Cm20FunctionCodes.reply.stopped + '', (event, message) => { handleReply('停止', message); });
        this.renderProcess.on(Cm20FunctionCodes.reply.deleted + '', (event, message) => { handleReply('删除', message); });
        this.renderProcess.on(Cm20FunctionCodes.reply.canceled + '', (event, message) => { handleReply('撤单', message); });
        this.renderProcess.on(Cm20FunctionCodes.reply.mannualBuy + '', (event, message) => { handleReply('手动买入', message); });

        this.renderProcess.on(Cm20FunctionCodes.notify.created + '', (event, task) => { this.handleTaskCreated(task); });
        this.renderProcess.on(Cm20FunctionCodes.notify.changed + '', (event, task) => { this.handleTaskChanged(task); });
        this.renderProcess.on(Cm20FunctionCodes.notify.deleted + '', (event, task) => { this.handleTaskDeleted(task); });

        this.lisen2WinSizeChange((width, height, isMaximized) => {

            let neth = height - (isMaximized ? 175 : 160);
            this.$mainView.style.height = neth + 'px';
            this.splitter.coordinate(0, this.splitter.$previous.offsetHeight + 123);
        });
    }

    focusCursor() {
        this.vaction.trigger('focus-on-search');
    }

    attachWinEvents() {

        // this.thisWindow.addListener('focus', () => { this.focusCursor(); });
        var tags = ['BUTTON', 'INPUT'];
        var blacks = ['identity-buy-unit', 'identity-bought-unit', 'identity-history-unit', 'identity-sell-unit', 'el-dialog__wrapper'];

        document.addEventListener('click', () => {
            
            if (tags.indexOf(event.target.tagName) >= 0 || blacks.some(token => event.target.classList.contains(token))) {
                return;
            }

            let $cursor = event.target.parentElement;
            let isPermited = true;

            while ($cursor && $cursor.tagName != 'BODY') {
                
                let classes = $cursor.classList;
                let isPrevented = tags.some(token => $cursor.tagName == token);
                let isBlack = blacks.some(token => classes.contains(token));

                if (isPrevented || isBlack) {

                    isPermited = false;
                    break;
                }

                $cursor = $cursor.parentElement;
            }

            if (isPermited) {
                this.focusCursor();
            }
        });
    }

    async chainedRequest() {

        await this.readSettings();
        this.requestAccounts();
        this.requestTasks();

        /**
         * 账号数据定时刷新
         */
        this.timer4AccountData = setInterval(() => { this.requestAccounts(); }, 1000 * 30);
    }

    /**
     * @param {HTMLElement} $container 
     */
    async build($container) {

        super.build($container);
        this.buildTree($container.firstElementChild.querySelector('table'));
        this.buildFooterRow($container.firstElementChild.lastElementChild);
        await this.chainedRequest();
        this.listen2Events();
        this.attachWinEvents();
        this.simulateWinSizeChange();
        this.cktimer = setInterval(() => { this.checkTime(); }, 1000 * 15);
        this.checkTime();
    }
};