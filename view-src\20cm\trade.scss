.v20cm {

	height: 100%;

	.el-button {

		height: 22px;
		padding: 2px 8px;
	}

	.el-button--mini {

		height: 20px;
		padding: 0 6px;
	}

	.el-input__inner {

		height: 20px;
		line-height: 20px;
		padding: 0 5px;
		background-color: #0C1016FF;
		color: white;
	}

	.el-input-number {

		line-height: 18px;

		.el-input-number__increase,
		.el-input-number__decrease {

			width: 13px;
			height: 8px;
			line-height: 8px !important;
			border: unset;
		}

		.el-input-number__increase {
			top: 1px;
		}

		.el-input-number__decrease {
			top: 11px;
		}

		.el-icon-arrow-up,
		.el-icon-arrow-down {
			transform: scale(0.7);
		}

		.el-icon-arrow-up {
			
			position: relative;
			top: -2px;
		}

		.el-icon-arrow-down {

			position: relative;
			top: -2px;
		}
	}

	.el-input-number.is-controls-right .el-input__inner {

		padding-left: 5px;
		padding-right: 10px;
	}

	.el-input-number.is-without-controls .el-input__inner {

		padding-left: 5px;
		padding-right: 5px;
	}

	.el-input__prefix {
					
		top: -3px;
		left: 8px;
	}

	.el-input__suffix {
		top: -4px;
	}

	.el-select {

		.el-icon-circle-close {
			margin-top: 9px;
		}

		.el-icon-arrow-up {

			margin-top: 9px;

			&.is-reverse {
				margin-top: 0;
			}
		}
	}

	.el-checkbox__inner {

		background-color: #FFF;
		border-color: #FFF;
	}

	.el-checkbox__label {
		color: #FFF !important;
	}

	.el-button+.el-button {
		margin-left: 5px;
	}

	input {
		text-align: left !important;
	}

	.search-keywords .el-input__inner {
		padding: 0 25px;
	}

	.micro-input {

		width: 30px;
		border-radius: 2px;
	}

	.short-input {

		width: 55px;
		border-radius: 2px;
	}

	.medium-input {

		width: 70px;
		border-radius: 2px;
	}

	.long-input {

		width: 80px;
		border-radius: 2px;
	}

	.longest-input {

		width: 150px;
		border-radius: 2px;
	}

	.shine-color {
		color: #8CB5ED;
	}

	.disabled-ctr {
		
		> label {
			color: #8393AB;
		}
	}

	.typical-header {

		background-color: #283A56;
		border-radius: 2px 2px 0px 0px;
	}

	.typical-content {

		background-color: #23354D;
		border-radius: 2px 2px 0px 0px;
		border: 1px solid #283A56;
	}

	.tab-unit {
		height: 28px;
	}

	.tab-panel {
		height: 28px;
	}

	.tab-operation {
		display: none;
	}

	.layout-table {

		height: 100%;
		width: 100%;
		table-layout: fixed;
		border-collapse: collapse;
		// border-top: 1px solid blue;
		// border-left: 1px solid blue;

		> tbody > tr > td {

			padding: 0;
			vertical-align: top;
			// border-right: 1px solid blue;
			// border-bottom: 1px solid blue;
		}

		.cell-left-pad {

			padding-left: 4px;
			background-color: #0C1016;
		}
	}

	.collapser-btn {

		width: 22px !important;
		height: 22px !important;
		border-radius: 2px;
		padding: 0;
		color: white;
		border: 1px solid #65748C;
		background-color: #283A56;
	}

	.row-header {

		height: 28px;

		.header {

			height: 100%;
	
			.header-inter {
	
				box-sizing: border-box;
				height: 100%;
				padding: 2px 4px;
	
				button {

					margin-top: 1px;
					background-image: unset;
					box-shadow: unset;
					background-color: #384E70;
					border: 1px solid #384E70;

					&:hover {
						background-color: #1f2631;
					}

					&:focus,
					&:active {
						background-color: #384E70 !important;
					}
				}
	
				.monitor {
	
					line-height: 24px;
	
					.prop-value {
	
						display: inline-block;
						margin-right: 20px;
					}
				}
			}
		}
	}

	.row-actions {

		height: 28px;

		.actions {
			
			height: 100%;

			.actions-inter {
	
				box-sizing: border-box;
				height: 100%;
				padding: 2px 6px;
	
				.action-name {	
					line-height: 24px;
				}

				.el-input__inner {

					background-color: #1A212B;
					color: #999999;
				}

				button {

					margin-top: 2px;
					margin-left: 5px;
				}
			}
		}
	}

	.row-main {

		height: auto;

		.main-view-root {

			padding-left: 1050px;
			box-sizing: border-box;
		}
	}

	.buys {

		width: 500px;
		margin-left: -1050px;
	}

	.boughts {

		width: 550px;
		margin-left: -550px;
	}

	.buys,
	.boughts {

		float: left;
		box-sizing: border-box;
		padding: 5px 0;
		overflow-x: hidden;
		overflow-y: scroll;
		border-right: 1px solid #283A56;

		.buys-inter {
			
			padding-left: 3px;

			.bulletin {

				height: 28px;
				line-height: 28px;
				padding: 0 8px;

				> .prop-value {
					padding-right: 20px;
				}
			}
		}

		.stock-unit {

			&:not(:first-child) {
				margin-top: 4px;
			}

			&.focused {

				border: 1px solid #2099FE ;
				
				.title {
					background-color: #1D5DB0 ;
				}

				.unit-body {
					background-color: #113362FF;
				}
			}

			.title {

				height: 28px;
				line-height: 28px;
				padding: 0 8px;
				border-bottom: 1px solid rgb(12, 16, 22);
				font-size: 16px;
				font-weight: bold;
			}

			.unit-body {
				padding: 6px 8px;
			}

			button {
				width: 50px;
			}

			.ctr-row {
				padding: 5px 0;
			}

			.ctr-dynamic {

				display: inline-block;
				margin-right: 40px;
			}

			.row-allocation {

				position: relative;
				top: 3px;

				.allocations {

					overflow: hidden;
					word-wrap: normal;
					white-space: nowrap;
				}
				
				.each-aloc {
					padding-right: 10px;
				}
			}

			.row-user-price > * {

				margin-top: -30px;
				margin-right: 70px;
			}

			.row-protect > * {
				margin-top: -60px;
			}

			.ctr-order-btn {
				
				position: absolute;
				right: 5px;
				bottom: 5px;
			}
		}
	}

	.data-wall {
		
		box-sizing: border-box;

		.entrust-views {

			height: 290px;
			box-sizing: border-box;
		}

		.corner-area {

			box-sizing: border-box;
			height: 470px;
			padding-top: 30px;
		}
	}

	.credit-option {

		position: absolute;
		right: 113px;
		top: 8px;
		z-index: 1;
	}

	.history-credit-option {

		position: absolute;
		right: 128px;
		top: 12px;
		z-index: 1;
	}

	.choice-box {

		position: relative;
    	// top: -8px;
		display: block;
		width: 410px;

		.el-radio {

			margin-right: 32px;
			line-height: 24px;
		}

		.percent-choice-3 {
			margin-right: 100px;
		}

		.percent-choice-4 {
			margin-right: 99px;
		}

		.abs-amount {

			display: block;
			width: 80px;
    		margin-top: -20px;
			margin-left: 60px;
		}

		.user-position {
			
			display: block;
			width: 60px;
			margin-top: -25px;
			margin-left: 225px;
		}
	}

	.ctr-thres {

		box-sizing: border-box;
		display: inline-block;
		width: 50%;
		
		&.col-first {
			padding-left: 40px;
		}
		
		&.col-second {
			padding-left: 40px;
		}

		.choice-check {

			width: 75px;

			&.shorter {
				width: 63px;
			}
		}
	}

	.thres-option-customDownRateOpen {

		.choice-check {
			width: 100px;
		}
	}

	.boughts {

		.ctr-thres {

			&.col-first,
			&.col-second {

				padding-left: 10px;
				width: 38%;
			}
		}

		.row-entrust {

			padding-left: 150px !important;

			.entrusts {
				min-height: 50px;
			}

			.summary {
			
				float: left;
				margin-left: -150px;
				line-height: 20px;
				font-weight: bold;
	
				.prop-name {
	
					display: inline-block;
					width: 100px;
					text-align: right;
				}
			}

			.each-entrust {

				display: block;
				float: left;
				width: 50%;
				line-height: 20px;

				.aname {

					display: inline-block;
					width: 45px;
					text-align: right;
				}
	
				.hands {
	
					display: inline-block;
					width: 95px;
				}
			}
		}

		.remote-queue {

			max-height: 130px;
			overflow-y: auto;
			
			.infinite-list {

				margin: 0;
				padding: 0;
				max-height: 130px;
				overflow: auto;
			}

			.remote {

				display: inline-block;
				box-sizing: border-box;
				width: 44.5px;
				height: 24px;
				line-height: 24px;
				padding-left: 3px;
				cursor: default;

				&.highlighted {
					background-color: #942C2CFF;
				}

				&.large-scale {
					background-color: #d28181;
				}
			}
		}
	}

	.bought-title {

		height: 100%;
		line-height: 28px;
		padding-left: 6px;
	}

	.type-tabs {

		height: 28px;
		overflow: hidden;
	}

	.entrust-views {

		padding-top: 2px;
		background-color: #1A212B;

		.entrust-options {

			position: relative;
			height: 0;

			.el-checkbox {

				position: absolute;
				z-index: 1;
				margin-left: 250px;
				margin-top: -30px;
			}
		}

		.stock-tabs {

			bottom: 3px;
			height: 24px;
			width: 100%;
			overflow: hidden;

			.tab-item {

				display: inline-block;
				height: 100%;
				line-height: 24px;
				max-width: 80px;
				text-align: center;
				cursor: default;

				&.buy {
					
					background-color: #942C2C;
					border: 1px solid #0C1016;
				}

				&.sell {

					background-color: #1BBE48;
					border: 1px solid #0C1016;
				}
			}
		}
	}

	.corner-tabs {

		margin-top: -30px;

		.tab-unit:last-child {

			&.selected {
				background-color: #1BBE48 !important;
			}
		}
	}

	.corner-views {
		box-sizing: border-box;
	}

	.boarding {

		box-sizing: border-box;
		min-width: 285px;
		padding-right: 2px;
		overflow: hidden;

		.boarding-title {

			height: 28px;
			line-height: 28px;
			padding-left: 6px;
			overflow: hidden;
		}

		.levels {

			float: left;
			box-sizing: border-box;
			width: 45%;

			.level-item {

				box-sizing: border-box;
				padding-left: 30px;
				height: 16.55px;

				.level-name {

					width: 30px;
					margin-left: -30px;
				}

				.level-p-h {

					box-sizing: border-box;
					height: 100%;

					> * {

						display: block;
						float: left;
						box-sizing: border-box;
						text-align: right;
					}
				}
		
				.level-price {
					width: 50%;
				}
		
				.level-hands {

					width: 50%;
					padding-right: 10px;
				}
			}
		
			.seperator {
				
				height: 1px;
				background-color: #0C1016;
			}
		}

		.transactions {

			float: left;
			box-sizing: border-box;
			width: 55%;

			.trans-item {

				box-sizing: border-box;
				padding-left: 75px;
				height: 17.2px;
				line-height: 17px;

				&.new-minute-start {
					border-bottom: 1px dotted white;
				}

				.trans-time {
					
					width: 75px;
					margin-left: -75px;
				}

				.trans-p-h {

					box-sizing: border-box;
					height: 100%;

					> * {

						display: block;
						float: left;
						box-sizing: border-box;
						text-align: right;
					}
				}
		
				.trans-price {
					width: 50%;
				}
		
				.trans-hands {

					width: 50%;
					padding-right: 10px;
				}
			}
		}
	}

	.sells {

		height: 100%;
		overflow: auto;

		.sell-unit {

			min-width: 430px;

			&:not(:first-child) {
				margin-top: 5px;
			}

			&.focused {

				border: 1px solid #2099FE ;
				
				.title {
					background-color: #1D5DB0 ;
				}

				.unit-body {
					background-color: #113362FF;
				}
			}

			.title {

				height: 36px;
				line-height: 36px;
				padding: 0 8px;
			}

			.unit-body {

				padding-left: 8px;
				
				.left-part {
					margin-right: 82px;
				}

				.right-part {

					float: right;
					width: 82px;
					text-align: center;
					padding-top: 5px;

					button {

						padding: 2px 0;
						margin: 0 0 5px 0;
					}
				}

				.info {

					height: 23px;
					line-height: 23px;
				}

				.ctr-dynamic {

					display: inline-block;
					margin-right: 10px;
				}
			}

			button {
				width: 70px;
			}

			.ctr-row {
				padding: 5px 0;
			}

			.summary {
				table {
					tr {
						height: 18px;
						td {
							line-height: 16px;
						}
					}
				}
			}
		}
	}

	.footer-row {

		position: absolute;
		bottom: 0;
		width: 100%;
		height: 32px;
		z-index: 889;
		box-sizing: border-box;
		padding-right: 240px;

		.footer-row-inner {

			height: 100%;
			line-height: 32px;
			padding-left: 20px;
			background-color: #283A56;
			overflow: hidden;

			.summary-item {
				padding-right: 35px;
			}
		}
	}

	.no-data-notice {

		width: 100%;
		height: 100%;
		min-height: 100px;
		display: table;

		.displaying {

			display: table-cell;
			vertical-align: middle;
			text-align: center;
			font-size: 18px;
		}
	}
}

.el-tooltip__popper.is-dark {

	background-color: #324F80;
    box-shadow: 0px 4px 20px 0px rgba(0, 0, 0, 0.5);
}