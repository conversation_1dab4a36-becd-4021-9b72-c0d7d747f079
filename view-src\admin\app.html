<div class="app-view-root s-border-box">
    <template>
        <div class="s-scroll-bar" style="overflow: auto">
            <div class="s-typical-toolbar">
                <el-button size="mini" type="primary" @click="createApp">
                    <i class="iconfont icon-add"></i> 创建应用
                </el-button>
                <el-button size="mini" class="s-pull-right" @click="refreshApp">
                    <span class="el-icon-refresh"></span> 刷新
                </el-button>
            </div>
            <data-tables layout="pagination,table" table-name="appManagement" configurable-column="false"
                role="orgAdmin" @row-click="setCurrentRow" ref="report-table" class="indicator s-searchable-table"
                v-bind:data="appList" v-bind:table-props="tableProps" v-bind:pagination-props="paginationDef"
                v-bind:search-def="searchDef">
                <el-table-column type="index" width="90" align="center" label="序号"></el-table-column>
                <el-table-column label="ID" min-width="150" prop="id"></el-table-column>
                <el-table-column label="应用名称" min-width="200" prop="appName" show-overflow-tooltip></el-table-column>
                <el-table-column label="已分享人员" min-width="200" show-overflow-tooltip>
                    <template slot-scope="scope">
                        <el-row>
                            <el-col class="s-ellipsis" :span="24">
                                <el-tooltip content="分享模板" placement="top" :enterable="false" :open-delay="850">
                                    <a class="s-cp icon-button operation-block iconfont icon-edit"
                                        @click="openShareDialog(scope.row)"></a>
                                </el-tooltip>
                                <span>
                                    {{ scope.row.users && scope.row.users.length > 0 ?
                                scope.row.users.map(user => user.fullName).join('、') : '暂无分享'}}
                                </span>
                            </el-col>
                        </el-row>
                    </template>
                </el-table-column>
                <el-table-column label="操作" width="150" show-overflow-tooltip>
                    <template slot-scope="scope">
                        <el-tooltip content="编辑" placement="top" :enterable="false" :open-delay="850">
                            <a class="s-cp icon-distance icon-button iconfont icon-edit" style="margin-right: 5px"
                                @click="editApp(scope.row)"></a>
                        </el-tooltip>
                        <el-tooltip content="删除" placement="top" :enterable="false" :open-delay="850">
                            <a class="s-cp icon-distance s-color-red iconfont icon-remove"
                                @click="removeApp(scope.row)"></a>
                        </el-tooltip>
                    </template>
                </el-table-column>
            </data-tables>
        </div>

        <el-dialog v-drag width="500px" class="app-dialog" :title="(appDialog.data.id ? '编辑' : '创建') + '应用'"
            :visible="appDialog.visible" v-bind:close-on-click-modal="false" v-bind:close-on-press-escape="false" :show-close="false">
            <el-form class="app-form" :model="appDialog.data" ref="appDialog" :rules="appDialog.rules" label-width="100px">
                <el-form-item label="应用名称:" prop="appName">
                    <el-input v-model="appDialog.data.appName" placeholder="请输入应用名称"></el-input>
                </el-form-item>
                <el-form-item label="应用简介:" prop="describe">
                    <el-input v-model="appDialog.data.describe" resize="none" type="textarea" :rows="4"
                        placeholder="请输入应用简介"></el-input>
                </el-form-item>
                <el-form-item label="URL地址" prop="url">
                    <el-input v-model="appDialog.data.url" placeholder="请输入URL地址"></el-input>
                </el-form-item>
            </el-form>
            <div slot="footer">
                <el-button type="primary" size="small" @click="saveApp">确定</el-button>
                <el-button @click="closeAppDialog" size="small">取消</el-button>
            </div>
        </el-dialog>

        <el-dialog class="app-dialog" v-drag title="分享应用" :visible="shareDialog.visible"
            v-bind:close-on-click-modal="false" v-bind:close-on-press-escape="false" :show-close="false">
            <el-form class="app-form" :model="shareDialog.data" ref="shareDialog" :rules="shareDialog.rules"
                label-width="100px">
                <el-form-item class="app-form-select" label="选择用户：" prop="shareUser">
                    <el-select style="width: 100%;" v-model="shareDialog.data.shareUser" multiple
                        placeholder="请选择需要分享的用户">
                        <el-option v-for="(item, idx) in users" :key="idx" :label="item.fullName" :value="item.id">
                        </el-option>
                    </el-select>
                </el-form-item>
            </el-form>
            <div slot="footer">
                <el-button type="primary" size="small" @click="shareToUser">确定</el-button>
                <el-button size="small" @click="closeShare">取消</el-button>
            </div>
        </el-dialog>
    </template>
</div>