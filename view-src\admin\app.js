const BaseAdminView = require('./baseAdminView').BaseAdminView;
const DataTables = require('../../libs/3rd/vue-data-tables.min.3.4.2');
const drag = require('../../directives/drag');

class View extends BaseAdminView {
    get repoApp() {
        return require('../../repository/app').repoApp;
    }

    get repoUser() {
        return require('../../repository/user').repoUser;
    }

    get systemUserEnum() {
        return require('../../config/system-enum.user').systemUserEnum;
    }

    standardAppItem(app) {
        app.users = (app.users || []).map(app_user => {
            let this_user = this.userListHash[app_user.id || app_user.userId] || {};
            app_user.fullName = this_user.fullName;
            return app_user;
        });
        return app;
    }

    createApp() {
        const self = this;
        this.vueApp = new Vue({
            el: this.$container.querySelector('.app-view-root'),
            data: {
                appDialog: {
                    visible: false,
                    data: {
                        id: 0,
                        appName: '',
                        describe: '',
                        url: '',
                        createUserId: 0,
                    },
                    rules: {
                        appName: { type: 'string', required: true, message: '请输入应用名称' },
                        url: [
                            { type: 'string', required: true, message: '请输入URL地址' },
                            {
                                validator(rule, value, callback) {
                                    if (!value.startsWith('http')) {
                                        callback('请以 http:// 或 https:// 开头');
                                    } else {
                                        callback();
                                    }
                                },
                            },
                        ],
                    },
                },
                shareDialog: {
                    visible: false,
                    data: {
                        shareUser: [],
                    },
                    rules: {
                        // shareUser: { type: 'array', required: true, message: '请至少选择一个用户', trigger: 'change' },
                    },
                },
                appList: [],
                tableProps: this.systemSetting.tableProps,
                paginationDef: { ...this.systemSetting.tablePagination, layout: 'prev,pager,next,sizes,total' },
                searchDef: {
                    inputProps: {
                        placeholder: '输入关键字筛选',
                        prefixIcon: 'iconfont icon-search',
                    },
                },
                users: [],
                currentRow: null,
            },
            directives: {
                drag,
            },
            components: {
                DataTables: DataTables.DataTables,
                DataTablesServer: DataTables.DataTablesServer,
            },
            mixins: [],
            watch: {
                'shareDialog.visible'(val) {
                    if (val && this.users.length == 0) {
                        this.getUsers();
                    }
                },
            },
            mounted() {
                this.loadAppList();
            },
            methods: {
                editApp(row) {
                    self.helper.extend(this.appDialog.data, row);
                    this.appDialog.visible = true;
                },
                removeApp(row) {
                    self.interaction.showConfirm({
                        title: '警告',
                        message: '确定要删除当前应用吗',
                        confirmed: async () => {
                            let loading = self.interaction.showLoading({
                                text: '正在删除，请稍后...',
                            });
                            let id = row.id;
                            try {
                                let resp = await self.repoApp.deleteApp(id);
                                if (resp.errorCode === 0) {
                                    this.appList.remove(x => x.id === id);
                                    self.interaction.showSuccess('删除报告成功!');
                                } else {
                                    self.interaction.showError(`删除报告失败,详细信息:${resp.errorCode}/${resp.errorMsg}!`);
                                }
                            } catch (e) {
                                self.interaction.showError('删除报告失败!');
                            } finally {
                                loading.close();
                            }
                        },
                    });
                },
                setCurrentRow(row) {
                    this.currentRow = row;
                },
                async getUsers() {
                    // let loading = self.interaction.showLoading({
                    //     text: '获取用户列表中...',
                    // });
                    // try {
                    let resp = await self.repoUser.getAll();
                    if (resp.errorCode === 0) {
                        let result = resp.data || [];
                        result.forEach(user => {
                            self.userListHash[user.id] = user;
                        });
                        this.users = result;
                    } else {
                        self.interaction.showError(`获取用户列表失败，详细信息:${resp.errorCode}/${resp.errorMsg}`);
                    }
                    // } catch (e) {
                    //     self.interaction.showError('获取用户列表失败!');
                    // } finally {
                    //     loading.close();
                    // }
                },
                shareToUser() {
                    this.$refs.shareDialog.validate(async valid => {
                        if (valid) {
                            try {
                                let resp = await self.repoApp.shareApp(this.currentRow.id, 1, this.renderMappings());
                                if (resp.errorCode === 0) {
                                    self.interaction.showSuccess('应用已分享成功!');
                                    this.refreshApp();
                                    this.closeShare();
                                } else {
                                    self.interaction.showError(`分享报告失败，详细信息:${resp.errorCode}/${resp.errorMsg}`);
                                }
                            } catch (error) {
                                console.log(error);
                                self.interaction.showError('保存分享失败!');
                            }
                        }
                    });
                },
                renderMappings() {
                    return this.shareDialog.data.shareUser;
                },
                closeShare() {
                    this.shareDialog.data.shareUser = [];
                    this.$refs.shareDialog.resetFields();
                    this.$refs.shareDialog.clearValidate();
                    this.shareDialog.visible = false;
                },
                createApp() {
                    this.appDialog.visible = true;
                },
                refreshApp() {
                    this.loadAppList();
                },
                async loadAppList() {
                    let loading = self.interaction.showLoading({
                        text: '获取列表信息中...',
                    });

                    // try {
                    await this.getUsers();
                    let resp = await self.repoApp.getAll();
                    if (resp.errorCode === 0) {
                        this.appList = (resp.data || []).map(x => self.standardAppItem(x));
                    } else {
                        self.interaction.showError(`获取列表信息失败，详细信息:${resp.errorCode}/${resp.errorMsg}`);
                    }
                    // } catch (error) {
                    //     self.interaction.showError('获取列表信息失败!');
                    // }

                    loading.close();
                },
                saveApp() {
                    this.$refs.appDialog.validate(async valid => {
                        if (valid) {
                            let data = this.appDialog.data;
                            try {
                                let resp = data.id ? await self.repoApp.updateApp(data) : await self.repoApp.createApp(data);
                                if (resp.errorCode === 0) {
                                    this.refreshApp();
                                    this.closeAppDialog();
                                } else {
                                    self.interaction.showError(`保存应用失败，错误信息：${resp.errorCode}/${resp.errorMsg}!`);
                                }
                            } catch (error) {
                                self.interaction.showError('保存应用失败!');
                            }
                        }
                    });
                },
                closeAppDialog() {
                    this.appDialog.data.id = 0;
                    this.appDialog.data.appName = '';
                    this.appDialog.data.describe = '';
                    this.appDialog.data.url = '';
                    this.appDialog.data.createUserId = 0;
                    this.appDialog.data.users = [];
                    this.$nextTick(() => {
                        // this.$refs.appDialog.resetFields();
                        this.$refs.appDialog.clearValidate();
                        this.appDialog.visible = false;
                    });
                },
                openShareDialog(row) {
                    this.shareDialog.data.shareUser = row.users ? row.users.map(x => x.userId) : [];
                    this.shareDialog.visible = true;
                },
            },
        });
    }

    constructor(view_name) {
        super(view_name, '应用管理');
        this.$container = null;
        this.vueApp = null;
        this.userListHash = {};
    }

    resizeWindow() {
        var winHeight = this.thisWindow.getSize()[1];
        //Title的高度是35 Tab的高度是30 底部状态栏30
        var extraHeight = 35 + 30 + 30;
        var net_height = winHeight - extraHeight;
        var box = this.$container.querySelector('.app-view-root .s-scroll-bar');
        if (box) {
            box.style.height = net_height + 'px';
        }
    }

    build($container) {
        this.$container = $container;
        this.createApp();
        this.resizeWindow();
    }
}

module.exports = View;
