
const ApproveController = require('./approve').ApproveController;

class View extends ApproveController {

    constructor(view_name, is_standalone_window) {
        super(view_name, is_standalone_window, '已审核订单');
    }

    handleInstruction(order) {
        
        if (order.instructionStatus != this.statuses.waiting.code) {
            this.tableObj.putRow(order);
        }
    }

    refresh() {

        this.searching.keywords = null;
        this.paging.page = 1;
        this.requestInstructions(false, '已审核');
    }

    build($container) {

        super.build($container);
        this.setupTable('smt-aac');
        this.requestInstructions(false, '已审核');
        this.createToolbarApp([]);
        this.lisen2WinSizeChange(this.handleWinSizeChangeProxy);
        this.simulateWinSizeChange();
    }
}

module.exports = View;
