<div class="view-root">
    
    <div class="toolbar">

        <el-input v-model="searching.keywords" class="input-searching" placeholder="请输入关键词" @change="filterRecords" clearable>
            <i slot="prefix" class="el-input__icon el-icon-search"></i>
        </el-input>

        <div class="s-pull-right">
            <el-button type="primary" size="mini" @click="approveAll">全部通过</el-button>
            <el-button type="primary" size="mini" @click="approveSelected">通过勾选</el-button>
            <el-button type="danger" size="mini" @click="rejectAll">全部驳回</el-button>
            <el-button type="danger" size="mini" @click="rejectSelected">驳回勾选</el-button>
        </div>

        <div class="s-pull-right">
            <el-pagination :page-sizes="paging.pageSizes"
                           :page-size.sync="paging.pageSize"
                           :total="paging.total"
                           :current-page.sync="paging.page"
                           :layout="paging.layout"
                           @size-change="handlePageSizeChange"
                           @current-change="handlePageChange"></el-pagination>
        </div>
        
    </div>

    <table>
        <tr>
            <th type="check" fixed-width="40" fixed></th>
            <th label="工作流ID" fixed-width="80" prop="workFlowId" overflowt></th>
            <th label="工作流" min-width="150" prop="workFlowName" searchable sortable overflowt></th>
            <th label="账号名称" min-width="150" prop="accountName" searchable sortable overflowt></th>
            <th label="产品" min-width="150" prop="fundName" searchable sortable overflowt></th>
            <th label="来源" min-width="80" prop="sourceUserName" searchable sortable overflowt></th>
            <th type="program" label="止损" fixed-width="60" prop="stopLoss" formatter="formatStoploss" export-formatter="formatStoplossText" sortable overflowt></th>
            <th type="program" label="指令类型" fixed-width="90" prop="instructionType" formatter="formatOrderType" sortable overflowt></th>
            <th label="代码" fixed-width="100" prop="instrument" overflowt sortable searchable></th>
            <th label="合约" fixed-width="80" prop="instrumentName" overflowt sortable searchable></th>
            <th type="program" label="方向" fixed-width="60" prop="direction" watch="direction" formatter="formatDirection" export-formatter="formatDirectionText" sortable></th>
            <th label="委托量" min-width="80" prop="volumeOriginal" align="right" thousands-int></th>
            <th type="program" label="指令状态" fixed-width="80" prop="instructionStatus" watch="instructionStatus" formatter="formatApproveStatus" export-formatter="formatApproveStatusText" sortable overflowt></th>
            <th type="program" label="执行状态" fixed-width="80" prop="executeStatus" watch="executeStatus" formatter="formatExecuteStatus" export-formatter="formatExecuteStatusText" sortable overflowt></th>
            <th type="program" label="开始时间" fixed-width="120" prop="startTime" formatter="formatDateTime" sortable></th>
            <th type="program" label="操作" fixed-width="100" align="center" fixed="right" formatter="createRowAction" exportable="false"></th>
        </tr> 
    </table>
</div>