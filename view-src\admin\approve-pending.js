const ApproveController = require('./approve').ApproveController;
const electronLocalshortcut = require('../../libs/3rd/electron-localshortcut');

class View extends ApproveController {

    constructor(view_name, is_standalone_window) {

        super(view_name, is_standalone_window, '待审核订单');
        this.approveShortcutKey = 'F10';
    }

    createRowAction(order) {

        let as = this.statuses;
        if (order.instructionStatus != as.waiting.code) {
            return '';
        }

        return `<button event.onclick="approveSingle">${as.passed.mean}</button>
                <button event.onclick="rejectSingle" class="danger">${as.rejected.mean}</button>`;
    }

    approveSingle(order) {

        this.tableObj.deleteRow(this.identifyRecord(order));
        this.makeOrdersGo(this.statuses.passed.code, [order]);
    }

    rejectSingle(order) {

        this.tableObj.deleteRow(this.identifyRecord(order));
        this.makeOrdersGo(this.statuses.rejected.code, [order]);
    }

    handleShortcutApproveAll() {
        this.approveAll();
    }

    approveAll() {

        if (this.tableObj.rowCount == 0) {
            return;
        }

        var all_orders = this.tableObj.extractAllRecords();
        this.makeOrdersGo(this.statuses.passed.code, all_orders, true);
        this.tableObj.uncheckAll();
        this.tableObj.refill([]);
    }

    rejectAll() {

        if (this.tableObj.rowCount == 0) {
            return;
        }

        var all_orders = this.tableObj.extractAllRecords();
        this.makeOrdersGo(this.statuses.rejected.code, all_orders, true);
        this.tableObj.uncheckAll();
        this.tableObj.refill([]);
    }

    approveSelected() {
        this.handleSelected(this.statuses.passed.code);
    }

    rejectSelected() {
        this.handleSelected(this.statuses.rejected.code);
    }

    handleSelected(status_code) {

        if (!this.tableObj.hasAnyRowsChecked) {

            this.interaction.showError('未勾选订单');
            return;
        }

        var checked_orders = this.tableObj.extractCheckedRecords();
        this.makeOrdersGo(status_code, checked_orders);
        this.tableObj.uncheckAll();
        checked_orders.forEach(the_order => {
            this.tableObj.deleteRow(this.identifyRecord(the_order));
        });
    }

    /**
     *
     * @param {Number} istatus <required>
     * @param {Array<Object>} orders <required>
     * @param {Boolean} is_all <optional> 非全部，则无需提供，默认为false
     */
    makeOrdersGo(istatus, orders, is_all) {

        if (!(orders instanceof Array) || orders.length == 0) {

            this.interaction.showError('没有符合条件的订单');
            return;
        }

        var data = {

            status: istatus,
            orders: orders.map(x => x.instructionId),
            isAll: !!is_all,
        };

        this.renderProcess.send(this.systemEvent.auditOrder, data);
    }

    handleInstruction(order) {

        if (order.instructionStatus == this.statuses.waiting.code) {
            this.tableObj.putRow(order);
        }
    }

    refresh() {

        this.searching.keywords = null;
        this.paging.page = 1;
        this.requestInstructions(true, '待审核');
    }

    dispose() {
        electronLocalshortcut.unregister(this.approveShortcutKey);
    }

    registerShortcutListener() {
        electronLocalshortcut.register(this.approveShortcutKey, () => { this.handleShortcutApproveAll(); });
    }

    build($container) {

        super.build($container);
        this.setupTable('smt-aap');
        this.requestInstructions(true, '待审核');
        this.createToolbarApp([this.approveAll, this.approveSelected, this.rejectAll, this.rejectSelected]);
        this.registerShortcutListener();
        this.lisen2WinSizeChange(this.handleWinSizeChangeProxy);
        this.simulateWinSizeChange();
    }
}

module.exports = View;
