const IView = require('../../component/iview').IView;
const SmartTable = require('../../libs/table/smart-table').SmartTable;
const ColumnCommonFunc = require('../../libs/table/column-common-func').ColumnCommonFunc;
const procedureRepo = require('../../repository/procedure').repoProcedure;

class ApproveController extends IView {

    get statuses() {
        return this.systemEnum.examineStatuses;
    }

    constructor(view_name, is_standalone_window, title) {

        super(view_name, is_standalone_window, title);

        this.searching = {
            keywords: null,
        };

        var pagination = this.systemSetting.tablePagination;
        this.paging = {

            pageSizes: pagination.pageSizes,
            pageSize: pagination.pageSize,
            layout: pagination.layout,
            total: 0,
            page: 1,
        };

        this.handleWinSizeChangeProxy = this.handleWinSizeChange.bind(this);
    }

    /**
     * @param {Array<Function>} extended_methods
     */
    createToolbarApp(extended_methods) {

        this.toolbar = new Vue({

            el: this.$container.querySelector('.view-root > .toolbar'),
            data: {
                searching: this.searching,
                paging: this.paging,
            },
            methods: this.helper.fakeVueInsMethod(this, [this.filterRecords, this.handlePageSizeChange, this.handlePageChange, ...extended_methods]),
        });
    }

    setupTable(table_name) {

        this.helper.extend(this, ColumnCommonFunc);
        var $table = this.$container.querySelector('.view-root > table');

        this.tableObj = new SmartTable($table, this.identifyRecord, this, {

            tableName: table_name,
            displayName: this.title,
            enableConfigToolkit: true,
            recordsFiltered: filtered_count => { this.paging.total = filtered_count; },
        });

        this.tableObj.setPageSize(this.paging.pageSize);
    }

    filterRecords() {
        this.tableObj.setKeywords(this.searching.keywords);
    }

    identifyRecord(record) {
        return record.id;
    }

    formatStoploss(order, is_stop_loss) {
        return this.helperUi.makeYesNoLabelHtml(!is_stop_loss, { yesLabel: '止损', noLabel: '非止损' });
    }

    formatStoplossText(order, is_stop_loss) {
        return !is_stop_loss ? '止损' : '非止损';
    }

    formatOrderType(order, order_type) {

        var its = this.systemEnum.instructionTypes;

        switch (order_type) {

            case its.algorithm.code:
                return its.algorithm.mean;

            case its.normal.code:
                return its.normal.mean;

            default:
                return 'unknown';
        }
    }

    formatApproveStatus(order, istatus) {

        let as = this.statuses;
        let result;

        switch (istatus) {

            case as.passed.code:
                result = [as.passed.mean, 's-bg-green'];
                break;

            case as.waiting.code:
                result = [as.waiting.mean, 's-bg-yellow'];
                break;

            case as.rejected.code:
                result = [as.rejected.mean, 's-bg-red'];
                break;

            default:
                result = ['unknown', ''];
        }

        return `<span class="s-flag ${result[1]}">${result[0]}</span>`;
    }

    formatApproveStatusText(order, istatus) {

        let as = this.statuses;

        switch (istatus) {

            case as.passed.code:
                return as.passed.mean;

            case as.waiting.code:
                return as.waiting.mean;

            case as.rejected.code:
                return as.rejected.mean;

            default:
                return 'unknown';
        }
    }

    formatExecuteStatus(order, execute_status) {
        return `<span class="s-flag ${execute_status ? 's-bg-green' : 's-bg-yellow'}">${execute_status ? '已结束' : '进行中'}</span>`;
    }

    formatExecuteStatusText(order, execute_status) {
        return execute_status ? '已结束' : '进行中';
    }

    /**
     *
     * @param {Boolean} is_waiting
     * @param {String} data_type_name
     */
    async requestInstructions(is_waiting, data_type_name) {

        if (this._isRequesting) {
            return;
        }

        this._isRequesting = true;
        var loading = this.interaction.showLoading({ text: `获取${data_type_name}订单...` });

        try {

            let resp = await procedureRepo.getInstructions();
            if (resp.errorCode === 0) {

                let all_orders = resp.data || [];
                let expected_orders = is_waiting ? all_orders.filter(x => x.instructionStatus == this.statuses.waiting.code)
                                                 : all_orders.filter(x => x.instructionStatus != this.statuses.waiting.code);

                let orders = expected_orders.map(x => { return this.reshapeOrder(x); });
                this.tableObj.refill(orders);
            } 
            else {
                this.interaction.showError(`获取${data_type_name}订单失败，${resp.errorCode}/${resp.errorMsg}`);
            }
        }
        catch (ex) {

            this.interaction.showError(`获取${data_type_name}列表异常`);
            console.error(ex);
        }
        finally {

            this._isRequesting = false;
            loading.close();
        }
    }

    reshapeOrder(order) {

        let content = (order.operateContent || {}).content || {};

        for (let key in content) {

            if (key == 'id') {

                order.instructionId = order.id;
                order.id = content.id;
            } 
            else {
                order[key] = content[key];
            }
        }

        delete order.operateContent;
        return order;
    }

    handlePageSizeChange() {
        this.tableObj.setPageSize(this.paging.pageSize);
    }

    handlePageChange() {
        this.tableObj.setPageIndex(this.paging.page);
    }

    handleInstruction(instruction) {
        console.error('method is not implemented', instruction);
    }

    config() {
        this.tableObj.showColumnConfigPanel();
    }

    listen2Events() {
        this.standardListen(this.systemEvent.notifyInstruction, this.handleInstruction.bind(this));
    }

    handleWinSizeChange(win_width, win_height) {
        this.tableObj.setMaxHeight(Math.max(100, win_height - 165));
    }

    build($container) {

        super.build($container);
        this.listen2Events();
    }
}

module.exports = { ApproveController };
