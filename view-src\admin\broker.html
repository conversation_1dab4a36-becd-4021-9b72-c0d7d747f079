<div class="broker-view-root">
    <template>
        <div class="s-scroll-bar" style="overflow: auto">
            <div class="s-typical-toolbar">
                <el-button size="mini" type="primary" @click="openBrokerCreationDialog">
                    <i class="iconfont icon-add"></i> 添加经纪商
                </el-button>
                <el-input v-model="searching.value" style="width: 160px;" placeholder="请输入关键词" clearable>
                    <i slot="prefix" class="el-input__icon el-icon-search"></i>
                </el-input>
                <el-button class="s-pull-right" size="mini" @click="refresh">
                    <span class="el-icon-refresh"></span> 刷新
                </el-button>

            </div>
            <data-tables layout="pagination,table" table-name="brokerManagement" :filters="filters" table-label="券商管理"
                role="superAdmin" ref="table" class="s-searchable-table"
                :default-sort="{prop: 'id', order: 'descending'}" v-bind:data="brokerList"
                v-bind:table-props="tableProps" v-bind:pagination-props="paginationDef" v-bind:search-def="searchDef">
                <el-table-column key="brokerManagementIndex" prop="index" label="序号" type="index" width="50"
                    align="center"></el-table-column>
                <el-table-column sortable key="brokerManagementBrokerName" label="名称" min-width="150" prop="brokerName">
                </el-table-column>
                <el-table-column sortable key="brokerManagement" label="经纪商代码" min-width="100"
                    show-overflow-tooltip prop="brokerId"></el-table-column>
                <el-table-column key="brokerManagementBrokerType" label="接口类型" min-width="95" align="center"
                    prop="brokerType" sortable="custom">
                    <template slot-scope="props">
                        <div v-html="renderBrokerType(props.row.brokerType)"></div>
                    </template>
                </el-table-column>
                <el-table-column prop="operation" key="brokerManagementOperation" label="操作" width="80"
                    class-name="s-col-oper">
                    <template slot-scope="props">
                        <el-tooltip content="修改" placement="top" :enterable="false" :open-delay="850">
                            <a class="el-icon-edit" @click="handleEditBroker(props.row)"></a>
                        </el-tooltip>
                        <el-tooltip content="删除" placement="top" :enterable="false" :open-delay="850">
                            <a class="icon-button el-icon-delete s-color-red"
                                @click="handleRemoveBroker(props.row)"></a>
                        </el-tooltip>
                    </template>
                </el-table-column>
            </data-tables>

            <el-dialog v-drag class="broker-dialog" v-bind:title="dialog.broker.title" width="400px"
                v-bind:visible="dialog.broker.visible" :show-close="false" v-bind:close-on-click-modal="false"
                v-bind:close-on-press-escape="false">
                <el-form class="broker-form" v-bind:model="dialog.broker.form" v-bind:rules="dialog.broker.rules"
                    ref="brokerForm" label-width="100px">
                    <el-row>
                        <el-col v-bind:span="24">
                            <el-form-item label="经纪商代码" prop="brokerId">
                                <el-input v-bind:disabled="currentBroker != null" v-model="dialog.broker.form.brokerId">
                                </el-input>
                            </el-form-item>
                            <el-form-item label="经纪商名称" prop="brokerName">
                                <el-input v-model="dialog.broker.form.brokerName"></el-input>
                            </el-form-item>
                            <el-form-item label="经纪商类型" prop="assetType">
                                <el-select style="width: 100%;" v-model="dialog.broker.form.assetType">
                                    <el-option v-for="(type, type_idx) in assetsTypes" v-bind:key="type_idx"
                                        v-bind:value="type.code" v-bind:label="type.name"></el-option>
                                </el-select>
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row style="margin-bottom: 10px">
                        <el-col v-bind:span="24">
                            <el-button size="small" @click="handleOpenIpDialog">添加</el-button>
                        </el-col>
                    </el-row>
                    <el-row>
                        <el-col v-bind:span="24">
                            <el-table configurable-column="false" max-height="160" v-bind:data="dialog.broker.ipList">
                                <el-table-column label="服务器地址" min-width="150" prop="ip"></el-table-column>
                                <el-table-column label="端口" min-width="60" prop="port"></el-table-column>
                                <el-table-column width="80" label="操作" fixed="right" class-name="s-col-oper">
                                    <template slot-scope='props'>
                                        <el-tooltip content="修改" placement="top" :enterable="false" :open-delay="850">
                                            <a class="icon-button iconfont icon-edit"
                                                @click="handleEditIp(props.row)"></a>
                                        </el-tooltip>
                                        <el-tooltip content="删除" placement="top" :enterable="false" :open-delay="850">
                                            <a class="icon-button el-icon-delete s-color-red"
                                                @click="handleDeleteIp(props.row)"></a>
                                        </el-tooltip>
                                    </template>
                                </el-table-column>
                            </el-table>
                        </el-col>
                    </el-row>
                </el-form>
                <div slot="footer">
                    <el-button type="primary" @click="handleSaveBroker" size="small">确定</el-button>
                    <el-button @click="() => dialog.broker.visible = false" size="small">取消</el-button>
                </div>
            </el-dialog>

            <el-dialog v-drag class="broker-dialog" append-to-body v-bind:title="dialog.ip.title" width="400px"
                v-bind:visible="dialog.ip.visible" v-bind:before-close="() => dialog.ip.visible = false"
                v-bind:close-on-click-modal="false" v-bind:close-on-press-escape="false">
                <el-form class="broker-form" v-bind:model="dialog.ip.form" v-bind:rules="dialog.ip.rules" ref="ipForm"
                    label-width="100px">
                    <el-row>
                        <el-col v-bind:span="24">
                            <el-form-item label="IP" prop="ip">
                                <el-input v-model="dialog.ip.form.ip"></el-input>
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row>
                        <el-col v-bind:span="24">
                            <el-col v-bind:span="24">
                                <el-form-item label="端口" prop="port">
                                    <el-input v-model="dialog.ip.form.port"></el-input>
                                </el-form-item>
                            </el-col>
                        </el-col>
                    </el-row>
                </el-form>
                <span slot="footer">
                    <el-button type="primary" @click="handleSaveIp" size="small">确定</el-button>
                    <el-button @click="() => dialog.ip.visible = false" size="small">取消</el-button>
                </span>
            </el-dialog>
        </div>
    </template>
</div>