const helper = require('../../../libs/helper').helper;
let enum_dict = {
    share_management: [1, 2, 11, 12],

    organization_dict: {
        default: { code: 'default' },
        gaoyu: { code: 'gaoyu' }
    },

    customerType: [
        { code: 1, value: "机构", color: "#3ead58" },
        { code: 2, value: "自然人", color: "#49ACF6" },
        { code: 3, value: "产品", color: "#C77DDF" },
    ],

    confirmType: [
        { code: 1, value: "未确认", color: "#ED8B26" },
        { code: 2, value: "已确认", color: "#339900" }
    ],

    notifyType: [

        { code: 1, value: "投资者风险匹配告知书及投资者确认函", color: "" },
        { code: 2, value: "风险不匹配警示函及投资者确认书", color: "" }
    ],

    conversionType: [

        { code: 1, value: "专业转普通", color: "#339900" },
        { code: 2, value: "普通转专业", color: "#ED8B26" }
    ],
    risk_name: [
        { code: 1, value: "汇总", color: "#339900" },
        { code: 0, value: "单票", color: "#ED8B26" }
    ],
    level_name: [
        { code: 0, value: "预警", color: "#6387ED" },
        { code: 1, value: "报警", color: "#F5AB00" },
        { code: 2, value: "严重报警", color: "#EF3939" }
    ],


    fundTypes: [
        { value: 1, text: '母基金', color: "#179CC5", type: 'primary' },
        { value: 2, text: '子基金', color: "#296FCB", type: 'success' },
        { value: 3, text: '期货账户', color: "#7D87DF" },
        { value: 4, text: '证券账号', color: "#C77DDF" },
        { value: 5, text: '虚拟账号基金', color: "#296FCB", type: 'info' },
        { value: 6, text: "虚拟母基金", color: "#179CC5", type: 'info' },
        { value: 7, text: "基金", color: "#ccc" },
    ],

    supportSsl: [
        { code: 1, value: "是", color: "#1BBE48" },
        { code: 0, value: "否", color: "red" }
    ],

    statusType: [
        { code: 1, value: "未提交", color: "#33CC66" },
        { code: 2, value: "审核中", color: "#663399" },
        { code: 3, value: "审核成功", color: "#33CCCC" },
        { code: 4, value: "驳回", color: "#336699" }
    ],

    bench: [

        { code: '399300.SZ', value: '沪深300' },
        { code: '000016.SH', value: '上证50' },
        { code: '399905.SZ', value: '中证500' },
        { code: '000852.SH', value: '中证1000' },
        { code: '000001.SH', value: '上证综指' },
        { code: '399001.SZ', value: '深证成指' },
        { code: 'NH0400.NHF', value: 'NH0400.NHF' },
        { code: 'NH0300.NHF', value: 'NH0300.NHF' }
    ],

    fund_type_dict: {

        mother_fund: { code: 1, value: '母基金' },
        child_fund: { code: 2, value: '子基金' },
        virtual_account_fund: { code: 5, value: '虚拟账号基金' },
    },

    obj_status_dict: {

        ok: { code: 1, value: '正常' },
        disabled: { code: 0, value: '禁用' }
    },

    order_status_dict: {

        canceled: { code: 54, value: '已取消' },
        confirmed: { code: 15, value: '已确认' },
        fully_exchanged: { code: 19, value: '全部成交' },
        invalid: { code: 30, value: '无效' },
        not_confirmed: { code: 13, value: '未确认' },
        not_traded: { code: 16, value: '未成交' },
        partial_exchanged: { code: 17, value: '部分成交' },
        submited: { code: 10, value: '已提交' }
    },

    money_management: [3, 4, 5, 6, 7],

    trading_direction_dict: {

        buy: { code: 1, value: '买入' },
        sell: { code: -1, value: '卖出' }
    },

    cash_flow_direction_dict: {

        fund_buy: { code: 1, value: "基金申购", color: "#3FA0E8" },
        fund_redeem: { code: 2, value: "基金赎回", color: "#4EB065" },
        bank_2_futures: { code: 3, value: "银转期", color: "#F09D47" },
        futures_2_bank: { code: 4, value: "期转银", color: "#5AB064" },
        bank_2_stock: { code: 5, value: "银转股", color: "#3FAFED" },
        stock_2_bank: { code: 6, value: "股转银", color: "#DE5E6E" },
        cost_transfer: { code: 7, value: "三费划转", color: "#3595CA" },
        cash_adj: { code: 8, value: "现金调整", color: " #3FAFE2" },
        otc: { code: 9, value: "场外交易", color: "#179CC5" },
        fund_passive_buy: { code: 11, value: "基金被申购", color: "#C27DDF" },
        fund_passive_redeem: { code: 12, value: "基金被赎回", color: "#E86A6A" },
        fund_customer_buy: { code: 13, value: "客户申购", color: "#3FA0E8" },
        fund_customer_sell: { code: 14, value: "客户赎回", color: "#4EB065" },
    },

    data_source_dict: {

        xuntou: { code: "1", value: "讯投", logo: "images/xuntou-logo.png" },
        tongdaxin: { code: "2", value: "通达信", logo: "images/tongdaxin-logo.jpg" },
        umxc: { code: "3", value: "交易平台", logo: "images/uxmc-logo.png" },
        hengsheng_pb: { code: "4", value: "恒生PB", logo: "images/hengsheng-logo.png" }
    },

    asset_unit_dict: {
        mother_fund: { code: 1, value: "母基金", is_fund: true, is_mother_fund: true, color: "#296FCB" },
        child_fund: { code: 2, value: "子基金", is_fund: true, is_child_fund: true, color: "#179CC5" },
        security_futures: { code: 3, value: "期货账户", is_account: true, color: "#7D87DF" },
        security_stock: { code: 4, value: "股票账户", is_account: true, color: "#49ACF6" },
        strategy: { code: 5, value: "策略", is_strategy: true, color: "#F09D47" },
        security_options: { code: 6, value: "期权账户", is_account: true, color: "#F09D47" }
    },

    asset_type_dict: {
        futures: { code: 1, value: "期货", color: "#7D87DF", has_price: true },
        stock: { code: 2, value: "股票", color: "#49ACF6", has_price: true },
        options: { code: 3, value: "期权", color: "#179CC5", has_price: true },
        bond: { code: 4, value: "债券", color: "#C77DDF", has_price: true },
        public_fund: { code: 5, value: "公募基金", is_fund: true, is_public_fund: true, color: "#996699", has_price: true },
        spotgoods: { code: 6, value: "现货", color: "#663399", has_price: true },
        buyback: { code: 7, value: "回购", color: "#3EAD58", has_price: true },
        cash: { code: 8, value: "现金", is_cash: true, color: "#3FAFE2" },
        deposite: { code: 9, value: "银行存款", is_cash: true, is_deposite: true, color: "#6666FF" },
        three_fee: { code: 10, value: "三费划拨", color: "#004455" },
        account_rec: { code: 11, value: "应收账款", color: "#3333FF" },
        account_pay: { code: 12, value: "应付账款", color: "#773333" },
        pledge: { code: 13, value: "质押", color: "#339966" },
        other_fund: { code: 14, value: "其它基金", color: "#446688" },
        private_fund: { code: 15, value: "私募基金", is_fund: true, is_private_fund: true, color: "#6666CC", has_price: true },
        otner_fund: { code: 17, value: "其它公募基金", is_fund: true, color: "#6644CC", has_price: true },
        other: { code: 100000, value: "其他", color: "#CC9999" }
    },

    asset_type_futur_stock: {
        futures: { code: 3, value: "期货", color: "#C27DDF", has_price: true },
        stock: { code: 4, value: "股票", color: "#5799EF", has_price: true },
    },
    assetsTypes: [
        { code: 1, value: "期货" },
        { code: 2, value: "股票" },
        { code: 3, value: "期权" },
        { code: 4, value: "债券" },
    ],

    file_types: [],
    file_fund_types: [],
    file_account_types: [],
    funds_list: [],

    cash_direction_dict: {

        withdraw: { code: -1, value: "-减少现金", style: "color:green," },
        deposite: { code: 1, value: "+增加现金", style: "color:red," }
    },

    trading_effect_dict: {

        open: { code: 0, value: '开仓' },
        close: { code: 1, value: '平仓' },
        close_today: { code: 2, value: '平今' },
        close_yesterday: { code: 3, value: '开昨' }
    },

    exchange_market_dict: {

        shfe: { code: "SHFE", value: "上期所" },
        dce: { code: "DCE", value: "大商所" },
        cffex: { code: "CFFEX", value: "中金所" },
        czce: { code: "CZCE", value: "郑商所" },
        shse: { code: "SHSE", value: "上海股票交易所" },
        szse: { code: "SZSE", value: "深圳股票交易所" }
    },
    profit_type: {

        win: "profit-positive",
        lose: "profit-negative",
        nochange: "profit-no-change"
    },

    capital_scale_dict: [

        { code: 1, value: '0亿 ~ 1亿' },
        { code: 2, value: '1亿 ~ 10亿' },
        { code: 3, value: '10亿 ~ 20亿' },
        { code: 4, value: '20亿 ~ 50亿' },
        { code: 5, value: '50亿以上' }
    ],
    ams_user_role_dict: {

        admin: { code: 1, value: "系统管理员" },
        product_manager: { code: 2, value: "产品经理" },
        risk_manager: { code: 3, value: "风控员" },
        trading_manager: { code: 4, value: "交易员" },
        data_import_manager: { code: 5, value: "数据导入员" },
        report_manager: { code: 6, value: "报表管理员" },
        viwer: { code: 7, value: "产品经理" },
        fund_viewer: { code: 9, value: "基金查看员" }
    },

    indicator_value_type_list: [
        { value: 1, text: '单值序列' },
        { value: 2, text: '多值序列' }
    ],
    conversion_type: [
        { value: 1, label: '专业转普通' },
        { value: 2, label: '普通转专业' },
    ],
    customer_cert_types: [

        { value: 1, label: '身份证' },
        { value: 2, label: '居住证' },
        { value: 3, label: '签证' },
        { value: 4, label: '护照' },
        { value: 5, label: '军人证' },
        { value: 6, label: '港澳通行证' },
        { value: 7, label: '台胞证' },
        { value: 8, label: '基金会' },
        { value: 9, label: '行政机关' },
        { value: 10, label: '其他' },
    ],
    company_cert_types: [

        { value: 11, label: '工商营业执照' },
        { value: 12, label: '居住税务登记证' },
        { value: 13, label: '组织机构代码证' },
        { value: 14, label: '事业单位法人证书' },
        { value: 15, label: '国税' },
        { value: 16, label: '地税' },
        { value: 17, label: '基金会' },
        { value: 18, label: '行政机关' },
        { value: 10, label: '其他' },
    ],
    riskGrade_list: [

        { value: 'R5', label: 'R5' }
    ],
    org_group_categories: {

        counselor: 1,
        fund: 2,
        fund_combination: 3
    },
    comm_base_list: [

        { code: "399300.SZ", value: "沪深300" },
        { code: "000016.SH", value: "上证50" },
        { code: "399905.SZ", value: "中证500" },
        { code: "000852.SH", value: "中证1000" },
        { code: "000001.SH", value: "上证综指" },
        { code: "399001.SZ", value: "深证成指" },
        { code: "NH0400.NHF", value: "NH0400.NHF" },
        { code: "NH0300.NHF", value: "NH0300.NHF" }
    ],
    group_list: [

        { "gpId": 220, "userName": "admin", "category": "2", "groupName": "aaa", "createDate": 1496802096000, "deleteFlag": "0" },
        { "gpId": 219, "userName": "admin", "category": "2", "groupName": "ddd", "createDate": 1496800789000, "deleteFlag": "0" },
        { "gpId": 218, "userName": "admin", "category": "2", "groupName": "测试", "createDate": 1496800690000, "deleteFlag": "0" }
    ],
    groupCategories: {

        interest: 1,
        fund: 2,
        fundCombination: 3
    },
    user_status: {

        ok: 1,
        disabled: 0
    },
    capital_management_dict: {
        autonomous: { value: 1, text: "自主发行" },
        consulting: { value: 2, text: "顾问管理" },
        scale: { value: 3, text: "管理规模" }
    },
    join_types: [
        { value: 1, text: "等权" },
        { value: 2, text: "纵向" }
    ],
    data_citys: [
        { name: "北京", value: "beijing" },
        { name: "上海", value: "shanghai" },
        { name: "广州", value: "guangzhou" },
        { name: "深圳", value: "shenzhen" },
        { name: "广东", value: "guangdong" },
        { name: "四川", value: "sichuan" },
        { name: "安徽", value: "anhui" },
        { name: "重庆", value: "chongqing" },
        { name: "福建", value: "fujian" },
        { name: "甘肃", value: "gansu" },
        { name: "广西", value: "guangxi" },
        { name: "贵州", value: "guizhou" },
        { name: "海南", value: "hainan" },
        { name: "河北", value: "hebei" },
        { name: "黑龙江", value: "heilongjiang" },
        { name: "河南", value: "henan" },
        { name: "湖北", value: "hubei" },
        { name: "湖南", value: "hunan" },
        { name: "江苏", value: "jiangsu" },
        { name: "江西", value: "jiangxi" },
        { name: "吉林", value: "jilin" },
        { name: "辽宁", value: "liaoning" },
        { name: "内蒙古", value: "neimenggu" },
        { name: "宁夏", value: "ningxia" },
        { name: "青海", value: "qinghai" },
        { name: "山东", value: "shandong" },
        { name: "山西", value: "shanxi" },
        { name: "陕西", value: "shangxi" },
        { name: "天津", value: "tianjin" },
        { name: "新疆", value: "xinjiang" },
        { name: "西藏", value: "xizang" },
        { name: "云南", value: "yunnan" },
        { name: "浙江", value: "zhejiang" },
        { name: "香港", value: "xianggang" },
        { name: "澳门", value: "aomen" },
        { name: "台湾", value: "taiwan" },
    ],
    capital_scales: [
        { value: 1, text: "0亿 ~ 1亿" },
        { value: 2, text: "1亿 ~ 10亿" },
        { value: 3, text: "10亿 ~ 20亿" },
        { value: 4, text: "20亿 ~ 50亿" },
        { value: 5, text: "50亿以上" }
    ],
    condition_types: [
        { value: 1, label: 'pms_counselor' },
        { value: 2, label: 'pms_product' },
        // {value: 3, label: 'pms_counselor'},
        // {value: 4, label: 'pms_counselor'},
    ],
    employee_numbers: [
        { value: 1, text: "1个 ~ 5个" },
        { value: 2, text: "5个 ~ 15个" },
        { value: 3, text: "15个 ~ 30个" },
        { value: 4, text: "30个 ~ 50个" },
        { value: 5, text: "50个以上" }
    ],
    grading_scores: [
        { value: 10, text: "10分" },
        { value: 9, text: "9分" },
        { value: 8, text: "8分" },
        { value: 7, text: "7分" },
        { value: 6, text: "6分" },
        { value: 5, text: "5分" },
        { value: 4, text: "4分" },
        { value: 3, text: "3分" },
        { value: 2, text: "2分" },
        { value: 1, text: "1分" }
    ],
    provinces: [
        { value: 11, text: "北京" },
        { value: 31, text: "上海" },
        { value: 44, text: "广东" },
        { value: 12, text: "天津" },
        { value: 50, text: "重庆" },
        { value: 51, text: "四川" },
        { value: 32, text: "江苏" },
        { value: 33, text: "浙江" },
        { value: 34, text: "安徽" },
        { value: 35, text: "福建" },
        { value: 36, text: "江西" },
        { value: 37, text: "山东" },
        { value: 41, text: "河南" },
        { value: 42, text: "湖北" },
        { value: 43, text: "湖南" },
        { value: 13, text: "河北" },
        { value: 14, text: "山西" },
        { value: 15, text: "内蒙古" },
        { value: 21, text: "辽宁" },
        { value: 22, text: "吉林" },
        { value: 23, text: "黑龙江" },
        { value: 45, text: "广西" },
        { value: 46, text: "海南" },
        { value: 52, text: "贵州" },
        { value: 53, text: "云南" },
        { value: 54, text: "西藏" },
        { value: 61, text: "陕西" },
        { value: 62, text: "甘肃" },
        { value: 63, text: "青海" },
        { value: 64, text: "宁夏" },
        { value: 65, text: "新疆" }
    ],
    user_type_dict: {
        capital: { value: 1, text: "资金方" },
        counselor: { value: 2, text: "投顾" }
    },
    pms_user_role_dict: {
        super_admin: { value: 1, text: "超级管理员" },
        capital_admin: { value: 2, text: "机构管理员" },
        capital_user: { value: 3, text: "机构用户" },
        counselor_user: { value: 4, text: "投顾用户" }
    },
    user_status_0: [
        { value: 1, text: "可用" },
        { value: 0, text: "禁用" }
    ],
    docking_type: [
        { value: '自营或委外', text: '自营或委外' },
        { value: '风控业务', text: '风控业务' },
        { value: 'PMS', text: 'PMS' },
        { value: '代销或引荐', text: '代销或引荐' },
        { value: '债券', text: '债券' },
        { value: '配资', text: '配资' }
    ],
    capitalist_status: [
        { value: 1, text: '跟进中' },
        { value: 2, text: '已递交资料' },
        { value: 3, text: '已合作' },
    ],
    role_status: [
        { value: 0, text: '禁用' },
        { value: 1, text: '可用' }
    ],
    task_status: [
        { code: '未开始', name: '未开始' },
        { code: '已结束', name: '已结束' }
    ],
    task_status_1: [
        { value: '1', text: '未开始' },
        { value: '0', text: '已结束' }
    ],
    builtin_counselor_tags: ["高收益", "回撤较小", "收益平衡", "日内专家", "多策略", "日内套利"],
    messages: {
        service_error: "服务调用错误",
        http_error: "HTTP未正常响应",
        value_absent: "--"
    },
    template_types: [
        { value: "operation", text: "运营问卷" },
        { value: "strategy-stock", text: "股票策略" },
        { value: "strategy-CTA", text: "CTA策略" },
        { value: "strategy-bond", text: "债券策略" },
        { value: "strategy-product", text: "产品问卷" }
    ],
    template_types_extend_1: [
        { value: "operation", text: "运营问卷" },
        { value: "strategy-stock", text: "股票策略" },
        { value: "strategy-CTA", text: "CTA策略" },
        { value: "strategy-bond", text: "债券策略" },
        { value: "strategy-product", text: "产品问卷" },
        { value: "riskAssessment", text: "风险测评" }
    ],
    sheet_types: [
        { value: "operation", text: "运营问卷" },
        { value: "strategy-stock", text: "股票策略" },
        { value: "strategy-CTA", text: "CTA策略" },
        { value: "strategy-bond", text: "债券策略" },
        { value: "strategy-product", text: "产品问卷" }
    ],
    sheet_status_dict: {
        created: { value: 1, text: '未发送', bg_color: '#3399FF', icon: 'fa fa-hourglass-o' },
        published: { value: 2, text: '已发送', bg_color: '#33CC99', icon: 'fa fa-paper-plane-o' },
        commited: { value: 3, text: '已回收', bg_color: '#66CCFF', icon: 'fa fa-stop-circle' },
        closed: { value: 4, text: '已关闭', bg_color: '#999966', icon: 'fa fa-folder-open-o' }
    },
    sheet_design_method: {
        read_sheet: "rs",
        new_sheet: "ns",
        update_sheet: "us",
        read_template: "rt",
        new_template: "nt",
        update_template: "ut"
    },
    sheet_open_mode: {
        reading: "rd",
        editing: "ed"
    },
    question_types: {
        single_choice: { code: "sc", name: "单选题", icon: "fa fa-dot-circle-o", compatible_controls: ['mc', 'dl', 'sort'] },
        multiple_choice: { code: "mc", name: "多选题", icon: "fa fa-check-square-o", compatible_controls: ['sc', 'dl', 'sort'] },
        dropdown_list: { code: "dl", name: "下拉题", icon: "fa fa-caret-square-o-down", compatible_controls: ['sc', 'mc', 'sort'] },
        sorting: { code: "sort", name: "排序题", icon: "fa fa-sort-numeric-asc", compatible_controls: ['sc', 'mc', 'dl'] },
        single_line_txt: { code: "slt", name: "文本 | 数字 | 日期题", icon: "fa fa-file-o", compatible_controls: [] },
        multiple_line_txt: { code: "mlt", name: "多行文本题", icon: "fa fa-file-text-o", compatible_controls: [] },
        comment: { code: "cmt", name: "说明文本块", icon: "fa fa-commenting", compatible_controls: [] },
        matrix_text: { code: "mt", name: "表格题", icon: "fa fa-table", compatible_controls: [] },
        matrix_single_choice: { code: "msc", name: "矩阵单选题", icon: "fa fa-table", compatible_controls: [] },
        matrix_multiple_choice: { code: "mmc", name: "矩阵多选题", icon: "fa fa-table", compatible_controls: [] },
        rich_text: { code: "rt", name: "富文本题", icon: "fa fa-image", compatible_controls: [] }
    },
    text_input_type_dict: {
        text: { code: "text", name: "普通文本" },
        number: { code: "number", name: "数字" },
        date: { code: "date", name: "日期" }
    },
    ratio_indexes: [
        { value: 1, text: '年化收益' },
        { value: 2, text: '夏普' },
        { value: 3, text: '最大回撤' }
    ],
    fund_columns: [
        { value: 2, text: '成立日期' },
        { value: 3, text: '策略类型' },
        { value: 4, text: '单位净值' },
        { value: 5, text: '累计净值' },
        { value: 6, text: '净值日期' },
        { value: 7, text: '频度' },
        { value: 8, text: '年化收益率' },
        { value: 9, text: '波动率' },
        { value: 10, text: '夏普' },
        { value: 11, text: '最大回撤' },
        { value: 12, text: '累计收益率' },
        { value: 13, text: '最大回撤持续期' },
        { value: 14, text: '最近一年收益率' },
        { value: 15, text: '卡玛比率' },
        { value: 16, text: '偏度' },
        { value: 17, text: '胜率' },
        { value: 18, text: '最大净值恢复期' },
        { value: 19, text: '詹森指数' },
        { value: 20, text: 'Hurst指数' },
        { value: 21, text: 'var' },
        { value: 22, text: '峰度' },
        { value: 23, text: '盈亏比' },
        { value: 24, text: '近一周收益' },
        { value: 25, text: '近一月收益' },
        { value: 26, text: '今年以来收益' },
        { value: 27, text: '最近一年收益' },
        { value: 28, text: '今年以来最大回撤' },
    ],
    cycle: [
        { value: 5, text: '一周' },
        { value: 20, text: '一月' },
        { value: 60, text: '三月' },
        { value: 120, text: '半年' },
        { value: 250, text: '一年' }
    ],
    asset_url_templates: {
        fund: { name: 'fund', url: '/pms/fund-analysis.html?asset_codes={fundCode}&fund_ids={fundId}&fund_type={fund_type}&is_private={is_private}' },
        company: { name: 'company', url: '/pms/counselor-detail.html?cid={comId}&ccode={companyCode}&cname={companyName}&public=0' },
        survey: { name: 'survey', url: '/pms/survey.html?sheet_id={sheet_id}&mode=rd' },
        surveyTask: { name: 'surveyTask', url: '/pms/survey-task.html?taskId={taskId}&taskName={taskName}' },
        contactRecord: { name: 'contactRecord', url: '/pms/capitalist-docking.html?recordId={recordId}&title={title}' },
        capitalist: { name: 'capitalist', url: '/pms/capitalist-maintain.html?capitalistId={capitalistId}&capitalistName={capitalistName}' }
    },
    note_template: `<p><span style="font-size:16px"><strong>一、公司运营（公司资质、股东关系、投研团队实力、发展规划）</strong></span></p>
                      <p><br /><span style="font-size:16px"><strong>二、产品绩效（与基准和同类比的超额收益、关键时期风险应对情况）</strong></span></p>
                      <p><br /><span style="font-size:16px"><strong>三、策略研发（投资理念、策略逻辑、研发流程、策略可持续性）</strong></span></p>
                      <p><br /><span style="font-size:16px"><strong>四、风险控制（风控重视度、风控流程、风控可持续性）</strong></span></p>
                      <p><br /><span style="font-size:16px"><strong>五、商业条件（费用水平、与我方对接的核心投研人员、产品形式）</strong></span></p>`,
    riskGrades: [
        { value: 'C1', text: 'C1' },
        { value: 'C2', text: 'C2' },
        { value: 'C3', text: 'C3' },
        { value: 'C4', text: 'C4' },
        { value: 'C5', text: 'C5' }
    ],

    chart_series_colors: ['#2DB1C0', '#B26CD2', '#2d37c0', '#e6359c', '#5799EF', '#E69934', '#459BCB', '#BCBA29', '#333333', '#7CB5EC', '#434348', '#90ED7D', '#F7A35C', '#8085E9', '#F15C80', '#E4D354', '#2B908F', '#F45B5B', '#91E8E1'],
    organizationTypeList: [
        '政府部门', '院校', '科研所', '国有企业', '集团企业', '股份合作企业', '联营企业', '有限责任公司', '股份有限公司', '私营企业', '港澳台商投资企业', '外商投资企业', '其他'
    ],
    orgIdentificationTypeList: [
        { text: '营业执照', value: 1 },
        { text: '税务登记证', value: 2 },
        { text: '组织机构代码证', value: 3 },
    ],
    identificationTypeList: [
        { text: '身份证', value: 1 },
        { text: '港澳通行证', value: 2 },
        { text: '护照', value: 3 }
    ],
    productTypeList: [

    ],
    productCategoryList: [
        { text: '私募证券投资基金', value: 1 },
        { text: '私募股权投资基金', value: 2 },
        { text: '其他私募投资基金', value: 3 },
    ],
    coloring: {
        default: { name: '默认', code: 'default', color: 'white', file: '/css/coloring/default.css' },
        light_blue: { name: '浅蓝', code: 'light_blue', color: '#49ACF6', file: '/css/coloring/light-blue.css' },
        deep_blue: { name: '深蓝', code: 'deep_blue', color: '#1D2088', file: '/css/coloring/deep-blue.css' },
        purple: { name: '紫色', code: 'purple', color: 'linear-gradient(90deg, #3E5FD7 0%, #6F54F0 100%)', file: '/css/coloring/purple.css' }
    }
};

let chart_typical_options = {
    title: {
        text: null,
        align: 'left',
        style: { color: '#333333', fontSize: '12px', fontWeight: 'bold' }
    },
    chart: {
        type: null
    },
    colors: enum_dict.chart_series_colors,
    xAxis: {
        title: { text: null },
        categories: []
    },
    yAxis: [
        {
            lineWidth: 0,
            title: { text: null },
            gridLineColor: '#999',
            gridLineDashStyle: 'longdash',
            gridLineWidth: 1,
            labels: { style: { color: enum_dict.chart_series_colors[0], fontSize: '12px' } }
        },
        {
            lineWidth: 0,
            title: { text: null },
            gridLineColor: '#aaa',
            gridLineDashStyle: 'longdash',
            gridLineWidth: 1,
            labels: { style: { color: enum_dict.chart_series_colors[1], fontSize: '12px' } },
            opposite: true
        }
    ],
    series: [],
    tooltip: {
        backgroundColor: '#1DA9E1',
        borderWidth: 0,
        shared: true,
        style: {
            color: 'white',
            fontSize: '12px'
        },
        formatter: null
    },
    legend: {
        enabled: true,
        itemStyle: { color: '#666666', fontWeight: 'bold' }
    },
    plotOptions: {
        series: {
            lineWidth: 1,
            animation: { duration: 500 },
            cursor: 'pointer',
            states: { hover: { lineWidth: 3 } },
            allowPointSelect: true,
            marker: { enabled: false, fillColor: 'white', lineWidth: 1, lineColor: null, radius: 4, symbol: 'circle' }
        }
    },
    credits: {
        enabled: false
    }
};

let enum_def = {
    source_type: {
        sys: { code: '6', name: '系统' },
        auto: { code: '7', name: '自动添加' }
    },

    data_type: {
        unknown: { code: '00', name: '未知' },
        netv: { code: '01', name: '净值序列' },
        position: { code: '02', name: '持仓截面' },
        position_series: { code: '03', name: '持仓截面序列' },
        order_position: { code: '04', name: '流水+持仓截面' }
    },

    asset_type: {
        general: { code: '00', name: '通用' },
        mother_fund: { code: '01', name: '母基金' },
        child_fund: { code: '02', name: '子基金' },
        futures: { code: '03', name: '期货' },
        stock: { code: '04', name: '股票' },
        options: { code: '05', name: '期权' }
    },

    indicator_type: {
        general: { code: '00', name: '通用' },
        profit_analysis: { code: '01', name: '盈亏分析' },
        position_exposure: { code: '02', name: '持仓暴露' },
        capability_analysis: { code: '03', name: '能力分析' },
        risk_analysis: { code: '04', name: '风险分析' }
    },

    value_type: {
        single: {code: 1, name: '单值序列'},
        multi_series: {code: 2, name: '多值序列'}
    }
};

let table_matrix = { max_rows: 20, max_cols: 10, empty_obj: { input: null } };

let report_config = {
    table_matrix: table_matrix,
    control_types: {
        menu: {
            key: 'menu',
            icon: 'icon-daohang',
            name: '导航',
            desc: '作为报告页面导航',
            user_options: {
                menu_level: 1,
                menu_text: null
            }
        },
        asset: {
            key: 'asset',
            icon: 'icon-quanyi',
            name: '权益',
            desc: '提供账户权益总览',
            user_options: { time_period: 0 }
        },
        position: {
            key: 'position',
            icon: 'icon-chicang',
            name: '持仓',
            desc: '标准持仓数据',
            user_options: { time_period: 0 }
        },
        order: {
            key: 'order',
            icon: 'icon-dingdan',
            name: '订单',
            desc: '标准订单数据',
            user_options: { time_period: 0 }
        },
        text: {
            key: 'text',
            icon: 'icon-wenben',
            name: '文本',
            desc: '作为一段文字显示（支持关键字占位符）',
            kw_indicator: '',
            popover_show: false,
            user_options: {
                content: null,
                font_size: 14,
                is_bold: false,
                is_italic: false,
                is_indented: false
            }
        },
        table_static: {
            key: 'table_static',
            icon: 'icon-biaoge',
            name: '指标集合',
            fontSize: 10,
            desc: '自行设置表格的行数、列数，已经每个单元格需要填充的内容（支持关键字占位符）',
            kw_indicator: '',
            popover_show: false,
            indicator_insert_pos: null,
            user_options: {
                matrix: helper.createArray(table_matrix.max_rows, helper.createArray(table_matrix.max_cols, table_matrix.empty_obj)),
                row_count: 3,
                col_count: 5,
                table_title: null,
                first_row_bold: false,
                first_col_bold: false,
                col_span: 24
            }
        },
        table_indicator: {
            key: 'table_indicator',
            icon: 'icon-zhibiao',
            name: '表格',
            desc: '绑定一个（或叠加多个同类型）指标，以表格方式展示数据',
            kw_indicator: '',
            popover_show: false,
            user_options: {
                table_title: null,
                bound_indicators: [],
                col_span: 24
            }
        },
        chart: {
            key: 'chart',
            icon: 'icon-tuxing',
            name: '图形',
            desc: '绑定一个（或叠加多个同类型）指标，以图形方式展示数据',
            kw_indicator: '',
            popover_show: false,
            previous_default_chart_type: 'spline',
            default_chart_type: 'spline',
            default_yaxis: 1,
            user_options: {
                chart_title: null,
                bound_indicators: [],
                col_span: 24
            }
        }
    },
    chart_types: {
        line: {
            name: '折线图',
            supportables: [
                enum_def.value_type.multi_series.code
            ],
            typical_options: helper.deepClone(chart_typical_options)
        },

        spline: {
            name: '曲线图',
            supportables: [
                enum_def.value_type.multi_series.code
            ],
            typical_options: helper.deepClone(chart_typical_options)
        },

        area: {
            name: '面积图',
            supportables: [
                enum_def.value_type.multi_series.code
            ],
            typical_options: helper.deepClone(chart_typical_options)
        },

        cum_area: {
            name: '堆叠面积图',
            supportables: [
                enum_def.value_type.multi_series.code
            ],
            typical_options: helper.extend(helper.deepClone(chart_typical_options), { plotOptions: { area: { stacking: 'normal' }, series: {
                        lineWidth: 1,
                        animation: { duration: 500 },
                        cursor: 'pointer',
                        states: { hover: { lineWidth: 3 } },
                        allowPointSelect: true,
                        marker: { enabled: false, fillColor: 'white', lineWidth: 1, lineColor: null, radius: 4, symbol: 'circle' }
                    } } })
        },

        column: {
            name: '柱形图',
            supportables: [
                enum_def.value_type.multi_series.code,
            ],
            typical_options: helper.deepClone(chart_typical_options)
        },

        pie: {
            name: '饼图',
            supportables: [
                enum_def.value_type.multi_series.code
            ],
            typical_options: helper.deepClone(chart_typical_options)
        }
    },
    time_period_dict: {
        latest: { code: 0, name: '最新' },
        one_week: { code: -8, name: '最近1周' },
        two_weeks: { code: -15, name: '最近2周' },
        one_month: { code: -32, name: '最近1月' },
        two_months: { code: -63, name: '最近2月' },
        three_months: { code: -94, name: '最近3月' },
    },
    font_sizes: [
        { value: 12, label: '12px' },
        { value: 14, label: '14px' },
        { value: 16, label: '16px' },
        { value: 18, label: '18px' },
        { value: 20, label: '20px' },
        { value: 22, label: '22px' },
        { value: 24, label: '24px' }
    ],
    col_spans: [
        { value: 24, label: '占整行' },
        { value: 18, label: '占3/4行' },
        { value: 16, label: '占2/3行' },
        { value: 12, label: '占1/2行' },
        { value: 8, label: '占1/3行' },
        { value: 6, label: '占1/4行' }
    ],
    menu_levels: [
        { value: 1, label: '1级菜单' },
        { value: 2, label: '2级菜单' },
        { value: 3, label: '3级菜单' },
        { value: 4, label: '4级菜单' }
    ],
    yaxises: [
        { value: 0, label: '左Y轴' },
        { value: 1, label: '右Y轴' }
    ]
};
report_config.indicator_table_cfg = {
    name: '指标表格',
    supportables: [
        enum_def.value_type.multi_series.code
    ],
    typical_options: { }
};
report_config.title_patterns = {

    fn: { exp: '{fund-name}', text: '基金名称' },
    y: { exp: '{year}', text: '年' },
    m: { exp: '{month}', text: '月' },
    d: { exp: '{day}', text: '日' }
};
report_config.chart_types.line.typical_options.chart.type = 'line';
report_config.chart_types.spline.typical_options.chart.type = 'spline';
report_config.chart_types.area.typical_options.chart.type = 'area';
report_config.chart_types.column.typical_options.chart.type = 'column';
report_config.chart_types.pie.typical_options.chart.type = 'pie';

const config = {
    enum_dict,
    chart_typical_options,
    enum_def,
    report_config
};

module.exports = { config };