<div class="maintain-view-root">
    <template>
        <div class="s-scroll-bar" style="overflow: auto">
            <div class="s-typical-toolbar">
                <div class="s-pull-left">
                    <el-input v-model="searching.value" class="s-w-150" @change="doFilter"
                        @keyup.native.enter="doFilter" placeholder="请输入关键词" clearable>
                        <i slot="prefix" class="el-input__icon el-icon-search"></i>
                    </el-input>
                </div>
                <div class="s-pull-right">

                    <span>节点状态</span>
                    <el-switch class="s-mgl-5" v-model="states.nodeStatus" @change="handleStatusChange"></el-switch>

                    <el-select placeholder="请选择service" v-model="binding.serviceName" @change="handleServiceSelect" filterable>
                        <el-option v-for="service in serviceList" :key="service.id" :label="service.serviceName" :value="service.id"></el-option>
                    </el-select>

                    <el-select placeholder="可选择Method" v-model="binding.methodName" filterable clearable>
                        <el-option v-for="method in methodList" :key="method" :label="method" :value="method"></el-option>
                    </el-select>

                    <el-input placeholder="请输入参数（请以','分隔）" class="s-w-200" size="mini"
                        @keyup.native.enter="searchResult" v-model="binding.args"></el-input>
                        
                    <el-button type="primary" size="mini" @click="searchResult">
                        <span class="el-icon-search"></span> 搜索
                    </el-button>

                    <el-button style="margin-top: 5px;" type="primary" size="mini" class="s-pull-right"
                        @click="cacheRecover">Recover</el-button>
                </div>
            </div>
            <data-tables layout="pagination,table" configurable-column="false" class="s-searchable-table"
                v-bind:table-props="tableProps" v-bind:pagination-props="paginationDef" v-bind:search-def="searchDef" :data="filterList">
                <el-table-column type="index" label="序号" width="80" align="center"></el-table-column>
                <template v-if="tableSchema.length > 0">
                    <el-table-column v-for="(item, item_idx) in tableSchema" min-width="140" show-oveflow-tooltip
                        :label="item.property" :prop="item.property" :key="item_idx">
                        <template slot-scope="scope">
                            <span>{{scope.row[item.property] !== undefined ? scope.row[item.property] : '---'}}</span>
                        </template>
                    </el-table-column>
                </template>
                <el-table-column v-else label="其他" min-width="100" align="center"></el-table-column>
            </data-tables>
        </div>
    </template>
</div>