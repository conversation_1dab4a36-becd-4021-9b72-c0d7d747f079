const DataTables = require('../../libs/3rd/vue-data-tables.min.3.4.2');
const BaseAdminView = require('./baseAdminView').BaseAdminView;

class View extends BaseAdminView {

    get repoMaintain() {
        return require('../../repository/maintain').repoMaintain;
    }

    constructor(view_name) {
        super(view_name, true, '运维管理');
        this.$container = null;
        this.vueApp = null;
        this.list = [];
        this.filterRecords = [];
        this.tableSchema = [];
        this.binding = {
            serviceName: null,
            methodName: null,
            args: null,
        };
        this.vueApp = null;
        this.searching = {
            prop: [],
            value: '',
        };

        this.states = { nodeStatus: false };
        this.serviceList = [];
        this.methodList = [];
        this.methodListMap = new Map();
    }

    createApp() {

        this.tableProps = {

            highlightCurrentRow: true,
            border: false,
            stripe: true,
            height: '500px',
        };

        const thisObj = this;        
        this.vueApp = new Vue({
            el: this.$container.querySelector('.maintain-view-root'),
            data: {
                states: this.states,
                searching: this.searching,
                tableProps: this.tableProps,
                paginationDef: { ...this.systemSetting.tablePagination, layout: 'prev,pager,next,sizes,total' },

                searchDef: {
                    inputProps: {
                        placeholder: '输入关键字筛选',
                        prefixIcon: 'iconfont icon-search',
                    },
                },
                binding: this.binding,
                tableSchema: this.tableSchema,
                list: this.list,
                filterList: this.filterRecords,
                serviceList: this.serviceList,
                methodListMap: this.methodListMap,
                methodList: this.methodList,

            },
            components: {
                DataTables: DataTables.DataTables,
            },
            methods: {
                handleServiceSelect(service_id) {
                    let matched = this.serviceList.find(serv => serv.id === service_id);
                    this.binding.serviceName = matched ? service_id : null;
                    this.methodList = this.methodListMap.get(service_id);
                    this.binding.methodName = null;
                },
                handleStatusChange() {
                    thisObj.handleStatusChange();
                },
                doFilter() {
                    thisObj.toFilterRecords();
                },
                searchResult() {
                    thisObj.doSearchResult();
                },
                cacheRecover() {
                    thisObj.interaction.showConfirm({
                        title: '提醒',
                        message: '确定从数据库覆盖数据到内存吗?',
                        confirmed: () => {
                            thisObj.doCacheRecover();
                        },
                        canceled: () => {},
                    });
                },
            },
        });
    }

    toFilterRecords() {
        this.filterRecords.clear();
        if (this.searching.value) {
            this.filterRecords.merge(
                this.list.filter(x => {
                    return Object.keys(x).some(item_key => {
                        let flag = false;
                        if (typeof x[item_key] === 'number') {
                            flag = x[item_key] == this.searching.value;
                        } else if (typeof x[item_key] === 'string') {
                            flag = x[item_key].toLowerCase().indexOf(this.searching.value.toLowerCase()) >= 0;
                        }
                        return flag;
                    });
                }),
            );
        } else {
            this.filterRecords.merge(this.list);
        }
    }

    doSearchResult() {
        if (!this.binding.serviceName) {
            this.interaction.showWarning('请输入ServiceName!');
            return;
        }
        let json = "["+ this.binding.args + "]";
        let args = JSON.parse(json);

        this.getList(this.binding.serviceName, this.binding.methodName, args);
    }

    async doCacheRecover() {
        if (!this.binding.serviceName) {
            this.interaction.showWarning('请输入ServiceName!');
            return;
        }

        let loading = this.interaction.showLoading({
            text: '正在获取数据中...',
        });
        try {
            let resp = await this.repoMaintain.getRecover(this.binding.serviceName);
            if (resp.errorCode === 0) {
                this.tableSchema.clear();
                this.interaction.showSuccess('操作成功!');
            } else {
                this.interaction.showError('操作失败，未能进行任何更改!');
            }
        } catch (e) {
            this.interaction.showError('操作失败!');
        } finally {
            loading.close();
        }
    }

    /**
     * @param {Array} records 
     */
    mergeKeys(records) {

        if (!(records instanceof Array) || records.length == 0) {
            return [];
        }

        let mergedMap = {};
        records.forEach(elem => {

            if (!elem) {
                return;
            }
            
            for (let key in elem) {
                if (mergedMap[key] === undefined) {
                    mergedMap[key] = true;
                }
            }
        });

        return this.helper.dictKey2Array(mergedMap).map(key => { return { property: key }; });
    }

    async getList(serviceName, methodName, args) {
        let loading = this.interaction.showLoading({
            text: '正在获取数据中...',
        });
        try {
            this.searching.prop.clear();
            let resp = await this.repoMaintain.getCache({
                args: args,
                serviceName: serviceName,
                methodName: methodName,
                page: 1,
                count: 10000,
            });
            if (resp.errorCode === 0) {
                this.tableSchema.clear();
                this.list.clear();
                this.filterRecords.clear();
                let data = typeof resp.data !== 'undefined' ? resp.data : [];
                if (!(data instanceof Array)) {
                    data = Array.of(data);
                }

                if (data.length > 0) {
                    this.tableSchema.merge(this.mergeKeys(data));
                }

                data.forEach(item => {
                    //补全某些property
                    let full_keys = this.tableSchema.map(x => x.property);
                    let this_keys = Object.keys(item);
                    let diff_keys = full_keys.filter(x => !this_keys.includes(x));
                    diff_keys.forEach(diff_key => {
                        item[diff_key] = null;
                    });
                    this.list.push(item);
                });
                if (this.searching.value) {
                    this.filterRecords.merge(
                        this.list.filter(x => {
                            return Object.keys(x).some(item_key => {
                                let flag = false;
                                if (typeof x[item_key] === 'number') {
                                    flag = x[item_key] == this.searching.value;
                                } else if (typeof x[item_key] === 'string') {
                                    flag = x[item_key].toLowerCase().indexOf(this.searching.value.toLowerCase());
                                }
                                return flag;
                            });
                        }),
                    );
                } else {
                    this.filterRecords.merge(this.list);
                }
            } else {
                this.interaction.showError('参数错误，未能获取数据!');
            }
        } catch (e) {
            this.interaction.showError('获取数据失败!');
        } finally {
            loading.close();
        }
    }

    async getServiceList() {

        let output = { flag: false, data: null };
        let loading = this.interaction.showLoading({ text: '请求服务接口列表...' });

        try {

            const resp = await this.repoMaintain.getService();
            if (resp.errorCode == 0) {

                output.flag = true;
                let serviceMap = resp.data;
                this.serviceList.clear();

                for (let key in serviceMap) {

                    let name =  key.split(".");
                    let service = new Object();
                    service.id = key;
                    service.serviceName = name.pop();
                    this.serviceList.push(service);
                    this.methodListMap.set(key, serviceMap[key]);
                }

                output.data = serviceMap;
            } 
            else {
                this.interaction.showHttpError(`查询服务接口列表失败,详细信息：${resp.errorMsg}`);
            }
        } 
        catch (error) {
            this.interaction.showError('查询服务接口列表失败');
        } 
        finally {
            loading.close();
        }

        return Promise.resolve(output);
    }

    async handleStatusChange() {

        var resp = await this.repoMaintain.setNodeStatus(this.states.nodeStatus ? 1 : 0);
        if (resp.errorCode == 0) {
            this.interaction.showSuccess('节点状态已设置');
        }
        else {
            this.interaction.showSuccess('操作错误：' + resp.errorMsg);
        }
    }

    async requestNodeStatus() {

        var resp = await this.repoMaintain.getNodeStatus();
        this.states.nodeStatus = resp.errorCode == 0 && resp.data == true;
    }

    resizeWindow() {

        var winHeight = this.thisWindow.getSize()[1];
        this.tableProps.height = winHeight - 135 + 'px';
    }

    build($container) {

        this.$container = $container;
        this.createApp();
        this.getServiceList();
        this.requestNodeStatus();
        setTimeout(() => { this.resizeWindow(); }, 1000);
    }
}

module.exports = View;
