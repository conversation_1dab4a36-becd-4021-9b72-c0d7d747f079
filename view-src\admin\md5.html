<div class="md5-view-root">

    <div class="toolbar">

        <template v-if="showAdd">

            <el-input v-model.trim="md5" class="s-w-150" placeholder="请输入文件HASH值" clearable></el-input>
            <el-input v-model.trim="version" class="s-w-200" placeholder="请输入备注信息（如版本）" class="s-mgl-10" clearable></el-input>
            <el-button type="primary" size="mini" @click="add" class="s-mgl-10">确定</el-button>
            <el-button size="mini" @click="showAdd = false">取消</el-button>

        </template>

        <template v-else>
            
            <el-button type="primary" size="mini" @click="showAdd = true">添加</el-button>
            <el-button type="primary" size="mini" @click="refresh">刷新</el-button>

        </template>

    </div>

    <div class="data-list">
        <table>
            <tr>
                <th label="HASH值" min-width="150" prop="md5" sortable overflowt></th>
                <th label="备注" min-width="150" prop="version" sortable overflowt></th>
                <th label="创建时间" fixed-width="100" prop="createTime" formatter="formatDateTime" sortable overflowt></th>
                <th label="操作" fixed-width="80">
                    <button class="danger" event.onclick="remove">删除</button>
                </th>
            </tr>
        </table>

    </div>

</div>