const { IView } = require('../../component/iview');
const { SmartTable } = require('../../libs/table/smart-table');
const { ColumnCommonFunc } = require('../../libs/table/column-common-func');
const { repoMaintain } = require('../../repository/maintain');

module.exports = class View extends IView {

    constructor() {
        super('@admin/md5', false, '软件HASH值维护');
    }

    identify(record) {
        return record.md5;
    }

    createApp($root) {

        this.uidata = {

            showAdd: false,
            md5: null,
            version: null,
        };

        this.toolbarApp = new Vue({

            el: $root,
            data: this.uidata,            
            methods: this.helper.fakeVueInsMethod(this, [

                this.add,
                this.refresh,
            ]),
        });
    }

    async add() {

        if (!this.uidata.md5) {
            return this.interaction.showError('未输入HASH值');
        }

        var resp = await repoMaintain.cmd5s(this.uidata.md5, this.uidata.version);

        if (resp.errorCode == 0) {

            this.interaction.showSuccess('已保存');
            this.uidata.showAdd = false;
            this.requestRecords();
        }
        else {
            this.interaction.showError(`添加错误(${resp.errorMsg})`);
        }
    }

    async remove(record = { md5: null, version: null, createTime: null }) {

        var resp = await repoMaintain.dmd5s(record.md5);

        if (resp.errorCode == 0) {

            this.interaction.showSuccess('已删除');
            this.uidata.showAdd = false;
            this.requestRecords();
        }
        else {
            this.interaction.showError(`删除错误(${resp.errorMsg})`);
        }
    }

    createTable($table) {
        
        this.helper.extend(this, ColumnCommonFunc);
        this.tableObj = new SmartTable($table, this.identify, this, {

            tableName: 'smt-admin-md5',
            displayName: this.title,
        });

        this.tableObj.setMaxHeight(600);
        this.tableObj.setPageSize(99999);
    }

    async requestRecords() {

        var resp = await repoMaintain.qmd5s();
        if (resp.errorCode == 0) {
            this.tableObj.refill(resp.data || []);
        }
        else {
            this.interaction.showError(`软件版本HASH数据, 请求异常(${resp.errorMsg})`);
        }
    }

    refresh() {

        this.interaction.showSuccess('刷新请求已发出');
        this.requestRecords();
    }

    build($container) {

        super.build($container);
        var $toolbar = this.$container.querySelector('.toolbar');
        var $table = this.$container.querySelector('.data-list');
        this.createApp($toolbar);
        this.createTable($table);
        this.requestRecords();
    }
}