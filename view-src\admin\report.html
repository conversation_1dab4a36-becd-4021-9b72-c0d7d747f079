<div class="report-view-root">
    <template>
        <div class="s-scroll-bar" style="overflow: auto;">
            <el-tabs type="border-card" v-model="tab" @tab-click="tabChange">
                <el-tab-pane label="指标管理" name="indicator">
                    <div class="s-typical-toolbar">
                        <el-button size="mini" type="primary" @click="createIndicator">
                            <i class="iconfont icon-add"></i> 创建指标
                        </el-button>
                        <el-button size="mini" @click="refreshIndicator" class="s-pull-right">
                            <span class="el-icon-refresh"></span> 刷新
                        </el-button>
                    </div>
                    <data-tables layout="pagination,table" configurable-column="false" table-name="indicatorManagement"
                        role="orgAdmin" ref="indicator-table" class="indicator s-searchable-table" v-bind:data="list"
                        v-bind:table-props="tableProps" v-bind:pagination-props="paginationDef"
                        v-bind:search-def="searchDef">
                        <el-table-column label="序号" width="80" type="index"></el-table-column>
                        <el-table-column label="指标名称" sortable show-overflow-tooltip prop="name"></el-table-column>
                        <el-table-column label="指标值类型" show-overflow-tooltip prop="valueType">
                            <template slot-scope="scope">
                                <span>{{formatValueType(scope.row)}}</span>
                            </template>
                        </el-table-column>
                        <el-table-column label="来源">
                            <template slot-scope="scope">
                                <span>{{formatSource(scope.row)}}</span>
                            </template>
                        </el-table-column>
                        <el-table-column label="创建用户名" prop="createUserName" show-overflow-tooltip></el-table-column>
                        <el-table-column label="操作" width="80">
                            <template slot-scope="scope">
                                <el-tooltip content="修改" placement="top" :enterable="false" :open-delay="850">
                                    <span class="iconfont icon-edit s-cp" style="margin-right: 5px"
                                        @click.stop="editIndicator(scope.row)"></span>
                                </el-tooltip>
                                <el-tooltip content="删除" placement="top" :enterable="false" :open-delay="850">
                                    <span class="iconfont icon-remove s-cp s-color-red"
                                        @click.stop="removeIndicator(scope.row)"></span>
                                </el-tooltip>
                            </template>
                        </el-table-column>
                    </data-tables>
                </el-tab-pane>
                <el-tab-pane label="模板管理" name="template">
                    <div class="s-typical-toolbar">
                        <el-button size="mini" type="primary" @click="createReportTemplate">
                            <i class="iconfont icon-add"></i> 创建模板
                        </el-button>
                        <!-- <el-button size="small" type="primary" @click="copyReportTemplate">
                            <i class="iconfont icon-add"></i> 复制模板
                        </el-button> -->
                        <el-button size="mini" class="s-pull-right" @click="refreshTemplate">
                            <span class="el-icon-refresh"></span> 刷新
                        </el-button>
                    </div>
                    <data-tables-server layout="pagination,table" configurable-column="false"
                        table-name="reportManagement" role="orgAdmin" ref="template-table"
                        class="s-searchable-table template" v-bind:data="reportList" v-bind:table-props="tableProps"
                        v-bind:pagination-props="paginationDef" v-bind:search-def="searchDef"
                        @query-change="handleQueryOrderChange">
                        <el-table-column label="序号" width="80" type="index"></el-table-column>
                        <el-table-column label="报告模板名称" show-overflow-tooltip>
                            <template slot-scope="scope">
                                <span>{{scope.row.report_info ? (scope.row.report_info.title_format || '未知') : '未知' }}</span>
                            </template>
                        </el-table-column>
                        <el-table-column label="创建者名称" show-overflow-tooltip prop="create_user_name"></el-table-column>
                        <el-table-column label="来源">
                            <template slot-scope="scope">
                                <span>{{ typeof scope.row.org_id === 'number' ? '自定': '系统'}}</span>
                            </template>
                        </el-table-column>
                        <el-table-column label="操作" width="80">
                            <template slot-scope="scope">
                                <el-tooltip content="修改" placement="top" :enterable="false" :open-delay="850">
                                    <span class="iconfont icon-edit s-cp" style="margin-right: 5px"
                                        @click.stop="editReportTemplate(scope.row)"></span>
                                </el-tooltip>
                                <el-tooltip content="删除" placement="top" :enterable="false" :open-delay="850">
                                    <span class="iconfont icon-remove s-cp s-color-red"
                                        @click.stop="removeReportTemplate(scope.row)"></span>
                                </el-tooltip>
                            </template>
                        </el-table-column>
                    </data-tables-server>
                </el-tab-pane>
                <el-tab-pane label="报告管理" name="report">
                    <div class="s-typical-toolbar">
                        <el-button size="mini" type="primary" @click="createDocument">
                            <i class="iconfont icon-add"></i> 创建报告
                        </el-button>
                        <el-button size="mini" class="s-pull-right" @click="refreshDocument">
                            <span class="el-icon-refresh"></span> 刷新
                        </el-button>
                    </div>
                    <data-tables layout="pagination,table" table-name="reportShareManagement"
                        configurable-column="false" role="orgAdmin" @row-click="setCurrentDocument" ref="report-table"
                        class="indicator s-searchable-table" v-bind:data="documentList" v-bind:table-props="tableProps"
                        v-bind:pagination-props="paginationDef" v-bind:search-def="searchDef">
                        <el-table-column type="index" width="90" align="center" label="序号"></el-table-column>
                        <el-table-column label="ID" min-width="150" prop="id"></el-table-column>
                        <el-table-column label="报告名称" min-width="200" sortable prop="reportName" show-overflow-tooltip>
                            <template slot-scope="scope">
                                <el-tooltip :content="`查看${scope.row.reportName ? scope.row.reportName : ''}报告`"
                                    placement="top" :enterable="false" :open-delay="850">
                                    <a class="s-cp icon-button iconfont s-underline report-name-txt"
                                        @click="viewDocument(scope.row)" href="#">{{scope.row.reportName}}</a>
                                </el-tooltip>
                            </template>
                        </el-table-column>
                        <el-table-column label="已分享人员" min-width="200" show-overflow-tooltip>
                            <template slot-scope="scope">
                                <el-row>
                                    <el-col class="s-ellipsis" :span="24">
                                        <el-tooltip content="分享模板" placement="top" :enterable="false" :open-delay="850">
                                            <a class="s-cp icon-button operation-block iconfont icon-edit"
                                                @click.stop="shareDocument(scope.row)"></a>
                                        </el-tooltip>
                                        <span v-if="scope.row.shareUsers && scope.row.shareUsers.length > 0">
                                            {{scope.row.shareUsers.map(user => user.fullName).join('、')}}
                                        </span>
                                        <span v-else>
                                            暂无分享
                                        </span>
                                    </el-col>
                                </el-row>
                            </template>
                        </el-table-column>
                        <el-table-column label="操作" width="120" show-overflow-tooltip>
                            <template slot-scope="scope">
                                <!--<el-tooltip content="查看报告" placement="top" :enterable="false" :open-delay="850">-->
                                <!--<a class="s-cp icon-button iconfont icon-chakanbaogao" @click="viewDocument(scope.row)"></a>-->
                                <!--</el-tooltip>-->
                                <el-tooltip :content="`编辑${scope.row.templateName}报告`" placement="top"
                                    :enterable="false" :open-delay="850">
                                    <a class="s-cp icon-distance icon-button iconfont icon-edit"
                                        @click="editDocument(scope.row)"></a>
                                </el-tooltip>
                                <el-tooltip content="删除报告" placement="top" :enterable="false" :open-delay="850">
                                    <a class="s-cp icon-distance s-color-red iconfont icon-remove"
                                        @click="removeDocument(scope.row)"></a>
                                </el-tooltip>
                            </template>
                        </el-table-column>
                    </data-tables>
                </el-tab-pane>
            </el-tabs>
        </div>

        <el-dialog v-drag class="indicator-form" :title="'模板管理'" ref="templateDesign" fullscreen
            :visible.sync="templateDialog.visible" :before-close="handleClose">
            <div class="container-content">

            </div>
        </el-dialog>

        <el-dialog v-drag width="400px" class="indicator-form" :title="(dialog.data.id ? '编辑': '创建') + '指标'"
            :visible="dialog.visible" v-bind:close-on-click-modal="false" v-bind:close-on-press-escape="false"
            :show-close="false">
            <el-form class="report-form" :model="dialog.data" ref="indicator" :rules="dialog.rules" label-width="100px">
                <el-form-item label="指标名称" prop="name">
                    <el-input v-model="dialog.data.name" placeholder="请填写指标名称"></el-input>
                </el-form-item>
                <el-form-item label="指标值类型" prop="valueType">
                    <el-select style="width: 100%" v-model="dialog.data.valueType" clearable placeholder="选择指标值类型">
                        <el-option v-for="(item, idx) in constant.INDICATOR_VALUE_TYPES_OPTIONS" :key="idx"
                            :label="item.text" :value="item.value"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="来源" prop="sourceType">
                    <el-select style="width: 100%" v-model="dialog.data.sourceType" clearable placeholder="选择来源">
                        <el-option v-for="(item, idx) in constant.SOURCE_OPTIONS" :key="idx" :label="item.text"
                            :value="item.value"></el-option>
                    </el-select>
                </el-form-item>
            </el-form>
            <div slot="footer">
                <el-button size="mini" type="primary" @click="submitIndicator">保存</el-button>
                <el-button size="mini" @click="closeIndicatorEdit">取消</el-button>
            </div>
        </el-dialog>

        <el-dialog v-drag class="crud-report" :title="(documentDialog.data.id ? '编辑' : '创建') + '报告'"
            :visible="documentDialog.visible" v-bind:close-on-click-modal="false" v-bind:close-on-press-escape="false"
            :show-close="false">
            <el-form class="report-form" :model="documentDialog.data" ref="documentDialog" :rules="documentDialog.rules"
                label-width="100px">
                <el-form-item label="报告名称:" prop="name">
                    <el-input v-model="documentDialog.data.reportName" placeholder="请输入报告名称"></el-input>
                </el-form-item>
                <el-form-item label="报告简介:" prop="description">
                    <el-input v-model="documentDialog.data.description" resize="none" type="textarea" :rows="4"
                        placeholder="请输入报告简介"></el-input>
                </el-form-item>
                <el-form-item label="选择模板">
                    <el-select clearable v-model="documentDialog.data.templateId" placeholder="请选择模板"
                        style="width: 100%;">
                        <el-option v-for="(item, idx) in reportList" :key="idx"
                            :label="item.report_info ? (item.report_info.title_format || '未知') : '未知'" :value="item.id">
                        </el-option>
                    </el-select>
                </el-form-item>
            </el-form>
            <div slot="footer">
                <el-button type="primary" size="small" @click="saveDocumentReport">确定</el-button>
                <el-button @click="closeDocumentDialog" size="small">取消</el-button>
            </div>
        </el-dialog>

        <el-dialog title="分享报告" v-drag class="indicator-form" :visible="shareDialog.visible"
            v-bind:close-on-click-modal="false" v-bind:close-on-press-escape="false" :show-close="false">
            <el-form class="report-form" :model="shareDialog.data" ref="shareDialog" :rules="shareDialog.rules"
                label-width="100px">
                <el-form-item class="report-form-select" label="选择用户：" prop="shareUser">
                    <el-select style="width: 100%;" v-model="shareDialog.data.shareUser" multiple
                        placeholder="请选择需要分享的用户">
                        <el-option v-for="(item, idx) in users.data" :key="idx" :label="item.fullName" :value="item.id">
                        </el-option>
                    </el-select>
                </el-form-item>
            </el-form>
            <div slot="footer">
                <el-button type="primary" size="small" @click="shareToUser">确定</el-button>
                <el-button size="small" @click="closeShare">取消</el-button>
            </div>
        </el-dialog>
        <el-dialog v-drag v-if="reportViewDialog.visible" class="report-dialog" :title="reportViewDialog.title"
            ref="reportViewDialog" fullscreen :visible.sync="reportViewDialog.visible">
            <div class="container-content"></div>
        </el-dialog>
    </template>
</div>