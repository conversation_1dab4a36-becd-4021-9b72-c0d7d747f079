<div class="risk-control-view-root">
    <template>
        <el-dialog class="risk-dialog" name="risk-dialog" v-drag ref="riskDialog" :show-close="false"
            :before-close="handleClose" title="风控设置" :visible.sync="riskDialog.visible">
            <div class="context-container"></div>
            <div slot="footer">
                <el-button @click="saveRiskTemplate" type="primary" size="mini">确定</el-button>
                <el-button @click="handleClose" size="mini">取消</el-button>
            </div>
        </el-dialog>
        <div class="s-scroll-bar" style="overflow: auto;">
            <el-tabs class="tabs" type="border-card" v-model="activeTab" @tab-click="updateTabChange">
                <el-tab-pane label="模板管理" name="template">
                    <div class="s-typical-toolbar">
                        <el-button type="primary" @click="createTemplate" type="primary" size="mini"><i
                                class="iconfont icon-add"></i> 创建新模板</el-button>
                        <!--<el-button clss="s-pull-right" style="float: right" size="small" @click="configTable('riskTemplateManagement')">-->
                        <!--<span class="iconfont icon-shezhi11"></span> 表格设置-->
                        <!--</el-button>-->
                    </div>
                    <data-tables configurable-columns="false" table-name="riskManagement" role="orgAdmin"
                        class="s-searchable-table risk-template" v-bind:data="Rules" v-bind:table-props="tableProps"
                        v-bind:pagination-props="paginationDef" v-bind:search-def="searchDef">
                        <el-table-column key="riskManagementIndex" label="序号" prop="index" type="index" width="60"
                            fixed="left" align="center"></el-table-column>
                        <el-table-column key="riskManagementTemplateName" label="模板名称" prop="templateName"
                            min-width="120" show-overflow-tooltip></el-table-column>
                        <el-table-column key="riskManagementTemplateType" label="模板类型" width="100">
                            <template slot-scope="scope">
                                <span>{{getTemplateType(scope.row)}}</span>
                            </template>
                        </el-table-column>
                        <el-table-column key="riskManagementOperation" label="操作" prop="operation" width="100"
                            fixed="right" class-name="s-col-oper">
                            <template slot-scope="props">
                                <el-tooltip content="编辑模板" placement="top" :enterable="false" :open-delay="850">
                                    <a class="s-cp icon-button iconfont icon-edit" @click="editTemplate(props.row)"></a>
                                </el-tooltip>
                                <el-tooltip content="删除模板" placement="top" :enterable="false" :open-delay="850">
                                    <a class="s-cp icon-button iconfont icon-remove s-color-red"
                                        @click="removeTemplate(props.row)"></a>
                                </el-tooltip>
                            </template>
                        </el-table-column>
                    </data-tables>
                </el-tab-pane>
                <el-tab-pane label="分类管理" name="classification">
                    <input type="file" ref="input" @change="processImport" class="form-import" />
                    <div class="s-typical-toolbar">
                        <el-button type="primary" size="mini" @click="openCreateDialog">创建分类</el-button>
                        <div class="s-pull-right">
                            <el-button size="mini" @click="downloadTemplate">
                                <span class="el-icon-download"></span> 下载模板
                            </el-button>
                            <!--<el-button style="float: right" size="small" @click="configTable('classificationManagement')">-->
                            <!--<span class="iconfont icon-shezhi11"></span> 表格设置-->
                            <!--</el-button>-->
                        </div>
                    </div>
                    <data-tables configurable-column="false" class="s-searchable-table risk-classification"
                        :data="classesList" v-bind:table-props="tableProps" v-bind:pagination-props="paginationDef"
                        v-bind:search-def="searchDef">
                        <el-table-column key="classificationManagementIndex" type="index" label="序号" width="80"
                            align="center"></el-table-column>
                        <el-table-column key="classificationManagementClassificationName" label="分类名称"
                            prop="classificationName"></el-table-column>
                        <el-table-column key="classificationManagementClassifyType" label="分类类型" prop="classifyType">
                            <template slot-scope="scope">
                                <span>{{scope.row.classifyType === constant.mode.READONLY ? '系统定义' : '自定义'}}</span>
                            </template>
                        </el-table-column>
                        <el-table-column key="classificationManagementCreateUser" label="创建人" prop="fullName"
                            width="180" show-overflow-tooltip>
                            <template slot-scope="scope">
                                <span>{{scope.row.classifyType === constant.mode.READONLY ? '超级管理员' : scope.row.fullName}}</span>
                            </template>
                        </el-table-column>
                        <el-table-column key="classificationManagementOperation" label="操作" width="180">
                            <template slot-scope="scope">
                                <el-tooltip content="查看" placement="top" :enterable="false" :open-delay="850">
                                    <a class="s-cp icon-button iconfont icon-chakan margin-r-10"
                                        @click="viewClassification(scope.row)"></a>
                                </el-tooltip>
                                <el-tooltip v-if="scope.row.classifyType == constant.mode.READWRITE" content="编辑"
                                    placement="top" :enterable="false" :open-delay="850">
                                    <a class="s-cp icon-button iconfont icon-edit margin-r-10"
                                        @click="editClassification(scope.row)"></a>
                                </el-tooltip>
                                <el-tooltip v-if="scope.row.classifyType == constant.mode.READWRITE" content="导入"
                                    placement="top" :enterable="false" :open-delay="850">
                                    <a class="s-cp icon-button iconfont icon-daoru margin-r-10"
                                        @click="importClassification(scope.row)"></a>
                                </el-tooltip>
                                <el-tooltip content="下载" placement="top" :enterable="false" :open-delay="850">
                                    <a class="s-cp icon-button el-icon-download margin-r-10"
                                        @click="downloadClassification(scope.row)"></a>
                                </el-tooltip>
                                <el-tooltip v-if="scope.row.classifyType == constant.mode.READWRITE" content="删除"
                                    placement="top" :enterable="false" :open-delay="850">
                                    <a class="s-cp icon-button el-icon-delete"
                                        @click="deleteClassification(scope.row)"></a>
                                </el-tooltip>
                            </template>
                        </el-table-column>
                    </data-tables>
                </el-tab-pane>
            </el-tabs>
        </div>
        <el-dialog class="risk-dialog" :close-on-click-modal="false" title="创建自定义分类" width="500px" v-drag
            :show-close="false" :visible="dialog.create.visible">
            <el-form class="classification-form" :model="classification" :rules="rules" :inline="true"
                ref="classification">
                <el-row :gutter="30">
                    <el-col class="classification-form-select" :span="24">
                        <el-form-item label="分类名称" prop="classificationName" :required="true">
                            <el-input v-model="classification.classificationName" placeholder="请输入分类名称"></el-input>
                        </el-form-item>
                    </el-col>
                    <!--<el-col :span="12">-->
                    <!--<el-form-item label="分类方法" :required="true" prop="classMethodId">-->
                    <!--<el-select class="s-w-full" v-model="classification.classMethodId" placeholder="请选择分类方法">-->
                    <!--<el-option v-for="(item, item_idx) in constant.ClassificationMethods" :label="item.classMethodName" :value="item.classMethodId" :key="item_idx"></el-option>-->
                    <!--</el-select>-->
                    <!--</el-form-item>-->
                    <!--</el-col>-->
                </el-row>
            </el-form>
            <div class="s-center" slot="footer">
                <el-button type="primary" size="mini" @click="createClassification">确定</el-button>
                <el-button size="mini" @click="closeCreateDialog">关闭</el-button>
            </div>
        </el-dialog>
        <el-dialog title="自定义分类" class="classification-dialog" v-drag :close-on-click-modal="false"
            :show-close="dialog.main.mode === 'view'" width="750px" :visible="dialog.main.visible"
            :before-close="closeDetailDialog">
            <el-row :gutter="30">
                <el-col :span="24" v-if="dialog.main.mode !== 'view'">
                    <el-button type="primary" size="small" @click="addInstrument">添加</el-button>
                </el-col>
                <el-col :span="24">
                    <data-tables configurable-column="false" :data="classification.instruments"
                        v-bind:table-props="tableProps" v-bind:pagination-props="paginationDef"
                        v-bind:search-def="{show: false}">
                        <el-table-column width="70" label="序号" type="index"></el-table-column>
                        <el-table-column width="100" show-overflow-tooltip label="一级分类" prop="primaryClass">
                            <template slot-scope="scope">
                                <span v-if="!scope.row.editable">{{scope.row.primaryClass}}</span>
                                <el-input class="input-wrapper" placeholder="一级分类" v-else size="mini"
                                    v-model.trim="scope.row.edit.primaryClass"></el-input>
                            </template>
                        </el-table-column>
                        <el-table-column width="100" show-overflow-tooltip label="二级分类" prop="secondaryClass">
                            <template slot-scope="scope">
                                <span v-if="!scope.row.editable">{{scope.row.secondaryClass}}</span>
                                <el-input class="input-wrapper" placeholder="二级分类" v-else size="mini"
                                    v-model.trim="scope.row.edit.secondaryClass"></el-input>
                            </template>
                        </el-table-column>
                        <el-table-column min-width="100" show-overflow-tooltip label="合约名称" prop="instrumentName">
                            <template slot-scope="scope">
                                <span v-if="!scope.row.editable">{{scope.row.instrumentName}}</span>
                                <el-input class="input-wrapper" v-model.trim="scope.row.edit.instrumentName"
                                    placeholder="合约名称" v-else></el-input>
                            </template>
                        </el-table-column>
                        <el-table-column min-width="100" show-overflow-tooltip prop="instrument" label="合约代码">
                            <template slot-scope="scope">
                                <span v-if="!scope.row.editable">{{scope.row.instrument}}</span>
                                <el-input class="input-wrapper" placeholder="合约代码" v-else
                                    v-model.trim="scope.row.edit.instrument"></el-input>
                            </template>
                        </el-table-column>
                        <el-table-column width="50" label="操作" align="center" v-if="dialog.main.mode !== 'view'">
                            <template slot-scope="scope">
                                <el-tooltip v-if="!scope.row.editable" content="编辑" placement="top" :enterable="false"
                                    :open-delay="850">
                                    <a class="icon-button el-icon-edit" @click="editInstrument(scope.row)"></a>
                                </el-tooltip>
                                <el-tooltip v-else content="保存" placement="top" :enterable="false" :open-delay="850">
                                    <a class="icon-button el-icon-document" @click="cacheInstrument(scope.row)"></a>
                                </el-tooltip>
                                <el-tooltip content="删除" placement="top" :enterable="false" :open-delay="850">
                                    <a class="icon-button el-icon-delete" @click="deleteInstrument(scope.row)"></a>
                                </el-tooltip>
                            </template>
                        </el-table-column>
                    </data-tables>
                </el-col>
            </el-row>
            <div slot="footer" v-if="dialog.main.mode !== 'view'">
                <el-button type="primary" size="mini" @click="saveInstruments">确定</el-button>
                <el-button size="mini" @click="closeDetailDialog">关闭</el-button>
            </div>
        </el-dialog>
    </template>
</div>