<div class="risk-dialog-root">
    <template>
        <div class="risk-section-main">
            <div class="risk-section-body-left s-scroll-bar">
                <div class="account-template-wrapper" v-if="context.action === constant.Actions.templateCreate.value"
                    key="select-account-template">
                    <el-checkbox v-model="templateModel.data.identityType"
                        :true-label="constant.IdentityTypes.account.code" :false-label="null">创建账号模板</el-checkbox>
                </div>
                <el-table configurable-column="false" ref="riskRule" class="risk-list" :data="rules"
                    @row-click="setSelectedRiskRule">
                    <el-table-column label="名称" show-overflow-tooltip min-width="110">
                        <template slot-scope="scope">
                            <span>{{scope.row.configurationName || scope.row.limitationName}}</span>
                        </template>
                    </el-table-column>
                    <el-table-column key="riskRuleOperation" label="操作" width="60" align="center">
                        <template slot-scope="scope">
                            <span v-if="context.action !== constant.Actions.templateReadonly.value &&
                                    context.action !== constant.Actions.view.value" class="el-icon-delete s-cp"
                                key="primary_operation" @click="removeRiskRule($event, scope.row)"></span>
                        </template>
                    </el-table-column>
                </el-table>
                <div class="s-center">
                    <el-button v-if="context.action !== constant.Actions.templateReadonly.value &&
                                        context.action !== constant.Actions.view.value" type="primary" size="small"
                        class="btn-add-rule" @click="addNewRiskRule" key="addRiskRule">添加风控规则</el-button>
                    <el-button v-if="context.action !== constant.Actions.templateReadonly.value &&
                                        context.action !== constant.Actions.view.value" type="primary" size="small"
                        class="btn-add-rule" @click="addNewLimitation" key="addTradeLimitation">添加交易限制</el-button>
                    <el-button v-if="context.action === constant.Actions.normal.value &&
                                        context.action !== constant.Actions.view.value" type="primary" size="small"
                        class="btn-add-rule" key="addTemplate" @click="createFromTemplate">从模板创建</el-button>
                </div>
                <div key="procedure-control" class="procedure-wrapper"
                    v-if="context.type === constant.IdentityTypes.account.code|| templateModel.data.identityType === constant.IdentityTypes.account.code">
                    <span class="procedure-text">账号流控信息</span>
                    <el-form label-width="70px">
                        <el-form-item label="撤单率:(%)">
                            <el-input v-model.number="account.cancelRate"
                                :readonly="context.action === constant.Actions.view.value" placeholder="撤单率（%）"
                                @focus="cancelRateFocus"></el-input>
                        </el-form-item>
                        <el-form-item label="下单速度:">
                            <el-input v-model.number="account.orderSpeed"
                                :readonly="context.action === constant.Actions.view.value" placeholder="下单速度"
                                @focus="orderSpeedFocus"></el-input>
                        </el-form-item>
                        <el-form-item label="错单数:">
                            <el-input v-model.number="account.errorCount"
                                :readonly="context.action === constant.Actions.view.value" placeholder="错单数"
                                @focus="errorCountFocus"></el-input>
                        </el-form-item>
                    </el-form>
                </div>
            </div>
            <div class="risk-section-body-right s-scroll-bar">
                <el-row>
                    <el-col :span="24">
                        <el-form :inline="true">
                            <template v-if="states.selectedRule.type === 'normal'">
                                <el-row>
                                    <el-col :span="16">
                                        <el-form-item label="风控名称" :required="true">
                                            <el-input class="risk-name" placeholder="风控名称" class="s-w-200"
                                                :readonly="riskRuleReadonly || context.action === constant.Actions.view.value"
                                                v-model="states.selectedRule.configurationName"></el-input>
                                        </el-form-item>
                                    </el-col>
                                    <el-col :span="8" v-if="context.action !== constant.Actions.templateReadonly.value &&
                                                       context.action !== constant.Actions.view.value &&
                                                       states.selectedRuleMode === 'display' && states.selectedRule">
                                        <div class="s-pull-right">
                                            <el-button size="small" type="primary" class="btn-hang"
                                                @click="editRiskRule" key="totalEdit">编辑</el-button>
                                        </div>
                                    </el-col>
                                    <el-col :span="8" v-if="!riskRuleReadonly && context.action !== constant.Actions.view.value" key="saveSelectedEdit">
                                        <div class="s-pull-right">
                                            <el-button type="primary" size="small" @click="saveSelectedEdit">确定</el-button>
                                            <el-button size="small" @click="clearSelectedRule">舍弃</el-button>
                                        </div>
                                    </el-col>
                                </el-row>

                                <el-row>
                                    <el-col :span="24">
                                        <el-form-item s-w-full>
                                            <div class="risk-section">
                                                <div class="risk-section-wrapper" key="openIndicator"
                                                    v-if="!riskRuleReadonly && context.action !== constant.Actions.view.value">
                                                    <el-button style="margin-left: 15px" type="primary" size="small"
                                                        @click="openIndicator">添加组合资产指标</el-button>
                                                </div>
                                                <el-table configurable-column="false"
                                                    :data="states.selectedRule.indexList" key="combo_data_table"
                                                    :class="{'risk-combo': !riskRuleReadonly}">
                                                    <el-table-column label="序号" width="60" align="center" type="index" :index="formatIndex"></el-table-column>
                                                    <el-table-column label="资产指标" prop="name"></el-table-column>
                                                    <el-table-column label="资产范围" prop="assessList">
                                                        <template slot-scope="scope">
                                                            <span v-html="formatAssetRange(scope.row)"></span>
                                                        </template>
                                                    </el-table-column>
                                                    <el-table-column label="汇总方式" width="100" align="center" prop="summary">
                                                        <template slot-scope="scope">
                                                            <span>{{formatSummary(scope.row)}}</span>
                                                        </template>
                                                    </el-table-column>
                                                    <el-table-column label="权重" width="80" align="center">
                                                        <template slot-scope="scope">
                                                            <div class="weight-input el-input" size="mini"
                                                                @change="updateExpression">
                                                                <input type="text" class="el-input__inner"
                                                                    :readonly="riskRuleReadonly"
                                                                    v-model.number="scope.row.weight"
                                                                    @input="updateExpression(scope.row)" />
                                                            </div>
                                                        </template>
                                                    </el-table-column>
                                                    <el-table-column label="操作" width="70" align="center"
                                                        key="operation" v-if="!riskRuleReadonly">
                                                        <template slot-scope="scope">
                                                            <span class="iconfont icon-edit s-cp"
                                                                style="margin-right: 5px"
                                                                @click="editIndicator(scope.row)"></span>
                                                            <span class="el-icon-delete s-cp"
                                                                @click="removeIndicator(scope.row)"></span>
                                                        </template>
                                                    </el-table-column>
                                                </el-table>
                                                <div class="equation-wrapper">
                                                    <el-form-item :required="true" s-w-inline label="计算公式">
                                                        <el-input size="mini"
                                                            :readonly="riskRuleReadonly || context.action === constant.Actions.view.value"
                                                            v-model="states.selectedRule.expression"
                                                            placeholder="填写计算公式"></el-input>
                                                    </el-form-item>
                                                </div>
                                            </div>
                                        </el-form-item>
                                    </el-col>
                                </el-row>

                                <el-row>
                                    <el-col :span="24">
                                        <el-form-item s-w-full>
                                            <div class="risk-section">
                                                <div class="risk-section-wrapper" key="createAlert"
                                                    v-if="!riskRuleReadonly && context.action !== constant.Actions.view.value">
                                                    <el-button size="small" type="primary" @click="createAlert">添加预警
                                                    </el-button>
                                                </div>
                                                <el-table configurable-column="false" warning-table
                                                    :data="states.selectedRule.warningList"
                                                    :class="{'risk-combo': !riskRuleReadonly}">
                                                    <el-table-column label="级别" width="100" prop="level">
                                                        <template slot-scope="scope">
                                                            <el-select v-if="!riskRuleReadonly" key="warningSelector"
                                                                size="mini" v-model="scope.row.warningType" size="mini">
                                                                <el-option
                                                                    v-for="(item, item_idx) in constant.WarningTypes"
                                                                    :label="item.label" :value="item.value"
                                                                    :key="item_idx"></el-option>
                                                            </el-select>
                                                            <span v-else>
                                                                {{formatAlertType(scope.row.warningType)}}
                                                            </span>
                                                        </template>
                                                    </el-table-column>
                                                    <el-table-column label="单位净值" prop="unitPureValue" min-width="150">
                                                        <template slot-scope="scope">
                                                            <template v-if="!riskRuleReadonly && scope.row.preposition">
                                                                <el-popover placement="bottom" width="400"
                                                                    class="risk-el-popover" trigger="manual"
                                                                    v-model="scope.row.netValuePopover">
                                                                    <div class="popover-inner-wrapper">
                                                                        <span class="popover-close el-icon-close"
                                                                            @click="scope.row.netValuePopover = !scope.row.netValuePopover"></span>
                                                                        <el-row :gutter="10">
                                                                            <el-col :span="5">
                                                                                <el-input clearable size="mni"
                                                                                    v-model="scope.row.minNetValue">
                                                                                </el-input>
                                                                            </el-col>
                                                                            <el-col :span="5">
                                                                                <el-select clearable size="mini" v-model="scope.row.minNetValueSymbol">
                                                                                    <el-option
                                                                                        v-for="(item, item_idx) in constant.Operators"
                                                                                        :label="item.label"
                                                                                        :value="item.value"
                                                                                        :key="item_idx"></el-option>
                                                                                </el-select>
                                                                            </el-col>
                                                                            <el-col :span="4" class="s-center">
                                                                                <span
                                                                                    class="net-value-label">单位净值</span>
                                                                            </el-col>
                                                                            <el-col :span="5">
                                                                                <el-select clearable
                                                                                    size="mini" v-model="scope.row.maxNetValueSymbol">
                                                                                    <el-option
                                                                                        v-for="(item,item_idx) in constant.Operators"
                                                                                        :label="item.label"
                                                                                        :value="item.value"
                                                                                        :key="item_idx"></el-option>
                                                                                </el-select>
                                                                            </el-col>
                                                                            <el-col :span="5">
                                                                                <el-input size="mini" clearable v-model="scope.row.maxNetValue">
                                                                                </el-input>
                                                                            </el-col>
                                                                        </el-row>
                                                                    </div>
                                                                    <el-tooltip content="单击文字可修改单位净值表达式"
                                                                        slot="reference" placement="top"
                                                                        :enterable="false" :open-delay="850">
                                                                        <span class="s-cp"
                                                                            @click="scope.row.netValuePopover = !scope.row.netValuePopover">{{formatEquation(scope.row, 'netValue')}}</span>
                                                                    </el-tooltip>
                                                                </el-popover>
                                                            </template>
                                                            <span
                                                                v-else>{{formatEquation(scope.row, 'netValue')}}</span>
                                                        </template>
                                                    </el-table-column>
                                                    <el-table-column label="指标" min-width="150">
                                                        <template slot-scope="scope">
                                                            <template v-if="!riskRuleReadonly">
                                                                <el-popover placement="bottom" width="400"
                                                                    class="risk-el-popover" trigger="manual"
                                                                    v-model="scope.row.indicatorPopover">
                                                                    <div class="popover-inner-wrapper">
                                                                        <span class="el-icon-close popover-close"
                                                                            @click="scope.row.indicatorPopover = !scope.row.indicatorPopover"></span>
                                                                        <el-row :gutter="10">
                                                                            <el-col :span="5">
                                                                                <el-input clearable size="mini"
                                                                                    v-model="scope.row.minValue">
                                                                                </el-input>
                                                                            </el-col>
                                                                            <el-col :span="5">
                                                                                <el-select size="mini"
                                                                                    clearable v-model="scope.row.minValueSymbol">
                                                                                    <el-option
                                                                                        v-for="(item, item_idx) in constant.Operators"
                                                                                        :label="item.label"
                                                                                        :value="item.value"
                                                                                        :key="item_idx"></el-option>
                                                                                </el-select>
                                                                            </el-col>
                                                                            <el-col :span="4" class="s-center">
                                                                                <span
                                                                                    class="warning-index-label">指标</span>
                                                                            </el-col>
                                                                            <el-col :span="5">
                                                                                <el-select clearable size="mini" v-model="scope.row.maxValueSymbol">
                                                                                    <el-option
                                                                                        v-for="(item, item_idx) in constant.Operators"
                                                                                        :label="item.label"
                                                                                        :value="item.value"
                                                                                        :key="item_idx"></el-option>
                                                                                </el-select>
                                                                            </el-col>
                                                                            <el-col :span="5">
                                                                                <el-input clearable size="mini"
                                                                                    v-model="scope.row.maxValue">
                                                                                </el-input>
                                                                            </el-col>
                                                                        </el-row>
                                                                    </div>
                                                                    <el-tooltip content="单击文字可修改指标表达式" slot="reference"
                                                                        placement="top" :enterable="false"
                                                                        :open-delay="850">
                                                                        <span class="s-cp"
                                                                            @click="scope.row.indicatorPopover = !scope.row.indicatorPopover">{{formatEquation(scope.row, 'variableIndex')}}</span>
                                                                    </el-tooltip>
                                                                </el-popover>
                                                            </template>
                                                            <span
                                                                v-else>{{formatEquation(scope.row, 'variableIndex')}}</span>
                                                        </template>
                                                    </el-table-column>
                                                    <el-table-column label="操作" min-width="100" align="center"
                                                        v-if="!riskRuleReadonly" key="operation_1">
                                                        <template slot-scope="scope">
                                                            <el-checkbox class="table-check-label risk-checkbox"
                                                                v-model="scope.row.preposition" :label="true">前置条件
                                                            </el-checkbox>
                                                            <span class="el-icon-delete s-cp"
                                                                @click="removeAlert(scope.row)"></span>
                                                        </template>
                                                    </el-table-column>
                                                </el-table>
                                            </div>
                                        </el-form-item>
                                    </el-col>
                                </el-row>

                                <el-row>
                                    <el-col :span="12">
                                        <el-form-item label="风控类型" label-width="95px" :required="true">
                                            <el-select v-if="!riskRuleReadonly" v-model="states.selectedRule.riskType"
                                                :disabled="context.action === constant.Actions.view.value"
                                                key="riskTypeKey" size="mini" style="width: 200px;" multiple collapse-tags>
                                                <el-option v-for="(item, item_idx) in constant.RiskOptions"
                                                    :label="item.label" :value="item.value" :key="item_idx"></el-option>
                                            </el-select>
                                            <label v-else class="risk-type-disabled">{{formatRiskType(states.selectedRule.riskType)}}</label>
                                        </el-form-item>
                                    </el-col>
                                    <el-col :span="12" key="showInterval" v-if="showInterval">
                                        <el-form-item label="运行间隔时间" label-width="95px" :required="true">
                                            <el-input-number v-model.number="states.selectedRule.interval" :min="0"
                                                size="mini" placeholder="单位：秒" :disabled="riskRuleReadonly"
                                                label="间隔时间"></el-input-number>
                                        </el-form-item>
                                    </el-col>
                                </el-row>

                                <el-row>
                                    <el-col :span="12">
                                        <el-form-item label="发送短信" label-width="95px" :required="true">
                                            <el-radio :disabled="riskRuleReadonly" class="risk-radio"
                                                v-model="states.selectedRule.sendMessage"
                                                v-for="(item, item_idx) in constant.SendOptions" :key="item_idx"
                                                :label="item.value">{{item.label}}</el-radio>
                                        </el-form-item>
                                    </el-col>
                                    <el-col :span="12">
                                        <el-form-item label="发送邮件" label-width="95px" :required="true">
                                            <el-radio class="risk-radio" :disabled="riskRuleReadonly"
                                                v-model="states.selectedRule.sendMail"
                                                v-for="(item, item_idx) in constant.SendOptions" :key="item_idx"
                                                :label="item.value">{{item.label}}</el-radio>
                                        </el-form-item>
                                    </el-col>
                                </el-row>
                            </template>
                            <template v-else>
                                <el-row>
                                    <el-col :span="12">
                                        <el-form-item label="交易限制名称" :required="true">
                                            <el-input class="s-w-200" :readonly="riskRuleReadonly"
                                                placeholder="请填写交易限制名称" v-model="states.selectedRule.limitationName">
                                            </el-input>
                                        </el-form-item>
                                    </el-col>
                                    <el-col :span="12" v-if="context.action !== constant.Actions.templateReadonly.value &&
                                                       context.action !== constant.Actions.view.value &&
                                                       states.selectedRuleMode === 'display' && states.selectedRule">
                                        <div class="s-pull-right">
                                            <el-button size="small" type="primary" class="btn-hang"
                                                @click="editRiskRule" key="totalEdit">编辑</el-button>
                                        </div>
                                    </el-col>
                                    <el-col :span="12" v-if="!riskRuleReadonly" key="saveLimitation">
                                        <div class="s-pull-right">
                                            <el-button type="primary" size="mini" @click="saveLimitation">确定</el-button>
                                            <el-button @click="clearSelectedLimitation" size="mini">舍弃</el-button>
                                        </div>
                                    </el-col>
                                </el-row>

                                <el-row>
                                    <el-col :span="24">
                                        <el-form-item s-w-full>
                                            <div class="risk-section">
                                                <div class="risk-section-wrapper" key="addClassification"
                                                    v-if="!riskRuleReadonly">
                                                    <el-button type="primary" size="mini" @click="addClassification">
                                                        添加分类</el-button>
                                                </div>
                                                <el-table configurable-column="false" :data="states.selectedRule.rules"
                                                    key="limitation_classification" transaction-table>
                                                    <el-table-column type="index" label="序号" align="center" width="80"></el-table-column>
                                                    <el-table-column label="资产类型" prop="assetType" min-width="150">
                                                        <template slot-scope="scope">
                                                            <span v-if="riskRuleReadonly">
                                                                <span class="s-color-red" key="flag"
                                                                    v-if="scope.row.flag">非</span>
                                                                {{scope.row.method}}：（{{formatLimitAssetType(scope.row)}}）
                                                            </span>
                                                            <template v-else>
                                                                <el-tooltip content="'含'表示允许交易当前分类；'非'表示禁止交易当前分类"
                                                                    placement="top" :enterable="false"
                                                                    :open-delay="850">
                                                                    <el-button size="mini"
                                                                        :type="!scope.row.flag ? 'primary':'danger'"
                                                                        @click="() => { scope.row.flag = !scope.row.flag }">
                                                                        {{!scope.row.flag ? '含' : '非'}}</el-button>
                                                                </el-tooltip>
                                                                <el-select @change="updatePrimaryOptions(scope.row)"
                                                                    size="mini" class="s-w-120 s-mgl-10"
                                                                    v-model="scope.row.method" clearable>
                                                                    <el-option
                                                                        v-for="(item, item_idx) in constant.IndustriesOptions"
                                                                        :value="item.name" :label="item.name"
                                                                        :key="item_idx"></el-option>
                                                                </el-select>
                                                                <template v-if="!scope.row.defaultClassify">
                                                                    <el-select size="mini"
                                                                        @change="updateSecondaryOptions(scope.row)"
                                                                        class="s-mgl-10" v-model="scope.row.primary"
                                                                        multiple clearable collapse-tags>
                                                                        <el-option
                                                                            v-for="(item, item_idx) in scope.row.PrimaryOptions"
                                                                            :value="item.id"
                                                                            :label="item.parentClassName"
                                                                            :key="item_idx"></el-option>
                                                                    </el-select>
                                                                    <el-select size="mini"
                                                                        class="s-mgl-10"
                                                                        @change="updateSelectAll(scope.row)"
                                                                        v-if="scope.row.cascade"
                                                                        key="scope.row.secondary"
                                                                        v-model="scope.row.secondary"
                                                                        multiple clearable collapse-tags>
                                                                        <el-option
                                                                            v-for="(item, item_idx) in scope.row.SecondaryOptions"
                                                                            :value="item.id" :label="item.className"
                                                                            :key="item_idx"></el-option>
                                                                    </el-select>
                                                                </template>
                                                                <span v-else class="default-classify">默认分类</span>
                                                            </template>
                                                        </template>
                                                    </el-table-column>
                                                    <el-table-column label="操作" align="center" v-if="!riskRuleReadonly"
                                                        width="80" key="operation3">
                                                        <template slot-scope="scope">
                                                            <span class="el-icon-delete s-cp"
                                                                @click="removeInstrument(scope.row)"></span>
                                                        </template>
                                                    </el-table-column>
                                                </el-table>
                                            </div>
                                        </el-form-item>
                                    </el-col>
                                </el-row>
                            </template>
                        </el-form>
                    </el-col>
                </el-row>
            </div>
        </div>
        <div class="risk-section-footer">
            <el-button v-if="context.action !== constant.Actions.templateReadonly.value &&
                                    context.action !== constant.Actions.view.value" style="margin-left: 15px"
                @click="openTemplate" type="primary" size="small" key="saveAsTemplate">存储模板</el-button>
        </div>
        <el-dialog append-to-body v-drag title="存储为模板" class="template-dialog risk-template-dialog"
            :close-on-click-modal="false" :show-close="false" :visible="states.dialog.templateVisible"
            :before-close="closeTemplate">
            <el-form class="risk-template-form" ref="template" :model="templateModel.data" :rules="templateModel.rules" label-width="80px">
                <el-row>
                    <el-col :span="12">
                        <el-form-item label="模板名称:" :required="true" prop="name">
                            <el-input v-model="templateModel.data.name" placeholder="输入模板"></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="模板类型:" :required="true" prop="type">
                            <el-select :disabled="templateModel.disabled" v-model="templateModel.data.type"
                                placeholder="选择模板类型" :disabled="context.type === constant.IdentityTypes.account.code">
                                <el-option v-for="(item, item_idx) in constant.IdentityTypes" :label="item.mean + '模板'"
                                    :value="item.code" :key="item_idx"></el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                </el-row>
            </el-form>
            <div class="s-center" slot="footer">
                <el-button type="primary" size="mini" @click="saveAsTemplate()">保存</el-button>
                <el-button size="mini" @click="closeTemplate">取消</el-button>
            </div>
        </el-dialog>

        <el-dialog append-to-body title="添加指标" :show-close="false" width="700px" class="central-dialog combo-dialog s-scroll-bar"
            :close-on-click-modal="false" :visible="states.dialog.indicatorVisible" :before-close="closeIndicator">
            <div class="indicator-left s-scroll-bar">
                <el-tree accordion :data="constant.IndexClassifications" node-key="id" @node-click="selectIndexClass"
                    :props="defaultProps"></el-tree>
            </div>
            <div class="indicator-right">
                <el-form label-width="80px">
                    <el-row>
                        <el-col :span="24">
                            <el-form-item label="指标名称">
                                <el-input v-model="states.indicators.name" :readonly="true" placeholder="请从左边选择指标">
                                </el-input>
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row v-if="states.indicators.show" key="classificationMethod">
                        <el-col :span="24">
                            <el-form-item>
                                <el-button class="btn-add-indicator" @click="addAssessCondition" type="primary"
                                    size="small" :disabled="indicatorReadonly">添加</el-button>
                                <el-table configurable-column="false" :data="states.indicators.assessList"
                                    indicator-table>
                                    <el-table-column label="分类方法" prop="method">
                                        <template slot-scope="scope">
                                            <el-select @change="updatePrimary(scope.row)"
                                                size="mini" clearable v-model="scope.row.method">
                                                <el-option v-for="(item, item_idx) in constant.IndustriesOptions"
                                                    :key="item_idx" :label="item.name" :value="item.name"></el-option>
                                            </el-select>
                                        </template>
                                    </el-table-column>
                                    <el-table-column label="一级分类" prop="primary">
                                        <template slot-scope="scope">
                                            <el-select v-if="!scope.row.defaultClassify"
                                                key="primaryClassification" @change="updateSecondary(scope.row)"
                                                size="mini" clearable multiple v-model="scope.row.primary" collapse-tags>
                                                <el-option v-for="(item, item_idx) in scope.row.PrimaryOptions"
                                                    :key="item_idx" :label="item.parentClassName" :value="item.id">
                                                </el-option>
                                            </el-select>
                                            <span v-else>默认分类</span>
                                        </template>
                                    </el-table-column>
                                    <el-table-column label="二级分类" prop="secondary">
                                        <template slot-scope="scope">
                                            <el-select v-if="!scope.row.defaultClassify && scope.row.cascade"
                                                key="secondaryClassification" @change="updateSelectRegion(scope.row)"
                                                size="mini" clearable multiple v-model="scope.row.secondary" collapse-tags>
                                                <el-option v-for="(item, item_idx) in scope.row.SecondaryOptions"
                                                    :key="item_idx" :label="item.className" :value="item.id">
                                                </el-option>
                                            </el-select>
                                            <span v-else>---</span>
                                        </template>
                                    </el-table-column>
                                    <el-table-column label="操作" width="60" align="center">
                                        <template slot-scope="scope">
                                            <span class="s-cp el-icon-delete"
                                                @click="removeAssessItem(scope.row)"></span>
                                        </template>
                                    </el-table-column>
                                </el-table>
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row v-if="states.indicators.show" key="summaryMethod">
                        <el-col :span="24">
                            <el-form-item label="汇总方式">
                                <el-radio class="risk-radio" v-for="(item, item_idx) in constant.SummaryMethods"
                                    :disabled="indicatorReadonly" v-model="states.indicators.summary" :key="item_idx"
                                    :label="item.value">{{item.label}}</el-radio>
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row>
                        <el-col :span="24">
                            <el-form-item label="算法描述">
                                <el-input v-model="states.indicators.description" :readonly="true" type="textarea">
                                </el-input>
                            </el-form-item>
                        </el-col>
                    </el-row>
                </el-form>
            </div>
            <div slot="footer">
                <el-button size="mini" type="primary" :disabled="indicatorReadonly" @click="createIndicator">保存
                </el-button>
                <el-button size="mini" @click="closeIndicator">取消</el-button>
            </div>
        </el-dialog>

        <el-dialog append-to-body title="选择应用风控模板" v-drag width="500px" :show-close="false"
            class="risk-dialog select-dialog" :close-on-click-modal="false" :visible="states.dialog.selectVisible"
            :before-close="closeSelectTemplate">
            <data-tables configurable-column="false" :data="templates" risk-table-list v-bind:table-props="tableProps"
                v-bind:pagination-def="paginationDef" v-bind:search-def="searchDef">
                <el-table-column type="index" width="80" align="center" label="序号"></el-table-column>
                <el-table-column min-width="180" label="模板名称" show-overflow-tooltip prop="templateName">
                </el-table-column>
                <el-table-column label="操作" align="center" width="100">
                    <template slot-scope="scope">
                        <el-radio @change="setChecked(scope.row, scope.$index)" :label="scope.$index"
                            v-model="scope.row.checked">选择模板</el-radio>
                    </template>
                </el-table-column>
            </data-tables>
            <div slot="footer">
                <el-button type="primary" size="mini" @click="applyTemplateRule">应用</el-button>
                <el-button @click="closeSelectTemplate" size="mini">取消</el-button>
            </div>
        </el-dialog>
    </template>
</div>