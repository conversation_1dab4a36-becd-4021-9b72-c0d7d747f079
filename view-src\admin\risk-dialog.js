const DataTables = require('../../libs/3rd/vue-data-tables.min.3.4.2');
const IView = require('../../component/iview').IView;
const drag = require('../../directives/drag');

class RiskDialog extends IView {

    get repoRisk() {
        return require('../../repository/risk').repoRisk;
    }

    constructor(view_name, is_standalone_window, title = '风控设置') {

        super(view_name, is_standalone_window, title);

        this.riskOptions = {
            before: {
                label: '事前风控',
                value: 1,
            },
            after: {
                label: '事后风控',
                value: 2,
            },
        };

        this.sendOptions = {
            send: {
                label: '发送',
                value: true,
            },
            noSend: {
                label: '不发送',
                value: false,
            },
        };

        this.sendNotification = {
            mail: this.sendOptions.noSend.value,
            message: this.sendOptions.noSend.value,
        };

        this.summaryMethods = {
            single: {
                value: true,
                label: '单票',
            },
            summarize: {
                value: false,
                label: '汇总',
            },
        };

        this.templateModel = {
            data: {
                name: '',
                identityType: null,
                type: null,
            },
            disabled: false,
            rules: {
                name: [
                    {
                        required: true,
                        message: '请输入风控模板名称',
                    },
                ],
                type: [
                    {
                        required: true,
                        message: '请选择模板类型',
                    },
                ],
            },
        };

        this.operators = {
            gt: {
                value: '<',
                label: '<',
            },
            gtEq: {
                value: '<=',
                label: '<=',
            },
        };

        this.SELECT_ALL = -1;

        this.warningLevel = {
            warning: {
                value: 1,
                label: '预警',
            },
            alert: {
                value: 2,
                label: '报警',
            },
            seriousAlert: {
                value: 3,
                label: '严重报警',
            },
        };

        //决定风控窗口的行为
        this.Actions = {
            normal: {
                key: 'identity',
                value: 'identity',
            },
            view: {
                key: 'view',
                value: 'view',
            },
            templateReadonly: {
                key: 'template_readonly',
                value: 'template_readonly',
            },
            templateCreate: {
                key: 'template_create',
                value: 'template_create',
            },
            templateEdit: {
                key: 'template_edit',
                value: 'template_edit',
            },
        };

        this.rules = [];

        this.templates = [];

        this.states = {
            selectedRule: this.createEmptyRule(),
            selectedRuleMode: 'display',
            indicators: this.createZeroIndicator(),
            indicatorMode: 'display',
            dialog: {
                indicatorVisible: false,
                templateVisible: false,
                selectVisible: false,
            },
            indicatorTab: 'normal',
        };

        this.context = {
            action: this.Actions.normal.value,
            name: null,
            type: null,
            identity: null,
            templateId: null,
        };

        this.indexClassifications = [];

        this.industriesOptions = [];

        this.SPECIALTYPE = '资产类';

        this.SPECIALTYPE_IDS = [301, 302, 303];

        this.constant = {

            IdentityTypes: this.systemEnum.identityType,
            SummaryMethods: this.helper.dict2Array(this.summaryMethods),
            IndexClassifications: this.indexClassifications,
            IndustriesOptions: this.industriesOptions,
            Operators: this.operators,
            Actions: this.Actions,
            WarningTypes: this.helper.dict2Array(this.warningLevel),
            SendOptions: this.sendOptions,
            SendNotification: this.sendNotification,
            RiskOptions: this.helper.dict2Array(this.riskOptions),
            RiskTypes: this.riskOptions,
            Tab: 'normal',
        };

        //账号流控信息
        this.accountProcedureControl = this.createEmptyPrcControl();
        this.step = 1;
        this.$container = null;
        this.vueApp = null;
    }

    get repoUser() {
        return require('../../repository/user').repoUser;
    }

    get isAccount() {
        return this.context.type === this.systemEnum.identityType.account.code;
    }

    get isAccountTemplate() {
        return this.templateModel.data.identityType === this.systemEnum.identityType.account.code;
    }

    get vueMethods() {
        return {
            clearSelectedRule: () => {
                this.interaction.showConfirm({
                    title: '提示',
                    message: '该操作不会保存您编辑的数据，是否要继续？',
                    confirmed: () => {
                        this.clearSelectedRule();
                    },
                });
            },
            saveSelectedEdit: () => {
                this.saveSelectedEdit();
            },
            setSelectedRiskRule: selected_rule => {
                this.setSelectedRiskRule(selected_rule);
            },
            applyTemplateRule: () => {
                this.setTemplateRule();
            },
            addNewRiskRule: () => {
                if (this.states.selectedRule.id && this.states.selectedRuleMode === 'edit') {
                    this.interaction.showConfirm({
                        message: '即将取消编辑当前内容，并切换至其他风控规则，要继续吗?',
                        title: '警告',
                        confirmed: () => {
                            this.addNewRiskRule();
                        },
                        canceled: () => {
                            console.log('the error has occurred!');
                        },
                    });
                } else {
                    this.addNewRiskRule();
                }
            },
            addNewLimitation: () => {
                if (this.states.selectedRule.id && this.states.selectedRuleMode === 'edit') {
                    this.interaction.showConfirm({
                        message: '即将取消编辑当前内容，并切换至其他风控规则，要继续吗?',
                        title: '警告',
                        confirmed: () => {
                            this.addNewLimitation();
                        },
                        canceled: () => {
                            console.log('the error has occurred!');
                        },
                    });
                } else {
                    this.addNewLimitation();
                }
            },
            openTemplate: template_rule => {
                this.openTemplate();
            },
            saveAsTemplate: () => {
                this.saveAsTemplate();
            },
            loadFromTemplate: () => {
                this.loadFromTemplate();
            },
            formatAssetRange: indicator_item => {
                return this.formatAssetRange(indicator_item);
            },
            formatSummary: indicator_item => {
                return this.formatSummary(indicator_item);
            },
            formatRiskType: risk_type => {
                return this.formatRiskType(risk_type);
            },
            updateExpression: () => {
                this.updateExpression();
            },
            editRiskRule: () => {
                this.setRiskRuleEditable();
            },
            removeRiskRule: ($event, rule) => {
                $event.preventDefault();
                $event.stopPropagation();
                let msg = rule.type === 'normal' ? '风控规则' : '交易限制';
                this.interaction.showConfirm({
                    message: '确定要移除当前' + msg + '吗',
                    title: '警告',
                    confirmed: () => {
                        if (rule.type === 'normal') {
                            this.removeRiskRule(rule);
                        } else {
                            this.removeLimit(rule);
                        }
                    },
                    canceled: () => {
                        console.log('the error has occurred in removing risk rule');
                    },
                });
            },
            createFromTemplate: () => {
                this.createFromTemplate();
            },
            openIndicator: () => {
                if (!this.vueApp.riskRuleReadonly) {
                    this._openIndicatorDialog();
                }
            },
            closeIndicator: () => {
                this.interaction.showConfirm({
                    message: '取消将不会进行任何修改，要确定吗?',
                    title: '提示',
                    confirmed: () => {
                        this._closeIndicatorDialog();
                        this.setZeroIndicator();
                        this.setIndicatorReadonly();
                    },
                });
            },
            editIndicator: this_indicator => {
                this.setIndicator(this_indicator);
                this.setIndicatorEditable();
                this._openIndicatorDialog();
            },
            closeTemplate: () => {
                this.states.dialog.templateVisible = false;
                this.vueApp.templateModel.data.name = '';
                if (this.vueApp.$refs.template) {
                    this.vueApp.$refs.template.resetFields();
                }
            },
            closeSelectTemplate: () => {
                this.states.dialog.selectVisible = false;
            },
            createIndicator: () => {
                this.createIndicator();
            },
            removeAssessItem: this_item => {
                this.removeAssessItem(this_item);
            },
            removeIndicator: this_indicator => {
                this.removeIndicator(this_indicator);
            },
            selectIndexClass: risk_kind => {
                this.setRiskKind(risk_kind);
            },
            formatIndex: index => {
                return this.getIndex(index + 1);
            },
            createAlert: () => {
                if (!this.vueApp.riskRuleReadonly) {
                    this.createAlert();
                }
            },
            formatEquation: (this_alert, type) => {
                return this.formatEquation(this_alert, type);
            },
            formatAlertType: this_type => {
                return this.formatAlertType(this_type);
            },
            removeAlert: this_alert => {
                this.removeAlert(this_alert);
            },
            addAssessCondition: () => {
                this.addAssessCondition();
            },
            updatePrimary: this_assess => {
                this.updatePrimary(this_assess);
            },
            updateSecondary: this_assess => {
                this.updateSecondary(this_assess);
            },
            updateSelectRegion: this_assess => {
                this.updateSelectRegion(this_assess);
            },
        };
    }

    createApp() {

        this.vueApp = new Vue({
            el: this.$container.querySelector('.risk-dialog-root'),
            components: {
                DataTables: DataTables.DataTables,
            },
            mixins: [],
            directives: {
                drag,
            },
            data: {
                constant: this.constant,
                context: this.context,
                account: this.accountProcedureControl,
                tradeLimitation: this.tradeLimitation,
                rules: this.rules,
                templates: this.templates,
                tableProps: this.systemSetting.tableProps,
                paginationDef: this.systemSetting.tablePagination,
                searchDef: {
                    show: false,
                },
                states: this.states,
                defaultProps: {
                    label: 'name',
                    children: 'categories',
                },
                templateModel: this.templateModel,
            },
            computed: {
                riskRuleReadonly() {
                    return this.states.selectedRuleMode === 'display';
                },
                indicatorReadonly() {
                    return this.states.indicatorMode === 'display';
                },
                showInterval() {
                    return (
                        this.states.selectedRule.riskType instanceof Array &&
                        this.states.selectedRule.riskType.includes(this.constant.RiskTypes.after.value)
                    );
                },
            },
            methods: this.helper.extend(
                {
                    setChecked: (this_template, index) => {
                        this.templates.forEach((item, item_idx) => {
                            if (index !== item_idx) {
                                item.checked = false;
                            } else {
                                item.checked = !item.checked;
                            }
                        });
                        console.log(this_template);
                    },
                    orderSpeedFocus: () => {
                        this.focusTradeTransaction();
                    },
                    cancelRateFocus: () => {
                        this.focusTradeTransaction();
                    },
                    errorCountFocus: () => {
                        this.focusTradeTransaction();
                    },
                    formatLimitAssetType: this_limitation => {
                        return this.formatLimitAssetType(this_limitation);
                    },
                    saveLimitation: () => {
                        this.saveLimitation();
                    },
                    editSelectedLimitation: () => {
                        this.editSelectedLimitation(this.states.selectedRule);
                    },
                    clearSelectedLimitation: () => {
                        this.interaction.showConfirm({
                            title: '提示',
                            message: '确定要放弃当前编辑的交易限制信息吗?',
                            confirmed: () => {
                                this.clearSelectedLimitation();
                            },
                            canceled: () => {
                                console.log('error');
                            },
                        });
                    },
                    addClassification: () => {
                        this.addClassification();
                    },
                    updatePrimaryOptions: this_type => {
                        this.updatePrimaryOptions(this_type);
                    },
                    updateSecondaryOptions: this_classify => {
                        this.updateSecondaryOptions(this_classify);
                    },
                    updateSelectAll: this_classify => {
                        this.updateSelectAll(this_classify);
                    },
                    addLimitation: () => {
                        this.addLimitation();
                    },
                    removeInstrument: current => {
                        this.interaction.showConfirm({
                            title: '警告',
                            message: '确定要删除当前行？',
                            confirmed: () => {
                                this.removeInstrument(current);
                            },
                            canceled: () => {
                                console.log('error remove instrument');
                            },
                        });
                    },
                    setSelectedLimitation: this_limit => {
                        this.setSelectedLimitation(this_limit);
                    },
                },
                this.vueMethods,
            ),
        });
    }

    focusTradeTransaction() {
        if (
            this.context.action === this.Actions.view.value ||
            (this.states.selectedRule.id &&
                this.states.selectedRule.type === 'normal' &&
                this.states.selectedRuleMode === 'edit')
        ) {
            return;
        }
        //如果当前是交易限制模式就切换成input模式
        if (this.states.selectedRule.type !== 'limitation') {
            let first = this.rules.find(x => x.type === 'limitation');
            if (!first) {
                first = this.createEmptyRule();
                first.type = 'limitation';
            }
            this.states.selectedRule = first;
        }
        this.setRiskRuleEditable();
    }

    setTemplateRule() {
        let counter = this.templates.filter(x => x.checked).length;
        if (counter > 1) {
            this.interaction.showError('至多选择一条风控规则!');
            return;
        }
        if (counter <= 0) {
            this.interaction.showError('至少选择一条风控规则!');
            return;
        }
        if (this.rules.length > 0) {
            this.interaction.showConfirm({
                title: '警告',
                message: `当前项已应用规则，如果你确定要从风控模板选择创建风控规则，则已应用的风控规则${
                    this.isAccount ? '和交易限制' : ''
                }将会移除，要确定吗?`,
                confirmed: () => {
                    this.applyTemplateRule();
                },
            });
        } else {
            this.applyTemplateRule();
        }
    }

    async applyTemplateRule() {
        let selectedTemplate = this.templates.find(x => x.checked) || {};
        let result = await this.getTemplateRule(selectedTemplate.templateId);
        if (!result.flag) {
            return;
        }
        let applyedRules = this.rules.filter(cdt => cdt.type === 'normal');
        //删除已有的风控规则
        if (applyedRules.length > 0) {
            let copyRules = this.helper.deepClone(applyedRules);
            let copyRulesPromise = copyRules.map(x => {
                return this.repoRisk.deleteRiskRule(x.id);
            });
            let delRiskResps = await Promise.all(copyRulesPromise);
            if (delRiskResps.some(resp => resp.errorCode !== 0)) {
                this.interaction.showError('移除已有风控规则失败，无法从模板创建!');
                return;
            }
        }
        let applyedLimitation = this.rules.filter(cdt => cdt.type === 'limitation');
        //如果不是账号类型是不会有交易限制的
        if (applyedLimitation.length > 0) {
            let copyLimitation = this.helper.deepClone(applyedLimitation);
            let copyLimitationPromise = copyLimitation.map(x => {
                return this.repoRisk.deleteLimitRules(x.id);
            });
            let delLimitationResps = await Promise.all(copyLimitationPromise);
            if (delLimitationResps.some(resp => resp.errorCode !== 0)) {
                this.interaction.showError('移除已有的交易限制规则失败，无法从模板创建!');
                return;
            }
        }
        let rulesPromise = result.data.riskConfigurationBeans.map(x => {
            let riskModel = this.constructRiskModel(x);
            if (riskModel.id) {
                delete riskModel.id;
            }
            return this.repoRisk.saveRiskRule(riskModel);
        });
        let rulesResps = await Promise.all(rulesPromise);
        if (rulesResps.some(x => x.errorCode !== 0)) {
            this.interaction.showError('应用风控模板规则失败,创建失败!');
            return;
        }
        let limitationPromise = result.data.restrictionBeans.map(x => {
            let limitationModel = this.constructLimitation(x);
            if (limitationModel.id) {
                delete limitationModel.id;
            }
            return this.repoRisk.saveLimitRule(limitationModel);
        });
        let limitationResps = await Promise.all(limitationPromise);
        if (limitationResps.some(x => x.errorCode !== 0)) {
            this.interaction.showError('应用交易限制模板规则失败,创建失败!');
            return;
        }
        this.states.dialog.selectVisible = false;
        //把后台返回的数据设置到rules上面去
        this.rules.clear();
        rulesResps.forEach((resp, index) => {
            result.data.riskConfigurationBeans[index].id = resp.data.id;
        });
        let applyRules = result.data.riskConfigurationBeans || [];
        this.rules.merge(applyRules);
        limitationResps.forEach((resp, index) => {
            result.data.restrictionBeans[index].id = resp.data.id;
        });
        let applyLimitations = result.data.restrictionBeans || [];
        this.rules.merge(applyLimitations);
        this.states.selectedRule = this.createEmptyRule();
        this.loadDefaultRule();
        this.interaction.showSuccess('当前风控模板已成功应用!');
    }

    async getTemplateRule(templateId) {
        let loading = this.interaction.showLoading({
            text: '正在获取风控模板规则...',
        });
        let result = {
            flag: false,
            data: [],
        };
        try {
            let resp = await this.repoRisk.getRiskRuleTemplate(templateId);
            if (resp.errorCode === 0) {
                result.flag = true;
                resp.data.riskConfigurationBeans = this.reshapeRiskList(resp.data.riskConfigurationBeans || []);
                resp.data.restrictionBeans = this.reshapeLimitation(resp.data.restrictionBeans || []);
                result.data = resp.data;
            } else {
                this.interaction.showError('获取风控模板规则失败!');
            }
        } catch (exp) {
            this.interaction.showError('获取风控模板规则失败!');
        } finally {
            loading.close();
        }
        return result;
    }

    async createFromTemplate() {
        let loading = this.interaction.showLoading({
            text: '正在获取风控模板信息...',
        });
        try {
            let resp = await this.repoRisk.getTemplateList();
            this.templates.clear();
            if (resp.errorCode === 0) {
                this.states.dialog.selectVisible = true;
                this.templates.merge(resp.data || []);
                this.templates.forEach(item => {
                    item.checked = false;
                });
            } else {
                this.interaction.showError('获取风控模板信息失败!');
            }
        } catch (e) {
            this.interaction.showError('获取风控模板信息失败!');
        } finally {
            loading.close();
        }
    }

    createEmptyRule() {
        return {
            id: null,
            type: 'normal',
            configurationName: null,
            limitationName: null,
            rules: [],
            indexList: [],
            expression: null,
            warningList: [],
            riskType: [this.riskOptions.after.value],
            interval: 30,
            sendMail: this.sendOptions.noSend.value,
            sendMessage: this.sendOptions.noSend.value,
        };
    }

    createEmptyPrcControl() {
        return {
            cancelRate: null,
            orderSpeed: null,
            errorCount: null,
        };
    }

    createIndicator() {
        var diff_names = this.states.indicators.assessList.distinct(x => x.name);
        if (diff_names.length !== this.states.indicators.assessList.length) {
            this.interaction.showError('不能重复添加分类方法!');
            return;
        }
        let range = this.states.indicators.assessList.map(indicator => {
            let result = { types: [], name: null, hash: {}, cascade: false, defaultClassify: false };
            //直接一级；
            if (indicator.defaultClassify) {
                result.types.push(indicator.method);
                result.defaultClassify = true;
            } else if (indicator.cascade) {
                //三级
                result.types.merge(indicator.secondary);
                result.cascade = true;
            } else {
                //二级
                result.types.merge(indicator.primary);
            }
            result.name = indicator.name;
            result.hash = indicator.hash;
            return result;
        });
        //把没有选中二阶的过滤掉
        let newIndicator = {
            id: this.states.indicators.id,
            name: this.states.indicators.name,
            assessList: range,
            range: range,
            summary: this.states.indicators.summary,
            weight: 1,
            description: this.states.indicators.description,
            riskKindId: this.states.indicators.riskKindId,
            show: this.states.indicators.show,
        };

        let exist = this.states.selectedRule.indexList.find(x => x.name === this.states.indicators.name);

        if (typeof newIndicator.id !== 'number' && !newIndicator.id) {
            if (exist) {
                this.interaction.showWarning('已存在同类的组合指标，无需重新添加!');
                return;
            }
            newIndicator.id = Math.random().toFixed(5) * 100000;
            this.states.selectedRule.indexList.push(newIndicator);
        } else {
            let updateIndicator = this.states.selectedRule.indexList.find(cdt => cdt.id === newIndicator.id);
            this.helper.extend(updateIndicator, newIndicator);
        }
        this.updateExpression();
        this._closeIndicatorDialog();
        this.setZeroIndicator();
        this.setIndicatorReadonly();
    }

    createAlert() {
        let alert = {
            warningType: null,
            id: new Date(),
            netValue: null,
            maxNetValueSymbol: null,
            minNetValueSymbol: null,
            maxNetValue: null,
            minNetValue: null,
            variableIndex: null,
            miniValue: null,
            maxiValue: null,
            maxValueSymbol: null,
            minValueSymbol: null,
        };

        this.states.selectedRule.warningList.push(alert);
    }

    createEmptyMetaRule() {
        return {
            timestamp: new Date().getTime() + Math.random().toFixed(3) * 1000,
            flag: false,
            //分类方法
            method: null,
            defaultClassify: false,
            cascade: false,
            //一级
            primary: [],
            PrimaryOptions: [],
            //二级
            secondary: [],
            SecondaryOptions: [],
            hash: null,
            types: [],
        };
    }

    createEmptyAssess() {
        return {
            timestamp: new Date().getTime() + Math.random().toFixed(3) * 1000,
            name: null,
            defaultClassify: true,
            cascade: false,
            //分类方法
            method: null,
            //一级
            primary: [],
            PrimaryOptions: [],
            //二级
            secondary: [],
            SecondaryOptions: [],
            hash: null,
        };
    }

    createZeroIndicator() {
        return {
            id: null,
            show: true,
            summary: this.summaryMethods.single.value,
            range: [],
            assessList: [],
            description: null,
            riskKindId: null,
            name: '',
        };
    }

    saveSelectedEdit() {

        let flag = this.checkRiskValid();
        if (!flag) {
            return;
        }
        //如果是保存在账号、策略、产品上，就直接提交给后端，否则，就缓存在前端
        if (this.context.action === this.Actions.normal.value) {
            this.interaction.showConfirm({
                message: '确定要提交当前编辑的风控规则吗?',
                title: '提醒',
                confirmed: async () => {
                    this.reqSaveRiskRule();
                },
                canceled: () => {
                    console.log('the error has occurred in saving risk rule');
                },
            });
        } else {
            this.createTemplateRule();
        }
    }

    formatRule(data) {
        return {
            id: data.id,
            type: data.type || 'normal',
            configurationName: data.configurationName || null,
            limitationName: data.limitationName || null,
            rules: data.rules || [],
            indexList: data.indexList || [],
            expression: data.expression || null,
            warningList: data.warningList || [],
            riskType: data.riskType || [],
            interval: data.interval || 0,
            sendMail: typeof data.sendMail === 'undefined' ? this.sendOptions.noSend.value : data.sendMail,
            sendMessage: typeof data.sendMessage === 'undefined' ? this.sendOptions.noSend.value : data.sendMessage,
        };
    }

    formatAssetRange(this_asset) {
        let range = this_asset.assessList;
        if (!this_asset.show || range.length <= 0) {
            return '---';
        }
        return (
            range
                .map(x => {
                    return x.types instanceof Array && x.types.length > 0
                        ? `${x.name}（${x.types
                              .map(type => {
                                  let hash = x.hash;
                                  return hash[type] && hash[type].parentClassName
                                      ? hash[type].parentClassName
                                      : '默认分类';
                              })
                              .join(',')}）`
                        : x.name;
                })
                .join(';<br/>') + (range.length > 0 ? ';' : '')
        );
    }

    formatLimitAssetType(this_limitation) {
        let name = this_limitation.method;
        let targetNode = this.industriesOptions.find(x => x.name === name) || {};
        return this_limitation.types
            .map(x => {
                return targetNode.hash[x] && targetNode.hash[x].parentClassName
                    ? targetNode.hash[x].parentClassName
                    : '默认类型';
            })
            .join(',');
    }

    formatSummary(indicator_item) {
        if (!indicator_item.show) {
            return '---';
        }
        return indicator_item.summary === this.summaryMethods.single.value
            ? this.summaryMethods.single.label
            : this.summaryMethods.summarize.label;
    }

    formatRiskType(risk_type) {
        return risk_type
            .map(type => {
                let target_type = this.helper.dict2Array(this.riskOptions).find(x => x.value === type) || {};
                return target_type.label || '未知类型';
            })
            .join('、');
    }

    formatEquation(current, key) {
        let result = '';
        switch (key) {
            case 'netValue':
                result = this._formatNetValueEquation(current);
                break;
            case 'variableIndex':
                result = this._formatValueEquation(current);
                break;
            default:
                result = '---';
                break;
        }

        return result;
    }

    formatAlertType(type) {
        let target = this.helper.dict2Array(this.warningLevel).find(cdt => cdt.value === type) || {};
        return target.label;
    }

    _formatValueEquation(current) {
        let left = this.helper.dict2Array(this.operators).find(cdt => cdt.value === current.minValueSymbol) || {};
        let right = this.helper.dict2Array(this.operators).find(cdt => cdt.value === current.maxValueSymbol) || {};

        if (
            (typeof current.minValue !== 'number' &&
                !current.minValue &&
                !left.label &&
                typeof current.maxValue !== 'number' &&
                !current.maxValue &&
                !right.label) ||
            (typeof current.minValue !== 'number' && !current.minValue && left.label) ||
            (typeof current.minValue === 'number' && !left.label) ||
            (typeof current.maxValue !== 'number' && !current.maxValue && right.label) ||
            (typeof current.maxValue === 'number' && !right.label)
        ) {
            return '---';
        } else if (typeof current.minValue !== 'number' && !current.minValue && !left.label) {
            return `指标 ${right.label} ${current.maxValue}`;
        } else if (typeof current.maxValue !== 'number' && !current.maxValue && !right.label) {
            return `${current.minValue} ${left.label} 指标`;
        }

        return `${current.minValue} ${left.label} 指标 ${right.label} ${current.maxValue}`;
    }

    _formatNetValueEquation(current) {
        if (!current.preposition) {
            return '---';
        }
        let left = this.helper.dict2Array(this.operators).find(cdt => cdt.value === current.minNetValueSymbol) || {};
        let right = this.helper.dict2Array(this.operators).find(cdt => cdt.value === current.maxNetValueSymbol) || {};

        if (
            (typeof current.minNetValue !== 'number' &&
                !current.minNetValue &&
                !left.label &&
                typeof current.maxNetValue !== 'number' &&
                !current.maxNetValue &&
                !right.label) ||
            (typeof current.minNetValue !== 'number' && !current.minNetValue && left.label) ||
            (typeof current.minNetValue === 'number' && !left.label) ||
            (typeof current.maxNetValue !== 'number' && !current.maxNetValue && right.label) ||
            (typeof current.maxNetValue === 'number' && !right.label)
        ) {
            return '---';
        } else if (typeof current.minNetValue !== 'number' && !current.minNetValue && !left.label) {
            return `单位净值 ${right.label} ${current.maxNetValue}`;
        } else if (typeof current.maxNetValue !== 'number' && !current.maxNetValue && !right.label) {
            return `${current.minNetValue} ${left.label} 单位净值`;
        }

        return `${current.minNetValue} ${left.label} 单位净值 ${right.label} ${current.maxNetValue}`;
    }

    updateExpression() {

        this.states.selectedRule.expression = this.states.selectedRule.indexList
                                              .map((cdt, index) => { return `${this.getIndex(index + 1)}*${cdt.weight}`; })
                                              .join(' + ');
    }

    setZeroIndicator() {
        this.states.indicators = this.createZeroIndicator();
    }

    setIndicatorReadonly() {
        this.states.indicatorMode = 'display';
    }

    setIndicator(risk_kind) {
        let indicator = this.helper.deepClone(risk_kind);
        if (!indicator) {
            return null;
        }
        //如果不是从左边el-tree点出来的,而是从编辑框里面出来的
        if (indicator.assessList) {
            this.states.indicators.id = indicator.id;
            this.states.indicators.riskKindId = indicator.riskKindId;
        } else {
            this.states.indicators.riskKindId = indicator.id;
        }
        this.states.indicators.show = indicator.show;
        this.states.indicators.assessList = this.constructClassification(indicator.assessList || []);
        this.states.indicators.name = indicator.name || '';
        this.states.indicators.range = indicator.range || [];
        this.states.indicators.description = indicator.description;
        this.states.indicators.summary =
            indicator.summary !== undefined ? indicator.summary : this.summaryMethods.single.value;
    }

    setIndicatorEditable() {
        this.states.indicatorMode = 'edit';
    }

    removeIndicator(this_indicator) {
        this.states.selectedRule.indexList.remove(x => x.name === this_indicator.name);
        this.updateExpression();
    }

    //删除交易限制里面的内容
    removeInstrument(current) {
        let id = current.timestamp || current.id;
        this.states.selectedRule.rules.remove(cdt => (cdt.id === id || cdt.timestamp === id) && id !== undefined);
    }

    removeAlert(this_alert) {
        this.states.selectedRule.warningList.remove(x => x.id === this_alert.id);
    }

    openTemplate() {
        if (this.rules.length <= 0) {
            this.interaction.showError('请先添加要创建的模板内容!');
            return;
        }
        // let editContext = this.states.selectedRule;
        // if ((editContext.type === 'normal' && !this.checkRiskValid()) ||
        //     (editContext.type === 'limitation' && !this.checkLimitationValid() )) {
        //     return false;
        // }
        if (this.templateModel.data.identityType) {
            this.templateModel.disabled = true;
            this.templateModel.data.type = this.templateModel.data.identityType;
        }
        this.states.dialog.templateVisible = true;
    }

    setSelectedRiskRule(selected_rule) {
        let cloned_rule = this.helper.deepClone(selected_rule);
        if (this.states.selectedRule.id && this.states.selectedRuleMode === 'edit') {
            this.interaction.showConfirm({
                title: '提示',
                message: '即将取消编辑当前内容，并切换至其他风控规则，要继续吗?',
                confirmed: () => {
                    this.states.selectedRule = cloned_rule;
                    this.setRiskRuleReadonly();
                },
            });
            return;
        }
        this.states.selectedRule = cloned_rule;
        this.setRiskRuleReadonly();
    }

    setRiskKind(risk_kind) {
        if (risk_kind.categories instanceof Array && risk_kind.categories.length > 0) {
            return;
        }

        if (this.states.indicators.name !== risk_kind.name && this.states.indicators.name) {
            this.interaction.showConfirm({
                message: '切换指标将清除右侧数据，是否确定？',
                title: '警告',
                confirmed: () => {
                    this.setIndicator(risk_kind);
                },
                canceled: () => {
                    console.log('the error has occurred!');
                },
            });
        } else {
            this.setIndicatorEditable();
            this.setIndicator(risk_kind);
        }
    }

    //当执行的是策略、账号、产品入口的时候
    async reqRiskRules() {
        let loading = this.interaction.showLoading({
            text: '正在获取风控信息...',
        });
        // try {
        this.rules.clear();
        let resp = await this.repoRisk.getRiskRuleList(this.context.identity);
        if (resp.errorCode === 0) {
            this.rules.merge(this.reshapeRiskList(resp.data || []));
        } else {
            this.interaction.showError(`获取风控规则失败，详细信息：${resp.errorCode}/${resp.errorMsg}`);
        }
        // } catch (e) {
        //     this.interaction.showError(`获取风控规则失败！`);
        // } finally {
        loading.close();
        // }
    }

    async reqRiskTemplate() {
        let loading = this.interaction.showLoading({
            text: '正在获取风控信息...',
        });
        // try {
        this.rules.clear();
        let resp = await this.repoRisk.getRiskRuleTemplate(this.context.identity);
        if (resp.errorCode === 0) {
            this.rules.merge(this.reshapeRiskList(resp.data.riskConfigurationBeans || []));
            this.rules.merge(this.reshapeLimitation(resp.data.restrictionBeans || []));
        } else {
            this.interaction.showError(`获取风控规则失败，详细信息：${resp.errorCode}/${resp.errorMsg}`);
        }
        // } catch (e) {
        //     this.interaction.showError(`获取风控规则失败！`);
        // } finally {
        loading.close();
        // }
    }

    async reqKindList() {
        let loading = this.interaction.showLoading({
            text: '正在获取...',
        });

        // try {
        this.indexClassifications.clear();
        let resp = await this.repoRisk.getRiskKind();
        if (resp.errorCode === 0) {
            this.indexClassifications.merge(this.reshapeCategories(resp.data || []));
        } else {
            this.interaction.showError(`获取风控资产指标列表失败，详细信息：${resp.errorCode}/${resp.errorMsg}`);
        }
        // } catch (e) {
        //     this.interaction.showError('获取风控资产指标列表失败!');
        // } finally {
        loading.close();
        // }
    }

    async reqIndustries() {
        let loading = this.interaction.showLoading({
            text: '正在获取...',
        });

        try {
            this.industriesOptions.clear();
            let regionResp = await this.repoRisk.getRiskClassification();
            if (regionResp.errorCode === 0) {
                this.industriesOptions.merge(this.reshapeIndustries(regionResp.data || []));
            } else {
                this.interaction.showError(
                    `获取统计范围类别失败,详细信息:${regionResp.errorCode}/${regionResp.errorMsg}!`,
                );
            }
        } catch (e) {
            this.interaction.showError('获取统计范围类别失败!');
        } finally {
            loading.close();
        }
    }

    reshapeRiskList(list) {
        if (!(list instanceof Array) || list.length == 0) {
            return [];
        }

        return list.map(this_rule => {
            let risk_type = [];
            if (this_rule.beforeCheck) {
                risk_type.push(this.riskOptions.before.value);
            }
            if (this_rule.afterCheck) {
                risk_type.push(this.riskOptions.after.value);
            }
            return this.formatRule({
                id: this_rule.id,
                type: 'normal',
                configurationName: this_rule.configurationName,
                expression: this_rule.expression,
                interval: this_rule.checkInterval,
                riskType: risk_type,
                sendMail: this_rule.sendMail,
                sendMessage: this_rule.sendMessage,
                indexList: this.reshapeIndicators(this_rule.riskNorms || []),
                warningList: this.reshapeWarningList(this_rule.riskWarnings || []),
            });
        });
    }

    reshapeWarningList(list) {

        if (!(list instanceof Array) || list.length == 0) {
            return [];
        }

        return list.map(x => {
            return {
                configurationId: x.configurationId,
                warningType: x.warningType,
                id: x.id,
                maxNetValueSymbol: x.minNetRelative,
                minNetValueSymbol: x.maxNetRelative,
                maxNetValue: x.minNetValue,
                minNetValue: x.maxNetValue,
                variableIndex: '指标',
                minValue: x.maxWarningValue,
                maxValue: x.minWarningValue,
                maxValueSymbol: x.minRelative,
                minValueSymbol: x.maxRelative,
                preposition: !!(x.maxNetRelative || x.minNetRelative || x.maxNetValue || x.minNetValue),
            };
        });
    }

    reshapeIndustries(list) {
        
        if (!(list instanceof Array) || list.length == 0) {
            return [];
        }
        list = list.filter(x => x.classificationName !== '');
        let industries_dict = list.groupBy(x => {
            return x.classificationName;
        });
        let schema = [];
        for (let key in industries_dict) {
            //orgId 0 和 非0 0肯定是直接用el-selector, 非0得看情况 如果直接全部都是没有一级分类和二级分类，那么就一致
            let cat = industries_dict[key];
            let hash = {};
            if (cat instanceof Array) {
                cat.forEach(x => {
                    hash[x.id] = x;
                });
            }
            let flag = true;
            let defaultClassify = false;
            if (cat.every(x => !x.parentClassName)) {
                flag = false;
                defaultClassify = true;
            }
            if (cat.every(x => x.orgId === 0)) {
                flag = false;
            }
            let schema_meta = { name: key, cascade: flag, hash, defaultClassify: defaultClassify };

            if (flag) {
                let groupBy = cat.groupBy(x => x.parentClassName);
                let primaryClassifies = Object.keys(groupBy);
                schema_meta.sub = primaryClassifies.map(key => {
                    let sub_group = groupBy[key].groupBy(x => x.className);
                    let sub_keys = Object.keys(sub_group);
                    let isOnly = sub_keys.length === 1;
                    let isUndefined = sub_keys[0] === 'undefined';
                    return {
                        parentClassName: key,
                        id:
                            groupBy[key] instanceof Array && groupBy[key].length > 0
                                ? groupBy[key][0].id
                                : Math.random().toFixed(3) * 1000,
                        sub: Object.keys(sub_group).map(sub_key => {
                            return {
                                className: isOnly && isUndefined ? '默认分组' : sub_key,
                                id: sub_group[sub_key][0].id,
                            };
                        }),
                    };
                });
            } else {
                schema_meta.sub = cat;
            }
            //对于那种一二级分类都没有的分类，就应该设置defaultClassify
            schema.push(schema_meta);
        }
        return schema;
    }

    reshapeCategories(list) {
        var kind_group_dict = list
            .map(x => {
                return this.helper.extend(x, { show: x.kind !== this.SPECIALTYPE });
            })
            .groupBy(x => {
                return x.kind;
            });
        var tree_schema = [];

        for (let key in kind_group_dict) {
            let cat = kind_group_dict[key];
            tree_schema.push({ name: key, categories: cat });
        }

        return tree_schema;
    }

    /**
     * 当处理到资产类的时候，要特别注意,它的下面没有复杂的数据结构，因此我直接单独挑出来处理的
     */
    reshapeIndicators(list) {

        if (!(list instanceof Array) || list.length == 0) {
            return [];
        }

        let schema = list.groupBy(x => x.rangeCode);
        for (let pk in schema) {
            //这儿主要是把什么类型跟后端存储的id的关系先记录下来，到时候要用的
            schema[pk].mapping = [];
            schema[pk].forEach((second, index) => {
                schema[pk].mapping.push({
                    type: second.classificationId,
                    configurationId: second.configurationId,
                });
                //先找description
                let this_class =
                    this.indexClassifications.find(x => x.categories.some(_ => _.id === second.riskKindId)) || {};
                let secondary = this_class.categories.find(_ => _.id === second.riskKindId) || {};
                //再找它是 那个4个类里面哪个下面的
                let systemDefined = this.industriesOptions.find(xx =>
                    xx.sub.some(_ => _.id === second.classificationId),
                );
                let selfDefined = this.industriesOptions.find(xxx => {
                    return xxx.cascade && xxx.sub.some(xx => xx.sub.some(x => x.id === second.classificationId));
                });
                //如果是自定义类呢
                let this_industry = systemDefined || selfDefined || {};
                second.description = secondary.description || '';
                second.name = this_industry.name || '';
                if (index === 0) {
                    schema[pk].name = secondary.name;
                    schema[pk].summary = second.byInstrument
                        ? this.summaryMethods.single.value
                        : this.summaryMethods.summarize.value;
                    schema[pk].show = !this.SPECIALTYPE_IDS.includes(second.riskKindId);
                    schema[pk].weight = second.weight;
                    schema[pk].description = second.description;
                    schema[pk].riskKindId = second.riskKindId;
                }
            });
            //根据name进行归类
            schema[pk] = {
                id: Math.random().toFixed(5) * 100000,
                assessList: schema[pk].groupBy(xx => xx.name),
                description: schema[pk].description,
                riskKindId: schema[pk].riskKindId,
                summary: schema[pk].summary,
                name: schema[pk].name,
                show: schema[pk].show,
                weight: schema[pk].weight,
                mapping: schema[pk].mapping,
            };
            //如果是特殊类型的话，mapping是肯定length为1的
            //设置el-select上面的range
            schema[pk].range = !this.SPECIALTYPE_IDS.includes(schema[pk].riskKindId)
                ? Object.keys(schema[pk].assessList)
                : [];
            let transformed = [];
            for (let key in schema[pk].assessList) {
                let this_node = schema[pk].assessList[key];
                let industry = this.industriesOptions.find(cdt => cdt.name === key) || {};
                transformed.push({
                    name: key,
                    hash: industry.hash,
                    options: industry.sub,
                    cascade: industry.cascade,
                    defaultClassify: industry.defaultClassify,
                    types: this_node.map(cdt => cdt.classificationId),
                });
            }
            schema[pk].assessList = !this.SPECIALTYPE_IDS.includes(schema[pk].riskKindId) ? transformed : [];
        }
        return this.helper.dict2Array(schema);
    }

    reshapeLimitation(limitations) {
        return limitations.map(item => {
            return this.formatRule(this.reshapeSingleLimitation(item));
        });
    }

    reshapeSingleLimitation(item) {
        let rules = this.helper.dict2Array(item.restrictionDetails.groupBy(x => x.rangeId));
        let meta = {
            errorCount: item.errorCount,
            orderSpeed: item.orderSpeed,
            cancelRate: item.cancelRate,
        };
        if (typeof meta.errorCount === 'number') {
            this.accountProcedureControl.errorCount = meta.errorCount;
        }
        if (typeof meta.orderSpeed === 'number') {
            this.accountProcedureControl.orderSpeed = meta.orderSpeed;
        }
        if (typeof meta.cancelRate === 'number') {
            this.accountProcedureControl.cancelRate = meta.cancelRate;
        }
        rules = rules.map(x => {
            let result = this.createEmptyMetaRule();
            x.forEach(type => {
                result.flag = typeof type.except !== 'undefined' ? type.except : type.isExcept;
                result.types.push(type.classificationInfoId);
            });
            let industry = this.industriesOptions.find(cdt => cdt.hash[x[0].classificationInfoId] !== undefined) || {};
            result.method = industry.name;
            result.hash = industry.hash;
            if (industry.defaultClassify) {
                result.defaultClassify = true;
                return result;
            }

            if (!industry.cascade) {
                result.primary = result.types;
                let primaryOptions = this.industriesOptions.find(cdt => cdt.name === industry.name);
                result.PrimaryOptions = primaryOptions ? primaryOptions.sub : [];
                result.defaultClassify = false;
                result.cascade = false;
            } else {
                //一二级都存在
                let primaryOptions = this.industriesOptions.find(cdt => cdt.name === industry.name);
                //构造一级的候选项
                result.PrimaryOptions = primaryOptions.sub;
                let secondaryOptions = [];
                if (primaryOptions.sub instanceof Array) {
                    let primaryClassify = result.types;
                    secondaryOptions = primaryOptions.sub.filter(cdt => primaryClassify.includes(cdt.id));
                }
                //增加全选按钮
                if (!result.SecondaryOptions.find(cdt => cdt.id === this.SELECT_ALL)) {
                    result.SecondaryOptions.push({
                        id: this.SELECT_ALL,
                        className: '全选',
                    });
                }
                secondaryOptions.forEach(cdt => {
                    cdt.sub.forEach((xx, index) => {
                        if (!result.SecondaryOptions.find(cdt => cdt.id === xx.id)) {
                            result.SecondaryOptions.push(xx);
                        }
                        if (index === 0) {
                            //把二级的已经选中的放入
                            result.primary.push(xx.id);
                        }
                    });
                });
                result.secondary = result.types;
                result.defaultClassify = false;
                result.cascade = true;
            }
            return result;
        });
        return Object.assign(meta, {
            id: item.id,
            type: 'limitation',
            limitationName: item.restrictionName,
            rules: rules,
        });
    }

    async reqLimitationRules() {
        let loading = this.interaction.showLoading({
            text: '正在获取...',
        });
        // try {
        let resp = await this.repoRisk.getLimitRules(this.context.identity);
        if (resp.errorCode === 0) {
            this.rules.merge(this.reshapeLimitation(resp.data || []));
            this.loadDefaultRule();
        } else {
            this.interaction.showError('获取交易限制失败!');
        }
        // } catch (e) {
        //     this.interaction.showError('获取交易限制失败!');
        // } finally {
        loading.close();
        // }
    }

    constructIndicator(indicators) {

        if (!(indicators instanceof Array) || indicators.length == 0) {
            return [];
        }

        //把资产类的内容拿出来单独处理
        let result = indicators.map((norm, index) => {
            console.log(norm);
            let decorated = [];
            if (this.SPECIALTYPE_IDS.includes(norm.riskKindId)) {
                return decorated;
            }
            let range = this.getIndex(index + 1);
            let riskKindId = norm.riskKindId;
            let weight = norm.weight;
            norm.assessList.forEach(condition => {
                condition.types.forEach(type => {
                    let classificationId = null;
                    if (!condition.defaultClassify) {
                        classificationId = type;
                    } else {
                        let ids = Object.keys(condition.hash);
                        classificationId = ids.length > 0 ? parseInt(ids[0]) : -1;
                    }
                    let construct_item = {
                        riskKindId,
                        classificationId,
                        rangeCode: range,
                        weight,
                        //单票 or 汇总
                        byInstrument: norm.summary === this.summaryMethods.single.value,
                    };
                    //如果没有mapping的话，说明是新增的
                    //如果有mapping的话，找到目标id
                    if (norm.mapping instanceof Array) {
                        let originRelationNode = norm.mapping.find(x => x.type === type) || {};
                        construct_item.configurationId = originRelationNode.configurationId;
                    }
                    decorated.push(construct_item);
                });
            });
            return decorated;
        });
        indicators.forEach((norm, index) => {
            if (this.SPECIALTYPE_IDS.includes(norm.riskKindId)) {
                let range = this.getIndex(index + 1);
                let riskKindId = norm.riskKindId;
                let weight = norm.weight;
                //对于这种特殊的，如果有mapping，mapping必定length是为1的
                result.push({
                    configurationId:
                        norm.mapping instanceof Array && norm.mapping.length === 1
                            ? norm.mapping[0].configurationId
                            : null,
                    riskKindId,
                    classificationId: null,
                    rangeCode: range,
                    weight,
                    //单票 or 汇总
                    byInstrument: false,
                });
            }
        });
        let output = [];
        result.forEach(x => {
            if (x instanceof Array) {
                x.forEach(y => {
                    output.push(y);
                });
            } else {
                output.push(x);
            }
        });
        return output;
    }

    async createTemplateLimitation() {
        let insertLimitation = this.constructLimitation(this.states.selectedRule);
        //如果已经有了模板id就单个保存
        if (this.context.templateId) {
            await this.reqSaveLimitation({
                id: this.context.templateId,
                identityType: this.context.identityType,
                name: this.context.name,
            });
        }

        if (!this.states.selectedRule.id && !this.states.selectedRule.timestamp) {
            let constructed = this.reshapeSingleLimitation(insertLimitation);
            constructed.timestamp = new Date().getTime() + Math.random().toFixed(3) * 1000;
            this.states.selectedRule.timestamp = constructed.timestamp;
            this.rules.push(this.helper.deepClone(constructed));
            constructed.rules.forEach((item, item_idx) => {
                this.states.selectedRule.rules[item_idx].types.clear();
                this.states.selectedRule.rules[item_idx].types.merge(item.types);
            });
        } else {
            //相等但是不能同时为undefined
            let val = this.states.selectedRule.id || this.states.selectedRule.timestamp;
            let oldLimitation = this.rules.find(x => (x.id === val || x.timestamp === val) && val !== undefined);
            let constructed = this.reshapeSingleLimitation(insertLimitation);
            this.helper.extend(oldLimitation, constructed, { id: val, timestamp: val });
            constructed.rules.forEach((item, item_idx) => {
                this.states.selectedRule.rules[item_idx].types.clear();
                this.states.selectedRule.rules[item_idx].types.merge(item.types);
            });
        }
        this.setRiskRuleReadonly();
    }

    constructAlert(alerts) {

        if (!(alerts instanceof Array) || alerts.length == 0) {
            return [];
        }

        return alerts.map(x => {
            return {
                warningType: x.warningType,
                minWarningValue: x.maxValue,
                maxWarningValue: x.minValue,
                minRelative: x.maxValueSymbol,
                maxRelative: x.minValueSymbol,
                minNetValue: x.maxNetValue,
                maxNetValue: x.minNetValue,
                minNetRelative: x.maxNetValueSymbol,
                maxNetRelative: x.minNetValueSymbol,
            };
        });
    }

    constructClassification(original) {
        return original.map(x => {
            let result = this.createEmptyAssess();
            result.name = x.name;
            result.types = x.types;
            if (x.defaultClassify && !x.cascade) {
                //仅有一级
                result.method = x.name;
            } else if (x.cascade) {
                //一二级都存在
                result.method = x.name;
                let primaryOptions = this.industriesOptions.find(cdt => cdt.name === x.name);
                //构造一级的候选项
                result.PrimaryOptions = primaryOptions.sub;
                let secondaryOptions = [];
                if (primaryOptions.sub instanceof Array) {
                    let primaryClassify = x.types;
                    secondaryOptions = primaryOptions.sub.filter(cdt => primaryClassify.includes(cdt.id));
                }
                //增加全选按钮
                if (!result.SecondaryOptions.find(cdt => cdt.id === this.SELECT_ALL)) {
                    result.SecondaryOptions.push({
                        id: this.SELECT_ALL,
                        className: '全选',
                    });
                }
                secondaryOptions.forEach(cdt => {
                    cdt.sub.forEach((xx, index) => {
                        if (!result.SecondaryOptions.find(cdt => cdt.id === xx.id)) {
                            result.SecondaryOptions.push(xx);
                        }
                        if (index === 0) {
                            //把二级的已经选中的放入
                            result.primary.push(xx.id);
                        }
                    });
                });
                result.secondary = x.types;
                result.defaultClassify = false;
                result.cascade = true;
            } else if (!x.cascade) {
                //仅到二级
                result.method = x.name;
                result.primary = x.types;
                let primaryOptions = this.industriesOptions.find(cdt => cdt.name === x.name);
                result.PrimaryOptions = primaryOptions ? primaryOptions.sub : [];
                result.defaultClassify = false;
                result.cascade = false;
            }
            result.hash = x.hash;
            return result;
        });
    }

    constructRiskModel(riskRule, template) {

        if (!riskRule) {
            return {};
        }
        let constructedModel = {

            id: riskRule.id,
            createUser: this.userInfo.userId,
            identityType: this.context.type,
            configurationName: riskRule.configurationName,
            expression: riskRule.expression,
            checkInterval: riskRule.interval,
            isActive: true,
            sendMail: riskRule.sendMail,
            sendMessage: riskRule.sendMessage,
            beforeCheck: riskRule.riskType.includes(this.riskOptions.before.value),
            afterCheck: riskRule.riskType.includes(this.riskOptions.after.value),
            riskNorms: this.constructIndicator(this.helper.deepClone(riskRule.indexList)),
            riskWarnings: this.constructAlert(this.helper.deepClone(riskRule.warningList)),
            orgId: this.userInfo.orgId,
        };
        if (template === undefined) {
            constructedModel.identity = this.context.identity;
        } else {
            constructedModel.templateId = template.id;
            constructedModel.identityType = template.identityType;
            constructedModel.templateName = template.name;
        }
        return constructedModel;
    }

    addClassification() {
        if (this.states.selectedRule.rules.some(x => !x.method)) {
            this.interaction.showWarning('请先完善当前分类信息!');
            return;
        }
        let meta_rule = this.createEmptyMetaRule();
        this.states.selectedRule.rules.push(meta_rule);
    }

    addNewRiskRule() {
        let newRule = this.createEmptyRule();
        this.states.selectedRule = newRule;
        this.setRiskRuleEditable();
    }

    addNewLimitation() {
        let newRule = this.createEmptyRule();
        newRule.type = 'limitation';
        this.states.selectedRule = newRule;
        this.setRiskRuleEditable();
    }

    addAssessCondition() {
        if (this.states.indicators.assessList.some(x => !x.method)) {
            this.interaction.showError('请先正确选择分类信息!');
            return;
        }
        this.states.indicators.assessList.push(this.createEmptyAssess());
    }

    updatePrimary(this_assess) {
        let id = this_assess.id || this_assess.timestamp;
        let method = this_assess.method;
        let updateAssess = this.states.indicators.assessList.find(
            x => (x.id === id || x.timestamp === id) && id !== undefined,
        );
        updateAssess.primary = [];
        updateAssess.PrimaryOptions = [];
        updateAssess.secondary = [];
        updateAssess.SecondaryOptions = [];
        let primaryOptions = this.industriesOptions.find(x => x.name === method);
        if (!primaryOptions) {
            updateAssess = this.helper.extend(this_assess, this.createEmptyAssess());
            return;
        }
        updateAssess.PrimaryOptions = primaryOptions.sub;
        updateAssess.name = primaryOptions.name;
        updateAssess.hash = primaryOptions.hash;
        updateAssess.defaultClassify = primaryOptions.defaultClassify;
        updateAssess.cascade = primaryOptions.cascade;
    }

    //交易限制方法
    updatePrimaryOptions(this_type) {
        let method = this_type.method;
        let this_classify = this.industriesOptions.find(x => x.name === method) || {};
        this_type.hash = this_classify.hash;
        if (this_classify.defaultClassify) {
            let keys = Object.keys(this_classify.hash);
            let first = keys.length > 0 ? keys[0] : '';
            this_type.defaultClassify = this_classify.defaultClassify;
            this_type.cascade = false;
            this_type.types = [parseInt(first)];
            return;
        }
        this_type.PrimaryOptions.clear();
        this_type.PrimaryOptions.merge(this_classify.sub || []);
        this_type.primary.clear();
        this_type.cascade = this_classify.cascade;
        this_type.defaultClassify = this_classify.defaultClassify;
        this_type.SecondaryOptions.clear();
        this_type.secondary.clear();
    }

    updateSecondary(this_assess) {
        let id = this_assess.id || this_assess.timestamp;
        let updateAssess = this.states.indicators.assessList.find(
            x => (x.id === id || x.timestamp === id) && id !== undefined,
        );
        let primaryClassify = this_assess.primary;
        if (primaryClassify.length <= 0) {
            updateAssess.secondary = [];
            updateAssess.SecondaryOptions = [];
            return;
        }
        let method = this_assess.method;
        let primaryOptions = this.industriesOptions.find(x => x.name === method);
        //只针对二级分类才会进行以下操作
        if (!primaryOptions.cascade) {
            return;
        }
        let secondaryOptions = [];
        //先把二级的候选项扔掉，再根据验证添加
        this_assess.SecondaryOptions.clear();
        if (primaryOptions.sub instanceof Array) {
            secondaryOptions = primaryOptions.sub.filter(cdt => primaryClassify.includes(cdt.id));
        }
        if (!updateAssess.SecondaryOptions.find(cdt => cdt.id === this.SELECT_ALL)) {
            updateAssess.SecondaryOptions.push({
                id: this.SELECT_ALL,
                className: '全选',
            });
        }
        secondaryOptions.forEach(x => {
            x.sub.forEach(xx => {
                if (!updateAssess.SecondaryOptions.find(cdt => cdt.id === xx.id)) {
                    updateAssess.SecondaryOptions.push(xx);
                }
            });
        });
        let remainIds = updateAssess.SecondaryOptions.map(cdt => cdt.id);
        this_assess.secondary.remove(x => !remainIds.includes(x));
    }

    updateSelectRegion(this_assess) {
        let id = this_assess.id || this_assess.timestamp;
        let updateAssess = this.states.indicators.assessList.find(
            x => (x.id === id || x.timestamp === id) && id !== undefined,
        );
        if (updateAssess.secondary.includes(this.SELECT_ALL)) {
            updateAssess.secondary.clear();
            updateAssess.SecondaryOptions.forEach(x => {
                updateAssess.secondary.push(x.id);
            });
            updateAssess.secondary.remove(x => x === this.SELECT_ALL);
        }
    }

    //交易限制部分
    updateSecondaryOptions(this_type) {
        let id = this_type.id || this_type.timestamp;
        let updateRule = this.states.selectedRule.rules.find(
            x => (x.id === id || x.timestamp === id) && id !== undefined,
        );
        let primaryClassify = this_type.primary;
        if (primaryClassify.length <= 0) {
            updateRule.secondary = [];
            updateRule.SecondaryOptions = [];
            return;
        }
        let method = this_type.method;
        let primaryOptions = this.industriesOptions.find(x => x.name === method);
        //只针对二级分类才会进行以下操作
        if (!primaryOptions.cascade) {
            return;
        }
        let secondaryOptions = [];
        //先把二级的候选项扔掉，再根据验证添加
        this_type.SecondaryOptions.clear();
        if (primaryOptions.sub instanceof Array) {
            secondaryOptions = primaryOptions.sub.filter(cdt => primaryClassify.includes(cdt.id));
        }
        if (!updateRule.SecondaryOptions.find(cdt => cdt.id === this.SELECT_ALL)) {
            updateRule.SecondaryOptions.push({
                id: this.SELECT_ALL,
                className: '全选',
            });
        }
        secondaryOptions.forEach(x => {
            x.sub.forEach(xx => {
                if (!updateRule.SecondaryOptions.find(cdt => cdt.id === xx.id)) {
                    updateRule.SecondaryOptions.push(xx);
                }
            });
        });
        let remainIds = updateRule.SecondaryOptions.map(cdt => cdt.id);
        this_type.secondary.remove(x => !remainIds.includes(x));
    }

    updateSelectAll(this_type) {
        let id = this_type.id || this_type.timestamp;
        let updateRule = this.states.selectedRule.rules.find(
            x => (x.id === id || x.timestamp === id) && id !== undefined,
        );
        if (updateRule.secondary.includes(this.SELECT_ALL)) {
            updateRule.secondary.clear();
            updateRule.SecondaryOptions.forEach(x => {
                updateRule.secondary.push(x.id);
            });
            updateRule.secondary.remove(x => x === this.SELECT_ALL);
        }
    }

    clearSelectedRule() {
        if (this.rules.length > 0) {
            this.loadDefaultRule();
        } else {
            this.states.selectedRule = this.createEmptyRule();
            this.setRiskRuleEditable();
        }
    }

    removeAssessItem(this_assess) {
        let id = this_assess.id || this_assess.timestamp;
        this.states.indicators.assessList.remove(x => (x.id === id || x.timestamp === id) && id !== undefined);
        this.states.indicators.range.remove(x => x === this_assess.name);
    }

    clearSelectedLimitation() {
        this.states.selectedRule = this.createEmptyRule();
        this.loadDefaultRule();
    }

    editSelectedLimitation(this_limit) {
        this.states.selectedRule = this.helper.deepClone(this_limit);
        this.setRiskRuleEditable();
    }

    getIndex(num) {
        let str = '';
        while (num > 0) {
            var m = num % 26;
            if (m === 0) {
                m = 26;
            }
            str = String.fromCharCode(m + 64) + str;
            num = (num - m) / 26;
        }
        return str;
    }

    _closeIndicatorDialog() {
        this.states.dialog.indicatorVisible = false;
    }

    _openIndicatorDialog() {
        this.states.dialog.indicatorVisible = true;
    }

    _validMathExpression(leftValue, leftOpt, rightValue, rightOpt) {
        console.log(leftValue, leftOpt, rightValue, rightOpt);
        //10 <= X <= 10
        let flag = true;
        if (
            !isNaN(parseFloat(leftValue)) &&
            leftOpt === this.operators.gtEq.value &&
            !isNaN(parseFloat(rightValue)) &&
            rightOpt === this.operators.gtEq.value &&
            leftValue < rightValue
        ) {
            flag = false;
        } else if (
            !isNaN(parseFloat(leftValue)) &&
            leftOpt &&
            !isNaN(parseFloat(rightValue)) &&
            rightOpt &&
            leftValue <= rightValue
        ) {
            // 20 < X < 30
            flag = false;
        }

        if (!((!isNaN(parseFloat(leftValue)) && leftOpt) || (!leftValue && !leftOpt))) {
            flag = false;
        }

        if (!((!isNaN(parseFloat(rightValue)) && rightOpt) || (!rightValue && !rightOpt))) {
            flag = false;
        }

        return flag;
    }

    checkAlert(alert) {
        //0˚
        if (!alert.warningType) {
            this.interaction.showWarning('请选择预警级别!');
            return false;
        }
        //1˚ check warning level
        let checkWarningLevel = this.states.selectedRule.warningList
            .filter(cdt => cdt.id !== alert.id)
            .find(cdt => cdt.warningType === alert.warningType);
        if (checkWarningLevel !== undefined) {
            this.interaction.showWarning('已存在相应的预警级别，无需重复添加!');
            return false;
        }
        if (!this._validMathExpression(alert.minValue, alert.minValueSymbol, alert.maxValue, alert.maxValueSymbol)) {
            this.interaction.showWarning(`指标表达式输入不正确`);
            return false;
        }
        if (
            alert.preposition &&
            !this._validMathExpression(
                alert.minNetValue,
                alert.minNetValueSymbol,
                alert.maxNetValue,
                alert.maxNetValueSymbol,
            )
        ) {
            this.interaction.showWarning(`单位净值表达式输入不正确`);
            return false;
        }
        return true;
    }

    checkLimitationValid() {
        if (!this.states.selectedRule.limitationName) {
            this.interaction.showError('请填写交易限制名称!');
            return false;
        }

        let name = this.states.selectedRule.limitationName;
        let exist = this.rules.find(x => x.limitationName === name);
        if (exist !== undefined) {
            let id = exist.id || exist.timestamp;
            let this_id = this.states.selectedRule.id || this.states.selectedRule.timestamp;
            if (id !== this_id) {
                this.interaction.showError('交易限制名称重复，请从新填写!');
                return false;
            }
        }
        let primaryClassification = this.states.selectedRule.rules.map(x => x.method);
        let de_duplicate = this.helper.deDuplicate(primaryClassification);
        //to unique array
        if (de_duplicate.length < primaryClassification.length) {
            this.interaction.showError('资产类型类别不能重复!');
            return false;
        }

        let flag = true;
        this.states.selectedRule.rules.forEach(item => {
            if (item.defaultClassify) {
                return;
            }
            if (!item.cascade && item.primary.length <= 0) {
                flag = false;
            }
            if (item.cascade && item.secondary.length <= 0) {
                flag = false;
            }
        });
        if (this.states.selectedRule.rules.length <= 0) {
            flag = false;
        }

        //如果需要验证输入信息的话
        if (this.isAccount || this.isAccountTemplate) {
            let errorCount = this.accountProcedureControl.errorCount;
            let orderSpeed = this.accountProcedureControl.orderSpeed;
            let cancelRate = this.accountProcedureControl.cancelRate;
            if (orderSpeed && orderSpeed != 0 && (!Number.isInteger(orderSpeed) || orderSpeed <= 0)) {
                this.interaction.showError('下单速度必须是一个正整数!');
                return false;
            }
            if (errorCount && errorCount != 0 && (!Number.isInteger(errorCount) || errorCount <= 0)) {
                this.interaction.showError('错单数量必须是一个正整数!');
                return false;
            }
            if (
                cancelRate &&
                cancelRate != 0 &&
                (typeof cancelRate !== 'number' || cancelRate < 0 || cancelRate > 100)
            ) {
                this.interaction.showError('撤单率必须是0~100!');
                return false;
            }
            //当对于账号的情况，如果三者有设置流控的话，资产类型可以为空的
            if (
                ((typeof cancelRate === 'number' && cancelRate > 0 && cancelRate < 100) ||
                    (Number.isInteger(errorCount) && errorCount > 0) ||
                    (Number.isInteger(orderSpeed) && orderSpeed > 0)) &&
                !flag
            ) {
                return true;
            }
        }

        if (!flag) {
            this.interaction.showError('请必须选择一个资产类型!');
            return false;
        }

        return true;
    }

    checkExpression() {

        let expression = this.states.selectedRule.expression;
        if (typeof expression != 'string' || expression.trim().length == 0) {
            
            this.interaction.showError('请输入计算公式');
            return false;
        }

        let final_exp = expression.trim();
        let starter = '_';
        let ender = '__';
        let methods = [

            { name: 'math.ceil', code: null }, 
            { name: 'math.abs', code: null },
            { name: 'math.floor', code: null },
            { name: 'math.max', code: null },
            { name: 'math.min', code: null },
            { name: 'math.pow', code: null },
            { name: 'math.round', code: null },
            { name: 'math.sqrt', code: null },
        ];

        /**
         * 将函数名称，替换为，占位符
         */

        methods.forEach((mtd, mtd_idx) => {

            mtd.code = `${starter}${mtd_idx}${ender}`;
            final_exp = final_exp.replace(new RegExp(mtd.name, 'g'), mtd.code);
        });

        /**
         * 将指标占位符，替换为，随机数
         */

        for (let idx = 1; idx <= 26; idx++) {

            let letter = this.getIndex(idx);
            if (final_exp.indexOf(letter) >= 0 || final_exp.indexOf(letter.toLowerCase()) >= 0) {

                let num = this.helper.makeRandomNum(1, 99999, true);
                final_exp = final_exp.replace(new RegExp(letter, 'gi'), num);
            }
        }

        /**
         * 将占位符，替换为，替换为，函数名称
         */

        methods.forEach(mtd => {
            final_exp = final_exp.replace(new RegExp(mtd.code, 'g'), mtd.name);
        });

        /**
         * 将小写的math，替换为eval可识别的Math
         */

        final_exp = final_exp.replace(/math/gi, 'Math');

        try {
            let result = eval(final_exp);
            if (typeof result == 'number') {
                return true;
            }
            else if (result === Infinity) {
                this.interaction.showError('表达式计算结果，可能为无限大，建议再次尝试');
                return false;
            }
            else if (isNaN(result)) {
                this.interaction.showError('表达式计算结果，可能为非数值，建议再次尝试');
                return false;
            }
            else {
                this.interaction.showError('表达式计算结果，无法得到单一返回数值，请检查');
                return false;
            }
        }
        catch(ex) {

            this.interaction.showError('尝试转换表达式，发生异常，请检查');
            return false;
        }
    }

    checkRiskValid() {

        if (!this.states.selectedRule.configurationName) {
            this.interaction.showError('风控名称不允许为空');
            return false;
        }
        if (this.states.selectedRule.indexList.length <= 0) {
            this.interaction.showError('请至少添加一条组合指标!');
            return false;
        }
        if (!this.checkExpression()) {
            return false;
        }
        if (!this.states.selectedRule.warningList.every(x => this.checkAlert(x))) {
            return false;
        }

        if (this.states.selectedRule.riskType.length <= 0) {
            this.interaction.showError('请至少选择一项风控类型!');
            return false;
        }

        if (this.states.selectedRule.riskType === this.riskOptions.after.value) {
            if (isNaN(this.states.selectedRule.interval)) {
                this.interaction.showError('运行间隔时间必须为数字!');
                return false;
            } else if (this.states.selectedRule.interval !== parseInt(this.states.selectedRule.interval)) {
                this.interaction.showError('运行间隔时间不能是小数!');
                return false;
            }
        }
        return true;
    }

    setRiskRuleReadonly() {
        this.states.selectedRuleMode = 'display';
    }

    setRiskRuleEditable() {
        this.states.selectedRuleMode = 'edit';
    }

    async reqSaveRiskRule(template) {

        let loading = this.interaction.showLoading({
            text: '正在保存...',
        });

        let riskModel = this.constructRiskModel(this.states.selectedRule, template);
        // try {
        let resp = !riskModel.id
            ? await this.repoRisk.saveRiskRule(riskModel)
            : await this.repoRisk.updateRiskRule(riskModel);
        if (resp.errorCode === 0) {
            if (!this.states.selectedRule.id) {
                this.states.selectedRule.id = resp.data.id;
                let configurationId = resp.data.id;
                //将configurationId设置给indexList
                this.states.selectedRule.indexList.forEach(indicator => {
                    indicator.mapping = [];
                    indicator.assessList.forEach(assess => {
                        assess.types.forEach(cdt => {
                            indicator.mapping.push({
                                type: cdt,
                                configurationId: configurationId,
                            });
                        });
                    });
                });
                //将configurationId设置给warningList
                this.states.selectedRule.warningList.forEach(warning => {
                    warning.configurationId = configurationId;
                });
                this.interaction.showSuccess('保存成功!');
                this.rules.push(this.helper.deepClone(this.states.selectedRule));
            } else {
                let modify = this.rules.find(x => x.id === this.states.selectedRule.id) || {};
                this.helper.extend(modify, this.states.selectedRule);
            }

            this.setRiskRuleReadonly();
        } else {
            this.interaction.showError(`保存风控规则失败，详细信息:${resp.errorCode}/${resp.errorMsg}`);
        }
        // } catch (exp) {
        //     this.interaction.showError(`保存风控规则失败!`);
        // } finally {
        loading.close();
        // }
    }

    async removeRiskRule(rule) {
        //如果还是没有存到数据库里面的rule
        if (!rule.id) {
            this.rules.remove(x => x.timestamp === rule.timestamp);
            this.updateRiskRule();
            return;
        }

        let loading = this.interaction.showLoading({
            text: '正在进行...',
        });
        let configurationId = rule.id;
        try {
            let resp = await this.repoRisk.deleteRiskRule(configurationId);
            if (resp.errorCode === 0) {
                this.interaction.showSuccess('操作成功!');
                this.rules.remove(x => x.id === configurationId);
                this.states.selectedRule = this.createEmptyRule();
                this.updateRiskRule();
            } else {
                this.interaction.showError(`删除风控规则失败，详细信息:${resp.errorCode}/${resp.errorMsg}`);
            }
        } catch (exp) {
            this.interaction.showError(`删除风控规则失败!`);
        } finally {
            loading.close();
        }
    }

    async reqSaveLimitation(template) {
        let loading = this.interaction.showLoading({
            text: '正在保存中...',
        });

        let sendModel = this.constructLimitation(this.states.selectedRule, template);

        try {
            let resp = sendModel.id
                ? await this.repoRisk.updateLimitRule(sendModel)
                : await this.repoRisk.saveLimitRule(sendModel);
            if (resp.errorCode === 0) {
                this.interaction.showSuccess('保存交易限制规则成功!');
                if (!this.states.selectedRule.id) {
                    this.states.selectedRule = this.reshapeSingleLimitation(resp.data);
                    this.rules.push(this.helper.deepClone(this.states.selectedRule));
                } else {
                    let updateModel = this.rules.find(x => x.id === sendModel.id);
                    let constructed = this.helper.deepClone(this.reshapeSingleLimitation(sendModel));
                    updateModel = this.helper.extend(updateModel, constructed);
                    //还要再把数据更新回去
                    constructed.rules.forEach((item, index) => {
                        this.states.selectedRule.rules[index].types.clear();
                        this.states.selectedRule.rules[index].types.merge(item.types);
                    });
                }
                this.setRiskRuleReadonly();
            } else {
                this.interaction.showError('保存交易限制规则失败!');
            }
        } catch (e) {
            this.interaction.showError('保存交易限制规则失败!');
        } finally {
            loading.close();
        }
    }

    async loadRiskRuleContent() {
        if (this.context.action === this.Actions.normal.value || this.context.action === this.Actions.view.value) {
            this.reqRiskRules().then(() => {
                this.reqLimitationRules();
            });
        } else if (
            this.context.action === this.Actions.templateReadonly.value ||
            this.context.action === this.Actions.templateEdit.value
        ) {
            //否则去加载模板的内容
            this.reqRiskTemplate().then(() => {
                this.loadDefaultRule();
            });
        } else {
            this.setRiskRuleEditable();
        }
    }

    updateRiskRule() {
        let riskRules = this.rules;
        if (riskRules.length <= 0) {
            this.states.selectedRule = this.createEmptyRule();
            this.states.selectedRule.type = 'normal';
            this.setRiskRuleEditable();
        } else {
            this.loadDefaultRule();
        }
    }

    /**
     * 更新交易限制的信息
     */
    updateTransLimit() {
        let limits = this.rules.filter(cdt => cdt.type === 'limitation');
        if (limits.length <= 0) {
            this.states.selectedRule = this.createEmptyRule();
            this.states.selectedRule.type = 'limitation';
            this.setRiskRuleEditable();
            if (
                this.context.type === this.constant.IdentityTypes.account.code ||
                this.templateModel.data.identityType === this.constant.IdentityTypes.account.code
            ) {
                this.accountProcedureControl.cancelRate = null;
                this.accountProcedureControl.orderSpeed = null;
                this.accountProcedureControl.errorCount = null;
            }
        }
    }

    async removeLimit(this_limit) {
        if (!this_limit.id) {
            this.rules.remove(x => x.timestamp === this_limit.timestamp);
            this.updateTransLimit();
            return;
        }

        let loading = this.interaction.showLoading({
            text: '正在删除...',
        });
        try {
            let resp = await this.repoRisk.deleteLimitRules(this_limit.id);
            if (resp.errorCode === 0) {
                this.rules.remove(cdt => cdt.id === this_limit.id);
                this.states.selectedRule = this.createEmptyRule();
                this.loadDefaultRule();
                this.interaction.showSuccess('删除交易限制成功!');
                this.updateTransLimit();
            } else {
                this.interaction.showError('删除交易限制失败!');
            }
        } catch (e) {
            this.interaction.showError('删除交易限制失败!');
        } finally {
            loading.close();
        }
    }

    //构建模板的规则
    async createTemplateRule() {
        let insertRule = this.states.selectedRule;
        //如果已经有了templateId的话，就直接单个保存
        if (this.context.templateId) {
            await this.reqSaveRiskRule({
                id: this.context.templateId,
                identityType: this.context.identityType,
                name: this.context.name,
            });
        }
        if (!insertRule.id && !insertRule.timestamp) {
            insertRule.timestamp = new Date().getTime() + Math.random().toFixed(5) * 10000;
            this.rules.push(this.helper.deepClone(insertRule));
        } else {
            //相等但是不能同时为undefined
            let oldRule = this.rules.find(
                x =>
                    (x.id === insertRule.id && x.id !== undefined) ||
                    (x.timestamp === insertRule.timestamp && x.timestamp !== undefined),
            );
            this.helper.extend(oldRule, insertRule, { id: insertRule.id, timestamp: insertRule.timestamp });
        }
        this.setRiskRuleReadonly();
    }

    async saveCurrentEdit() {
        //如果用户在当前的上操作，但是注意，区别一下交易限制和风控内容
        if (this.states.selectedRule != null && this.states.selectedRuleMode === 'edit') {
            //如果是为特定的账号应用风控规则
            if (this.context.action === this.Actions.normal.value) {
                if (this.states.selectedRule.type === 'normal') {
                    let riskModel = this.constructRiskModel(this.states.selectedRule);
                    let resp = !this.states.selectedRule.id
                        ? await this.repoRisk.saveRiskRule(riskModel)
                        : await this.repoRisk.updateRiskRule(riskModel);
                    if (resp.errorCode === 0) {
                        if (this.states.selectedRule.id === undefined) {
                            this.states.selectedRule.id = resp.data.id;
                            this.rules.push(this.helper.deepClone(this.states.selectedRule));
                        } else {
                            let modify = this.rules.find(x => x.id === this.states.selectedRule.id) || {};
                            this.helper.extend(modify, this.states.selectedRule);
                        }
                    }
                } else {
                    let sendModel = this.constructLimitation(this.states.selectedRule);
                    let resp = sendModel.id
                        ? await this.repoRisk.updateLimitRule(sendModel)
                        : await this.repoRisk.saveLimitRule(sendModel);
                    if (resp.errorCode === 0) {
                        if (!this.states.selectedRule.id) {
                            this.states.selectedRule = this.reshapeSingleLimitation(resp.data);
                            this.rules.push(this.helper.deepClone(this.states.selectedRule));
                        } else {
                            let updateModel = this.rules.find(x => x.id === sendModel.id);
                            let constructed = this.helper.deepClone(this.reshapeSingleLimitation(sendModel));
                            updateModel = this.helper.extend(updateModel, constructed);
                            //还要再把数据更新回去
                            constructed.rules.forEach((item, index) => {
                                this.states.selectedRule.rules[index].types.clear();
                                this.states.selectedRule.rules[index].types.merge(item.types);
                            });
                        }
                    }
                }
                this.setRiskRuleReadonly();
            } else {
                if (this.states.selectedRule.type === 'normal') {
                    //否则仅仅就把编辑的内容放在那儿，等待统一保存
                    this.createTemplateRule();
                } else {
                    this.createTemplateLimitation();
                }
            }
        }
    }

    saveAsTemplate() {
        let template = this.vueApp.$refs.template;
        if (template) {
            template.validate(async valid => {
                if (valid) {
                    let loading = this.interaction.showLoading({
                        text: '正在保存模板，当前操作耗时较多，请耐心等待...',
                    });
                    try {
                        this.saveCurrentEdit();
                        let rulesOriginal = this.helper.deepClone(this.rules.filter(_ => _.type === 'normal'));
                        let ruleTemplates = rulesOriginal.map(x => {
                            let model = this.constructRiskModel(x, {
                                name: this.templateModel.data.name,
                                identityType: this.templateModel.data.type,
                                id: this.context.templateId,
                            });
                            //对于从账号那些进来的，全部是新增
                            if (this.context.action === this.Actions.normal.value) {
                                delete model.id;
                            }
                            //后端要求当id不存在的时候 全部给0
                            if (model.id === undefined) {
                                model.id = 0;
                            }
                            return model;
                        });
                        //把需要保存成风控的保存成风控
                        let limitationOriginal = this.helper.deepClone(
                            this.rules.filter(cdt => cdt.type === 'limitation'),
                        );
                        let limitationTemplates = limitationOriginal.map(x => {
                            let model = this.constructLimitation(x, {
                                name: this.templateModel.data.name,
                                identityType: this.templateModel.data.type,
                                id: this.context.templateId,
                            });
                            //对于从账号那些进来的，全部是新增
                            if (this.context.action === this.Actions.normal.value) {
                                delete model.id;
                            }
                            //后端要求当id不存在的时候 全部给0
                            if (model.id === undefined) {
                                model.id = 0;
                            }
                            return model;
                        });
                        let resp = await this.repoRisk.saveTemplate({
                            templateId: this.context.templateId,
                            riskConfigurationBeans: ruleTemplates,
                            restrictionBeans: limitationTemplates,
                        });
                        if (resp.errorCode === 0) {
                            let saveName = this.templateModel.data.name;
                            let templateId = null;
                            resp.data.restrictionBeans.forEach(x => {
                                if (x.templateId && !templateId) {
                                    templateId = x.templateId;
                                }
                            });
                            if (!templateId) {
                                resp.data.riskConfigurationBeans.forEach(x => {
                                    if (x.templateId && !templateId) {
                                        templateId = x.templateId;
                                    }
                                });
                            }
                            //自己创建的，肯定是自己的orgId
                            this.notifyTemplateCreated({
                                templateName: saveName,
                                templateId: templateId,
                                identityType: this.templateModel.data.type,
                                orgId: this.userInfo.orgId,
                            });
                            //把创建模式变成编辑模式
                            if (this.context.action === this.Actions.templateCreate.value) {
                                this.templateModel.data.name = saveName;
                                this.context.name = saveName;
                                this.context.templateId = templateId || null;
                            }
                        } else {
                            this.interaction.showError('模板存储失败!');
                        }
                    } catch (e) {
                        this.interaction.showError('模板存储失败!');
                    } finally {
                        loading.close();
                    }
                } else {
                    console.log('valid error');
                }
            });
        }
    }

    notifyTemplateCreated(templateMsg) {
        if (this.context.action === this.Actions.templateCreate.value) {
            this.templateModel.data.name = '';
            if (this.vueApp.$refs.template) {
                this.vueApp.$refs.template.resetFields();
            }
        }
        this.templateModel.disabled = false;
        this.states.dialog.templateVisible = false;
        if (this.context.action === this.Actions.normal.value) {
            this.interaction.showSuccess('模板存储成功!');
        } else {
            let msg = {
                templateName: templateMsg.templateName,
                templateId: templateMsg.templateId,
                identityType: templateMsg.identityType,
                orgId: templateMsg.orgId,
            };
            this.trigger('save', msg);
        }
    }

    saveLimitation() {
        if (!this.checkLimitationValid()) {
            return false;
        }
        if (this.context.action === this.Actions.normal.value) {
            this.reqSaveLimitation();
        } else {
            this.createTemplateLimitation();
        }
    }

    constructLimitation(origin, template) {
        //过滤掉没有选择的二阶的行业类别
        let model = this.helper.deepClone(origin);
        let details = [];
        let meta = {
            errorCount: this.accountProcedureControl.errorCount || null,
            //下单速度
            orderSpeed: this.accountProcedureControl.orderSpeed || null,
            //撤单率
            cancelRate: this.accountProcedureControl.cancelRate || null,
        };
        model.rules.forEach((x, index) => {
            //没有一二级
            if (x.defaultClassify) {
                details.push({
                    restrictionId: model.id,
                    rangeId: index,
                    classificationInfoId: x.types[0],
                    isExcept: x.flag,
                });
                return;
            }
            //只有一级
            if (!x.cascade) {
                x.primary.forEach(item => {
                    x.types.push(item);
                    details.push({
                        restrictionId: model.id,
                        rangeId: index,
                        classificationInfoId: item,
                        isExcept: x.flag,
                    });
                });
            } else {
                //二级
                x.secondary.forEach(item => {
                    x.types.push(item);
                    details.push({
                        restrictionId: model.id,
                        rangeId: index,
                        classificationInfoId: item,
                        isExcept: x.flag,
                    });
                });
            }
        });
        let constructed_model = Object.assign(
            {
                id: model.id,
                restrictionName: model.limitationName,
                identityName: this.context.name,
                identityType: this.context.type,
                description: null,
                isActive: true,
                restrictionDetails: details,
            },
            meta,
        );

        if (template === undefined) {
            constructed_model.identity = this.context.identity;
        } else {
            constructed_model.templateId = template.id;
            constructed_model.identityType = template.identityType;
            constructed_model.templateName = template.name;
        }
        return constructed_model;
    }

    /**
     * 对于从账号 产品 策略的入口，会有type （策略、产品、 账号的区分），identity 特征id， name 需要显示的title
     * @param params
     */
    async setContext(params) {

        let action = params.action || this.Actions.normal.value;
        let matched_action = this.helper.dict2Array(this.Actions).first(x => x.key === action);
        this.context.action = matched_action.value;
        this.context.type = params.type || '';
        this.templateModel.data.identityType = params.identityType;
        this.templateModel.data.type = params.identityType;
        if (this.isAccount) {
            this.templateModel.data.identityType = this.systemEnum.identityType.account.code;
            this.templateModel.data.type = this.systemEnum.identityType.account.code;
        }
        this.context.identity = params.identity || '';
        this.context.name = params.name || '';
        let window_title = '';
        if (this.context.action !== this.Actions.normal.value) {
            if (this.context.action === this.Actions.templateCreate.value) {
                window_title = '创建风控模板';
            } else {
                window_title = '风控模板 - ' + params.name || '';
            }
            this.templateModel.data.name = this.context.name;
            this.context.templateId = this.context.identity;
        } else {
            let matched_identity = this.helper.dict2Array(this.systemEnum.identityType).first(x => {
                return x.code === params.type;
            });
            let identity_name = matched_identity.mean;
            window_title = (params.name && identity_name ? params.name + ' - ' + identity_name : '') + '风控设置';
        }
        await this.reqKindList();
        await this.reqIndustries();
        this.loadRiskRuleContent();
        // this.setWindowTitle(window_title);
    }

    want2SaveTemplate() {
        if (this.vueApp.rules.length <= 0) {
            this.interaction.showError('请至少添加一条风控规则再操作!!!');
            return false;
        }
        if (this.templateModel.data.identityType) {
            this.templateModel.disabled = true;
            this.templateModel.data.type = this.templateModel.data.identityType;
        }
        this.states.dialog.templateVisible = !this.states.dialog.templateVisible;
    }

    loadDefaultRule() {

        if (this.rules.length > 0) {
            let first = this.rules[0];
            this.setSelectedRiskRule(first);
            this.vueApp.$refs.riskRule.setCurrentRow(first);
        } else {
            this.setRiskRuleEditable();
        }
        if (this.rules.filter(x => x.type === 'limitation').length <= 0) {
            this.accountProcedureControl = this.helper.extend(
                this.accountProcedureControl,
                this.createEmptyPrcControl(),
            );
        }
    }

    dispose() {
        this.vueApp = null;
        while (this.$container.hasChildNodes()) {
            this.$container.removeChild(this.$container.firstChild);
        }
    }

    listen2Events() {
        this.renderProcess.on('set-context-data', (event, args) => {
            this.setContext(args);
        });
    }

    build($container) {

        this.$container = $container;
        this.listen2Events();
        this.createApp();
    }
}

module.exports = RiskDialog;
