.body-layout,
.layout-content {
    height: 100%;
}

.risk-dialog-root {

    height: 100%;
    background-color: #38475a;

    .el-table .el-table__header-wrapper {
        background-color: #38475a;
    }

    .el-table .el-table__body-wrapper {
        background-color: #38475a;
    }

    .el-table .el-table__footer-wrapper {
        background-color: #38475a;
    }

    .el-popover {
        background-color: #38475a;
        border: 1px solid #0c84da;
    }

    .el-input-number__decrease,
    .el-input-number__increase {
        color: white;
    }

    .el-select__tags-text {
        color: white;
    }

    .el-tabs__header {
        margin: 0;
    }

    .el-checkbox__label {
        color: white;
    }

    .el-table .el-table__body tr td,
    .el-table .el-table__footer tr td {
        box-shadow: 1px 1px 0px #475e7a inset;
    }

    .risk-section-main {

        background-color: inherit;
        height: 100%;
        box-sizing: border-box;
        width: 100%;
        overflow: hidden;
    }

    .el-tabs__nav-wrap::after {
        background-color: #475e7a;
        height: 1px;
    }

    .risk-section-body-left {

        height: 100%;
        float: left;
        border-right: 1px solid #475e7a;
        width: 25%;
        box-sizing: border-box;
        overflow: auto;
    }

    .account-template-wrapper {
        margin-bottom: 10px;
    }

    .btn-add-rule {
        width: calc(100% - 30px);
        margin: 15px 15px 0px 15px;
    }

    .procedure-wrapper {
        margin-top: 10px;
        padding: 10px;
    }

    .procedure-text {
        font-size: 13px;
        display: inline-block;
        margin-bottom: 10px;
        color: white;
    }

    .risk-type-disabled {
        color: #f5f5f5;
    }

    .risk-section-body-right {

        float: left;
        box-sizing: border-box;
        width: 75%;
        height: 100%;
        padding: 10px;
        overflow: auto;

        .el-form-item {
            margin-right: 0px;
        }

        .el-table .cell {
            line-height: 34px!important;
        }
    }

    .el-table .el-table__header th.is-leaf {
        box-shadow: 1px 0px 0px #475e7a inset;
    }

    .el-table .el-table__header th:first-child {
        box-shadow: 0px 0px 0px #475e7a inset;
    }

    .el-table .el-table__body td:first-child,
    .el-table .el-table__footer td:first-child {
        box-shadow: 0px 1px 0px #475e7a inset;
    }

    .risk-list {
        .el-table__header th.is-leaf,
        .el-table__body tr td,
        .el-table__footer tr td {
            box-shadow: 0px 1px 0px #475e7a inset !important;
        }

        .el-table__header th:first-child {
            box-shadow: 0px 1px 0px #475e7a inset !important;
        }
    }

    .risk-combo.el-table .el-table__header th {
        box-shadow: 0px 1px 1px #475e7a inset;
    }

    .btn-add-indicator {
        margin-bottom: 10px;
    }

    .el-table[indicator-table] {
        border: 1px solid #475e7a;
        border-bottom: none;
    }

    .el-table .el-table__empty-block {
        background-color: #38475a;
        border-left: none;
        border-top: 1px solid #475e7a;
    }

    .el-table .el-table__body tr {
        background-color: #38475a;
    }

    .el-table__body-wrapper {
        border: 1px solid #475e7a;
        border-top: none;
        border-left: none;
    }

    .el-table .el-table__header th,
    .el-table .el-table__body tr.el-table__row--striped td {
        background-color: #38475a;
    }

    .el-table[warning-table] .el-table__body-wrapper {
        border-bottom: none;
    }

    .el-table[transaction-table] .el-table__header-wrapper {
        border-top: 1px solid #475e7a;
    }

    .risk-section-footer {
        position: absolute;
        left: 0;
        bottom: 0;
        border-top: 1px solid #475e7a;
        background-color: inherit;
        height: 44px;
        line-height: 44px;
        z-index: -1;
        width: 100%;
    }

    .btn-hang,
    .transaction-hang {
        position: absolute;
        right: 20px;
        top: 7px;
        z-index: 1000;
    }

    .risk-name {
        width: 320px;
    }

    .el-input-number__decrease,
    .el-input-number__increase {
        background-image: linear-gradient(-180deg, #3e8aff 0%, #135fd5 100%);
        border: none;
        height: 24px;
    }

    .el-input-number__decrease {
        margin-left: -3px;
    }

    .el-input-number__increase {
        margin-right: -3px;
    }

    .risk-dialog > .el-dialog {
        width: 754px;
    }

    .central-dialog,
    .template-dialog,
    .select-dialog {
        margin-top: 15vh !important;
    }

    .select-dialog > .el-dialog {
        width: 520px;
    }

    .central-dialog > .el-dialog {
        width: 600px;
    }

    .central-dialog .el-dialog__body {
        padding: 10px 0;
    }

    .risk-section {
        margin: 5px 0;
        box-sizing: border-box;
        width: 100%;
        border: 1px solid #475e7a;
        border-radius: 5px;
        overflow: hidden;
    }

    .el-form-item__label {
        font-size: 12px !important;
    }

    .risk-dialog .el-form-item__content {
        margin-left: 0 !important;
    }

    .risk-dialog .el-date-editor {
        width: 165px !important;
    }

    .risk-dialog .el-input-group__prepend,
    .risk-dialog .el-input-group__append {
        
        border: none;
        font-size: 17px;
        padding: 0 5px;
    }

    .el-form-item[s-w-full],
    .el-form-item[s-w-full] > .el-form-item__content {
        width: 100% !important;
    }

    .el-form-item[s-w-inline] {
        width: 100% !important;
    }

    .risk-section-wrapper {
        margin: 5px;
    }

    .risk-warning .el-form-item__label {
        float: left;
        text-align: left;
    }

    .warning-critical {
        width: calc(60% - 5px);
        margin-right: 5px;
        float: left;
    }

    .warning-relationship {
        width: calc(40% - 5px);
        margin-left: 5px;
        float: left;
    }

    .warning-label {
        text-align: center;
    }

    .local-limitation-wrapper,
    .equation-wrapper {
        padding: 15px 10px 0px;
        box-sizing: border-box;
        margin-bottom: 5px;
        float: left;
        width: 100%;
        height: auto;
    }

    .el-form-item[s-w-inline] .el-form-item__content {
        width: calc(100% - 80px);
    }

    .default-classify {

        display: inline-block;
        margin: 5px 0;
        padding: 2px 10px;
        border: 1px solid #475e7a;
        border-radius: 3px;
    }

    .warning-index-label {

        margin: 0px 5px;
        font-size: 14px;
        display: inline-block;
        line-height: 30px;
        color: white;
    }

    .net-value-label {

        margin: 0px 5px;
        font-size: 12px;
        display: inline-block;
        line-height: 30px;
    }

    .table-check-label {
        margin-right: 5px;
    }

    .table-check-label > .el-checkbox__label {
        font-size: 12px;
    }

    .el-tree-node__content {
        background-color: #232b37;
        color: white;
    }

    .el-tree-node__content:hover,
    .el-tree-node__content:active {
        background-color: #001a34;
        color: white;
    }

    .el-tree-node.is-current > .el-tree-node__content {
        background-color: #001a34;
        color: white;
    }

    .el-tree-node:focus > .el-tree-node__content {
        background-color: #001a34;
        color: white;
    }

    .el-select .el-tag {
        background-color: #38475a;
    }

    .central-dialog .el-dialog__body {
        float: left;
        width: 100%;
        padding: 0px;
    }

    .central-dialog .el-dialog__footer {
        border-top: 1px solid #475e7a;
        clear: both;
    }

    .el-form-item[s-w-region] .el-select {
        width: 350px;
    }

    .el-select-group__title {
        color: #909399;
    }

    .popover-inner-wrapper {
        padding-top: 15px;
        position: relative;
    }

    .popover-close {
        position: absolute;
        right: -5px;
        top: -5px;
        font-size: 14px;
        cursor: pointer;
    }

    .risk-dialog {

        .el-dialog__body {
            padding: 5px;
        }
    }

    .risk-template-dialog {

        .risk-template-form {
            
            background-color: #2c3544;
            padding: 0 5px;
            padding-bottom: 1px;
            .el-form-item {
                background-color: #2c3544;
            }
        }
    }

    div.risk-el-popover {
        background-color: #232b37;
        border: 1px solid #232b37;
    }
}

.combo-dialog {

    top: 1px;
    right: 1px;

    .indicator-left {

        box-sizing: border-box;
        max-height: 200px;
        padding: 10px 10px;
        overflow: auto;
        border-bottom: 1px solid #666;
    }

    .indicator-right {

        box-sizing: border-box;
        padding: 10px 10px;
        border-left: 1px solid #475e7a;
    }

    .el-table .cell {
        line-height: 34px !important;
    }
}

.el-form-item {
    margin-bottom: 0;
}

.el-tree-node:focus > .el-tree-node__content {
    background-color: #222;
}

.el-tree-node__content:hover {
    background-color: #222;
}