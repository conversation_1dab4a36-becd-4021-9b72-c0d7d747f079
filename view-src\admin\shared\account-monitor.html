<div class="xsplitter">

    <div class="part-upper" style="height: 300px;">

        <div class="toolbar">

            <el-input v-model="searching.keywords" class="input-searching" placeholder="输入关键词过滤" @change="filterRecords" clearable>
                <i slot="prefix" class="el-input__icon el-icon-search"></i>
            </el-input>
            
            <span>&nbsp;&nbsp;</span>

            <el-select v-model="searching.assetType" @change="filterRecords" style="width: 100px;" clearable>
                <el-option v-for="(item, item_idx) in assetTypes" :key="item_idx" :label="item.mean" :value="item.code"></el-option>
            </el-select>

            <span>&nbsp;&nbsp;</span>

            <el-select v-model="searching.checkType" @change="filterRecords" style="width: 150px;" clearable>
                <el-option v-for="(item, item_idx) in checkTypes" :key="item_idx" :label="item.mean" :value="item.code"></el-option>
            </el-select>

            <span class="pagination-wrapper" style="display: block; float: right;">

                <el-pagination :page-sizes="paging.pageSizes" :page-size.sync="paging.pageSize" :total="paging.total"
                    :current-page.sync="paging.page" :layout="paging.layout" @size-change="handlePageSizeChange"
                    @current-change="handlePageChange"></el-pagination>

            </span>

        </div>

        <table>
            <tr>
                <th label="账号名称" prop="accountName" watch="accountName" min-width="120" searchable sortable overflowt></th>
                <th label="ID" prop="accountId" min-width="100" searchable sortable overflowt></th>
                <th label="账号类型" prop="assetType" min-width="70" formatter="formatAssetType" sortable overflowt></th>
                <th class="cell-row-actions" label="操作" fixed-width="80" align="center" fixed="right" exportable="false" formatter="formatActions"></th>
            </tr>
        </table>

    </div>

    <div class="splitter-bar"></div>

    <div class="part-lower">

        <div class="account-summary">
            <div class="summary-tabs">
                <!-- multiple tabs 123 -->
            </div>
            <div class="summary-content">
                <!-- multiple tabs content 123 -->
            </div>
        </div>

    </div>

    <div class="dialog-error-list">

        <template>

            <el-dialog width="1100px" :title="dialog.title" :visible="dialog.visible" :close-on-click-modal="false" :show-close="false">

                <div class="table-box"></div>

                <div slot="footer">
                    <el-button @click="closeDialog" type="primary" size="small">关闭</el-button>
                </div>

            </el-dialog>

        </template>
    </div>

</div>