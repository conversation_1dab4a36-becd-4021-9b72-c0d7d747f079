const helper = require('../../../libs/helper').helper;
const IView = require('../../../component/iview').IView;
const SmartTable = require('../../../libs/table/smart-table').SmartTable;
const TabList = require('../../../component/tab-list').TabList;
const Tab = require('../../../component/tab').Tab;
const Splitter = require('../../../component/splitter').Splitter;
const ColumnCommonFunc = require('../../../libs/table/column-common-func').ColumnCommonFunc;
const repoAccountAround = require('../../../repository/account-around').repoAccountAround;

class CheckTypeItem {

    /**
     * @param {String} name 
     * @param {String} code 
     * @param {String} mean 
     */
    constructor(name, code, mean) {

        this.timeName = 'time_of_' + name;
        this.flagName = 'flag_of_' + name;
        this.code = code;
        this.mean = mean;
        this.isCover = mean.indexOf('覆盖') >= 0;
        this.isCheck = mean.indexOf('检测') >= 0;
    }
}

const CheckTypes = [

    new CheckTypeItem('wtfg', 0, '委托覆盖'),
    new CheckTypeItem('cjfg', 1, '成交覆盖'),
    new CheckTypeItem('ccfg', 2, '持仓覆盖'),
    new CheckTypeItem('zjfg', 3, '资金覆盖'),
    new CheckTypeItem('wtjc', 4, '委托检测'),
    new CheckTypeItem('cjjc', 5, '成交检测'),
    new CheckTypeItem('zjjc', 6, '资金检测'),
];

const CheckTypesMap = {};

/**
 * @param {*} time_name 
 * @returns {CheckTypeItem}
 */
function Seek(time_name) {
    return CheckTypesMap[time_name];
}

const PlaceHolder = '--';

/**
 * 列表数据结构（账号监控记录结构）
 */
class AccountResult {

    constructor(struc) {

        this.accountId = struc.accountId;
        this.accountName = struc.accountName;
        this.assetType = struc.assetType;

        let thisObj = this;
        let checkResults = struc.checks || [];

        CheckTypes.forEach(ct => {

            let matched = checkResults.find(cr => cr.checkType == ct.code);
            thisObj[ct.timeName] = matched ? matched.checkTime : undefined;
            thisObj[ct.flagName] = matched ? !!matched.checkFlag : undefined;
        });
    }
}

class Controller extends IView {

    constructor(view_name) {

        super(view_name, false, '账号监控');
        this.userModes = { super: 'super', org: 'org', broker: 'broker' };
        this.checkTypes = CheckTypes;
    }

    createToolbarApp() {

        this.scopedTypes = {

            all: { code: 'all', mean: '全部' },
            ok: { code: 'ok', mean: '各项均正常' },
            cover: { code: 'cover', mean: '错误 = 覆盖类' },
            check: { code: 'check', mean: '错误 = 检测类' },
            anyError: { code: 'any-error', mean: '错误 = 任意项' },
        };

        var assetsType = this.systemEnum.assetsType;
        this.assetTypes = {

            all: { code: 'all', mean: '全部账号' },
            stock: assetsType.stock,
            future: assetsType.future,
            option: assetsType.option,
        };

        this.searching = {

            keywords: '',
            assetType: this.assetTypes.all.code,
            checkType: this.scopedTypes.all.code,
        };

        var pagination = this.systemSetting.tablePagination;
        this.paging = {

            pageSizes: pagination.pageSizes,
            pageSize: pagination.pageSize,
            layout: pagination.layout,
            total: 0,
            page: 1,
        };

        var checkTypeOptions = [];
        checkTypeOptions.merge(helper.dict2Array(this.scopedTypes));
        /** 此处使用time name赋值给code字段，便于筛选时对接查找索引 */
        checkTypeOptions.merge(CheckTypes.map(item => ({ code: item.timeName, mean: `仅错误 = ${item.mean}` })));

        this.toolbar = new Vue({

            el: this.$container.querySelector('.part-upper > .toolbar'),
            data: {

                checkTypes: checkTypeOptions,
                assetTypes: helper.dict2Array(this.assetTypes),
                searching: this.searching,
                paging: this.paging,
            },

            methods: this.helper.fakeVueInsMethod(this, [this.filterRecords, this.exportSome, this.refresh, this.config, this.handlePageSizeChange, this.handlePageChange]),
        });
    }

    /**
     * @param {AccountResult} record 
     */
    identifyRecord(record) {
        return record.accountId;
    }

    /**
     * 建立检测类型索引，提高后续访问检测类型数据的效率
     */
    setupCheckTypesMap() {
        CheckTypes.forEach(item => { CheckTypesMap[item.timeName] = item; });
    }

    setupTable() {

        var $table = this.$container.querySelector('.part-upper > table');
        var $before = $table.querySelector('th.cell-row-actions');
        var $header = $before.parentNode;

        CheckTypes.forEach(item => {

            let $th = document.createElement('th');
            $th.setAttribute('label', item.mean);
            $th.setAttribute('prop', item.timeName);
            $th.setAttribute('watch', `${item.timeName},${item.flagName}`);
            $th.setAttribute('formatter', 'formatCheckResult');
            $th.setAttribute('export-formatter', 'formatCheckResultText');
            $th.setAttribute('min-width', '80');
            $th.setAttribute('sortable', true);
            $th.setAttribute('overflowt', true);
            $header.insertBefore($th, $before);
        });

        this.tableObj = new SmartTable($table, this.identifyRecord, this, {

            tableName: 'smt-aam',
            displayName: this.title,
            enableConfigToolkit: true,
            recordsFiltered: (filtered_count) => { this.paging.total = filtered_count; },
            rowSelected: (account) => { this.handleAccountChange(account); },
        });

        this.tableObj.setPageSize(this.paging.pageSize);
        this.tableObj.setMaxHeight(200);
    }

    setupDynamicPart() {

        let $splitter = this.$container.querySelector('.splitter-bar');
        let $summary = this.$container.querySelector('.account-summary');

        if (this.isSuperMode) {

            $splitter.remove();
            $summary.remove();
            this.lisen2WinSizeChange(this.adjustTableHeight.bind(this));
        }
        else {

            this.buildSplitter($splitter);
            this.setupSummary($summary);
        }
    }

    /**
     * @param {AccountResult} record 
     * @param {String} time_val 
     * @param {String} time_name 
     */
    formatCheckResult(record, time_val, time_name) {

        let matched = Seek(time_name);
        let isOk = record[matched.flagName];

        if (isOk === undefined) {
            return '<span class="s-color-red">0</span>';
        }
        else if (isOk === true) {
            return `<span>${time_val}</span>`;
        }
        else {
            return `<a title="点击查看错误详情" class="s-color-red s-cp s-underline" event.onclick="showDetailError">${time_val}</a>`;
        }
    }

    /**
     * @param {AccountResult} record 
     * @param {String} time_val 
     * @param {String} time_name 
     */
    formatCheckResultText(record, time_val, time_name) {
        return time_val || PlaceHolder;
    }

    formatActions() {

        return `<span class="smart-table-action">
                    <a class="lock-button icon-button">...</a>
                    <ul>
                        <li><a class="icon-button iconfont icon-fugai" event.onclick="doCover">覆盖</a></li>
                        <li><a class="icon-button iconfont icon-qingsuan" event.onclick="doTradeCheck">成交检测</a></li>
                        <li><a class="icon-button iconfont icon-qingsuan" event.onclick="doOrderCheck">委托检测</a></li>
                        <li><a class="icon-button iconfont icon-qingsuan" event.onclick="doBalanceCheck">权益检测</a></li>
                    </ul>
                </span>`;
    }

    /**
     * @param {AccountResult} record 
     * @param {HTMLElement} $link 
     * @param {HTMLTableCellElement} $cell 
     * @param {String} time_val 
     * @param {String} time_name 
     */
    showDetailError(record, $link, $cell, time_val, time_name) {

        let matched = Seek(time_name);
        this.showDetailDialog(record, matched.code, matched.mean);
    }

    /**
     * 
     * @param {AccountResult} record 
     * @param {Number} typeCode
     * @param {String} typeName
     */
    showDetailDialog(record, typeCode, typeName) {

        var thisObj = this;

        function showDialog() {
            
            thisObj.dialogSetting.visible = true;
            thisObj.dialogSetting.title = `${record.accountName} / 错误详情`;
            thisObj.detailApp.$nextTick(() => {

                thisObj.setAsTableBox(thisObj.detailApp.$el.querySelector('.table-box'));
                thisObj.$tableBox.innerHTML = '';
                thisObj.request2ShowDetail(record, typeCode, typeName);
            });
        }

        if (this.detailApp) {

            showDialog();
            return;
        }

        this.dialogSetting = { visible: false, title: '错误详情' };
        this.detailApp = new Vue({

            el: this.$container.querySelector('.dialog-error-list'),
            data: {
                dialog: this.dialogSetting,
            },
            methods: this.helper.fakeVueInsMethod(this, [this.closeDialog]),
        });

        this.detailApp.$nextTick(() => { showDialog(); });
    }

    /**
     * @param {HTMLElement} $tableBox 
     */
    setAsTableBox($tableBox) {
        this.$tableBox = $tableBox;
    }

    closeDialog() {
        this.dialogSetting.visible = false;
    }

    /**
     * @param {AccountResult} record
     */
    doCover(record) {
        this.executeGeneralTask(record, '覆盖', repoAccountAround.overlap, [record.accountId, true]);
    }

    /**
     * @param {AccountResult} record
     */
    doTradeCheck(record) {
        this.executeGeneralTask(record, '成交检测', repoAccountAround.tradeCheck, [record.accountId]);
    }

    /**
     * @param {AccountResult} record
     */
    doOrderCheck(record) {
        this.executeGeneralTask(record, '委托检测', repoAccountAround.orderCheck, [record.accountId]);
    }

    /**
     * @param {AccountResult} record
     */
    doBalanceCheck(record) {
        this.executeGeneralTask(record, '权益检测', repoAccountAround.balanceCheck, [record.accountId]);
    }

    /**
     * execute a general task
     * @param {AccountResult} record
     * @param {String} task_name
     * @param {Function} task
     * @param {Array} args
     * @param {Function} callback
     */
    async executeGeneralTask(record, task_name, task, args, callback) {

        this.interaction.showConfirm({

            title: '操作确认',
            message: `是否确认，对账号 [${record.accountName}] 执行 [${task_name}] 操作`,
            confirmed: async () => {

                let resp = await task.call(repoAccountAround, ...args);

                if (typeof callback == 'function') {
                    callback(resp);
                } 
                else {

                    if (resp.errorCode === 0) {
                        this.interaction.showSuccess(`[${task_name}] 操作已执行`);
                    } 
                    else {
                        this.interaction.showError(`操作 [${task_name}] 发生异常：${resp.errorCode}/${resp.errorMsg}`);
                    }
                }
            },
        });
    }

    adjustTableHeight(win_width, win_height, is_maximized) {
        this.tableObj.setMaxHeight(win_height - (is_maximized ? 180 : 165));
    }

    _handleActivation() {
        this.tableObj.fitColumnWidth();
    }

    /**
     * @param {HTMLElement} $splitter
     */
    buildSplitter($splitter) {

        this.splitter = new Splitter('admin-account-monitor', $splitter, this.handleSpliting.bind(this), { previousMinHeight: 155, nextMinHeight: 200 });
        setTimeout(() => { this.splitter.recover(); }, 1000);
    }

    /**
     * @param {Number} previous_height
     * @param {Number} next_height
     */
    handleSpliting(previous_height, next_height) {

        this.tableObj.setMaxHeight(previous_height - 75);
        this.summary.tabs.forEach((tab) => { tab.viewEngine.setHeight(next_height); });
    }

    /**
     * @param {HTMLElement} $summary
     */
    setupSummary($summary) {

        var tablist = new TabList({

            allowCloseTab: false,
            embeded: true,
            $navi: $summary.querySelector('.summary-tabs'),
            $content: $summary.querySelector('.summary-content'),
            tabCreated: this.handleTabCreated.bind(this),
        });

        tablist.openTab(true, '@shared/order-list', '今日订单');
        tablist.openTab(true, '@shared/position-list', '今日持仓');
        tablist.openTab(true, '@shared/exchange-list', '今日成交');
        tablist.openTab(true, '@shared/history-order-list', '历史订单');
        tablist.openTab(true, '@shared/history-position-list', '历史持仓');
        tablist.openTab(true, '@shared/history-exchange-list', '历史成交');
        tablist.openTab(true, '@shared/history-equity-list', '历史权益');
        tablist.openTab(true, '@shared/history-cash-inout', '出入金');

        this.summary = tablist;
    }

    /**
     * @param {Tab} tab
     */
    handleTabCreated(tab) {

        if (this.contextInfo) {
            this.summary.fireEventOnTab(tab, this.systemEvent.viewContextChange, this.constructContextParms(this.contextInfo));
        }
    }

    /**
     * @param {AccountResult} account
     */
    handleAccountChange(account) {

        if (this.isSuperMode) {
            return;
        }

        if (this.contextInfo && this.contextInfo.accountId === account.accountId) {
            return;
        }

        this.contextInfo = account;
        if (this.summary) {
            this.summary.fireEventOnAllTabs(this.systemEvent.viewContextChange, this.constructContextParms(account));
        }
    }

    /**
     * @param {AccountResult} account
     */
    constructContextParms(account) {

        return {

            accountId: account.accountId,
            accounts: [
                {
                    accountId: account.accountId,
                    accountName: account.accountName,
                    assetType: account.assetType,
                    isCredit: !!account.credit,
                },
            ],
        };
    }

    /**
     * 请求账号检测状态信息
     * @param {AccountResult} record 
     * @param {Number} typeCode
     * @param {String} typeName
     */
    async request2ShowDetail(record, typeCode, typeName) {

        if (this._isRequestingDetail) {
            return;
        }

        this._isRequestingDetail = true;
        let scopeInfo = `账号( ${record.accountName} )的检测项( ${typeName} )`;
        let $loading = this.interaction.showLoading({ text: `获取${scopeInfo}，错误详情...` });
        let resp = await repoAccountAround.queryDetail(record.accountId, typeCode);

        $loading.close();
        this._isRequestingDetail = false;

        if (resp.errorCode != 0) {

            this.interaction.showError(`获取${scopeInfo}失败，${resp.errorCode}/${resp.errorMsg}`);
            return;        
        }

        let resultStr = (resp.data || {}).result;
        if (helper.isNone(resultStr) || typeof resultStr != 'string') {

            this.closeDialog();
            this.interaction.showMessage(`${scopeInfo}<br/>无错误详情`);
            return;
        }

        let errors;
        try {
            errors = JSON.parse(resultStr);
        }
        catch(ex) {
            errors = resultStr;
        }

        if (typeof errors == 'string') {
            
            this.closeDialog();
            this.interaction.showAlert({ message: `${scopeInfo}
                                                <br/>错误详情如下：
                                                <br/>
                                                <div class="content-box s-scroll-bar">${errors}</div>`, customClass: 'message-detail-error' });
            return;
        }

        if (!(errors instanceof Array) || errors.length == 0) {
            
            this.closeDialog();
            this.interaction.showAlert({ message: `${scopeInfo}
                                                <br/>错误详情如下：
                                                <br/>
                                                <div class="content-box s-scroll-bar">${JSON.stringify(errors)}</div>`, customClass: 'message-detail-error' });
            return;
        }

        let titles = errors[0];

        if (!(titles instanceof Array) || titles.length <= 1) {

            this.closeDialog();
            this.interaction.showAlert(`${scopeInfo}<br/>无明确错误信息`);
            return;
        }

        let cols = titles.map((headerText, index) => {

            let propName = index;
            let width = 50 + typeof headerText == 'string' ? headerText.length * 20 : 50;
            return `<th label="${headerText}" prop="${propName}" min-width="${width}" formatter="formatContent" sortable overflowt></th>`;
        });

        let tableDef = `<table><tr>${cols}</tr></table>`;
        let $tableWrapper = document.createElement('div');
        this.$tableBox.appendChild($tableWrapper);
        $tableWrapper.innerHTML = tableDef;

        function identifyErrorRecord(error) {
            return error.errorItemId;
        }

        let errorRecords = errors.slice(1);
        errorRecords.forEach((item, item_idx) => { item.errorItemId = item_idx; });
        this.tableDetail = new SmartTable($tableWrapper, identifyErrorRecord, this, {

            tableName: 'smt-aamd',
            displayName: 'account-monitor-error',
        });

        this.tableDetail.setPageSize(*********);
        this.tableDetail.setMaxHeight(500);
        this.tableDetail.refill(errorRecords);
    }

    formatContent(record, fieldValue, fieldName) {

        if (typeof fieldValue == 'number') {

            return Number.isInteger(fieldValue) ? ColumnCommonFunc.thousands(record, fieldValue, fieldName)
                                                : ColumnCommonFunc.thousandsDecimal(record, fieldValue, fieldName);
        }
        else {
            return fieldValue;
        }
    }

    /**
     * 请求账号检测状态信息
     * @param {Boolean} isInterval 是否为定时刷新
     */
    async requestStates(isInterval) {

        if (this._isRequesting) {
            return;
        }

        this._isRequesting = true;
        let $loading;

        if (!isInterval) {
            $loading = this.interaction.showLoading({ text: '获取账号监控数据...' });
        }

        let resp = await repoAccountAround.queryAll();
        let records = resp.data || [];

        if (!isInterval) {
            $loading.close();
        }

        this._isRequesting = false;

        if (resp.errorCode != 0 || !(records instanceof Array)) {

            this.interaction.showError(`获取账号列表失败，${resp.errorCode}/${resp.errorMsg}`);
            return;        
        }

        let results = records.map(item => new AccountResult(item));
        
        if (!isInterval) {

            this.tableObj.refill(results);

            if (results.length > 0) {
                this.handleAccountChange(results[0]);
            }
        }
        else {
            results.forEach(act => { this.tableObj.putRow(act); });
        }
    }

    handlePageSizeChange() {
        this.tableObj.setPageSize(this.paging.pageSize);
    }

    handlePageChange() {
        this.tableObj.setPageIndex(this.paging.page);
    }

    filterRecords() {

        let thisObj = this;
        let keywords = this.searching.keywords;
        var astype = this.searching.assetType;
        let cktype = this.searching.checkType;

        /**
         * @param {AccountResult} record
         */
        function matchKeywords(record, keywords) {
            return !keywords || record.accountName.indexOf(keywords) >= 0 || thisObj.helper.pinyin(record.accountName).indexOf(keywords) >= 0;
        }

        /**
         * @param {AccountResult} record
         */
        function matchAssetType(record, astype) {

            if (astype == thisObj.assetTypes.all.code || helper.isNone(astype)) {
                return true;
            }
            else {
                return record.assetType == astype;
            }
        }

        /**
         * @param {AccountResult} record
         */
        function matchCheckType(record, cktype) {

            if (cktype == thisObj.scopedTypes.all.code || helper.isNone(cktype)) {
                return true;
            }

            if (cktype == thisObj.scopedTypes.ok.code) {
                return !CheckTypes.some(item => record[item.flagName] == false || record[item.flagName] == undefined);
            }

            if (cktype == thisObj.scopedTypes.cover.code) {
                return CheckTypes.some(item => item.isCover && record[item.flagName] == false);
            }

            if (cktype == thisObj.scopedTypes.check.code) {
                return CheckTypes.some(item => item.isCheck && record[item.flagName] == false);
            }

            if (cktype == thisObj.scopedTypes.anyError.code) {
                return CheckTypes.some(item => record[item.flagName] == false);
            }

            let matched = Seek(cktype);
            return record[matched.flagName] == false;
        }

        /**
         * @param {AccountResult} record
         */
        function searcher(record) {
            return matchKeywords(record, keywords) && matchAssetType(record, astype) && matchCheckType(record, cktype);
        }

        this.paging.page = 1;
        this.tableObj.setPageIndex(1, false);
        this.tableObj.customFilter(searcher);
    }

    exportSome() {
        this.tableObj.exportAllRecords(`账号列表-${new Date().format('yyyyMMdd')}`);
    }

    refresh() {
        
        this.searching.keywords = null;
        this.searching.assetType = this.assetTypes.all.code;
        this.searching.checkType = this.scopedTypes.all.code;
        this.paging.page = 1;
        this.requestStates();
    }

    config() {
        this.tableObj.showColumnConfigPanel();
    }

    dispose() {
        clearInterval(this.refreshTimer);
    }

    async build($container, view_option) {

        super.build($container);

        const tag = (view_option || {}).tag;
        this.isSuperMode = tag === this.userModes.super;
        this.isOrgMode = tag === this.userModes.org;
        this.isBrokerMode = tag === this.userModes.broker;

        this.helper.extend(this, ColumnCommonFunc);
        this.createToolbarApp();
        this.setupCheckTypesMap();
        this.setupTable();
        this.setupDynamicPart();
        this.registerEvent(this.systemEvent.tabActivated, this._handleActivation.bind(this));
        this.requestStates();
        this.refreshTimer = setInterval(() => { this.requestStates(true); }, 1000 * 60);
    }
}

module.exports = Controller;