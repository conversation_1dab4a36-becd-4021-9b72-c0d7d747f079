<div class="view-root">

    <el-input v-model.number="strategyName" placeholder="目标策略" disabled>
        <template slot="prepend">策略</template>
    </el-input>

    <el-select @change="handleStrategyChange" v-model="strategyId" v-show="false" placeholder="选择策略" filterable clearable>
        <el-option v-for="(item, item_idx) in strategies" :key="item_idx" :value="item.strategyId" :label="item.strategyName"></el-option>
    </el-select>

    <el-select @change="handleAccountChange" v-model="accountId" placeholder="选择账号" filterable clearable>
        <el-option v-for="(item, item_idx) in accounts" :key="item_idx" :value="item.accountId" :label="item.accountName"></el-option>
    </el-select>

    <el-select v-model="direction" @change="handleDirectionChange">
        <el-option v-for="(item, item_idx) in directions" :key="item_idx" :value="item.code" :label="item.mean"></el-option>
    </el-select>

    <el-select v-model="effect">
        <el-option v-for="(item, item_idx) in effects" :key="item_idx" :value="item.code" :label="item.mean"></el-option>
    </el-select>

    <el-select v-model="flag">
        <el-option v-for="(item, item_idx) in flags" :key="item_idx" :value="item.code" :label="item.mean"></el-option>
    </el-select>

    <el-autocomplete class="target-instrument"
                     v-model.trim="keywords"
                     @keydown.native="handleKeywordsChange"
                     @clear="handleClearIns" 
                     @select="handleSelect"
                     :fetch-suggestions="searchInstrments" placeholder="合约代码" clearable>
                
                    <template slot-scope="{ item: ins }">
                        <span class="item-code">[{{ ins.instrument }}] </span>
                        <span class="item-name">{{ ins.instrumentName }}</span>
                    </template>

                    <template slot="prepend">合约</template>

    </el-autocomplete>

    <el-input v-model.number="entrustVolume" placeholder="调仓数量">
        <template slot="prepend">数量</template>
    </el-input>

    <el-input v-model="entrustPrice" placeholder="调仓价格">
        <template slot="prepend">价格</template>
    </el-input>

    <div class="account-pos-info">
        <label>总仓 {{ guide.totalPosition }}</label>
        <label class="s-center">昨仓 {{ guide.yesterdayPosition }}</label>
        <label class="s-right">今仓 {{ guide.todayPosition }}</label>
    </div>

    <el-button :type="isBuy ? 'danger' : 'success'" size="small" class="btn-order" @click="showConfirm">{{ isBuy ? '买入' : '卖出' }}</el-button>
    <el-button type="info" size="small" class="btn-cancel" @click="cancel">取消</el-button>

</div>