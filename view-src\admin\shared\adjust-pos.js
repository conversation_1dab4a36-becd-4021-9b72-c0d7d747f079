const IView = require('../../../component/iview').IView;
const { repoStrategy } = require('../../../repository/strategy');
const { BizHelper } = require('../../../libs/helper-biz');
const { Position } = require('../../../model/position');

/**
 * 产品结构
 */
class AccountInfo {

    constructor(struc) {
    
        this.accountId = struc.accountId;
        this.accountName = struc.accountName;
        this.assetType = struc.assetType;
        this.financeAccountName = struc.financeAccountName;
    }
}

/**
 * 策略结构
 */
class StrategyInfo {

    constructor(struc) {
    
        this.strategyId = struc.id;
        this.strategyName = struc.strategyName;
        this.fundId = struc.fundId;
        this.fundName = struc.fundName;

        let accounts = struc.strategyAccounts;
        let account_infos = [new AccountInfo({})];
        account_infos.clear();

        if (accounts instanceof Array) {
            account_infos.merge(accounts.map(acnt => new AccountInfo(acnt)));
        }

        this.accounts = account_infos;
    }
}

class View extends IView {

    get isBuy() {
        return this.vdata.direction == this.directions.buy.code;
    }

    get isSell() {
        return this.vdata.direction == this.directions.sell.code;
    }

    get assetType() {
        return this._assetType || this.systemEnum.assetsType.stock.code;
    }

    get theStrategy() {
        return this.strategies.find(stra => stra.strategyId == this.vdata.strategyId);
    }

    get theAccount() {
        return this.accounts.find(acnt => acnt.accountId == this.vdata.accountId);
    }

    constructor(view_name, is_standalone_window) {
        super(view_name, is_standalone_window, '交易调仓');
    }

    resetVolumePrice() {

        this.vdata.entrustVolume = 0;
        this.vdata.entrustPrice = 0.00;
    }

    resetGuide() {

        this.guide.todayPosition = 0;
        this.guide.totalPosition = 0;
        this.guide.yesterdayPosition = 0;
    }

    /**
     * @param {Boolean} forced 
     */
    async requestStrategies(forced) {

        if (forced !== true && this.strategies.length > 0) {
            return;
        }

        if (this._isRequestingStrategies) {
            return;
        }

        this._isRequestingStrategies = true;
        var loading = this.interaction.showLoading({ text: `获取策略列表...` });

        try {

            let resp = await repoStrategy.getAll();
            if (resp.errorCode === 0) {

                let data_list = resp.data || [];
                if (data_list instanceof Array) {

                    let strategies = data_list.map(item => new StrategyInfo(item));
                    this.strategies.clear();
                    this.strategies.merge(strategies);
                    this.simpleStrategies.clear();
                    this.simpleStrategies.merge(strategies.map(item => ({ strategyId: item.strategyId, strategyName: item.strategyName })));
                    
                    if (strategies.length > 0 && this.helper.isNotNone(this.pendingIdentityId)) {

                        let matched = strategies.find(stra => stra.strategyId == this.pendingIdentityId);
                        if (matched) {

                            this.vdata.strategyId = this.pendingIdentityId;
                            this.vdata.strategyName = matched.strategyName;
                            this.pendingIdentityId = undefined;
                            this.handleStrategyChange();
                        }                        
                    }
                }
            }
            else {
                this.interaction.showError(`获取策略列表失败，${resp.errorCode}/${resp.errorMsg}`);
            }
        }
        catch (ex) {

            this.interaction.showError(`获取策略列表异常`);
            console.error(ex);
        } 
        finally {

            this._isRequestingStrategies = false;
            loading.close();
        }
    }

    createOrderApp() {

        this.guide = {

            totalPosition: 0,
            todayPosition: 0,
            yesterdayPosition: 0,
        };

        this.vdata = {

            belongingFundName: null,
            strategyId: null,
            strategyName: null,
            strategies: this.simpleStrategies,
            accountId: null,
            accounts: this.accounts,

            direction: this.directions.buy.code,
            directions: this.helper.deepClone(this.directions),
            effect: this.effects.open.code,
            effects: this.effects,
            flag: this.flags.history.code,
            flags: this.flags,

            keywords: null,
            instrument: null,
            instrumentName: null,
            entrustVolume: 0,
            entrustPrice: 0.00,
            guide: this.guide,
        };

        this.vapp = new Vue({

            el: this.$container.querySelector('.view-root'),
            data: this.vdata,
            computed: {

                isBuy: () => {
                    return this.isBuy;
                },
            },

            methods: this.helper.fakeVueInsMethod(this, [

                this.handleStrategyChange,
                this.handleAccountChange,
                this.handleDirectionChange,
                this.handleKeywordsChange,
                this.handleClearIns,
                this.handleSelect,
                this.searchInstrments,
                this.showConfirm,
                this.cancel,
            ]),
        });
    }

    handleStrategyChange() {

        let accounts = [];

        if (this.isStrategyNotSelected()) {
            this.vdata.accountId = null;
        }
        else {

            let selected = this.theStrategy;
            accounts.merge(selected.accounts.map(acnt => ({ accountId: acnt.accountId, accountName: acnt.accountName, assetType: acnt.assetType })));
            this.vdata.accountId = selected.accounts[0].accountId;
        }

        this.accounts.clear();
        this.accounts.merge(accounts);
        this.handleAccountChange();
    }

    handleAccountChange() {

        let ast = this.systemEnum.assetsType;
        let selected = this.accounts.find(acnt => acnt.accountId == this.vdata.accountId);
        this._assetType = selected ? selected.assetType : ast.stock.code;
        this.handleClearIns();
    }

    handleDirectionChange() {
        this.resetVolumePrice();
    }

    handleKeywordsChange(ev) {

        if (event.keyCode == 8) {

            event.returnValue = false;
            this.handleClearIns();
        }
    }

    handleClearIns() {

        this.vdata.keywords = null;
		this.vdata.instrument = null;
        this.vdata.instrumentName = null;
        this.resetVolumePrice();
        this.resetGuide();
    }

    handleSelect(selected_ins) {

		let { instrument, instrumentName } = selected_ins;
        this.vdata.keywords= `${ instrumentName }-${ instrument }`;
        this.vdata.instrument = instrument;
        this.vdata.instrumentName = instrumentName;        
        this.requestPosition(instrument);
    }

    searchInstrments(kw, match_callback) {

        let keywords = typeof kw == 'string' ? kw.trim() : '';

        if (this.isAccountNotSelected()) {

            match_callback([]);
            this.interaction.showError('账号未选择，无法确定合约搜索范围');
            return;
        }

        if (keywords.length < 1) {

            match_callback([]);
            return;
        }

        let matches = BizHelper.filterInstruments(this.assetType, keywords);
        if (matches.length == 1) {

            match_callback([]);
            this.handleSelect(matches[0]);
            return;
        }

        match_callback(matches);
    }

    requestPosition(instrument) {

        if (this.isStrategyNotSelected() || this.isAccountNotSelected()) {
            return;
        }

        let param = [{

            identityId: this.theStrategy.strategyId, 
            accountId: this.theAccount.accountId, 
            instrument: instrument,
        }];

        this.renderProcess.send(this.systemEvent.transmitMsg2TradingServer, this.serverFunction.requestAccountPosition, param);
    }

    handleContextChange(identityId) {
        
        let matched = this.strategies.find(stra => stra.strategyId == identityId);
        if (matched) {

            this.vdata.strategyId = identityId;
            this.vdata.strategyName = matched.strategyName;
            this.handleStrategyChange();
        }
        else {
            this.pendingIdentityId = identityId;
        }
    }

    handlePositionPush(event, data, reqId) {

        let positions = data.data;
        if (!Array.isArray(positions)) {

            console.error('unexpected position (of an account) data structure', data);
            return;
        }

        if (positions.length == 0) {
            this.resetGuide();
        }
        else {

            let first_pos = new Position(positions[0]);
            this.guide.totalPosition = first_pos.totalPosition;
            this.guide.todayPosition = first_pos.todayPosition;
            this.guide.yesterdayPosition = first_pos.yesterdayPosition;
        }
    }    

    isStrategyNotSelected() {
        return this.helper.isNone(this.vdata.strategyId);
    }

    isAccountNotSelected() {
        return this.helper.isNone(this.vdata.accountId);
    }

    isInstrumentOk () {
        return !!this.vdata.instrument;
    }

    isEntrustVolumeOk () {

        let { entrustVolume } = this.vdata;
        return entrustVolume > 0 && Number.isInteger(entrustVolume);
    }

    isEntrustPriceOk () {

        let price = +this.vdata.entrustPrice;
        return !Number.isNaN(price) && price > 0;
    }
    
    showConfirm() {

        let { effect, flag, instrument, instrumentName, entrustVolume, entrustPrice } = this.vdata;

        if (this.isStrategyNotSelected()) {
            return this.interaction.showError('请选择策略');
        }
        else if (this.isAccountNotSelected()) {
            return this.interaction.showError('请选择账号');
        }
        else if (!this.isInstrumentOk()) {
            return this.interaction.showError('请输入合约');
        }
        else if (!this.isEntrustVolumeOk()) {
            return this.interaction.showError(`无效委托数量：${ entrustVolume }`);
        }
        else if (!this.isEntrustPriceOk()) {
            return this.interaction.showError(`无效委托价格：${ entrustPrice }`);
        }
        
        let isCloseToday = effect == this.effects.closeToday.code;
        let isClose = effect == this.effects.close.code;
        let { todayPosition, totalPosition } = this.guide;

        if (isCloseToday && entrustVolume > todayPosition) {
            return this.interaction.showError(`今仓最多${ todayPosition }可平`);
        }
        else if (isClose && entrustVolume > totalPosition) {
            return this.interaction.showError(`总仓最多${ totalPosition }可平`);
        }

        let isFuture = this.assetType == this.systemEnum.assetsType.future.code;
        let isMarketShfe = isFuture && instrument.toLowerCase().indexOf(this.systemTrdEnum.market.shfe.code.toLowerCase()) >= 0;

        if (isMarketShfe) {

            if (isCloseToday && flag == this.flags.history.code) {
                return this.interaction.showError('上期所合约平今指令不能调昨仓');
            }
            else if (isClose && flag == this.flags.today.code) {
                return this.interaction.showError('上期所合约平昨指令不能调今仓');
            }
        }

        let items = [

			['策略', this.theStrategy.strategyName, 's-ellipsis'],
			['账号', this.theAccount.accountName, 's-ellipsis'],
			['方向', this.isBuy ? '买入' : '卖出', this.isBuy ? 's-color-red' : 's-color-green'],
			['合约', `${ instrumentName } / ${ instrument }`, 's-ellipsis'],
			['数量', entrustVolume.thousands()],
			['价格', entrustPrice],
        ];
        
        let message = items.map(x => `<div><span>${x[0]}：</span><span class="${x[2] || ''}">${x[1]}</span></div>`).join('');
        this.interaction.showConfirm({
            
            message: message,
            confirmed: () => {
                this.makeOrder(); 
            },
        });        
    }

    makeOrder() {

        let { userId } = this.userInfo;
        let { direction, effect, flag, instrument, entrustVolume, entrustPrice } = this.vdata;
        let { strategyId, fundId } = this.theStrategy;
        let { accountId } = this.theAccount;
        let nowTs = Date.now();

        let order = {

            strategyId,
            fundId,
            accountId,
            userId,
            instrument,
            volume: entrustVolume,
            price: entrustPrice,
            priceType: this.systemTrdEnum.pricingType.fixedPrice.code,
            bsFlag: direction,
            positionEffect: effect,
            customId: `adjustpos-${ strategyId }-${ nowTs }`,
            orderTime: nowTs,
            adjustFlag: flag,
            hedgeFlag: this.systemTrdEnum.hedgeFlag.Speculate.code,
        };

        this.renderProcess.send(this.systemEvent.transmitMsg2TradingServer, this.serverFunction.strategyAdjustPosition, order);
        this.interaction.showSuccess('调仓请求已发送');
        this.trigger('adjust-pos-done');
    }

    cancel() {
        this.trigger('adjust-pos-done');
    }

    prepare() {

        this.directions = this.systemTrdEnum.tradingDirection;
        
        this.effects = {

            open: { code: 0, mean: '[OPEN] 开仓' },
            close: { code: 1, mean: '[CLOSE] 平仓' },
            closeToday: { code: 3, mean: '[CLOSE TODAY] 平今' },
        };

        this.flags = {

            history: { code: 1, mean: '[YESTERDAY] 调昨' },
            today: { code: 2, mean: '[TODAY] 调今' },
        };

        /**
         * @returns {Array<StrategyInfo>}
         */
        function makeStrategies() {
            return [];
        }

        /**
         * @returns {Array<{ strategyId, strategyName }>}
         */
        function makeLimitedStrategies() {
            return [];
        }

        /**
         * @returns {Array<{ accountId, accountName, assetType }>}
         */
        function makeAccounts() {
            return [];
        }

        this.strategies = makeStrategies();
        this.simpleStrategies = makeLimitedStrategies();
        this.accounts = makeAccounts();
    }

    listen2Events() {

        this.renderProcess.on(this.serverEvent.accountPositionPush, this.handlePositionPush.bind(this));
        this.registerEvent(this.systemEvent.viewContextChange, this.handleContextChange.bind(this));
    }

    build($container) {

        super.build($container);
        this.prepare();
        this.createOrderApp();
        this.listen2Events();
        this.requestStrategies();
        window.adjustPos = this;
    }
}

module.exports = View;