<div class="view-org-root">

    <div class="toolbar">

        <el-button type="primary" size="mini" @click="create">
            <i class="iconfont icon-add"></i> 创建机构</el-button>

        <el-input v-model="searching.keywords" class="input-searching" placeholder="输入关键词过滤" @change="filterRecords" clearable>
            <i slot="prefix" class="el-input__icon el-icon-search"></i>
        </el-input>

        <span class="pagination-wrapper">

            <el-pagination :page-sizes="paging.pageSizes" :page-size.sync="paging.pageSize" :total="paging.total"
                :current-page.sync="paging.page" :layout="paging.layout" @size-change="handlePageSizeChange"
                @current-change="handlePageChange"></el-pagination>

        </span>

    </div>

    <table>
        <tr>
            <th label="机构名称" prop="orgName" min-width="150" searchable sortable overflowt></th>
            <th label="联系人" prop="contract" min-width="100" searchable sortable overflowt></th>
            <th label="电话" prop="phone" min-width="100" searchable sortable overflowt></th>
            <th label="邮箱" prop="email" min-width="100" searchable sortable overflowt></th>
            <th label="域名" prop="domain" min-width="170" searchable sortable overflowt></th>
            <th label="机构状态" prop="status" fixed-width="80" align="center" fixed="right" formatter="formtOrgStatus" export-formatter="formtOrgStatusText"></th>
            <th label="操作" fixed-width="80" align="center" fixed="right" exportable="false">
                <a class="icon-button iconfont icon-edit" event.onclick="edit" title="编辑"></a>
                <a class="icon-button iconfont icon-remove s-color-red" event.onclick="delete" title="删除"></a>
            </th>
        </tr>
    </table>

    <div class="dialog-editing">

        <template>

            <el-dialog class="org-dialog" width="600px" v-bind:title="dialog.title" :show-close="false"
                v-bind:visible="dialog.visible" v-bind:close-on-click-modal="false" :show-close="false"
                v-bind:close-on-press-escape="false">

                <el-form class="org-form" size="mini" v-bind:model="formd" v-bind:rules="rules" ref="editForm" label-width="80px" :inline="true">

                    <el-form-item label="机构名称" prop="orgName">
                        <el-input :disabled="isUpdating" :maxlength="30" v-model.trim="formd.orgName" placeholder="机构名称，创建后不可修改">
                        </el-input>
                    </el-form-item>

                    <el-form-item label="域名" prop="domain">
                        <el-input v-model.trim="formd.domain" :maxlength="100"></el-input>
                    </el-form-item>

                    <el-form-item label="联系人" prop="contract">
                        <el-input v-model.trim="formd.contract" :maxlength="30"></el-input>
                    </el-form-item>

                    <el-form-item label="电话" prop="phone">
                        <el-input v-model.trim="formd.phone" :maxlength="18"></el-input>
                    </el-form-item>

                    <el-form-item label="邮箱" prop="email">
                        <el-input v-model.trim="formd.email" :maxlength="50"></el-input>
                    </el-form-item>

                    <el-form-item label="介绍" prop="introduction">
                        <el-input v-model.trim="formd.introduction" resize="none" :maxlength="200"></el-input>
                    </el-form-item>

                    <el-form-item label="状态" prop="status">
                        <el-radio-group v-model="formd.status">
                            <el-radio v-for="(item, item_idx) in orgStatus" :key="item_idx" :label="item.code">{{ item.mean }}</el-radio>
                        </el-radio-group>
                    </el-form-item>

                </el-form>

                <div slot="footer">
                    <el-button type="primary" @click="save" size="small">确定</el-button>
                    <el-button @click="unsave" size="small">取消</el-button>
                </div>

            </el-dialog>
        </template>
    </div>
</div>