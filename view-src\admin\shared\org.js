
const IView = require('../../../component/iview').IView;
const SmartTable = require('../../../libs/table/smart-table').SmartTable;
const ColumnCommonFunc = require('../../../libs/table/column-common-func').ColumnCommonFunc;
const repoOrg = require('../../../repository/org').repoOrg;

class Organization {

    constructor(struc) {

        this.id = struc.id;
        this.orgName = struc.orgName;
        this.status = struc.status;
        this.contract = struc.contract;
        this.domain = struc.domain;
        this.email = struc.email;
        this.introduction = struc.introduction;
        this.phone = struc.phone;

        if (this.status === null || this.status === undefined) {
            this.status = 1;
        }
    }
}

class Controller extends IView {

    get $form() {
        return this.dialogApp.$refs.editForm;
    }

    identifyRecord(record) {
        return record.id;
    }
    
    constructor(view_name) {

        super(view_name, false, '机构管理');
        this.orgStatus = {
            enabled: { code: 1, mean: '启用' },
            disabled: { code: 2, mean: '禁用' },
        };
    }

    isOrgEnabled(org_status) {
        return org_status === this.orgStatus.enabled.code;
    }

    createToolbarApp() {

        this.searching = {
            keywords: '',
        };

        var pagination = this.systemSetting.tablePagination;
        this.paging = {

            pageSizes: pagination.pageSizes,
            pageSize: pagination.pageSize,
            layout: pagination.layout,
            total: 0,
            page: 1,
        };

        this.toolbar = new Vue({

            el: this.$container.querySelector('.view-org-root > .toolbar'),

            data: {
                searching: this.searching,
                paging: this.paging,
            },

            methods: this.helper.fakeVueInsMethod(this, [

                this.create,
                this.filterRecords,
                this.handlePageSizeChange,
                this.handlePageChange,
            ]),
        });
    }

    /**
     * @param {Organization} record 
     */
    formtOrgStatus(record) {

        var class_name = this.isOrgEnabled(record.status) ? 'is-checked' : '';
        return this.helperUi.formatSwitchButton(this.switchStatus, class_name);
    }

    /**
     * @param {Organization} record 
     */
    formtOrgStatusText(record) {
        return this.isOrgEnabled(record.status) ? '正常' : '已禁用';
    }

    /**
     * @param {Organization} record 
     */
    switchStatus(record) {
        
        var new_status = this.isOrgEnabled(record.status) ? this.orgStatus.disabled.code : this.orgStatus.enabled.code;
        var copy = new Organization({});
        this.helper.extend(copy, record);
        copy.status = new_status;

        this.submit(copy, (revised) => {
            this.tableObj.updateRow(revised);
        });
    }

    create() {
        this.promptDialog();
    }

    /**
     * @param {Organization} record 
     */
    edit(record) {
        this.promptDialog(record);
    }

    /**
     * @param {Organization} record 
     */
    delete(record) {
        
        this.interaction.showConfirm({

            title: '删除确认',
            message: `删除机构：${record.orgName} ?`,
            confirmed: () => { this.deleteRecord(record); }
        });
    }

    /**
     * @param {Organization} record 
     */
    async deleteRecord(record) {

        let result = await repoOrg.deleteOrg(record.id);
        if (result.errorCode == 0) {

            this.tableObj.deleteRow(record.id);
            this.interaction.showSuccess('已删除');
        }
        else {
            this.interaction.showError('删除操作错误：' + result.errorMsg);
        }
    }

    setupTable() {

        this.helper.extend(this, ColumnCommonFunc);
        var $table = this.$container.querySelector('.view-org-root > table');
        this.tableObj = new SmartTable($table, this.identifyRecord, this, {

            tableName: 'smt-ao',
            displayName: this.title,
            recordsFiltered: (filtered_count) => { this.paging.total = filtered_count; },
        });
        
        this.tableObj.setPageSize(this.paging.pageSize);
    }

    async requestOrgs() {

        if (this._isRequesting) {
            return;
        }
        
        this._isRequesting = true;
        var loading = this.interaction.showLoading({ text: `获取机构列表...` });

        try {

            let resp = await repoOrg.getAll();
            if (resp.errorCode === 0) {

                let list = resp.data || [];
                if (list instanceof Array) {

                    let orgs = list.map(x => new Organization(x));
                    this.tableObj.refill(orgs);
                }
            } 
            else {
                this.interaction.showError(`获取机构列表失败，${resp.errorCode}/${resp.errorMsg}`);
            }
        }
        catch (ex) {
            
            this.interaction.showError(`获取机构列表异常`);
            console.error(ex);
        }
        finally {

            this._isRequesting = false;
            loading.close();
        }
    }

    handlePageSizeChange() {
        this.tableObj.setPageSize(this.paging.pageSize);
    }

    handlePageChange() {
        this.tableObj.setPageIndex(this.paging.page);
    }

    filterRecords() {

        let thisObj = this;
        let keywords = this.searching.keywords;

        /**
         * 
         * @param {Organization} record 
         */
        function filterByPinyin(record) {
            return thisObj.helper.pinyin(record.orgName).indexOf(keywords) >= 0;
        }

        this.paging.page = 1;
        this.tableObj.setPageIndex(1, false);
        this.tableObj.setKeywords(keywords, false);
        this.tableObj.mixedFilter(filterByPinyin, 'or');
    }

    exportSome() {
        this.tableObj.exportAllRecords(`机构列表-${ new Date().format('yyyyMMdd') }`);
    }

    refresh() {

        this.searching.keywords = null;
        this.paging.page = 1;
        this.requestOrgs();
    }

    /**
     * @param {Organization} org
     */
    promptDialog(org) {

        if (org === undefined) {

            this.helper.extend(this.formdata, new Organization({}));
            this.dialog.title = '新建机构';
        }
        else {

            this.dialog.title = '编辑机构信息';
            this.helper.extend(this.formdata, org);
        }

        this.dialog.visible = true;
        setTimeout(() => { this.$form.clearValidate(); }, 200);
    }

    /**
     * @param {Organization} org 
     * @param {Function} callback 
     */
    async submit(org, callback) {

        let is_create = this.helper.isNone(org.id);
        let result = is_create ? await repoOrg.createOrg(org) 
                               : await repoOrg.updateOrg(org);

        if (result.errorCode != 0) {
            this.interaction.showError('机构信息保存失败：' + result.errorMsg);
        }
        else {
            if (is_create) {
                callback(new Organization(result.data));
            }
            else {
                callback(org);
            }
            this.interaction.showSuccess('机构信息已保存');
        }
    }

    save() {

        this.$form.validate(result => {

            if (result) {

                this.submit(this.formdata, (revised) => {
                    this.dialog.visible = false;
                    this.tableObj.putRow(revised);
                });
            }
        });
    }

    unsave() {
        this.dialog.visible = false;
    }

    /**
     * @param {String} org_name 
     * @param {Function} callback 
     */
    checkOrgNameUnique(org_name, callback) {

        if (this.tableObj.rowCount <= 1) {
            callback();
            return;
        }

        var records = this.tableObj.extractAllRecords();
        var current_org_id = this.formdata.id;
        var matched = records.find(x => x instanceof Organization 
                                    && x.id != current_org_id
                                    && x.orgName == org_name);

        if (matched === undefined) {
            callback();
        } 
        else {
            callback('机构名称已存在');
        }
    }

    createDialogApp() {

        const rules = {

            orgName: [

                { type: 'string', required: true, message: '请输入机构名称' },
                { validator: (rule, value, callback) => { this.checkOrgNameUnique(value, callback); } },
                this.systemSetting.specialCharacterFilterRule,
                this.systemSetting.limitInputLengthRule,
            ],

            phone: { type: 'string', required: true, message: '请输入电话号码' },
            contract: [
                { required: true, message: '请输入联系人' },
                this.systemSetting.specialCharacterFilterRule,
                this.systemSetting.limitInputLengthRule,
            ],
            email: [
                { type: 'email', message: '邮箱格式不正确' },
                { required: true, message: '请输入邮箱' },
                this.systemSetting.limitInputLengthRule,
            ],
            domain: [
                { type: 'string', required: true, message: '请输入域名' },
                {
                    pattern: /^((http:\/\/)|(https:\/\/))?([a-zA-Z0-9]([a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?\.)+[a-zA-Z]{2,6}(\/)?$/,
                    message: '请正确输入域名',
                },
                this.systemSetting.limitInputLengthRule,
            ],
        };

        this.formdata = new Organization({});
        this.dialog = { visible: false, title: null };

        this.dialogApp = new Vue({

            el: this.$container.querySelector('.view-org-root > .dialog-editing'),

            data: {

                dialog: this.dialog,
                formd: this.formdata,
                rules: rules,
                orgStatus: this.orgStatus,
            },

            computed: {

                isUpdating: () => {
                    return !this.helper.isNone(this.formdata.id);
                }
            },

            methods: this.helper.fakeVueInsMethod(this, [

                this.save,
                this.unsave,
            ]),
        });
    }

    adjustTableHeight(win_width, win_height, is_maximized) {
        this.tableObj.setMaxHeight(win_height - (is_maximized ? 175 : 160));
    }

    build($container) {
        
        super.build($container);
        this.createToolbarApp();
        this.setupTable();
        this.requestOrgs();
        this.createDialogApp();
        this.lisen2WinSizeChange(this.adjustTableHeight.bind(this));
    }
}

module.exports = Controller;
