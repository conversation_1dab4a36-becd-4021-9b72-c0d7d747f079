<div class="view-fund-root">

    <div class="toolbar">

        <el-button type="primary" size="mini" @click="create">
            <i class="iconfont icon-add"></i> 创建产品
        </el-button>

        <el-input v-model="searching.keywords" class="input-searching" placeholder="输入关键词过滤" @change="filterRecords" clearable>
            <i slot="prefix" class="el-input__icon el-icon-search"></i>
        </el-input>

        <span class="pagination-wrapper">

            <el-pagination :page-sizes="paging.pageSizes" :page-size.sync="paging.pageSize" :total="paging.total"
                :current-page.sync="paging.page" :layout="paging.layout" @size-change="handlePageSizeChange"
                @current-change="handlePageChange"></el-pagination>
        </span>

        <div class="s-pull-right">

            <el-button size="mini" @click="refresh">
                <i class="iconfont icon-refresh"></i> 刷新</el-button>

            <el-button size="mini" @click="exportSome">
                <i class="iconfont icon-export"></i> 导出</el-button>

            <el-button size="mini" @click="config">
                <i class="iconfont icon-setting"></i> 配置</el-button>
        </div>

    </div>

    <table>
        <tr>
            <th label="产品名称" 
                min-width="180" 
                prop="fundName" fixed sortable overflowt>
                <a class="s-underline s-cp" event.onclick="viewReportTemplate" title="查看产品报告">$fundName$</a>
            </th>

            <th label="ID" min-width="140" prop="id" overflowt></th>

            <th label="类型"
                min-width="80" 
                prop="fundType" 
                formatter="formatFundType" 
                export-formatter="formatFundTypeText" sortable></th>

            <th label="备案号" min-width="110" prop="amacCode" overflowt></th>
            <th label="成立日期" min-width="100" prop="establishedDay" sortable overflowt></th>
            <th label="管理机构" min-width="150" prop="orgName" sortable overflowt></th>
            <th label="策略类型" min-width="150" prop="strategyType" sortable overflowt></th>

            <th label="报告模板" 
                min-width="100" 
                prop="reportTemplates" 
                formatter="formatReportTemplate"
                export-formatter="formatReportTemplateText"
                condition="normal" overflowt></th>
            
            <th label="基金经理" min-width="90" prop="fundManager" overflowt></th>
            <th label="创建人" min-width="90" prop="creatorUserFullName" overflowt></th>

            <th label="分享用户" 
                min-width="150" 
                prop="users" 
                formatter="formatShares"
                export-formatter="formatSharesText"
                condition="normal" overflowt></th>

            <th label="已绑账号"
                min-width="200" 
                prop="accounts" 
                formatter="formatAccount" 
                export-formatter="formatAccountText"
                condition="normal" overflowt></th>

            <th label="估值方式" 
                min-width="100" 
                prop="valuation" 
                formatter="formatValuation" 
                export-formatter="formatValuationText"
                condition="normal" overflowt></th>

            <th label="权益"
                min-width="100" 
                prop="balance" 
                align="right" sortable summarizable overflowt thousands></th>

            <th label="市值"
                min-width="100" 
                prop="marketValue" 
                align="right" sortable summarizable overflowt thousands></th>

            <th label="收益率"
                min-width="80"
                prop="risePercent" 
                align="right" 
                class-maker="makeBenefitClass" sortable percentage overflowt></th>

            <th label="当前净值"
                min-width="80" 
                prop="navRealTime" 
                align="right"
                precision="4" 
                condition="broker" sortable></th>

            <th label="参考净值" 
                min-width="80" 
                prop="nav" 
                align="right" 
                precision="4" 
                condition="broker" sortable>
            </th>

            <th label="设置清盘" 
                min-width="80" 
                prop="closedFlag" 
                align="center" 
                formatter="formatFundStatus"
                export-formatter="formatFundStatusText" sortable></th>

            <th label="启用风控" 
                min-width="80" 
                prop="riskEnable" 
                align="center" 
                formatter="formatRiskControled"
                export-formatter="formatRiskControledText" sortable></th>

            <th label="操作" 
                fixed-width="70" 
                align="center" 
                fixed="right"
                exportable="false">
                <span class="smart-table-action">
                    <a class="lock-button icon-button">...</a>
                    <ul>
                        <li><a class="icon-button el-icon-edit" event.onclick="edit"> 编辑</a></li>
                        <li><a class="icon-button iconfont icon-tianjiafengkong" event.onclick="openRiskControlSetting"> 风控设置</a></li>
                        <li><a class="icon-button el-icon-delete s-color-red" event.onclick="delete"> 删除产品</a></li>
                    </ul>
                </span>
            </th>

        </tr>
    </table>

    <div class="dialog-editing">

        <template>

            <el-dialog width="630px" :visible="dialog.visible" :title="dialog.title" :close-on-click-modal="false"
                :close-on-press-escape="false" :show-close="false">

                <el-form ref="editform" class="dialog-body-form" label-width="110px" :model="formd" :rules="rules"
                    :inline="true">

                    <el-form-item label="产品名称" prop="fundName">
                        <el-input v-model.trim="formd.fundName" placeholder="请输入产品名称"></el-input>
                    </el-form-item>

                    <el-form-item label="基金类型" prop="fundType">
                        <el-select v-model="formd.fundType">
                            <el-option v-for="(item, item_idx) in fundTypes" :key="item_idx" :value="item.code"
                                :label="item.mean"></el-option>
                        </el-select>
                    </el-form-item>

                    <el-form-item label="备案号" prop="amacCode">
                        <el-input v-model.trim="formd.amacCode"></el-input>
                    </el-form-item>

                    <el-form-item label="成立日期" prop="establishedDay">
                        <el-date-picker v-model="formd.establishedDay" value-format="yyyy-MM-dd"></el-date-picker>
                    </el-form-item>

                    <el-form-item label="基金经理" prop="fundManager">
                        <el-input v-model.trim="formd.fundManager"></el-input>
                    </el-form-item>

                    <el-form-item label="策略类型" prop="strategyType">
                        <el-input v-model.trim="formd.strategyType"></el-input>
                    </el-form-item>

                    <el-form-item label="参考基准" prop="basisReference">
                        <el-select v-model="formd.basisReference">
                            <el-option v-for="ref in basisReferences" :key="ref.code" :value="ref.code"
                                :label="ref.mean"></el-option>
                        </el-select>
                    </el-form-item>

                    <el-form-item label="管理机构" prop="orgId">
                        <el-select v-model="formd.orgId" :disabled="isOrgFixed" @change="handleOrgChange"
                            placeholder="请选择所属机构" clearable filterable>
                            <el-option v-for="(item, item_idx) in orgs" :key="item_idx" :label="item.orgName"
                                :value="item.id"></el-option>
                        </el-select>
                    </el-form-item>

                </el-form>

                <div slot="footer">
                    <el-button @click="save" type="primary" size="small">确定</el-button>
                    <el-button @click="unsave" size="small">取消</el-button>
                </div>

            </el-dialog>

        </template>
    </div>

    <div class="dialog-user">

        <template>

            <el-dialog title="分享产品给用户"
                        width="540px"
                        :visible="dialog.visible" 
                        :close-on-click-modal="false" 
                        :show-close="false">

                <div class="dialog-body-inner">

                    <div class="single-row">
                        <label>目标产品：</label>
                        <span class="s-bold">{{ dialog.fundName }}</span>
                    </div>

                    <div class="single-row">

                        <label>分享用户：</label>
                        <br>
                        <el-transfer filter-placeholder="关键字搜索"
                                     v-model="dialog.selected"
                                     :titles="['可选用户', '已选用户']"
                                     :data="dialog.users"
                                     :props="{ key: 'userId', label: 'userName' }" filterable></el-transfer>
                    </div>
                </div>
                <div slot="footer">
                    <el-button type="primary" size="small" @click="saveUser">确定</el-button>
                    <el-button size="small" @click="unsaveUser">取消</el-button>
                </div>
            </el-dialog>
        </template>

    </div>

    <div class="dialog-account">

        <template>
            <el-dialog :visible="dialog.visible" :close-on-click-modal="false" title="绑定账号" :show-close="false"
                width="540px">
                <div class="dialog-body-inner">
                    <div class="single-row">
                        <label>目标产品：</label>
                        <span class="s-bold">{{ dialog.fundName }}</span>
                    </div>
                    <div class="single-row">
                        <label>选择账号：</label>
                        <br>
                        <el-transfer filter-placeholder="关键字搜索"
                                     v-model="dialog.selected"
                                     :titles="['可选账号', '已选账号']"
                                     :data="dialog.accounts"
                                     :props="{ key: 'accountId', label: 'accountName' }" filterable></el-transfer>

                    </div>
                </div>
                <div slot="footer">
                    <el-button type="primary" size="small" @click="saveAccount">确定</el-button>
                    <el-button size="small" @click="unsaveAccount">取消</el-button>
                </div>
            </el-dialog>
        </template>

    </div>

    <div class="dialog-template">

        <template>
            <el-dialog :visible="dialog.visible" :close-on-click-modal="false" title="绑定报告模板" :show-close="false"
                width="420px">
                <div class="dialog-body-inner">
                    <div class="single-row">
                        <label>目标产品</label>
                        <span class="s-bold">{{ dialog.fundName }}</span>
                    </div>
                    <div class="single-row">
                        <label>选择模板</label>
                        <el-select v-model="dialog.selected" placeholder="选择需要绑定的模板" :clearable="true"
                            :filterable="true" multiple collapse-tags>
                            <el-option v-for="(item, item_idx) in dialog.templates" :key="item_idx"
                                :label="item.templateName" :value="item.templateId"></el-option>
                        </el-select>
                    </div>
                </div>
                <div slot="footer">
                    <el-button type="primary" size="small" @click="saveTemplate">确定</el-button>
                    <el-button size="small" @click="unsaveTemplate">取消</el-button>
                </div>
            </el-dialog>
        </template>

    </div>

    <div class="dialog-valuation">

        <template>
            <el-dialog :visible="dialog.visible" :close-on-click-modal="false" title="配置估值方式" :show-close="false"
                width="420px">
                <div class="dialog-body-inner">
                    <div class="single-row">
                        <label>目标产品</label>
                        <span class="s-bold">{{ dialog.fundName }}</span>
                    </div>
                    <div class="single-row">
                        <label>选择估值方式号</label>
                        <el-select v-model="dialog.selected" placeholder="选择估值方式" :filterable="true">
                            <el-option v-for="(item, item_idx) in dialog.methods" :key="item_idx" :label="item.mean"
                                :value="item.code"></el-option>
                        </el-select>
                    </div>
                </div>
                <div slot="footer">
                    <el-button type="primary" size="small" @click="saveValuation">确定</el-button>
                    <el-button size="small" @click="unsaveValuation">取消</el-button>
                </div>
            </el-dialog>
        </template>

    </div>

    <div class="dialog-report">
        <el-dialog v-if="dialog.visible" class="report-dialog" :title="dialog.title" ref="reportDialog" width="1000px"
            :visible.sync="dialog.visible">
            <div class="container-content"></div>
        </el-dialog>
    </div>

</div>