
const IView = require('../../../component/iview').IView;
const SmartTable = require('../../../libs/table/smart-table').SmartTable;
const ColumnCommonFunc = require('../../../libs/table/column-common-func').ColumnCommonFunc;
const repoFund = require('../../../repository/fund').repoFund;
const repoAccount = require('../../../repository/account').repoAccount;
const repoOrg = require('../../../repository/org').repoOrg;
const repoUser = require('../../../repository/user').repoUser;
const repoIndicator = require('../../../repository/indicator').repoIndicator;
const { BrowserWindow } = require('@electron/remote');

const FundTypes = {

    mother: { code: 1, mean: '母基金' },
    child: { code: 2, mean: '子基金' },
    virtual: { code: 3, mean: '虚拟子基金' },
};

const ValuationMethods = {

    unassess: { code: -1, mean: '不估值' },
    t0: { code: 0, mean: 'T+0' },
    t1: { code: 1, mean: 'T+1' },
    t2: { code: 2, mean: 'T+2' },
};

const BasisReferences = {

    zz500: { code: 'SZSE.305', mean: '中证500' },
    hs300: { code: 'SZSE.399300', mean: '沪深300' },
    sz50: { code: 'SHSE.000016', mean: '上证50' },
};

const Settings = { unset: '---' };


class Organization {

    constructor(struc) {

        this.id = struc.id;
        this.orgName = struc.orgName;
    }
}

class Account {

    constructor(struc) {

        this.accountId = struc.accountId;
        this.accountName = struc.accountName;
        this.assetType = struc.assetType;
        this.financeAccountName = struc.financeAccountName;
        this.credit = !!struc.credit;
    }
}

class ReportTemplate {

    constructor(struc) {

        this.templateId = struc.templateId;
        this.templateName = struc.templateName;
        this.default = false;
    }
}

class SysUser {

    constructor(struc) {
        
        this.userId = struc.userId;
        this.userName = struc.userName;
        this.fullName = struc.fullName;

        this.orgId = struc.orgId;
        this.orgName = struc.orgName;
        this.status = struc.status;
    }
}

class FundUser extends SysUser {

    constructor(struc) {

        super(struc);
        this.shareType = struc.shareType;
    }
}

class Fund {

    /**
     * @param {*} struc 产品基础信息
     * @param {*} detail_struc 产品运营参数详情信息
     */
    constructor(struc, detail_struc) {

        /**
         * 基金基本信息
         */

        this.amacCode = struc.amacCode;
        this.basisReference = struc.basisReference;
        this.closedFlag = struc.closedFlag;
        this.establishedDay = struc.establishedDay;
        this.fundManager = struc.fundManager;
        this.fundName = struc.fundName;
        this.fundType = struc.fundType;
        this.id = struc.id;
        this.orgId = struc.orgId;
        this.orgName = struc.orgName;
        this.fundOrganization = struc.orgName;
        this.reportTemplates = this.formalizeTemplates(struc.reportTemplates);
        this.riskEnable = struc.riskEnable;
        this.strategyType = struc.strategyType;
        this.valuation = struc.valuation;

        /**
         * 基金详细运行数据
         */

        if (detail_struc === null || detail_struc === undefined) {
            detail_struc = {};
        }

        this.available = detail_struc.available;
        this.balance = detail_struc.balance;
        this.closeProfit = detail_struc.closeProfit;
        this.commission = detail_struc.commission;
        this.frozenCommission = detail_struc.frozenCommission;
        this.frozenMargin = detail_struc.frozenMargin;
        this.inMoney = detail_struc.inMoney;
        this.loanBuyBalance = detail_struc.loanBuyBalance;
        this.loanSellBalance = detail_struc.loanSellBalance;
        this.loanSellQuota = detail_struc.loanSellQuota;
        this.margin = detail_struc.margin;
        this.marketValue = detail_struc.marketValue;
        this.nav = detail_struc.nav;
        this.navRealTime = detail_struc.navRealTime;
        this.outMoney = detail_struc.outMoney;
        this.positionProfit = detail_struc.positionProfit;
        this.preBalance = detail_struc.preBalance;
        this.risePercent = detail_struc.risePercent;
        this.withdrawQuota = detail_struc.withdrawQuota;

        /**
         * 绑定交易账号
         */

        this.accounts = this.extractAccounts(struc.accounts);
        
        /**
         * 分享给的用户
         */

        var all_users = this.formalizeUsers(struc.users);
        var creator = all_users.find(x => x.shareType == 0);
        this.creatorUserName = creator ? creator.userName : null;
        this.creatorUserFullName = this.creatorUserName;
        this.users = all_users.filter(x => x !== creator);
    }

    /**
     * @param {Array} accounts 
     */
    extractAccounts(accounts) {

        if (!Array.isArray(accounts)) {
            return [];
        }

        return accounts.map(x => new Account(x));
    }

    /**
     * @param {Array} templates 
     */
    formalizeTemplates(templates) {
        return templates instanceof Array ? templates.map(x => new ReportTemplate(x)) : [];
    }

    /**
     * @param {Array} users 
     */
    formalizeUsers(users) {
        return users instanceof Array ? users.map(x => new FundUser(x)) : [];
    }
}

class Controller extends IView {

    get workingFund() {
        return this._workingFund;
    }

    /**
     * is an editing, not a creation
     */
    get isEditing() {
        return this._workingFund instanceof Fund;
    }

    /**
     * @param {Fund} fund 
     */
    setAsWorkingFund(fund) {
        this._workingFund = fund;
    }

    clearWorkingFund() {
        delete this._workingFund;
    }

    get $editform() {
        return this.dialogEditApp.$refs.editform;
    }

    constructor(view_name) {

        super(view_name, false, '产品管理');

        this.fundCloseStatus = {

            ok: { code: false, mean: '正常' },
            closed: { code: true, mean: '已清盘' },
        };

        this.fundRiskControlStatus = {

            enabled: { code: false, mean: '已启用' },
            disabled: { code: true, mean: '已禁用' },
        };

        /**
         * 机构列表
         */
        this.orgs = [new Organization({})];
        this.orgs.pop();
    }

    /**
     * @param {Fund} record 
     */
    identifyRecord(record) {
        return record.id;
    }

    isFundClosed(closed_flag) {
        return closed_flag === this.fundCloseStatus.closed.code;
    }

    isFundDisabled(disabled_flag) {
        return disabled_flag === this.fundRiskControlStatus.disabled.code;
    }

    createToolbarApp() {

        this.searching = {
            keywords: '',
        };

        var pagination = this.systemSetting.tablePagination;
        this.paging = {

            pageSizes: pagination.pageSizes,
            pageSize: pagination.pageSize,
            layout: pagination.layout,
            total: 0,
            page: 1,
        };

        this.toolbar = new Vue({

            el: this.$container.querySelector('.view-fund-root > .toolbar'),

            data: {
                searching: this.searching,
                paging: this.paging,
            },

            methods: this.helper.fakeVueInsMethod(this, [

                this.create,
                this.filterRecords,
                this.exportSome,
                this.refresh,
                this.config,
                this.handlePageSizeChange,
                this.handlePageChange,
            ]),
        });
    }

    /**
     * @param {Fund} record 
     */
    async handleBindReportTemplate(record) {

        this.setAsWorkingFund(record);
        var selected_template_ids = record.reportTemplates.map(x => x.templateId);

        if (this.bindTemplateApp) {

            this.reptdata.visible = true;
            this.reptdata.selected = selected_template_ids;
            this.reptdata.fundName = record.fundName;
            this.requestSetTemplates();
            return;
        }

        this.reptdata = {

            visible: false,
            selected: [],
            fundName: null,
            templates: [new ReportTemplate({})],
        };

        this.reptdata.templates.pop();
        this.reptdata.selected = selected_template_ids;
        this.reptdata.fundName = record.fundName;

        this.bindTemplateApp = new Vue({

            el: this.$container.querySelector('.view-fund-root > .dialog-template'),
            data: {
                dialog: this.reptdata,
            },
            methods: this.helper.fakeVueInsMethod(this, [

                this.saveTemplate,
                this.unsaveTemplate,
            ]),
        });

        this.bindTemplateApp.$nextTick(() => { this.reptdata.visible = true; });
        this.requestSetTemplates();
    }

    async requestSetTemplates() {

        var resp = await repoIndicator.getReportTemplateList({ key_word: '', page_no: 1, page_size: 99999 });
        if (resp.errorCode == 0) {

            let data_list = resp.data;
            let templates = data_list instanceof Array ? data_list.map(x => new ReportTemplate({ templateId: x.id, templateName: (x.report_info || {}).title_format })) : [];
            this.reptdata.templates.clear();
            this.reptdata.templates.merge(templates);
        }
        else {
            this.interaction.showError('获取模板列表发生异常：' + resp.errorMsg);
            this.reptdata.templates.clear();
        }
    }

    async saveTemplate() {

        var fund_id = this.workingFund.id;
        var template_ids = this.reptdata.selected;
        var post_data = {};
        template_ids.forEach(tid => { post_data[tid] = 0 });
        var resp = await repoIndicator.bindTemplate(fund_id, post_data);

        if (resp.errorCode == 0) {

            let choosed_templates = this.reptdata.templates.filter(x => this.reptdata.selected.includes(x.templateId));
            this.tableObj.updateRow({ id: fund_id, reportTemplates: this.helper.deepClone(choosed_templates) });
            this.interaction.showSuccess('模板已绑定');
        }
        else {
            this.interaction.showError('绑定模板发生异常：' + resp.errorMsg);
        }

        this.reptdata.visible = false;
        this.clearWorkingFund();
    }

    unsaveTemplate() {

        this.reptdata.visible = false;
        this.clearWorkingFund();
    }

    /**
     * @param {Fund} record 
     * @param {Array} templates 
     */
    formatReportTemplate(record, templates) {

        var content;
        if (templates.length == 0) {
            content = Settings.unset;
        }
        else if (templates.length <= 2) {
            content = templates.map(x => x.templateName).join(' ,');
        }
        else {
            content = `${ templates.slice(0, 2).map(x => x.templateName).join(' ,') }, 等${ templates.length }`;
        }

        return `<a class="s-underline s-cp" event.onclick="handleBindReportTemplate">${ content }</a>`;
    }

    /**
     * @param {Fund} record 
     * @param {Array} templates 
     */
    formatReportTemplateText(record, templates) {
        return templates.length == 0 ? '' : templates.map(x => x.templateName).join(',');
    }

    /**
     * @param {Fund} record 
     * @param {*} fund_type 
     */
    formatFundType(record, fund_type) {

        switch (fund_type) {

            case FundTypes.mother.code: return `<a class="s-flag s-bg-green">${ FundTypes.mother.mean }</a>`;
            case FundTypes.child.code: return `<a class="s-flag s-bg-orange">${ FundTypes.child.mean }</a>`;
            case FundTypes.virtual.code: return `<a class="s-flag s-bg-red">${ FundTypes.virtual.mean }</a>`;
        }

        return Settings.unset;
    }

    /**
     * @param {Fund} record 
     * @param {*} fund_type 
     */
    formatFundTypeText(record, fund_type) {

        switch (fund_type) {

            case FundTypes.mother.code: return FundTypes.mother.mean;
            case FundTypes.child.code: return FundTypes.child.mean;
            case FundTypes.virtual.code: return FundTypes.virtual.mean;
        }

        return Settings.unset;
    }

    /**
     * @param {Fund} record 
     * @param {Array<FundUser>} users 
     */
    formatShares(record, users) {

        var content;
        var subset = users.filter(x => !!x.fullName);

        if (subset.length == 0) {
            content = Settings.unset;
        }
        else if (subset.length <= 2) {
            content = subset.map(x => x.fullName).join(' ,');
        }
        else {
            content = `${ subset.slice(0, 2).map(x => x.fullName).join(' ,') }, 等${ subset.length }人`;
        }

        return `<a class="s-underline s-cp" event.onclick="showBindUsersDialog">${ content }</a>`;
    }

    /**
     * @param {Fund} record 
     * @param {Array<FundUser>} users 
     */
    formatSharesText(record, users) {

        var subset = users.filter(x => !!x.fullName);
        return subset.length == 0 ? '' : subset.map(x => x.fullName).join(',');
    }

    /**
     * @param {Fund} record 
     * @param {Array<Account>} accounts 
     */
    formatAccount(record, accounts) {

        var content;
        if (accounts.length == 0) {
            content = Settings.unset;
        }
        else if (accounts.length == 1) {
            content = accounts[0].accountName;
        }
        else {
            content = `${ accounts[0].accountName }, 等${ accounts.length }个`;
        }

        return `<a title="绑定账号" class="s-underline s-cp" event.onclick="showBindAccountDialog">${ content }</a>`;
    }

    /**
    * @param {Fund} record
    * @param {Array<Account>} accounts 
    */
    formatAccountText(record, accounts) {
        return accounts.length == 0 ? '' : accounts.map(x => x.accountName).join(',');
    }

    /**
     * @param {Fund} record 
     * @param {*} valuation_method 
     */
    formatValuation(record, valuation_method) {

        var content = this.formatValuationText(record, valuation_method);
        return `<a class="s-underline s-cp" event.onclick="showValuationConfigDialog">${ content }</a>`;
    }

    /**
     * @param {Fund} record 
     * @param {*} valuation_method 
     */
    formatValuationText(record, valuation_method) {
        
        var content;
        switch (valuation_method) {

            case ValuationMethods.unassess.code: content = ValuationMethods.unassess.mean; break;
            case ValuationMethods.t0.code: content = ValuationMethods.t0.mean; break;
            case ValuationMethods.t1.code: content = ValuationMethods.t1.mean; break;
            case ValuationMethods.t2.code: content = ValuationMethods.t2.mean; break;
            default: content = Settings.unset;
        }

        return content;
    }

    /**
     * @param {Fund} record 
     * @param {Array<FundUser>} users 
     * @param {String} handler_name 
     */
    showValuationConfigDialog(record) {

        this.setAsWorkingFund(record);

        if (this.configValApp) {

            this.cfgdata.visible = true;
            this.cfgdata.selected = record.valuation;
            this.cfgdata.fundName = record.fundName;
            return;
        }

        this.cfgdata = {

            visible: false,
            selected: null,
            fundName: null,
            methods: this.helper.deepClone(ValuationMethods),
        };

        this.cfgdata.selected = record.valuation;
        this.cfgdata.fundName = record.fundName;

        this.configValApp = new Vue({

            el: this.$container.querySelector('.view-fund-root > .dialog-valuation'),
            data: {
                dialog: this.cfgdata,
            },
            methods: this.helper.fakeVueInsMethod(this, [

                this.saveValuation,
                this.unsaveValuation,
            ]),
        });

        this.configValApp.$nextTick(() => { this.cfgdata.visible = true; });
    }

    saveValuation() {

        var fund = this.workingFund;
        var method = this.cfgdata.selected;

        fund.valuation = method;
        this.submit(fund, () => {

            /**
             * 此行代码意在使table row update检测到数据产生过变更，最终执行更新
             */
            fund.valuation = Settings.unset;
            this.tableObj.updateRow({ id: fund.id, valuation: method });
        });

        this.cfgdata.visible = false;
        this.clearWorkingFund();
    }

    unsaveValuation() {

        this.cfgdata.visible = false;
        this.clearWorkingFund();
    }

    /**
     * @param {Fund} record 
     */
    switchFundStatus(record) {

        var new_flag = this.isFundClosed(record.closedFlag) ? this.fundCloseStatus.ok.code : this.fundCloseStatus.closed.code;
        record.closedFlag = new_flag;

        this.submit(record, () => {
            
            /**
             * 此行代码意在使table row update检测到数据产生过变更，最终执行更新
             */
            record.closedFlag = Settings.unset;
            this.tableObj.updateRow({ id: record.id, closedFlag: new_flag });
        });
    }

    /**
     * @param {Fund} record 
     * @param {*} closed_flag 
     */
    formatFundStatus(record, closed_flag) {
        
        var classes = [];
        this.isFundClosed(closed_flag) && classes.push('is-checked');
        return this.helperUi.formatSwitchButton(this.switchFundStatus, classes.join(' '));
    }

    /**
     * @param {Fund} record 
     * @param {*} closed_flag 
     */
    formatFundStatusText(record, closed_flag) {
        return this.isFundClosed(closed_flag) ? '已清盘' : '存续中';
    }

    /**
     * @param {Fund} record 
     */
    switchRiskControlOption(record) {

        var new_flag = this.isFundDisabled(record.riskEnable) ? this.fundRiskControlStatus.enabled.code : this.fundRiskControlStatus.disabled.code;
        record.riskEnable = new_flag;

        this.submit(record, () => {

            /**
             * 此行代码意在使table row update检测到数据产生过变更，最终执行更新
             */
            record.riskEnable = Settings.unset;
            this.tableObj.updateRow({ id: record.id, riskEnable: new_flag });
        });
    }

    /**
     * @param {Fund} record 
     * @param {Boolean} risk_control_enabled 
     */
    formatRiskControled(record, risk_control_enabled) {
        
        var classes = [];
        risk_control_enabled && classes.push('is-checked');
        return this.helperUi.formatSwitchButton(this.switchRiskControlOption, classes.join(' '));
    }
    /**
     * @param {Fund} record 
     * @param {Boolean} risk_control_enabled 
     */
    formatRiskControledText(record, risk_control_enabled) {
        return risk_control_enabled ? '已启用' : '禁用';
    }

    createReportDialog() {

        const drag = require('../../../directives/drag');
        const NumberMixin = require('../../../mixin/number').NumberMixin;
        const UiBuisinessMixin = require('../../../mixin/ui-business').UiBuisinessMixin;

        this.reportdlg = { visible: false, title: '产品报告' };
        this.reportDialog = new Vue({

            el: this.$container.querySelector('.view-fund-root > .dialog-report'),
            data: {
                menu: null,
                dialog: this.reportdlg,
            },
            directives: {
                drag,
            },
            mixins: [NumberMixin, UiBuisinessMixin],
        });
    }

    /**
     * 
     * @param {Fund} fund 
     */
    viewReportTemplate(fund) {

        if (!this.reportDialog) {
            this.createReportDialog();
        }

        this.reportdlg.visible = true;
        this.reportdlg.title = '产品报告 > ' + fund.fundName;

        var templates = fund.reportTemplates;
        var restricts = templates.length > 0 ? templates.map(x => x.templateId).join(',') : '';
        var default_template_id = templates.length > 0 ? templates[0].templateId : null;
        var param = {

            identity: fund.id,
            identityName: fund.fundName,
            type: 'product',
            restricts: restricts,
            defaultTemplateId: default_template_id,
        };

        setTimeout(() => { this.buildReportTemplate(param); }, 200);
    }

    buildReportTemplate(param) {

        const NewReport = require('../../admin/new-report');

        if (this.newReport) {

            this.newReport.dispose();
            delete this.newReport;
        }

        this.newReport = new NewReport('@admin/new-report', true, { title: `产品报告 > ${ param.fundName }` });
        this.newReport.trigger('setContextData', param);
        this.newReport.loadBuild(this.reportDialog.$refs.reportDialog.$el.querySelector('.container-content'));
    }

    /**
     * @param {Fund} record 
     */
    async showBindAccountDialog(record) {

        this.setAsWorkingFund(record);
        var selected = record.accounts.map(x => x.accountId);

        if (this.bindAccountApp) {

            this.acntdata.visible = true;
            this.acntdata.selected = selected;
            this.acntdata.fundName = record.fundName;
            this.requestSetAccounts();
            return;
        }

        this.acntdata = {

            visible: false,
            selected: [],
            fundName: null,
            accounts: [new Account({})],
        };

        this.acntdata.accounts.pop();
        this.acntdata.selected = selected;
        this.acntdata.fundName = record.fundName;

        this.bindAccountApp = new Vue({

            el: this.$container.querySelector('.view-fund-root > .dialog-account'),
            data: {
                dialog: this.acntdata,
            },
            methods: this.helper.fakeVueInsMethod(this, [

                this.saveAccount,
                this.unsaveAccount,
            ]),
        });

        this.bindAccountApp.$nextTick(() => { this.acntdata.visible = true; });
        this.requestSetAccounts();
    }

    async requestSetAccounts() {

        var resp = await repoAccount.getSimpleAccountList(false, this.workingFund.orgId);
        if (resp.errorCode == 0) {

            let data_list = resp.data;
            let accounts = data_list instanceof Array ? data_list.map(x => new Account(x)) : [];
            this.acntdata.accounts.clear();
            this.acntdata.accounts.merge(accounts);
        }
        else {
            this.interaction.showError('获取机构账号列表发生异常：' + resp.errorMsg);
            this.acntdata.accounts.clear();
        }
    }

    async saveAccount() {

        var fund_id = this.workingFund.id;
        var account_ids = this.acntdata.selected;
        var resp = await repoFund.bindAccount2Fund(fund_id, account_ids);

        if (resp.errorCode == 0) {

            /**
             * [{ accountId, accountName, assetType, financeAccount, fundId, fundName, fundType, id }]
             */
            let { data } = resp;
            let feedbacks = data instanceof Array ? data : [];
            let feedback_ids = feedbacks.map(x => x.accountId);
            let accounts = this.acntdata.accounts.filter(x => feedback_ids.includes(x.accountId));
            this.tableObj.updateRow({ id: fund_id, accounts: accounts });            
            let isPartial = feedback_ids.length < account_ids.length;
            
            if (isPartial) {
                this.interaction.showSuccess(`部分绑定成功，选择个数 = ${account_ids.length}，已绑定 = ${feedback_ids.length}`);
            }
            else {
                this.interaction.showSuccess('账号已绑定');
            }
        }
        else {
            this.interaction.showError('绑定账号发生异常：' + resp.errorMsg);
        }

        this.acntdata.visible = false;
        this.clearWorkingFund();
    }

    unsaveAccount() {

        this.acntdata.visible = false;
        this.clearWorkingFund();
    }

    /**
     * @param {Fund} record 
     */
    async showBindUsersDialog(record) {

        try {
            await this.resetAllUsers();
        }
        catch(ex) {
            console.error(ex);
        }

        this.setAsWorkingFund(record);
        var all = this.allUsers.filter(usr => usr.orgId == record.orgId && usr.userName !== this.workingFund.creatorUserName)
                                .map(usr => ({ userId: usr.userId, userName: `${usr.userName} / ${usr.fullName}` }));

        var rusers = record.users;
        var selected = all.filter(usr => rusers.some(item => item.userId == usr.userId)).map(usr => usr.userId);

        if (this.bindUserApp) {

            this.shrdata.visible = true;
            this.shrdata.fundName = record.fundName;
            this.shrdata.users = all;
            this.shrdata.selected = selected;
            return;
        }

        this.shrdata = {

            visible: true,
            fundName: null,
            users: [],
            selected: [],
        };

        this.shrdata.fundName = record.fundName;
        this.shrdata.users = all;
        this.shrdata.selected = selected;

        this.bindUserApp = new Vue({

            el: this.$container.querySelector('.view-fund-root > .dialog-user'),
            data: {
                dialog: this.shrdata,
            },
            methods: this.helper.fakeVueInsMethod(this, [

                this.saveUser,
                this.unsaveUser,
            ]),
        });

        this.bindUserApp.$nextTick(() => { this.shrdata.visible = true; });
    }

    async saveUser() {

        var fund = this.workingFund;
        var fund_id = fund.id;
        var selected = this.shrdata.selected;

        let resp = await repoFund.shareFund2User(fund_id, selected);
        if (resp.errorCode == 0) {

            let selected_users = this.allUsers.filter(x => selected.includes(x.userId)).map(x => new FundUser(x));
            this.tableObj.updateRow({ id: fund_id, users: selected_users });
            this.interaction.showSuccess('交易员已绑定到产品');
        }
        else {
            this.interaction.showError('绑定交易员发生异常：' + resp.errorMsg);
        }

        this.shrdata.visible = false;
        this.clearWorkingFund();
    }

    unsaveUser() {

        this.shrdata.visible = false;
        this.clearWorkingFund();
    }

    create() {

        this.clearWorkingFund();
        this.showEditDialog();
    }

    /**
     * @param {Fund} record 
     */
    edit(record) {

        this.setAsWorkingFund(record);
        this.showEditDialog(record);
    }

    /**
     * @param {Fund} record 
     */
    openRiskControlSetting(record) {

        let fund_id = this.identifyRecord(record);
        let fund_name = record.fundName;

        this.execRskWinOpen({

            type: this.systemEnum.identityType.fund.code,
            identity: fund_id,
            name: fund_name,
        });
    }

    execRskWinOpen({ type, identity, name }) {

        let args = { type, identity, name, action: 'identity' };
        let title = '风控设置';

        if (this.winRsk && !this.winRsk.isDestroyed()) {
            this.winRsk.show();
            this.winRsk.webContents.send('set-context-data', args);
        }

        var window_options = { width: 940, height: 620, minimizable: false, maximizable: false, highlight: true };
        this.renderProcess.once(this.systemEvent.huntWinTabViewFromRender, (event, win_id) => {

            this.winRsk = BrowserWindow.fromId(win_id);
            this.winRsk.on('closed', () => { this.winRsk = null; });
            setTimeout(() => { this.winRsk.webContents.send('set-context-data', args); }, 1000);
        });

        this.renderProcess.send(this.systemEvent.huntWinTabViewFromRender, '@admin/risk-dialog', title, window_options);
    }

    /**
     * @param {Fund} record 
     */
    delete(record) {

        this.interaction.showConfirm({

            title: '删除确认',
            message: `删除产品：${ record.fundName } ?`,
            confirmed: () => { this.deleteRecord(record); }
        });
    }

    /**
     * @param {Fund} record 
     */
    async deleteRecord(record) {

        let record_id = this.identifyRecord(record);
        let result = await repoFund.deleteFund(record_id);
        if (result.errorCode == 0) {

            this.interaction.showSuccess('产品已删除');
            this.tableObj.deleteRow(record_id);
            this.clearWorkingFund();
        }
        else {
            this.interaction.showError('删除操作错误：' + result.errorMsg);
        }
    }

    setupTable() {

        this.helper.extend(this, ColumnCommonFunc);
        var $table = this.$container.querySelector('.view-fund-root > table');

        if (this.isNormal) {

            let $col_brokers = $table.querySelectorAll('th[condition="broker"]');
            $col_brokers.forEach($col => { $col.remove(); });
        }
        else {

            let $col_normals = $table.querySelectorAll('th[condition="normal"]');
            $col_normals.forEach($col => { $col.remove(); });
        }

        this.tableObj = new SmartTable($table, this.identifyRecord, this, {

            tableName: 'smt-apl',
            displayName: this.title,
            enableConfigToolkit: true,
            recordsFiltered: (filtered_count) => { this.paging.total = filtered_count; },
            rowSelected: (product) => { this.handleFundChange(product); },
        });

        this.tableObj.setPageSize(this.paging.pageSize);
        this.tableObj.setMaxHeight(200);
    }

    /**
     * @param {Fund} fund 
     * @param {Boolean} forced 
     */
    handleFundChange(fund, forced) {

        if (this.contextInfo && this.contextInfo.id === fund.id && forced !== true) {
            return;
        }

        this.contextInfo = fund;
        var accounts = fund.accounts;
        var context_info = {

            fundId: fund.id,
            accounts: accounts.map(x => {
                return {
                    accountId: x.accountId,
                    accountName: x.accountName,
                    assetType: x.assetType,
                    isCredit: x.credit,
                };
            })
        };

        this.trigger(this.systemEvent.viewContextChange, context_info);
    }

    async requestProducts() {

        if (this._isRequesting) {
            return;
        }

        this._isRequesting = true;
        var loading = this.interaction.showLoading({ text: `获取产品列表...` });

        try {

            let resp = await repoFund.getAll();
            if (resp.errorCode === 0) {

                let basics = resp.data || [];
                if (basics instanceof Array && basics.length > 0) {

                    let all_fund_ids = basics.map(x => x.id);
                    let resp_detail = await repoFund.getFundDetail(all_fund_ids);
                    if (resp_detail.errorCode === 0) {

                        let details = resp_detail.data || [];
                        this.funds = this.combineFundStrucs(basics, details);
                        this.tableObj.refill(this.funds);
                        this.requestSetFundUserNames();
                    }
                    else {
                        this.tableObj.refill(basics.map(x => new Fund(x)));
                    }
                }
            }
            else {
                this.interaction.showError(`获取产品列表失败，${resp.errorCode}/${resp.errorMsg}`);
            }
        }
        catch (ex) {

            this.interaction.showError(`获取产品列表异常`);
            console.error(ex);
        }
        finally {

            this._isRequesting = false;
            loading.close();
        }
    }

    async resetAllUsers() {

        let users = await this.requestConstructAllUsers();

        // 去掉管理员自己( issued by @yking & fixed by @steven )
        users.remove(x => x instanceof SysUser && x.userId == this.userInfo.userId);

        let users_map = {};
        users.forEach(usr => { users_map[usr.userId] = usr; });

        this.allUsers = users;
        this.usersMap = users_map;
    }

    async requestSetFundUserNames() {

        if (this.allUsers === undefined) {

            try {
                await this.resetAllUsers();
            }
            catch(ex) {
                console.error(ex);
            }
        }

        this.funds.forEach(the_fund => {

            let fund_org_id = the_fund.orgId;
            let fund_users = the_fund.users;

            /**
             * 当前的fund users为仅含：userId / userName / shareType 字段信息的原始自带信息（其他字段信息皆为undefined）
             */

            fund_users.forEach(fusr => {
                
                let matched = this.usersMap[fusr.userId];
                if (matched instanceof SysUser) {
                    // 为fund上自带的user结构，作字段扩展
                    this.helper.extend(fusr, matched);
                }
            });

            let org_users = fund_users.filter(x => x.orgId === fund_org_id);
            let create_user = this.allUsers.find(x => x.userName == the_fund.creatorUserName);
            let full_name = create_user ? create_user.fullName : the_fund.creatorUserName;
            this.tableObj.updateRow({ id: the_fund.id, creatorUserFullName: full_name, users: org_users });
        });
    }

    /**
     * @returns {Array<SysUser>}
     */
    async requestConstructAllUsers() {

        let users = [];
        let resp = await repoUser.getAll();
        if (resp.errorCode === 0) {

            let all_users = resp.data || [];
            users.merge(all_users.map(usr => {

                let userId = usr.id;
                let userName = usr.username;
                let fullName = usr.fullName;
                let orgId = usr.orgId;
                let orgName = usr.orgName;
                let status = usr.status;

                return new SysUser({ userId, userName, fullName, orgId, orgName, status });
            }));
        }

        return users;
    }

    /**
     * @param {Array} basics 
     * @param {Array} details 
     * @returns {Array<Fund>}
     */
    combineFundStrucs(basics, details) {

        let funds = [];
        basics.forEach(the_basic => {
            let the_detail = details.find(x => x.fundId == the_basic.id);
            funds.push(new Fund(the_basic, the_detail));
        });
        return funds;
    }

    handlePageSizeChange() {
        this.tableObj.setPageSize(this.paging.pageSize);
    }

    handlePageChange() {
        this.tableObj.setPageIndex(this.paging.page);
    }

    filterRecords() {

        let thisObj = this;
        let keywords = this.searching.keywords;

        /**
         * @param {Fund} record 
         */
        function filterByPinyin(record) {
            return thisObj.helper.pinyin(record.fundName).indexOf(keywords) >= 0;
        }

        this.tableObj.setKeywords(keywords, false);
        this.tableObj.mixedFilter(filterByPinyin, 'or');
    }

    exportSome() {
        this.tableObj.exportAllRecords(`产品列表-${new Date().format('yyyyMMdd')}`);
    }

    refresh() {

        this.searching.keywords = null;
        this.paging.page = 1;
        this.requestProducts();
    }

    config() {
        this.tableObj.showColumnConfigPanel();
    }

    setHeight(height) {

        this.tableObj.setMaxHeight(height - 85);
        this.tableObj.scroll2Top(0);
    }

    /**
     * @param {Fund} changed 基金类实例（或具有同样结构的一般JSON数据对象）
     * @param {Function} callback 提交基金信息处理成功，回调函数，参数(revised_fund)
     */
    async submit(changed, callback) {

        let is_create = this.helper.isNone(this.identifyRecord(changed));
        let result = is_create ? await repoFund.createFund(changed)
                               : await repoFund.updateFund(changed);

        if (result.errorCode != 0) {
            this.interaction.showError('产品信息保存失败：' + result.errorMsg);
        }
        else {
            
            if (is_create) {
                callback(new Fund(result.data));
            }
            else {
                callback(result.data);
            }

            this.interaction.showSuccess('产品信息已保存');
        }
    }

    /**
     * @param {Boolean} is_create 
     * @param {Fund} fund_response 
     */
    updateBack2Table(is_create, fund_response) {
                
        try {

            if (is_create) {

                if (!(fund_response.accounts instanceof Array)) {
                    fund_response.accounts = this.helper.deepClone(this.formdata.accounts);
                }

                if (!(fund_response.users instanceof Array)) {
                    fund_response.users = this.helper.deepClone(this.formdata.users);
                }

                this.tableObj.insertRow(fund_response);
            }
            else {
                this.tableObj.updateRow(fund_response);
            }
        }
        catch (ex) {
            console.error(ex);
        }
    }

    save() {

        this.$editform.validate(result => {

            if (!result) {
                return;
            }

            let is_create = this.helper.isNone(this.identifyRecord(this.formdata));
            this.submit(this.formdata, (fund_response) => {

                this.editdg.visible = false;
                this.updateBack2Table(is_create, fund_response);
            });
        });
    }

    unsave() {
        this.editdg.visible = false;
    }

    handleOrgChange() {

        var matched = this.orgs.find(x => x.id == this.formdata.orgId);
        this.formdata.orgName = matched instanceof Organization ? matched.orgName : null;
        this.formdata.fundOrganization = matched instanceof Organization ? matched.orgName : null;
    }

    /**
     * @param {Fund} fund 
     */
    showEditDialog(fund) {

        if (this.dialogEditApp) {

            this.helper.extend(this.formdata, fund ? this.helper.deepClone(fund) : new Fund({}));
            this.editdg.title = fund ? '编辑产品信息' : '新建产品';
            this.editdg.visible = true;
            setTimeout(() => { this.$editform.clearValidate(); }, 200);
            return;
        }

        const rules = {

            fundName: { type: 'string', required: true, message: '请输入产品名称' },
            amacCode: { type: 'string', required: true, message: '请输入备案号' },
            establishedDay: { type: 'string', required: true, message: '请选择成立日期' },
            orgId: { type: 'number', required: true, message: '请选择管理机构' },
            fundManager: { type: 'string', required: true, message: '请输入基金经理' },
            strategyType: { type: 'string', required: true, message: '请输入策略类型' },
            fundType: { type: 'number', required: true, message: '请选择基金类型' },
            basisReference: { type: 'string', required: true, message: '请选择参考基准' },
        };

        this.editdg = { visible: false, title: null };
        this.formdata = new Fund({});

        this.dialogEditApp = new Vue({

            el: this.$container.querySelector('.view-fund-root > .dialog-editing'),

            data: {

                dialog: this.editdg,
                rules: rules,
                basisReferences: this.helper.deepClone(BasisReferences),
                fundTypes: this.helper.deepClone(FundTypes),
                fundCloseStatus: this.helper.deepClone(this.fundCloseStatus),
                formd: this.formdata,
                orgs: this.orgs,
            },

            computed: {

                isOrgFixed: () => {
                    return this.isOrgFixed || !this.helper.isNone(this.formdata.id);
                }
            },

            methods: this.helper.fakeVueInsMethod(this, [

                this.handleOrgChange,
                this.save,
                this.unsave,
            ]),
        });

        this.helper.extend(this.formdata, fund ? this.helper.deepClone(fund) : new Fund({}));
        this.editdg.title = fund ? '编辑产品信息' : '新建产品';
        this.editdg.visible = true;
        this.requestBindOrgs();

        this.dialogEditApp.$nextTick(() => {
            this.$editform.clearValidate();
        });
    }

    async requestBindOrgs() {

        if (this._hasRequestedOrgs) {
            return;
        }

        if (this._isRequestingOrgs) {
            return;
        }

        this._isRequestingOrgs = true;
        var loading = this.interaction.showLoading({ text: `获取机构列表...` });

        try {

            let resp = await repoOrg.getAll();
            if (resp.errorCode === 0) {

                let list = resp.data || [];
                if (list instanceof Array) {

                    let orgs = list.map(x => new Organization(x));
                    this.orgs.clear();
                    this.orgs.merge(orgs);
                    this._hasRequestedOrgs = true;
                }
            }
            else {
                this.interaction.showError(`获取机构失败，${resp.errorCode}/${resp.errorMsg}`);
            }
        }
        catch (ex) {

            this.interaction.showError(`获取机构异常`);
            console.error(ex);
        }
        finally {

            this._isRequestingOrgs = false;
            loading.close();
        }
    }

    async requestInitialList() {

        await this.requestProducts();
        if (this.tableObj.rowCount > 0) {
            this.handleFundChange(this.tableObj.allRows[0].rowData, true);
        }
    }

    build($container, view_options) {

        super.build($container);

        this.isNormal = !!view_options.isNormal;
        this.isBroker = !!view_options.isBroker;
        this.isOrgFixed = false;

        this.createToolbarApp();
        this.setupTable();
        this.requestInitialList();

        window.adminProductList = this;
    }
}

module.exports = Controller;