
const IView = require('../../../component/iview').IView;
const TabList = require('../../../component/tab-list').TabList;
const Tab = require('../../../component/tab').Tab;
const Splitter = require('../../../component/splitter').Splitter;

class View extends IView {

    constructor(view_name, is_standalone_window) {

        super(view_name, is_standalone_window, '产品');
        this.userModes = { normal: 'normal',  broker: 'broker' };
    }

    buildTop() {

        this.top = new TabList({

            allowCloseTab: false,
            embeded: true,
            $navi: this.$container.querySelector('.view-product-list > .product-tabs'),
            $content: this.$container.querySelector('.view-product-list > .product-list-content'),
            tabCreated: this.handleProductTabCreation.bind(this),
        });

        this.top.openTab(true, '@admin/shared/product-list', '产品列表', this.userStates);
    }

    buildSplitter() {

        var bar_name = 'admin-product';
        var $bar = this.$container.querySelector('.splitter-bar');
        this.splitter = new Splitter(bar_name, $bar, this.handleSpliting.bind(this), { previousMinHeight: 155, nextMinHeight: 200 });
        setTimeout(() => { this.splitter.recover(); }, 1000);
    }

    /**
     * @param {Number} previous_height 
     * @param {Number} next_height 
     */
    handleSpliting(previous_height, next_height) {

        this.top.tabs.forEach(tab => {
            tab.viewEngine.setHeight(previous_height);
        });

        this.summary.tabs.forEach(tab => {
            tab.viewEngine.setHeight(next_height);
        });
    }

    /**
     * @param {Tab} tab 
     */
    handleProductTabCreation(tab) {

        /**
         * todo19: 视图加载过程中，该方法将被调用2次，尚未察觉原因，临时作此处理
         */

        if (this._hasRegisteredHandler) {
            return;
        }

        this._hasRegisteredHandler = true;
        tab.viewEngine.registerEvent(this.systemEvent.viewContextChange, this.handleContextChange.bind(this));
    }

    handleContextChange(context) {

        if (!this.summary) {
            console.error('product changed, but product summary is not ready', context);
            return;
        }

        this.contextInfo = context;
        this.summary.fireEventOnAllTabs(this.systemEvent.viewContextChange, context);
    }

    /**
     * @param {Tab} tab 
     */
    handleSummaryTabCreated(tab) {

        if (this.contextInfo === undefined) {
            return;
        }

        this.summary.fireEventOnTab(tab, this.systemEvent.viewContextChange, this.contextInfo);
    }

    /**
     * @param {Tab} tab 
     */
    handleSummaryTabFocused(tab) {
        //
    }

    buildSummary() {

        this.summary = new TabList({

            allowCloseTab: false,
            embeded: true,
            $navi: this.$container.querySelector('.product-summary > .summary-tabs'),
            $content: this.$container.querySelector('.product-summary > .summary-content'),
            tabCreated: this.handleSummaryTabCreated.bind(this),
            tabFocused: this.handleSummaryTabFocused.bind(this),
        });

        this.summary.openTab(true, '@shared/order-list', '今日订单');
        this.summary.openTab(true, '@shared/position-list', '今日持仓');
        this.summary.openTab(true, '@shared/exchange-list', '今日成交');
        this.summary.openTab(true, '@shared/history-order-list', '历史订单');
        this.summary.openTab(true, '@shared/history-position-list', '历史持仓');
        this.summary.openTab(true, '@shared/history-exchange-list', '历史成交');
        this.summary.openTab(true, '@shared/history-equity-list', '历史权益');
    }

    refresh() {
        this.top.focusedTab.viewEngine.refresh();
    }

    config() {
        this.top.focusedTab.viewEngine.config();
    }

    exportSome() {
        this.top.focusedTab.viewEngine.exportSome();
    }

    clone() {
        this.top.focusedTab.viewEngine.clone();
    }

    build($container, view_option) {

        super.build($container);
        const tag = (view_option || {}).tag;
        
        this.userStates = {

            isNormal: tag === this.userModes.normal,
            isBroker: tag === this.userModes.broker,
            isOrgFixed: !this.userInfo.isSuperAdmin,
        };

        this.buildSummary();
        this.buildTop();
        this.buildSplitter();

        window.adminProduct = this;
    }
}

module.exports = View;
