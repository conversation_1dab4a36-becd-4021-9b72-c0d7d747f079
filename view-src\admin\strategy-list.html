<div class="strategy-list-view-root">

    <template>

        <div class="table-box s-scroll-bar">
            
            <div class="s-typical-toolbar">

                <el-button type="primary" @click="openStrategyCreationDialog" size="mini">
                    <i class="iconfont icon-add"></i> 创建策略
                </el-button>

                <el-input v-model="searching.value" style="width: 160px;" placeholder="请输入关键词" clearable>
                    <i slot="prefix" class="el-input__icon el-icon-search"></i>
                </el-input>

            </div>

            <data-tables layout="pagination,table" table-name="strategyManagement" :filters="filters" table-label="策略管理"
                role="orgAdmin" :default-sort="{prop: 'id', order: 'descending'}" @row-click="handleRowClick"
                ref="table" class="s-searchable-table" v-bind:data="strategyList" v-bind:table-props="tableProps"
                v-bind:pagination-props="{ show: false, layout: 'prev,pager,next,sizes,total' }"
                v-bind:search-def="searchDef">
                <el-table-column prop="index" fixed="left" show-overflow-tooltip label="序号" type="index" width="50"
                    align="center">
                </el-table-column>
                <el-table-column fixed="left" show-overflow-tooltip label="策略ID" prop="id" min-width="150"
                    sortable="custom">
                    <template slot-scope="scope">
                        <span>{{scope.row.id}}</span>
                    </template>
                </el-table-column>
                <el-table-column fixed="left" show-overflow-tooltip label="策略名称" prop="strategyName" min-width="120"
                    sortable="custom">
                    <template slot-scope="scope">
                        <span>{{scope.row.strategyName}}</span>
                    </template>
                </el-table-column>
                <el-table-column :formatter="formatMaxMoney" prop="maxLimitMoney" align="right" show-overflow-tooltip
                    label="最大使用金额" min-width="120" sortable="custom">
                    <template slot-scope="props">
                        <div>{{getPrimaryLimitMoney(props.row) | thousands}}</div>
                    </template>
                </el-table-column>
                <el-table-column show-overflow-tooltip label="报告模板" min-width="110" prop="reportTemplate">
                    <template slot-scope="scope">
                        <el-tooltip content="绑定报告模板" placement="top" :enterable="false" :open-delay="850">
                            <a style="margin-right: 5px" class="icon-button s-cp iconfont icon-edit"
                                @click.stop="handleBindReportTemplate(scope.row)"></a>
                        </el-tooltip>
                        <span>{{ Array.isArray(scope.row.reportTemplates) && scope.row.reportTemplates.length>0 ?
                    scope.row.reportTemplates.map(x => x.templateName).join('、') : '暂未绑定'}}</span>
                    </template>
                </el-table-column>
                <el-table-column prop="traders" show-overflow-tooltip label="交易员"
                    min-width="140" sortable="custom">
                    <template slot-scope='props'>
                        <span @click.stop="handleOpenTraderBindingDialog(props.row)" class="s-cp s-underline">
                            <a v-if="props.row.traders && props.row.traders.length > 0">
                                {{props.row.traders.map(user => user.fullName || user.userName).join('、')}}
                            </a>
                            <a v-else>未绑定</a>
                        </span>
                    </template>
                </el-table-column>

                <el-table-column prop="riskUsers" show-overflow-tooltip label="风控员"
                    min-width="140" sortable="custom">
                    <template slot-scope='props'>
                        <span @click.stop="handleOpenRiskBindingDialog(props.row)" class="s-cp s-underline">
                            <a v-if="props.row.riskUsers && props.row.riskUsers.length > 0">
                                {{props.row.riskUsers.map(user => user.fullName || user.userName).join('、')}}
                            </a>
                            <a v-else>未绑定</a>
                        </span>
                    </template>
                </el-table-column>

                <el-table-column align="right" show-overflow-tooltip width="100" label="权益" prop="balance"
                    sortable="custom" :formatter="thousands"></el-table-column>
                <el-table-column align="right" show-overflow-tooltip width="100" label="市值" prop="marketValue"
                    sortable="custom" :formatter="thousands"></el-table-column>
                <el-table-column align="right" show-overflow-tooltip width="100" label="收益率" prop="risePercent"
                    sortable="custom" :formatter="percentage">
                    <template slot-scope="scope">
                        <span
                            :class="scope.row.risePercent > 0 ? 's-color-red' : scope.row.risePercent < 0 ? 's-color-green' : null">{{typeof scope.row.risePercent === 'number' ? scope.row.risePercent.toFixed(2) + '%' : (scope.row.risePercent || '0%') }}</span>
                    </template>
                </el-table-column>
                <el-table-column :formatter="formatReduce" align="right" show-overflow-tooltip width="100" label="平仓盈亏"
                    prop="closeProfit" sortable="custom">
                    <template slot-scope="props">
                        <div :class="getColor(getReduce(props.row.strategyAccounts, 'closeProfit'))">
                            {{ getReduce(props.row.strategyAccounts, 'closeProfit') | thousands }}
                        </div>
                    </template>
                </el-table-column>
                <el-table-column :formatter="formatReduce" align="right" show-overflow-tooltip width="100" label="浮动盈亏"
                    prop="floatProfit" sortable="custom">
                    <template slot-scope="props">
                        <div :class="getColor(getReduce(props.row.strategyAccounts, 'floatProfit'))">
                            {{ getReduce(props.row.strategyAccounts, 'floatProfit') | thousands }}
                        </div>
                    </template>
                </el-table-column>
                <el-table-column prop="online" label="在线" :formatter="formatOnline" align="center" width="60">
                    <template slot-scope="scope">
                        <span :class="getConnectClass(scope.row.connectCount)">{{makeOnlineStatus(scope.row)}}</span>
                    </template>
                </el-table-column>
                <el-table-column label="实例数" align="center" width="60" prop="connectCount">
                    <template slot-scope="scope">
                        <span @click.stop="viewOnlineInstance(scope.row)"
                            class="s-cp">{{makeOnlineStatistics(scope.row)}}</span>
                    </template>
                </el-table-column>
                <el-table-column prop="operation" label="操作" width="130" fixed="right" class-name="s-col-oper">
                    <template slot-scope='props'>
                        <el-tooltip content="账号设置" placement="top" :enterable="false" :open-delay="850">
                            <a class="s-cp icon-button iconfont icon-shezhi11"
                                @click.stop="handleAccountSetting(props.row)"></a>
                        </el-tooltip>
                        <el-tooltip content="风控设置" placement="top" :enterable="false" :open-delay="850">
                            <a class="s-cp icon-button icon-tianjiafengkong iconfont"
                                @click.stop="riskConfig(props.row)"></a>
                        </el-tooltip>
                        <el-tooltip content="修改" placement="top" :enterable="false" :open-delay="850">
                            <a class="s-cp icon-button icon-edit iconfont" @click.stop="handleEdit(props.row)"></a>
                        </el-tooltip>
                        <el-tooltip content="删除" placement="top" :enterable="false" :open-delay="850">
                            <a class="s-cp icon-button el-icon-delete s-color-red"
                                @click.stop="handleDelete(props.row)"></a>
                        </el-tooltip>
                    </template>
                </el-table-column>
            </data-tables>
        </div>

        <el-dialog width="400px" 
                   :title="dialog.strategy.title" 
                   :visible="dialog.strategy.visible" 
                   :show-close="false"
                   :close-on-click-modal="false" 
                   :close-on-press-escape="false">

            <el-form class="dialog-body-form s-pd-10" 
                     ref="strategyForm"
                     @submit.native.prevent 
                     :model="dialog.strategy.form"
                     :rules="dialog.strategy.rules" label-width="80px">

                <el-form-item label="策略名称" prop="strategyName" :maxlength="25">
                    <el-input size="small" v-model.trim="dialog.strategy.form.strategyName"></el-input>
                </el-form-item>

                <el-form-item label="策略描述" prop="description" :maxlength="50">
                    <el-input type="textarea" v-model.trim="dialog.strategy.form.description"></el-input>
                </el-form-item>

            </el-form>

            <span slot="footer">
                <el-button type="primary" @click="saveStrategy" size="small">确定</el-button>
                <el-button @click="doClose" size="small">取消</el-button>
            </span>

        </el-dialog>

        <el-dialog id="strategy-account" width="600px" title="账号设置" 
                   :show-close="false"
                   :visible="dialog.account.visible" 
                   :close-on-click-modal="false"
                   :close-on-press-escape="false">

            <div v-if="dialog.account.accountList.length <= 0">
                <h2 align="center">当前产品暂无绑定账号</h2>
            </div>

            <el-form v-else @submit.native.prevent :model="dialog.account" class="s-pd-10" ref="accountSettingRef">
                <el-row :key="item.accountName + index" v-for="(item, index, key) in dialog.account.accountListHash">
                    <el-col :span="12">
                        <span style="font-size: 15px">账号：</span>
                        <span style="font-size: 14px">{{item.accountName}}</span>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item
                            :rules="[{required: true, message: '请输入最大使用金额', trigger: 'blur'}, {  type: 'number', trigger: 'blur', message: '请正确输入最大使用金额' }]"
                            :prop="'accountListHash['+index+'].tempMaxLimitMoney'">
                            <span style="font-size: 15px">最大使用金额：</span>
                            <el-input size="mini" style="width: 150px;" placeholder="请输入"
                                :maxlength="15" v-model.number="item.tempMaxLimitMoney"></el-input>
                        </el-form-item>
                    </el-col>
                </el-row>
            </el-form>
            <span slot="footer">
                <el-button @click="handleSaveAccount" type="primary" size="small">确定</el-button>
                <el-button @click="closeAccountDialog" size="small">取消</el-button>
            </span>
        </el-dialog>

        <el-dialog width="600px" title="配置报告模板" 
                   :visible="dialog.report.visible" 
                   :show-close="false" 
                   :close-on-click-modal="false" 
                   :close-on-press-escape="false">

            <el-form label-width="70px" class="s-pd-10">

                <el-form-item label="报告模板">
                    <el-select v-model="dialog.report.model" class="s-full-width" placeholder="选择报告模板" multiple>
                        <el-option v-for="item in dialog.report.data" :key="item.id" :label="item.label" :value="item.id"></el-option>
                    </el-select>
                </el-form-item>

                <el-form-item label="默认模板">
                    <span style="margin-left: 5px" 
                          v-if="Array.isArray(dialog.report.model) && dialog.report.model.length <= 0">暂未选择任何模板</span>
                    <template v-else>
                        <el-radio v-for="(item, item_idx) in dialog.report.defaultOptions" :key="item_idx"
                            v-model="dialog.report.defaultTemplateId" :label="item.id">{{item.label}}</el-radio>
                    </template>
                </el-form-item>

            </el-form>

            <span slot="footer">
                <el-button @click="handleSaveReport" type="primary" size="small">确定</el-button>
                <el-button @click="handleCloseReport" size="small">取消</el-button>
            </span>

        </el-dialog>

        <el-dialog width="520px" title="绑定交易员"
                   :visible="dialog.trader.visible" 
                   :show-close="false"
                   :close-on-click-modal="false" 
                   :close-on-press-escape="false">

            <div class="s-pd-10">

                <el-transfer filter-placeholder="关键字搜索"
                                v-model="dialog.trader.model"
                                :titles="['可选交易员', '已选交易员']"
                                :data="dialog.trader.data"
                                :props="{ key: 'id', label: 'fullName' }" filterable></el-transfer>
                                
            </div>

            <span slot="footer">
                <el-button @click="handleSaveTrader" type="primary" size="small">确定</el-button>
                <el-button @click="() => dialog.trader.visible = false" size="small">取消</el-button>
            </span>

        </el-dialog>

        <el-dialog width="520px" title="绑定风控员"
                   :visible="dialog.riskUser.visible" 
                   :show-close="false"
                   :close-on-click-modal="false" 
                   :close-on-press-escape="false">

            <div class="s-pd-10">

                <el-transfer filter-placeholder="关键字搜索"
                                v-model="dialog.riskUser.model"
                                :titles="['可选风控员', '已选风控员']"
                                :data="dialog.riskUser.data"
                                :props="{ key: 'id', label: 'fullName' }" filterable></el-transfer>

            </div>

            <span slot="footer">
                <el-button @click="handleSaveRiskUser" type="primary" size="small">确定</el-button>
                <el-button @click="() => dialog.riskUser.visible = false" size="small">取消</el-button>
            </span>

        </el-dialog>

        <el-dialog title="在线列表"
                   :visible="dialog.online.visible" 
                   :close-on-click-modal="false"
                   :before-close="() => { dialog.online.visible = false }">

            <div class="s-pd-10">
                <data-tables configurable-column="false" 
                             :data="strategyOnline.onlineList"
                             :table-props="strategyOnline.common.tableProps" 
                             :search-def="strategyOnline.common.searchDef" 
                             :pagination-props="strategyOnline.common.paginationDef"
                             class="s-searchable-table" stripe>

                    <el-table-column type="index" align="center" label="序号"></el-table-column>
                    <el-table-column label="IP" align="center" prop="ip"></el-table-column>
                    <el-table-column label="操作" align="center">
                        <template slot-scope="scope">
                            <span @click="forceOffline(scope.row)">强制下线</span>
                        </template>
                    </el-table-column>
                    <el-table-column v-for="item in strategyOnline.tabStructure" :key="item" :prop="item" :label="item">
                        <template slot-scope="scope">
                            <span>{{getConvertProperty(scope.row, item)}}</span>
                        </template>
                    </el-table-column>
                </data-tables>
            </div>

        </el-dialog>

    </template>
</div>