
const BaseAdminView = require('./baseAdminView').BaseAdminView;
const DataTables = require('../../libs/3rd/vue-data-tables.min.3.4.2');
const strategyOnline = require('./strategy-online/strategy-online');
const NumberMixin = require('../../mixin/number').NumberMixin;
const UiBuisinessMixin = require('../../mixin/ui-business').UiBuisinessMixin;

const ShareTypes = {

    creator: { code: 0, mean: '创建' },
    trader: { code: 1, mean: '交易' },
    riskProtector: { code: 2, mean: '风控' },
    inspector: { code: 3, mean: '查看' },
}

class View extends BaseAdminView {

    get repoFund() {
        return require('../../repository/fund').repoFund;
    }

    get orgRepo() {
        return require('../../repository/org').repoOrg;
    }

    get repoUser() {
        return require('../../repository/user').repoUser;
    }

    get repoAccount() {
        return require('../../repository/account').repoAccount;
    }

    get repoIndicator() {
        return require('../../repository/indicator').repoIndicator;
    }

    get sysSetting() {
        return this.systemSetting;
    }

    get repoStrategy() {
        return require('../../repository/strategy').repoStrategy;
    }

    get formatters() {
        return {
            formatYesNo: (row, column) => {
                let prop = column.property;
                return !!row[prop] ? '是' : '否';
            },
            formatValuation: (row, column) => {
                let val = row[column.property];
                let matched = this.valuationConfigs.find(valuation => valuation.val == val);
                return matched ? matched.label : '未知';
            },
            formatFundType: (row, column) => {
                let type = row[column.property];
                type *= 1;
                if (![1, 2, 3].includes(type)) {
                    return '未知';
                }
                return this.fundTypes.find(cdt => type === cdt.val).label;
            },
            thousands(row, column) {
                return NumberMixin.methods.thousands(row[column.property], false, 2);
            },
            formatAccount: (row, column) => {
                return Array.isArray(row[column.property]) && row[column.property].length > 0
                    ? row[column.property].map(manager => manager.accountName).join('、')
                    : '暂未绑定';
            },
            formatOnline: (row, column) => {
                return !!row.connectCount > 0 ? '是' : '否';
            },
            formatMaxMoney: (row, column) => {
                let sum = 0;
                row.strategyAccounts.forEach(account => {
                    sum += account.maxLimitMoney;
                });
                return sum;
            },
            formatReduce: (row, column) => {
                let list = row.strategyAccounts || [];
                let key = column.property;
                if (list.length == 0) {
                    return 0;
                }
                return list.reduce(
                    (c, n) => {
                        c[key] += n[key] || 0;
                        return c;
                    },
                    { [key]: 0 },
                )[key];
            },
        };
    }

    constructor(view_name) {

        super(view_name, '策略管理');
        this.$container = null;
        this.createdSummary = false;
        this.cachedAccountsHash = {};
        this.vueApp = null;

        this.strategyList = [];
        this.strategyListHash = {};
        this.userList = [];
        this.riskUserList = [];
        this.traderList = [];
        this.userListHash = {};

        this.strategyOnline = strategyOnline;
        this.financeAccountList = [];
        this.financeAccountListHash = {};

        this.fundList = [];
        this.fundListHash = {};
        this.reportTemplateList = [];
        this.reportTemplateHash = {};
        this.$el = null;
        this.currentStrategy = null;
        this.searching = {
            prop: ['id', 'strategyName'],
            value: '',
        };
    }

    createApp() {


        let controller = this;
        this.tableProps = {

            highlightCurrentRow: true,
            border: false,
            stripe: true,
            height: '260px',
        };

        this.vueApp = new Vue({
            el: this.$el,
            components: {
                DataTables: DataTables.DataTables,
            },
            filters: {},
            data: {
                searching: this.searching,
                filters: [this.searching],
                strategyOnline: this.strategyOnline.appData,
                tableProps: this.tableProps,
                paginationDef: this.sysSetting.tablePagination,
                searchDef: {
                    inputProps: {
                        placeholder: '输入关键字筛选',
                        prefixIcon: 'iconfont icon-search',
                    },
                },
                strategyList: this.strategyList,
                reportTemplateList: this.reportTemplateList,
                dialog: {
                    online: {
                        visible: false,
                        context: null,
                    },
                    strategy: {
                        visible: false,
                        title: null,
                        //获取form的结构
                        form: this.standardStrategyItem({}),
                        rules: {
                            strategyName: [
                                { type: 'string', required: true, message: '请输入策略名称' },
                                controller.systemSetting.specialCharacterFilterRule,
                                controller.systemSetting.limitInputLengthRule,
                            ],
                            fundId: [
                                { required: true, message: '请选择要绑定的产品' },
                                {
                                    validator(rule, value, callback) {
                                        let currentStrategy = controller.vueApp.dialog.strategy.form;
                                        let otherStrategy = controller.strategyList.filter(
                                            strategy =>
                                                strategy.strategyName == currentStrategy.strategyName &&
                                                strategy.id != currentStrategy.id,
                                        );
                                        if (otherStrategy.length <= 0) {
                                            callback();
                                        } else {
                                            let flag = otherStrategy.some(cdt => {
                                                return cdt.fundId == value;
                                            });

                                            if (flag) {
                                                callback(new Error('相同策略名无法绑定同一个产品，请修改'));
                                            }
                                            callback();
                                        }
                                    },
                                },
                            ],
                        },
                        accountList: [],
                    },
                    trader: {
                        visible: false,
                        data: controller.traderList,
                        model: [],
                    },
                    riskUser: {
                        visible: false,
                        data: controller.riskUserList,
                        model: [],
                    },
                    account: {
                        visible: false,
                        accountList: [],
                        accountListHash: {},
                    },
                    report: {
                        visible: false,
                        model: [],
                        data: controller.reportTemplateList,
                        defaultOptions: [],
                        defaultTemplateId: null,
                    },
                },
                fundList: [],
            },
            computed: {
                isSuperAdmin: function() {
                    return controller.userInfo.isSuperAdmin;
                },
                isOrgAdmin: function() {
                    return controller.userInfo.isOrgAdmin;
                },
            },
            mixins: [NumberMixin, UiBuisinessMixin],
            watch: {
                'dialog.report.model'(newValue) {
                    if (Array.isArray(newValue)) {
                        this.dialog.report.defaultOptions = newValue.map(tid => {
                            let template = controller.reportTemplateHash[tid];
                            return {
                                label: controller.helper.readKey(template, 'report_info.title_format'),
                                id: tid,
                            };
                        });
                        if (newValue.every(x => x !== this.dialog.report.defaultTemplateId)) {
                            this.dialog.report.defaultTemplateId = null;
                        }
                    } else {
                        this.dialog.report.defaultTemplateId = null;
                    }
                },
            },
            methods: this.helper.extend(
                {
                    handleBindReportTemplate: async strategy => {
                        this.currentStrategy = strategy;
                        await this.getReportTemplateList();
                        let bindTemplates = strategy.reportTemplates || [];
                        let defaultTpl = bindTemplates.find(x => x.default);
                        if (defaultTpl) {
                            this.vueApp.dialog.report.defaultTemplateId = defaultTpl.templateId;
                        }
                        bindTemplates = bindTemplates.map(x => x.templateId);
                        //默认模板的候选项
                        this.vueApp.dialog.report.defaultOptions = bindTemplates.map(tid => {
                            let template = controller.reportTemplateHash[tid];
                            return {
                                label: controller.helper.readKey(template, 'report_info.title_format'),
                                id: tid,
                            };
                        });
                        //绑定模板回填
                        this.vueApp.dialog.report.model = bindTemplates;
                        this.vueApp.dialog.report.visible = true;
                    },
                    async handleSaveReport() {
                        let defaultId = this.dialog.report.defaultTemplateId;
                        if (!defaultId) {
                            controller.interaction.showError('请选择一个默认报告模板!');
                            return;
                        }
                        let strategyId = controller.currentStrategy.id;
                        let json = {};
                        this.dialog.report.model.forEach(key => {
                            json[key] = 0;
                        });
                        json[defaultId] = 1;
                        let flag = await controller.bindTemplate(strategyId, json);
                        if (flag) {
                            controller.currentStrategy.reportTemplates = this.dialog.report.model.map(x => {
                                let templateDetail = controller.reportTemplateHash[x];
                                return {
                                    default: defaultId === x,
                                    templateId: x,
                                    templateName: controller.helper.readKey(templateDetail, 'report_info.title_format'),
                                };
                            });
                            this.handleCloseReport();
                            setTimeout(() => {
                                controller.interaction.showSuccess('保存模板信息成功!');
                            }, 300);
                        }
                    },
                    handleCloseReport() {
                        
                        this.dialog.report.visible = false;
                        this.dialog.report.defaultOptions = [];
                        this.dialog.report.defaultTemplateId = null;
                        this.dialog.report.model = [];
                        controller.currentStrategy = null;
                    },
                    riskConfig(selected_strategy) {

                        let strategyId = selected_strategy.id;
                        let strategyName = selected_strategy.strategyName;
                        controller.openWinRskSetting({
                            type: controller.systemEnum.identityType.strategy.code,
                            identity: strategyId,
                            name: strategyName,
                        });
                    },
                    getConvertProperty: (row, key) => {
                        return this.strategyOnline.getConnectProperty(row, key);
                    },
                    forceOffline: instance => {
                        this.interaction.showConfirm({
                            title: '警告',
                            message: '确定要将该实例强制下线？',
                            confirmed: () => {
                                this.strategyOnline.doForceOffline(instance, this.vueApp.dialog.online.context);
                            },
                            canceled: () => {
                                console.log('user cancel');
                            },
                        });
                    },
                    handleRowClick: row => {
                        this.currentStrategy = row;
                        this.setWindowTitle(`策略 - ${row.strategyName}`);
                        this.handleStrategySelection(this.currentStrategy);
                    },
                    async openStrategyCreationDialog() {
                        let result = await controller.getFundList();
                        if (result.flag) {
                            this.fundList = result.data;
                        }
                        controller.currentStrategy = null;
                        this.dialog.strategy.title = '创建策略';
                        this.dialog.strategy.form = controller.standardStrategyItem({});
                        this.$nextTick(() => {
                            this.$refs.strategyForm.clearValidate();
                        });
                        this.dialog.strategy.visible = true;
                    },
                    async handleEdit(row) {
                        controller.currentStrategy = row;
                        if (this.fundList.length <= 0) {
                            let results = await controller.getFundList();
                            this.fundList = results.data;
                        }
                        controller.helper.extend(this.dialog.strategy.form, row);
                        //当绑定了产品的时候不能再允许修改产品
                        if (Array.isArray(row.strategyAccounts) && row.strategyAccounts.length > 0) {
                            this.dialog.strategy.disabled = true;
                        }
                        this.dialog.strategy.title = `修改策略 ${row.strategyName}`;
                        if (this.$refs.strategyForm) {
                            this.$refs.strategyForm.clearValidate();
                        }
                        this.dialog.strategy.visible = true;
                    },
                    async handleSaveTrader() {

                        let result = await controller.updateStrategyUsers(
                            controller.currentStrategy.id,
                            this.dialog.trader.model,
                            controller.systemUserEnum.userRole.tradingMan.code,
                            ShareTypes.trader.code);

                        if (result.flag) {

                            this.dialog.trader.visible = false;
                            controller.currentStrategy.traders = result.data.map(user => {
                                let targetUser = controller.userListHash[user.userId] || {};
                                return {
                                    userId: user.userId,
                                    userName: user.userName,
                                    fullName: targetUser.fullName,
                                };
                            });
                            this.updateTable(controller.currentStrategy);
                        }
                    },
                    async handleSaveRiskUser() {

                        let result = await controller.updateStrategyUsers(
                            controller.currentStrategy.id,
                            this.dialog.riskUser.model,
                            controller.systemUserEnum.userRole.riskProtector.code,
                            ShareTypes.riskProtector.code,
                        );

                        if (result.flag) {

                            this.dialog.riskUser.visible = false;
                            // 保证traderList是最新的
                            // await controller.getUserList();
                            controller.currentStrategy.riskUsers = result.data.map(user => {
                                let targetUser = controller.userListHash[user.userId] || {};
                                return {
                                    userId: user.userId,
                                    userName: user.userName,
                                    fullName: targetUser.fullName,
                                };
                            });
                            this.updateTable(controller.currentStrategy);
                        }
                    },
                    getPrimaryLimitMoney(strategy) {
                        let sum = 0;
                        strategy.strategyAccounts.forEach(account => {
                            sum += account.maxLimitMoney;
                        });
                        return sum;
                    },
                    async handleOpenTraderBindingDialog(row) {
                        controller.currentStrategy = row;
                        var flag = await controller.getUserList();
                        if (flag) {
                            this.dialog.trader.model = row.traders.map(user => user.userId);
                            this.dialog.trader.visible = true;
                        }
                    },
                    async handleOpenRiskBindingDialog(row) {
                        controller.currentStrategy = row;
                        var flag = await controller.getUserList();
                        if (flag) {
                            this.dialog.riskUser.model = row.riskUsers.map(user => user.userId);
                            this.dialog.riskUser.visible = true;
                        }
                    },
                    getReduce(list, key) {
                        if (list.length == 0) {
                            return 0;
                        }
                        return list.reduce(
                            (c, n) => {
                                c[key] += n[key] || 0;
                                return c;
                            },
                            { [key]: 0 },
                        )[key];
                    },
                    getColor(num) {
                        return num > 0 ? 's-color-red' : num < 0 ? 's-color-green' : 's-color-grey';
                    },
                    getConnectClass(counter) {
                        return counter > 0 ? 's-color-green' : 's-color-red';
                    },
                    updateTable(result) {
                        if (typeof controller.strategyListHash[result.id] === 'undefined') {
                            controller.strategyList.unshift(result);
                        } else {
                            var index = controller.strategyList.findIndex(cdt => cdt.id == result.id);
                            this.$set(this.strategyList, index, result);
                        }
                        if (controller.strategyList.length > 0) {
                            controller.setDefaultSelect(controller.strategyList[0]);
                            controller.handleStrategySelection(controller.strategyList[0]);
                        }
                        controller.strategyListHash[result.id] = result;
                    },
                    async handleAccountSetting(row) {

                        this.dialog.account.accountList = [];
                        this.dialog.account.accountListHash = {};
                        controller.currentStrategy = row;
                        //保证FundList是最新的，需要重复拉取
                        await controller.getFundList();
                        this.dialog.account.visible = true;
                        // 已绑定账户
                        let bindedAccounts = row.strategyAccounts;

                        let thisAccountList = [];
                        controller.fundList.forEach(thisFund => {
                            if (typeof thisFund !== 'undefined') {
                                thisAccountList = thisAccountList.concat(thisFund.accounts.map(input => {
                                    let output = {
                                        maxLimitMoney: 0,
                                        accountName: input.accountName,
                                        accountId: input.accountId,
                                        fundId: thisFund.id,
                                        fundName: thisFund.fundName,
                                        tempMaxLimitMoney: 0,
                                    };
    
                                    let thisBundleAccount = bindedAccounts.find(cdt => cdt.accountId == input.accountId);
    
                                    if (typeof thisBundleAccount !== 'undefined') {
                                        output.id = thisBundleAccount.detailId;
                                        output.maxLimitMoney = thisBundleAccount.maxLimitMoney;
                                        output.tempMaxLimitMoney = thisBundleAccount.maxLimitMoney;
                                    }
    
                                    return output;
                                }));    
                                
                            }
                        });

                        this.dialog.account.accountList = thisAccountList;
                        thisAccountList.forEach(item => {
                            this.dialog.account.accountListHash[item.accountId] = item;
                        });
                        
                    },
                    handleSaveAccount() {

                        var saveAction = async () => {

                            await controller.getAccountList();
                            let saveReq = [];
                            controller.currentStrategy.balance = 0;
                            //需要先把accountList里面没有的内容删除掉
                            let strategyAccounts = controller.currentStrategy.strategyAccounts;
                            let deletedAccounts = strategyAccounts.filter(x =>
                                this.dialog.account.accountList.every(account => account.accountId != x.accountId),
                            );
                            //如果有需要删除的账户
                            if (deletedAccounts.length > 0) {
                                
                                // let delAccountList = [];
                                
                                // deletedAccounts.forEach(account_item => {
                                //     delAccountList.push(
                                //         controller.deleteStrategyAccount(
                                //             controller.currentStrategy.id,
                                //             account_item.detailId,
                                //         ),
                                //     );
                                // });

                                // let delResps = await Promise.all(delAccountList);
                                // if (delResps.some(x => !x)) {
                                //     controller.interaction.showError('删除策略账户失败，无法继续进行下一步!');
                                //     return;
                                // } else {
                                //     let ids = deletedAccounts.map(x => x.accountId);
                                //     controller.currentStrategy.strategyAccounts.remove(x => ids.includes(x.accountId));
                                // }
                            }
                            
                            //准备处理对分配最大金额
                            this.dialog.account.accountList.forEach(account => {
                                let targetAccount = controller.financeAccountListHash[account.accountId] || {};
                                let financeAccount = targetAccount.financeAccount || null;
                                let original_limit_num = account.maxLimitMoney || 0;
                                let saveData = Object.assign(account, {
                                    financeAccount: financeAccount,
                                    maxLimitMoney: account.tempMaxLimitMoney
                                });
                                //对0的过滤 如果之前是0现在还是0就过滤，否则就不过滤
                                if (original_limit_num == 0 && saveData.maxLimitMoney == 0) return;
                                saveData.userId = controller.userInfo.userId;
                                if (saveData.id !== undefined) {
                                    saveReq.push(
                                        controller.updateStrategyAccounts(controller.currentStrategy.id, saveData),
                                    );
                                } else {
                                    saveReq.push(
                                        controller.saveStrategyAccounts(controller.currentStrategy.id, saveData),
                                    );
                                }
                            });
                            let fullResponseList = await Promise.all(saveReq);
                            //对批量新增的特殊处理，当时记得好像后台允许批量新增导致的
                            fullResponseList.forEach(resp => {
                                if (Array.isArray(resp.data) && resp.data.length >= 1) {
                                    resp.data = resp.data[0];
                                }
                            });
                            fullResponseList.forEach(resp => {
                                if (!resp.flag) return;
                                let update = controller.currentStrategy.strategyAccounts.find(
                                    cdt => cdt.accountId == resp.data.accountId,
                                );
                                if (resp.data.floatProfit === undefined) {
                                    resp.data.floatProfit = 0;
                                }
                                resp.data.detailId = resp.data.id;
                                update
                                    ? controller.helper.extend(update, resp.data)
                                    : controller.currentStrategy.strategyAccounts.push(resp.data);
                                controller.currentStrategy.balance += resp.data.maxLimitMoney;
                            });
                            if (fullResponseList.every(resp => resp.flag)) {
                                controller.interaction.showSuccess('账户已经全部保存成功!');
                            } else {
                                controller.interaction.showWarning('有部分账户尚未保存成功!');
                            }
                            this.updateTable(controller.currentStrategy);
                            this.dialog.account.visible = false;
                        };

                        this.$refs.accountSettingRef.validate(valid => {
                            if (valid) {
                                let strategyAccounts = controller.currentStrategy.strategyAccounts;
                                let deletedAccounts = strategyAccounts.filter(x =>
                                    this.dialog.account.accountList.every(account => account.accountId != x.accountId),
                                );
                                //如果有需要删除的账户
                                if (deletedAccounts.length > 0) {
                                    deletedAccounts.map(x => '"' + x.accountName + '"').join('、');
                                    controller.interaction.showConfirm({
                                        title: '警告-危险操作',
                                        message: `（${deletedAccounts
                                            .map(x => '账号 - ' + x.accountName)
                                            .join('、')}）已从（产品 - ${
                                            controller.currentStrategy.fundName
                                        }）解绑，调整策略账号会删除策略与以上账号关联，请确认已清空策略在上述账号中的持仓！确定要进行当前操作吗?`,
                                        confirmed: () => { saveAction(); },
                                    });
                                } else {
                                    saveAction();
                                }
                            } else {
                                console.log("the user's input is invalid");
                            }
                        });
                    },
                    closeAccountDialog() {

                        this.dialog.account.visible = false;
                        this.dialog.account.accountListHash = {};
                        this.dialog.account.accountList = [];
                        if (this.$refs.accountSettingRef) {
                            this.$refs.accountSettingRef.clearValidate();
                        }
                    },
                    getFundSummary(list) {
                        let sum = 0;
                        list.forEach(account => {
                            sum += account.maxLimitMoney;
                        });
                        return sum;
                    },
                    doClose() {
                        this.dialog.strategy.disabled = false;
                        this.dialog.strategy.form = controller.standardStrategyItem({});
                        this.dialog.strategy.visible = false;
                        controller.currentStrategy = null;
                    },
                    saveStrategy() {
                        this.$refs.strategyForm.validate(async valid => {
                            if (valid) {
                                let result = await controller.saveStrategy(this.dialog.strategy.form);
                                if (result.flag) {
                                    this.dialog.strategy.form.id === undefined
                                        ? (this.dialog.strategy.form.id = result.data.id)
                                        : null;
                                    let strategy = Object.assign(this.dialog.strategy.form, result.data);
                                    // if (!strategy.fundName) {
                                    //     let this_fund = controller.fundList.find(cdt => cdt.id == strategy.fundId);
                                    //     strategy.fundName = this_fund ? this_fund.fundName : '';
                                    // }
                                    this.updateTable(controller.helper.deepClone(strategy));
                                }
                                this.dialog.strategy.visible = false;
                                this.dialog.strategy.disabled = false;
                                controller.currentStrategy = null;
                            }
                        });
                    },
                    makeOnlineStatus: strategy => {
                        return this.getOnlineStatus(strategy) ? '在线' : '不在线';
                    },
                    makeOnlineStatistics(strategy) {
                        return strategy.connectCount || 0;
                    },
                    viewOnlineInstance(strategy) {
                        let strategyId = strategy.id;
                        controller.strategyOnline.adaptOnlineList(strategyId, () => {
                            this.dialog.online.visible = true;
                            this.dialog.online.context = strategy;
                        });
                    },
                    handleDelete: row => {
                        controller.interaction.showConfirm({
                            title: '警告',
                            message: '确定要删除当前策略吗？',
                            confirmed: () => {
                                let strategyId = row.id;
                                this.removeStrategy(strategyId).then(flag => {
                                    if (flag) {
                                        this.strategyList.remove(cdt => cdt.id == strategyId);
                                        delete this.strategyListHash[strategyId];
                                        this.interaction.showSuccess('操作成功!');
                                    }
                                });
                            },
                        });
                    },
                },
                this.formatters,
            ),
        });
    }

    setHeight(height) {
        this.tableProps.height = height - 50 + 'px';
    }

    getOnlineStatus (strategy) {
        return strategy.connectCount && strategy.connectCount > 0;
    }

    /**
     * 更新策略用户
     */
    async updateStrategyUsers(strategy_id, user_ids, role_id, share_type) {
        let output = {
            flag: false,
            data: null,
        };
        try {
            const resp = await this.repoStrategy.updateStrategyUsers(strategy_id, user_ids, role_id, share_type);
            if (resp.errorCode == 0) {
                this.interaction.showSuccess('分配人员成功');
                output.flag = true;
                if (typeof resp.data !== 'undefined') {
                    output.data = resp.data;
                }
            } else {
                console.log(resp);
                this.interaction.showHttpError('分配人员失败');
            }
        } catch (error) {
            console.log(error);
            this.interaction.showError('分配人员失败');
        }
        return Promise.resolve(output);
    }

    async getFundList() {
        let output = {
            flag: false,
            data: null,
        };

        let loading = this.interaction.showLoading({
            text: '请求产品列表...',
        });
        try {
            const resp = await this.repoFund.getAll();
            if (resp.errorCode === 0) {
                let list = resp.data;
                list.forEach(fund => {
                    let fundId = fund.id;
                    this.fundListHash[fundId] = fund;
                });
                output.data = list;
                this.fundList = list;
                output.flag = true;
            } else {
                this.interaction.showHttpError('获取产品列表失败，详细信息："' + resp.errorMsg + '"');
            }
        } catch (exp) {
            this.interaction.showHttpError('获取产品列表失败!');
        } finally {
            loading.close();
        }

        return Promise.resolve(output);
    }

    setDefaultSelect (first) {

        if (this.vueApp.$refs.table && this.vueApp.$refs.table.$children[1] && first) {
            
            let defaultRow = first;
            this.vueApp.$refs.table.$children[1].setCurrentRow(defaultRow);
            this.setWindowTitle(`策略 - ${defaultRow.strategyName}`);
        }
    }

    async removeStrategy (strategyId) {

        let output = false;

        let loading = this.interaction.showLoading({
            text: '操作进行中...',
        });

        try {
            const resp = await this.repoStrategy.delete(strategyId);
            if (resp.errorCode === 0 ) {
                output = true;
            } else {
                this.interaction.showHttpError('删除策略失败，详细信息'+ resp.errorMsg);
            }
        } catch (exp) {
            this.interaction.showHttpError('操作失败!');
        } finally {
            loading.close();
        }

        return Promise.resolve(output);
    }

    async saveStrategy(item) {

        let output = {
            flag: false,
            data: null,
        };
        try {
            let resp = item.id ? await this.repoStrategy.update(item) : await this.repoStrategy.create(item);
            if (resp.errorCode == 0) {
                this.interaction.showSuccess('保存策略成功');
                output.flag = true;
                if (typeof resp.data !== 'undefined') {
                    output.data = this.standardStrategyItem(resp.data);
                }
            } else {
                this.interaction.showHttpError(`保存策略失败: ${resp.errorMsg}`);
            }
        } catch (error) {
            this.interaction.showError(`保存策略失败`);
        }
        return Promise.resolve(output);
    }

    async updateStrategyAccounts(strategy_id, account) {
        let output = {
            flag: false,
            data: null,
        };
        try {
            const resp = await this.repoStrategy.updateBoundAccount(strategy_id, account);
            if (resp.errorCode == 0) {
                // this.interaction.showSuccess('修改产品账户成功');
                output.flag = true;
                if (typeof resp.data !== 'undefined') {
                    output.data = resp.data;
                }
            } else {
                console.log(resp);
                this.interaction.showHttpError('修改产品账户失败，详细信息:\"'+ resp.errorMsg +"\"");
            }
        } catch (error) {
            console.log(error);
            this.interaction.showError('修改产品账户失败');
        }
        return Promise.resolve(output);
    }

    /**
     * 保存策略账户
     */
    async saveStrategyAccounts(strategy_id, product) {
        let output = {
            flag: false,
            data: null,
        };
        /**
         * 必须字段 financeAccount
         */
        try {
            const resp = await this.repoStrategy.bindAccount(strategy_id, [product]);
            if (resp.errorCode == 0) {
                output.flag = true;
                if (typeof resp.data !== 'undefined') {
                    output.data = resp.data;
                }
            } else {
                this.interaction.showHttpError('保存账号失败,详细信息:\"' + resp.errorMsg + "\"");
            }
        } catch (error) {
            this.interaction.showError('保存账号失败');
        }
        return Promise.resolve(output);
    }

    /**
     * 获取账户列表
     */
    async getAccountList() {
        let output = {
            flag: false,
            data: null,
        };

        try {
            let resp = await this.repoAccount.getAll();
            if (resp.errorCode === 0 && typeof resp.data !== 'undefined') {
                this.financeAccountList = resp.data;
                resp.data.forEach(financeAccount => {
                    let accountId = financeAccount.id;
                    this.financeAccountListHash[accountId] = financeAccount;
                });
                output.data = resp.data;
                output.flag = true;
            }
        } catch (exp) {
            this.interaction.showHttpError('获取账户信息失败!');
            output.flag = false;
        }

        return Promise.resolve(output);
    }

    async handleStrategySelection(current_strategy) {

        if (!current_strategy) {
            return;
        }

        var strategy_id = current_strategy.id;
        if (this.cachedAccountsHash[strategy_id] == undefined) {
            var resp = await this.repoAccount.getAll({ strategy_id });
            this.cachedAccountsHash[strategy_id] = resp.data || [];
        }

        var belong_accounts = this.cachedAccountsHash[strategy_id];
        this.trigger(this.systemEvent.viewContextChange, {
            strategyId: strategy_id, 
            accounts: belong_accounts.map(x => {
                return {
                    accountId: x.id, 
                    accountName: x.accountName, 
                    assetType: x.assetType, 
                    isCredit: !!x.credit 
                };
            }),
        });
    }

    //标准化策略Meta数据
    standardStrategyItem(strategy) {

        strategy.strategyName = strategy.strategyName || null;
        strategy.description = strategy.description || null;
        strategy.available = typeof strategy.available === "number" ? strategy.available : 0;
        strategy.balance = typeof strategy.balance === "number" ? strategy.balance : 0;
        strategy.closeProfit = typeof strategy.closeProfit === "number" ? strategy.closeProfit : 0;
        strategy.commission = typeof strategy.commission === "number" ? strategy.commission : 0;
        strategy.connectCount = typeof strategy.connectCount === "number" ? strategy.connectCount : 0;
        strategy.diffBalance = typeof strategy.diffBalance === "number" ? strategy.diffBalance : 0;
        strategy.frozenCommission = typeof strategy.frozenCommission === "number" ? strategy.frozenCommission : 0;
        strategy.frozenMargin = typeof strategy.frozenMargin === "number" ? strategy.frozenMargin : 0;
        strategy.margin = typeof strategy.margin === "number" ? strategy.margin : 0;
        strategy.marketValue = typeof strategy.marketValue === "number" ? strategy.marketValue : 0;
        strategy.positionProfit = typeof strategy.positionProfit === "number" ? strategy.positionProfit : 0;
        strategy.preBalance = typeof strategy.preBalance === "number" ? strategy.preBalance : 0;
        strategy.risePercent = typeof strategy.risePercent === "number"? strategy.risePercent : 0;
        strategy.status = strategy.status || false;
        strategy.withdrawQuota = typeof strategy.withdrawQuota === "number" ?  strategy.withdrawQuota : 0;
        if (!(strategy.users instanceof Array)) {
            strategy.users = [];
        }
        strategy.users.forEach(user => {
            let userId = user.userId;
            let this_user = this.userListHash[userId];
            if (typeof this_user !== "undefined") {
                user.fullName = this_user.fullName;
            }
        });
        if (!Array.isArray(strategy.traders)) {
            strategy.traders = [];
            strategy.users.filter(x => x.shareType === ShareTypes.trader.code).forEach(user => {
                strategy.traders.push(user);
            });
        }

        if (!Array.isArray(strategy.riskUsers)) {
            strategy.riskUsers = [];
            strategy.users.filter(x => x.shareType === ShareTypes.riskProtector.code).forEach(user => {
                strategy.riskUsers.push(user);
            });
        }

        if (!Array.isArray(strategy.reportTemplates)) {
            strategy.reportTemplates = [];
        }

        if (!Array.isArray(strategy.strategyAccounts)) {
            strategy.strategyAccounts = [];
        }
        let result = {};
        this.helper.extend(result, strategy);
        return result;
    }

    refresh() {

        this.searching.value = '';
        this.getStrategyList(true);
    }

    async getReportTemplateList() {

        let output = {

            flag: false,
            data: null,
        };

        let loading = this.interaction.showLoading({ text: '请求报告模板列表...' });
        try {
            this.reportTemplateList.clear();
            const resp = await this.repoIndicator.getReportTemplateList({
                key_word: '',
                page_size: 999,
                page_no: 1,
            });
            if (resp.errorCode === 0) {
                loading.close();
                (resp.data || []).forEach(template => {
                    let info = {
                        id: template.id,
                        label: this.helper.readKey(template, 'report_info.title_format'),
                    };
                    this.reportTemplateHash[template.id] = template;
                    this.reportTemplateList.push(info);
                });
                output.data = resp.data;
                output.flag = true;
            } else {
                this.interaction.showHttpError(`请求报告模板列表出错,详细信息：${resp.errorCode}/${resp.errorMsg}`);
            }
        } catch (error) {
            loading.close();
            this.interaction.showError('请求报告模板列表出错');
        }
        return output;
    }

    async getStrategyList(is_force) {

        let loading = this.interaction.showLoading({ text: '请求策略列表...' });
        this.strategyList.clear();
        this.strategyListHash = {};
        const resp = await this.repoStrategy.getAll();

        if (resp.errorCode == 0) {

            loading.close();
            let list = resp.data || [];
            if (list.length <= 0) {
                return;
            }
            this.handleStrategySelection(list[0]);
            this.createdSummary = true;
            await Promise.all([this.getReportTemplateList(), await this.getUserList()]);
            list.forEach(the_strategy => {
                let strategy = this.standardStrategyItem(the_strategy);
                this.strategyListHash[strategy.id] = strategy;
            });
            list = list.orderByDesc(cdt => cdt.id);
            this.strategyList.merge(list);
        } 
        else {
            this.interaction.showHttpError(`查询策略列表出错,详细信息：${resp.errorCode}/${resp.errorMsg}`);
        }
    }

    /**
     * 获取交易员列表
     */
    async getUserList(callback) {
        let flag = false;
        let loading = this.interaction.showLoading({
            text: '请求分享人员列表...',
        });
        try {
            this.riskUserList.clear();
            this.traderList.clear();
            const resp = await this.repoUser.getAll();
            if (resp.errorCode === 0) {
                let full = resp.data || [];
                let traderList = full.filter(x => x.roleId === this.systemUserEnum.userRole.tradingMan.code);
                let riskUserList = full.filter(x => x.roleId === this.systemUserEnum.userRole.riskProtector.code);
                this.traderList.merge(traderList);
                this.riskUserList.merge(riskUserList);
                traderList.forEach(user => {
                    let userId = user.id;
                    this.userListHash[userId] = user;
                });
                //保存风控员
                riskUserList.forEach(user => {
                    let userId = user.id;
                    this.userListHash[userId] = user;
                });
                if (typeof callback === 'function') {
                    callback(full);
                }
                flag = true;
            } else {
                console.log(resp);
                this.interaction.showHttpError('查询人员列表出错');
            }
        } catch (error) {
            console.log(error);
            this.interaction.showError('查询人员列表出错');
        } finally {
            loading.close();
        }
        return Promise.resolve(flag);
    }

    async getStrategyDetail() {
        
        let respStrategyDetails = await this.repoStrategy.getDetail(this.strategyList.map(x => x.id));
        let strategyDetails = (respStrategyDetails.data || {}).data || [];

        this.strategyList.forEach((item, index) => {

            let detail = strategyDetails[index];
            item.marketValue = detail.marketValue || 0;
            item.balance = detail.balance || 0;
            item.risePercent = detail.risePercent || 0;
            this.strategyListHash[item.id] = item;
        });
    }

    async build($container) {

        this.$container = $container;
        this.$el = this.$container.querySelector('.strategy-list-view-root');
        this.createApp();
        await this.getStrategyList();
        this.getStrategyDetail();
    }
}

module.exports = View;
