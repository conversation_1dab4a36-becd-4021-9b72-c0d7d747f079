const BaseAdminView = require('../baseAdminView').BaseAdminView;
class pageController extends BaseAdminView {
    get strategyRepo() {
        return require('../../../repository/strategy').repoStrategy;
    }

    get Interaction() {
        return this.interaction;
    }

    constructor() {
        super();

        this.strategyId = null;

        this.onlineList = [];
        this.tabStructure = {};

        this.appData = {
            online: 'online',
            onlineList: this.onlineList,
            tabStructure: this.tabStructure,
            common: {
                tableProps: {
                    highlightCurrentRow: true,
                },
                searchDef: {
                    show: false,
                    inputProps: {
                        placeholder: '输入关键字筛选',
                        prefixIcon: 'iconfont icon-search',
                    },
                },
                paginationDef: this.systemSetting.tablePagination,
            },
        };
    }

    getConnectProperty(context, key) {
        if (key !== 'description') {
            return context[key] || '---';
        }

        let obj = null;

        //如果能够解析成JSON，就分散解析，如果不能，就直接显示description
        try {
            obj = JSON.parse(context[key]);
        } catch (e) {
            return context['description'];
        }
        console.log(obj);
        return '---';
    }

    async doForceOffline(instance, context) {
        let result = await this.sendForceOfflineReq(instance);
        if (result) {
            this.Interaction.showSuccess('已成功将该实例下线!');
            this.appData.onlineList.remove(cdt => cdt.key === instance.key);
            context.connectCount -= 1;
            let keySets = [];
            this.appData.onlineList.forEach(item => {
                Object.keys(item).forEach(_key => {
                    keySets.push(_key);
                });
            });
            keySets = Array.from(new Set(keySets));
            Object.keys(this.appData.tabStructure).forEach(key => {
                if (!keySets.some(k => k === key)) {
                    delete this.appData.tabStructure[key];
                }
            });

            let noJsonCounter = this.appData.onlineList.filter(_ => {
                try {
                    let obj = JSON.parse(_.description);
                } catch (e) {
                    return true;
                }
                return false;
            }).length;

            //当不能解析成json的description字段的数据没有的时候，就直接不再显示这个列
            if (noJsonCounter <= 0) {
                delete this.appData.tabStructure.description;
            }
        }
    }

    async sendForceOfflineReq(instance) {
        let result = false;

        try {
            let resp = await this.strategyRepo.takeDownInstance({
                strategyId: this.strategyId,
                instance_key: instance.key,
            });
            if (resp.errorCode === 0) {
                result = true;
            } else {
                this.Interaction.showHttpError(`强制下线未成功，相信信息：${resp.errorCode}/${resp.errorMsg}`);
            }
        } catch (exp) {
            this.Interaction.showHttpError('操作失败 !');
        }

        return Promise.resolve(result);
    }

    async getOnlineList(callback) {
        try {
            let resp = await this.strategyRepo.getOnlineInstance(this.strategyId);
            if (resp.errorCode === 0) {
                resp.data.forEach(item => {
                    try {
                        let json = item.description;
                        let obj = JSON.parse(json);
                        Object.keys(obj).forEach(objKey => {
                            Object.assign(this.appData.tabStructure, {
                                [objKey]: objKey,
                            });
                            item[objKey] = obj[objKey];
                        });
                    } catch (e) {
                        console.log('cannot parse to json');
                        this.appData.tabStructure['description'] = 'description';
                    }
                });
                this.appData.onlineList = resp.data;
                if (typeof callback === 'function') {
                    callback();
                }
            } else {
                this.Interaction.showHttpError(`获取在线列表未成功，详细信息：${resp.errorCode}/${resp.errorMsg}`);
            }
        } catch (exp) {
            this.Interaction.showHttpError('获取在线列表失败!');
        }
    }

    adaptOnlineList(strategyId, callback) {
        this.strategyId = strategyId;
        this.getOnlineList(callback);
    }
}

module.exports = new pageController();
