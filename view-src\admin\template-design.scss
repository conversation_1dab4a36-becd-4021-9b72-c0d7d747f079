.t-design {
    height: calc(100% - 30px);
    page-break-before: always;
    user-select: none;

    .clearfix {
        clear: both;
    }

    .design-area {
        display: flex;
        // height: 1px;
    }
    /* header */
    .design-header {
        width: 100%;
        height: 40px;
        line-height: 40px;
        background: rgba(32, 34, 37, 0.5);
        box-shadow: 0 0 20px rgb(0, 0, 0);
        z-index: 1;
        i {
            padding-left: 10px;
        }
    }
    .icon-save {
        font-size: 22px;
        color: rgba(255, 255, 255, 0.83);
        cursor: pointer;
    }
    /* list */
    .list-item {
        transition: all 1s;
        display: inline-block;
        margin-right: 10px;
    }
    .list-enter, .list-leave-to
    /* .list-leave-active for below version 2.1.8 */ {
        opacity: 0;
        transform: translateY(30px);
    }
    .list-leave-active {
        position: absolute;
    }
    .list-move {
        transition: transform 0.2s;
    }
    /* drag-widget */
    .drag-widget {
        background: #28292d;
        width: 160px;
        position: relative;
        overflow-x: hidden;
        overflow-y: auto;
        height: calc(100% - 70px);
    }
    .drag-widget::-webkit-scrollbar {
        display: none;
    }
    .drag-widget .query input {
        border-radius: 0;
    }
    .type-box {
        cursor: move;
        padding: 10px;
        user-select: none;
        color: white;
    }
    .type-box:hover {
        background: rgba(117, 209, 255, 0.8);
    }
    .drag-widget .delete-area,
    .config-area .delete-area {
        height: 100%;
        width: 100% !important;
        max-width: 100% !important;
        min-width: 100% !important;
        overflow: hidden;
        padding: 0;
        top: 0;
        left: 0;
        position: absolute;
        background: rgb(31, 37, 41) !important;
        z-index: 1;
    }
    .drag-widget .delete-area::before,
    .config-area .delete-area::before {
        content: '\e6c8';
        display: block;
        position: absolute;
        font-size: 30px;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        color: #fff !important;
    }
    /* preview-area */
    .preview-area {
        flex: 1;
        position: relative;
        background: #101013;
        overflow-x: hidden;
        overflow-y: auto;
        min-height: 1px;
    }
    .preview-area .hover-box {
        position: fixed;
        transition: all 0.2s;
        background: rgba(159, 193, 255, 0.479);
        z-index: 0;
    }
    .drag-area > span {
        display: flex;
        flex-wrap: wrap;
        height: calc(100% - 70px);
    }
    .preview-area .preview-box {
        flex: 1;
        position: relative;
        margin-bottom: 8px;
        overflow: hidden;
        max-height: 436px;
        background-color: rgba(70, 70, 70, 0.1);
    }
    .preview-box.current {
        background: rgba(85, 255, 193, 0.28);
    }
    .item-name {
        font-size: 12px;
    }
    .close-btn {
        cursor: pointer;
        position: absolute;
        right: 10px;
        top: 10px;
    }
    .hint {
        position: absolute;
        top: 50%;
        left: 50%;
        font-size: 28px;
        color: white;
        transform: translate(-50%, -50%);
    }
    .single-table > div:first-child {
        background: linear-gradient(90deg, rgba(80, 80, 80, 0.13) 0%, rgba(180, 180, 180, 0.13) 100%);
    }
    .single-table > div:last-child {
        background: linear-gradient(90deg, rgba(180, 180, 180, 0.13) 0%, rgba(80, 80, 80, 0.13) 100%);
    }
    .single-table div {
        padding: 10px;
        color: white;
    }
    .multi-title {
        padding: 10px;
        background: linear-gradient(90deg, rgba(80, 80, 80, 0.13) 0%, rgba(180, 180, 180, 0.13) 50%, rgba(80, 80, 80, 0.13) 100%);
        color: white;
    }
    .empty {
        height: 30px;
        left: 0;
        width: 100%;
        /* height: 100%; */
        display: flex;
        justify-content: center;
        align-items: center;
        background: rgba(191, 191, 191, 0.18);
        font-size: 14px;
    }
    .interval {
        position: absolute;
        top: 10px;
        right: 15px;
        color: rgba(255, 255, 255, 0.5);
    }
    .excel {
        position: absolute;
        top: 7px;
        right: 35px;
        color: rgba(255, 255, 255, 0.5);
    }
    .excel i {
        font-size: 20px;
        cursor: pointer;
    }
    .preview-area .drag-box {
        position: fixed;
        background: rgba(255, 167, 38, 0.5);
        z-index: 2;
        width: 100px;
        height: 20px;
        line-height: 20px;
        cursor: move;
        padding: 10px;
        color: #fff;
        overflow: hidden;
        text-overflow: ellipsis;
    }
    /* config-area */
    .config-area {
        width: 300px;
        background: #28292d;
        position: relative;
        padding-top: 10px;
    }
    .interval-input {
        width: 160px;
    }
    .config-form {
        position: absolute;
        z-index: 1;
        padding: 0 10px;
    }
    .el-input-number__decrease,
    .el-input-number__increase {
        border: none;
        background: rgba(212, 212, 212, 0.13);
        transition: all 0.2s;
    }
    .el-input-number__decrease:hover,
    .el-input-number__increase:hover {
        background: rgba(212, 212, 212, 0.27);
    }
    .el-checkbox__inner {
        border-color: rgba(255, 255, 255, 0.274);
        background-color: #191d25;
    }
    .save-btn {
        margin-left: 30px;
        background-color: rgb(93, 93, 93);
    }
    .config-drag {
        width: 300px;
        height: 100%;
        position: absolute;
        top: 0;
        right: 0;
    }
    .config-drag .preview-box {
        z-index: 999;
    }
    .none {
        display: none;
    }
    .desp {
        color: rgba(255, 255, 255, 0.73);
        padding-left: 30px;
        font-size: 14px;
    }
    /* sidebar */
    .sidebar-container {
        // height: 100%;
        width: inherit;
        transition: all 0.3s;
    }
    .scrollbar-wrapper {
        overflow-x: hidden !important;
        margin-bottom: 0 !important;
        width: 100%;
    }
    .scrollbar-wrapper::-webkit-scrollbar {
        display: none;
    }
    .preview-area::-webkit-scrollbar {
        display: none;
    }
    /* table */
    .el-table,
    .el-table__expanded-cell,
    .el-table .el-table__body tr,
    .el-table th,
    .el-table tr {
        background-color: transparent !important;
    }
    .el-table .el-table__header-wrapper {
        background-color: rgba(190, 190, 190, 0.19);
    }
    .el-table .el-table__body-wrapper {
        background-color: rgba(70, 70, 70, 0.1);
    }
    .el-table .el-table__header th > .cell,
    .el-table .el-table__footer td > .cell,
    .el-table .cell {
        color: rgba(255, 255, 255, 0.85);
    }
    .el-table .el-table__header th,
    .el-table .el-table__body tr.el-table__row--striped td {
        background-color: rgba(180, 180, 180, 0.13);
    }
    .el-table .el-table__header th.is-leaf,
    .el-table .el-table__body tr td,
    .el-table .el-table__footer tr td {
        box-shadow: none;
    }
    .el-table .el-table__body tr:hover {
        background-color: transparent !important;
        background: linear-gradient(90deg, rgba(60, 60, 60, 0.13) 0%, rgba(200, 200, 200, 0.13) 50%, rgba(60, 60, 60, 0.13) 100%) !important;
    }
    .el-table .el-table__body tr:hover > td {
        background-color: transparent !important;
    }
    .el-table .el-table__empty-block {
        background: rgba(191, 191, 191, 0.18);
    }
    /* tab */
    .el-tabs--card > .el-tabs__header {
        border: none !important;
        margin: 0;
    }
    .el-tabs__item {
        padding: 0 10px !important;
        border: none !important;
        flex: 1;
        transition: all 0.3s !important;
        height: 36px;
        line-height: 36px;
    }

    .el-tabs__item {
        color: white;
    }
    .el-tabs__item:hover {
        background-color: rgba(160, 160, 160, 0.3);
    }
    .el-tabs__item.is-active {
        background-color: rgba(160, 160, 160, 0.3);
    }
    .el-tabs__item.is-active:hover {
        background-color: rgba(175, 175, 175, 0.527);
    }
    .el-tabs__nav {
        border: none !important;
        display: flex;
        width: 100%;
    }
    .template-header {
        position: fixed;
        z-index: 999;
        bottom: 20px;
        left: 50%;
        transform: translateX(-50%);
        padding: 4px;
        background-color: rgba(175, 175, 175, 0.212);
        opacity: 0.2;
        transition: all 0.3s;
        overflow: hidden;
    }

    .el-form-item__label {
        color: white;
    }

    .template-header:hover {
        opacity: 1;
    }
    .normal-btn {
        display: block;
        float: right;
        width: 14px;
        margin-left: 8px;
        margin-top: 4px;
        cursor: pointer;
        font-size: 16px;
        color: rgba(255, 255, 255, 0.83);
    }
    .toggle-btn {
        display: block;
        float: right;
        width: 24px;
        margin-left: 8px;
        cursor: pointer;
        font-size: 22px;
        color: rgba(255, 255, 255, 0.83);
        transition: all 0.3s;
    }
    .custom-fade-enter-active {
        transition: all 0.3s ease;
    }
    .custom-fade-leave-active {
        transition: all 0.3s cubic-bezier(1, 0.5, 0.8, 1);
    }
    .custom-fade-enter,
    .custom-fade-leave-to {
        transform: translateX(10px);
        opacity: 0;
    }
    .toggle-btn.rotate {
        margin-left: 0;
    }
    .toggle-btn::before {
        transition: all 0.3s;
        display: block;
    }
    .rotate::before {
        transform: rotate(90deg);
    }

    .regression-container {
        padding: 20px 10px 20px;
        margin-bottom: 10px;
        border-bottom: 1px dashed rgba(161, 170, 183, 0.45);
        display: block;
        width: 100%;
        float: left;
    }

    .regression-msg-item {
        width: 11.111111111111%;
        float: left;
        display: block;
        height: 45px;
    }

    .regression-msg-money,
    .regression-msg-time {
        width: 15%;
    }

    .regression-msg-percentage {
        width: 7.2%;
    }

    .regression-msg-title {
        color: #0c84da;
        font-size: 12px;
        display: inline-block;
        margin-bottom: 8px;
    }

    @media print {
        .no-print {
            display: none;
        }
        .drag-area > span {
            height: unset;
        }
    }
}
