const DataTables = require('../../libs/3rd/vue-data-tables.min.3.4.2');
const BaseAdminView = require('./baseAdminView').BaseAdminView;
const drag = require('../../directives/drag');
const { app } = require('@electron/remote');
const encryptPasscodeAllowed = app.encryptionOptions.encryptPasscode;

class View extends BaseAdminView {

    get systemEnum() {
        return require('../../config/system-enum').systemEnum;
    }

    get repoTerminal() {
        return require('../../repository/terminal').repoTerminal;
    }

    constructor(view_name) {

        super(view_name, '终端管理');
        this.$container = null;
        this.statusOptions = {
            enabled: {
                status: 1,
                label: '启用',
            },
            disabled: {
                status: 0,
                label: '禁用',
            },
        };
        this.searching = {
            prop: ['terminalName', 'description'],
            value: '',
        };
        this.terminalConfigs = this.helper.dict2Array(this.systemEnum.terminalInterface);
        this.terminalList = [];
        this.terminalListHash = {};
    }

    createApp() {
        
        const controller = this;
        this.tableProps = { maxHeight: 500, ...this.systemSetting.tableProps };
        this.vueApp = new Vue({
            el: this.$container.querySelector('.terminal-view-root'),
            components: {
                DataTables: DataTables.DataTables,
            },
            data: {
                searching: this.searching,
                filters: [this.searching],
                statusOptions: this.statusOptions,
                terminalList: this.terminalList,
                terminalListHash: this.terminalListHash,
                tableProps: this.tableProps,
                searchDef: {
                    inputProps: {
                        placeholder: '输入关键字筛选',
                        prefixIcon: 'el-icon-search',
                    },
                },
                paginationDef: { ...this.systemSetting.tablePagination, layout: 'prev,pager,next,sizes,total' },
                dialog: {
                    visible: false,
                },
                title: null,
                rules: {
                    terminalName: [
                        {
                            required: true,
                            message: '请输入终端名称',
                        },
                        controller.systemSetting.specialCharacterFilterRule,
                        controller.systemSetting.limitInputLengthRule,
                    ],
                    pwd: [
                        {
                            required: true,
                            message: '请输入密码',
                        },
                        controller.systemSetting.specialCharacterFilterRule,
                        controller.systemSetting.limitInputLengthRule,
                    ],
                    interfaceType: [
                        {
                            required: true,
                            message: '请选择类型',
                        },
                    ],
                    status: [{ type: 'number', required: true, message: '请选择状态' }],
                },
                interfaceTypeOptions: this.helper.dict2Array(this.systemEnum.terminalInterface).map(type => ({
                    text: type.mean,
                    value: type.code,
                })),
                terminalModel: {
                    terminalName: '',
                    pwd: '',
                    description: '',
                    interfaceType: '',
                    id: '',
                    status: 1,
                },
                isShowPassword: true,
            },
            directives: {
                drag,
            },
            methods: {
                changeInputMode() {
                    this.isShowPassword = !this.isShowPassword;
                },
                updateTerminal(terminal) {

                    let statuses = controller.statusOptions;
                    terminal.status = terminal.status == statuses.enabled.status ? statuses.disabled.status : statuses.enabled.status;
                    this.setTerminalModel(terminal);
                    this.dialog.visible = true;
                },
                formatAccessible(status, options) {
                    // 0 禁用 1 启用
                    return controller.helperUi.makeYesNoLabelHtml(status === controller.statusOptions.enabled.status, options);
                },
                formatType: interface_type => {
                    return this.formatTerminalInterfaceType(interface_type);
                },
                delRecord(selectRow) {
                    controller.interaction.showConfirm({
                        title: '提示',
                        message: '确定要移除当前终端吗?',
                        confirmed: () => {
                            controller.removeTerminal(selectRow);
                        },
                    });
                },
                saveConfirm() {
                    this.$refs.terminalModel.validate(valid => {
                        if (!valid) {
                            console.log('please check your input!');
                            return;
                        }
                        let createMsg = '新建终端之后，终端名称将不能再次修改，确定要提交吗?';
                        let updateMsg = '确定要修改终端信息吗?';
                        controller.interaction.showConfirm({
                            title: '提示',
                            message: this.terminalModel.id ? updateMsg : createMsg,
                            confirmed: () => {
                                var method = this.terminalModel.id ? 'updateTerminal' : 'createTerminal';
                                if (encryptPasscodeAllowed) {
                                    this.terminalModel.pwd != null && this.terminalModel.pwd != '' && this.terminalModel.pwd != undefined ?
                                        this.terminalModel.pwd = controller.helper.aesEncrypt(this.terminalModel.pwd) : null;
                                }
                                controller.repoTerminal[method](this.terminalModel).then(
                                    resp => {
                                        if (resp.errorCode === 0) {
                                            controller.interaction.showSuccess('操作成功!');
                                            this.updateView(resp.data);
                                            this.handleClose();
                                        } else {
                                            controller.interaction.showError(`操作终端失败，详细信息：${resp.errorMsg}！`);
                                        }
                                    },
                                    () => {
                                        this.destroyInput();
                                        controller.interaction.showError('操作失败!');
                                    },
                                );
                            },
                        });
                    });
                },
                addTerminal() {
                    this.dialog.visible = true;
                    this.title = '创建终端';
                    if (this.$refs.terminalModel) {
                        this.$refs.terminalModel.clearValidate();
                    }
                },
                updateView(data) {
                    if (typeof this.terminalListHash[data.id] === 'undefined') {
                        this.terminalList.unshift(data);
                    } else {
                        var index = this.terminalList.findIndex(cdt => cdt.id == data.id);
                        this.$set(this.terminalList, index, data);
                    }
                    this.terminalListHash[data.id] = data;
                },
                destroyInput() {
                    Object.keys(this.terminalModel).forEach(key => {
                        this.terminalModel[key] = '';
                    });
                    if (this.$refs.terminalModel) {
                        this.$refs.terminalModel.clearValidate();
                    }
                },
                handleClose() {
                    this.destroyInput();
                    this.dialog.visible = false;
                },
                editRow(row) {
                    this.destroyInput();
                    this.dialog.visible = true;
                    this.title = `修改终端 ${row.terminalName}`;
                    this.setTerminalModel(row);
                },
                setTerminalModel(row) {
                    Object.keys(row).forEach(prop => {
                        if (prop != 'pwd') {
                            this.terminalModel[prop] = row[prop];
                        }
                    });
                },
            },
        });
        this.vueApp.$nextTick(() => {
            this.resizeWindow();
        });
    }

    formatTerminalInterfaceType(interface_type) {

        let matched = this.terminalConfigs.find(x => x.code === interface_type);
        if (matched) {
            return `<span class="s-flag ${ matched.flagBgColor }">${ matched.mean }</span>`;
        }
        else {
            return `<span class="s-flag">未知终端</span>`;
        }
    }

    async removeTerminal(selectRow) {
        try {
            let resp = await this.repoTerminal.deleteTerminal(selectRow.id);
            if (resp.errorCode === 0) {
                this.interaction.showSuccess('删除终端信息成功!');
                this.terminalList.remove(cdt => cdt.id == selectRow.id);
                delete this.terminalListHash[selectRow.id];
            } else {
                this.interaction.showError(`删除终端信息失败，详细信息:${resp.errorMsg}!`);
            }
        } catch (exp) {
            console.log(exp);
            this.interaction.showError('删除终端信息失败!');
        }
    }

    async getTerminalList() {

        let loading = this.interaction.showLoading({
            text: '请求终端列表...',
        });
        this.terminalList.clear();
        try {
            let resp = await this.repoTerminal.getAll();
            if (resp.errorCode === 0) {
                this.terminalList.merge(resp.data.orderByDesc(cdt => cdt.id));
                resp.data.forEach(item => {
                    this.terminalListHash[item.id] = item;
                });
            } else {
                this.interaction.showHttpError(`请求终端列表数据失败，详细信息:${resp.errorCode}/${resp.errorMsg}`);
            }
        } catch (exp) {
            this.interaction.showHttpError('请求终端列表数据失败!');
        } finally {
            loading.close();
        }
    }

    refresh() {

        this.searching.value = null;
        this.getTerminalList();
    }

    resizeWindow() {
        
        var winHeight = this.thisWindow.getSize()[1];
        this.tableProps.maxHeight = winHeight - 140;
    }

    build($container) {

        this.$container = $container;
        this.createApp();
        this.getTerminalList();
    }
}

module.exports = View;
