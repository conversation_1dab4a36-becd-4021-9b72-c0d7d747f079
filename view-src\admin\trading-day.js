const BaseAdminView = require('./baseAdminView').BaseAdminView;
class View extends BaseAdminView {
    get repoTradingDay() {
        return require('../../repository/trading-day').repoTradingDay;
    }

    constructor(view_name) {
        super(view_name, '交易日管理');

        this.vueApp = null;

        this.$container = null;

        let StepLength = 12;

        this.AppData = {
            $date: new Date(),
            groupsDate: {},
            stepLength: StepLength,
            sendDateSet: [],
            extendDate: '',
            counter: 0,
            popover: {
                visible: false,
            },
            MonthTitle: [
                {
                    index: 0,
                    label: '日',
                },
                {
                    index: 1,
                    label: '一',
                },
                {
                    index: 2,
                    label: '二',
                },
                {
                    index: 3,
                    label: '三',
                },
                {
                    index: 4,
                    label: '四',
                },
                {
                    index: 5,
                    label: '五',
                },
                {
                    index: 6,
                    label: '六',
                },
            ],
        };
    }

    createApp() {
        let controller = this;
        this.vueApp = new Vue({
            el: this.$container.querySelector('.trading-day-view-root'),
            data: this.AppData,
            mixins: [],
            filters: {
                formatTime: time => {
                    let date = new Date(time);
                    if (isNaN(date)) {
                        return '---';
                    }

                    return this.helper.time2String(date, 'yyyy-MM-dd');
                },
            },
            computed: {
                isEmpty: () => {
                    return Object.keys(this.AppData.groupsDate).length <= 0;
                },
            },
            methods: {
                setTradingDay: flag => {
                    this.setTradingDay(flag, this.vueApp.sendDateSet);
                },
                setting: context => {
                    let date = context.tradingDay;
                    //超时或者周末是不允许高亮的
                    if (this.isTimeout(date) || this.isWeekend(date)) {
                        return;
                    }
                    context.highlight = !context.highlight;
                    this.vueApp.updateSendParams(context);
                },
                // 维持提交给后端的数据，当当前日期高亮就添加，否则就从容器中移除
                updateSendParams(context) {
                    let tradingDay = context.tradingDay;

                    if (context.highlight) {
                        this.sendDateSet.push(context);
                    } else {
                        this.sendDateSet.remove(cdt => cdt.tradingDay === tradingDay);
                    }
                },
                isCurrentMonth: item => {
                    try {
                        let tradingDay = item[0].tradingDay;
                        let compare = new Date(tradingDay);
                        let now = new Date();
                        return compare.getFullYear() === now.getFullYear() && compare.getMonth() === now.getMonth();
                    } catch (e) {
                        return false;
                    }
                },
                getTradingCellClass: day => {
                    //默认交易日的话，就是最简单的基本样式
                    let tradingDay = day.tradingDay;
                    let outputClass = {};
                    let isTimeout = this.isTimeout(tradingDay);
                    // let isWeekend = this.isWeekend(tradingDay);
                    //如果超时，就没有必要再进行下面的操作
                    if (isTimeout) {
                        return 'trading-disabled';
                    }

                    //如果是周末且不是交易日，直接维持周末本来的样式
                    if (!day.isTradingDay) {
                        outputClass['no-trading-day'] = true;
                    } else {
                        outputClass['trading-day'] = true;
                    }

                    //选中样式
                    if (day.highlight) {
                        outputClass['highlight-cell'] = true;
                    }

                    if (!this.isWeekend(tradingDay)) {
                        outputClass['normal-cell'] = true;
                    }

                    return outputClass;
                },
                preYear: direction => {
                    if (this.AppData.counter <= 0) {
                        this.interaction.showWarning('没有更多数据了!');
                        return;
                    }

                    this.offsetYear(direction);
                },
                nextYear: direction => {
                    if (Object.keys(this.AppData.groupsDate).length <= 0) {
                        this.interaction.showWarning('没有更多数据了!');
                        return;
                    }

                    this.offsetYear(direction);
                },
                getMonthStartPosition: offset => {
                    return this.getMonthStartPosition(offset);
                },
                getMonthOffset: timestamp => {
                    return this.getMonthOffset(timestamp);
                },
                getLineTitle: index => {
                    return this.getLineTitle(index);
                },
                getCurrentMonth: nextCount => {
                    return this.getCurrentMonth(nextCount);
                },
                getCurrentYear: offset => {
                    return this.getCurrentYear(offset);
                },
                getDateCell: (counter, offset) => {
                    return this.getDateCell(counter, offset);
                },
                getMonthDateCounter: offset => {
                    return this.getMonthDateCounter(offset);
                },
                isTimeout: context => {
                    return this.isTimeout(context.tradingDay);
                },
                isWeekend: (year, month, day) => {
                    if (!month && !day) {
                        return this.isWeekend(year);
                    }

                    let dateText = year + '-' + month + '-' + day;
                    return this.isWeekend(dateText);
                },
                extendTradingDate: () => {
                    let lastYear = this.vueApp.extendDate.getFullYear();
                    let now = this.AppData.$date.getFullYear();

                    if (lastYear <= now) {
                        this.interaction.showError('选择的时间有误!');
                        return;
                    }

                    let sendYears = [];
                    for (let i = now + 1; i <= lastYear; i++) {
                        sendYears.push(i);
                    }

                    this.generateTradingDay(sendYears, () => {
                        this.vueApp.popover.visible = false;
                    });
                },
            },
        });
    }

    checkTradingDay(flag, tradingDays) {
        let result = true;
        if (tradingDays.length <= 0) {
            this.interaction.showWarning('请先选择日期!');
            return false;
        }

        //1˚：已经是交易日，就不再需要设置交易日了

        if (flag && tradingDays.some(cdt => cdt.isTradingDay)) {
            this.interaction.showWarning('你选择的日期中包含交易日，无需重复设置!');
            return false;
        }

        //2˚：不是交易日的，就不能再设置成非交易日了
        if (!flag && tradingDays.some(cdt => !cdt.isTradingDay)) {
            this.interaction.showWarning('你选择的日期中包含非交易日，无需重复设置!');
            return false;
        }

        //3˚：周末是不能设置成交易日的
        if (flag && tradingDays.some(cdt => this.isWeekend(cdt.tradingDay))) {
            this.interaction.showWarning('根据证券政策法规，周末无法交易!');
            return false;
        }

        return result;
    }

    normalizeSendParams() {
        this.AppData.sendDateSet = [];
    }

    async setTradingDay(flag, tradingDays) {
        if (tradingDays && !Array.isArray(tradingDays)) {
            tradingDays = [tradingDays];
        }

        let checkResult = this.checkTradingDay(flag, tradingDays);

        if (!checkResult) {
            return false;
        }

        let requestSet = [];

        tradingDays.forEach(current => {
            if (flag) {
                requestSet.push(this.addTradingDay(current.tradingDay));
            } else {
                requestSet.push(this.deleteTradingDay(current.tradingDay));
            }
        });

        let resp = await Promise.all(requestSet);

        //当所有的交易日成功设置
        if (resp.every(cdt => cdt.errorCode === 0)) {
            this.interaction.showSuccess('操作成功!');
            tradingDays.forEach(cdt => {
                cdt.highlight = false;
                cdt.isTradingDay = flag;
            });
            this.normalizeSendParams();
        }
    }

    async addTradingDay(tradingDay, callback) {
        let resp = null;

        tradingDay = this.helper.time2String(new Date(tradingDay), 'yyyyMMdd');

        let loading = this.interaction.showLoading({
            text: '操作中，请稍后...',
        });

        try {
            resp = await this.repoTradingDay.addTradingDay(tradingDay);
            if (resp.errorCode === 0) {
                if (typeof callback === 'function') {
                    callback(resp);
                }
            } else {
                this.interaction.showError(`操作失败，详细信息:${resp.errorCode}/${resp.errorMsg}`);
            }
        } catch (e) {
            this.interaction.showError('操作失败');
        } finally {
            loading.close();
        }

        return Promise.resolve(resp);
    }

    async deleteTradingDay(tradingDay) {
        let resp = null;

        tradingDay = this.helper.time2String(new Date(tradingDay), 'yyyyMMdd');

        let loading = this.interaction.showLoading({
            text: '操作中，请稍后...',
        });

        try {
            resp = await this.repoTradingDay.deleteTradingDay(tradingDay);
            if (resp.errorCode === 0) {
                // if (typeof callback === 'function') {
                //     callback(resp);
                // }
            } else {
                this.interaction.showError(`操作失败，详细信息:${resp.errorCode}/${resp.errorMsg}`);
            }
        } catch (e) {
            this.interaction.showError('操作失败');
        } finally {
            loading.close();
        }

        return Promise.resolve(resp);
    }

    async getTradingDayBatch(endDay, beginDay) {
        if (typeof beginDay === 'undefined') {
            beginDay = this.helper.time2String(new Date(), 'yyyyMMdd');
        }

        let loading = this.interaction.showLoading({
            text: '获取交易日列表中，请稍后...',
        });

        try {
            let resp = await this.repoTradingDay.getTradingDayBatch({
                beginDay: beginDay,
                endDay: endDay,
            });

            if (resp.errorCode === 0) {
                this.vueApp.groupsDate = {};
                if (Array.isArray(resp.data)) {
                    // 将后端返回的日期按照大小顺序排序
                    resp.data = resp.data.orderBy(r => r.tradingDay);
                    console.log(resp.data);
                    let infrastructure = {};
                    resp.data.forEach(tradingDay => {
                        tradingDay.isTradingDay = false;
                        tradingDay.tradingDay = this.helper.string2Date(tradingDay.tradingDay).format('yyyy-MM-dd');
                        let thisDate = new Date(tradingDay.tradingDay);

                        let objKey =
                            thisDate.getMonth() + 1 < 10 ? '0' + (thisDate.getMonth() + 1) : thisDate.getMonth() + 1;

                        if (typeof infrastructure[thisDate.getFullYear() + '-' + objKey] === 'undefined') {
                            infrastructure[thisDate.getFullYear() + '-' + objKey] = [
                                {
                                    isTradingDay: true,
                                    highlight: false,
                                    tradingDay: tradingDay.tradingDay,
                                },
                            ];
                        } else {
                            infrastructure[thisDate.getFullYear() + '-' + objKey].push({
                                isTradingDay: true,
                                highlight: false,
                                tradingDay: tradingDay.tradingDay,
                            });
                        }
                    });

                    Object.keys(infrastructure).forEach(key => {
                        //当前月的第一天
                        let thisMonthDate = new Date(key + '-01');
                        let now = new Date();

                        //当前月的总天数
                        let length = this.getMonthDateCounter(thisMonthDate.getMonth() - now.getMonth());

                        //需要记住 补足的日期数
                        let insert = [];

                        var context = infrastructure[key];

                        for (var i = 1; i <= length; i++) {
                            var flag2 = true;
                            var assembleText = key + '-' + (i >= 10 ? i : '0' + i);
                            for (var j = 0; j < context.length; j++) {
                                if (assembleText === context[j].tradingDay) {
                                    flag2 = false;
                                    break;
                                }
                            }

                            if (flag2) {
                                insert.push(assembleText);
                            }
                        }

                        insert.forEach(item => {
                            infrastructure[key].push({
                                tradingDay: item,
                                isTradingDay: false,
                                highlight: false,
                            });
                        });

                        infrastructure[key] = infrastructure[key].orderBy(cdt => cdt.tradingDay);
                    });

                    this.vueApp.groupsDate = infrastructure;
                    console.log(this.vueApp.groupsDate);
                }
            } else {
                this.interaction.showError('获取交易日列表失败!');
            }
        } catch (e) {
            console.log(e);
            this.interaction.showError('获取交易日列表失败!');
        } finally {
            loading.close();
        }
    }

    getCurrentTradingDay() {
        this.repoTradingDay.getCurrentTradingDay().then(resp => {
            console.log(resp.data);
        });
    }

    async generateTradingDay(date_list, callback) {
        if (!Array.isArray(date_list)) {
            date_list = [date_list];
        }

        let loading = this.interaction.showLoading({
            text: '操作正在进行，请稍后...',
        });

        try {
            let promiseGenerators = [];

            date_list.forEach(t_year => {
                promiseGenerators.push(this.repoTradingDay.generateTradingDay(t_year));
            });

            let resp = await Promise.all(promiseGenerators);
            if (resp.every(cdt => cdt.errorCode === 0)) {
                this.interaction.showSuccess('生成成功!');
                if (typeof callback === 'function') {
                    callback();
                }
            } else {
                this.interaction.showError('生成交易日失败!');
            }
        } catch (e) {
            this.interaction.showError('生成交易日失败!');
        } finally {
            loading.close();
        }
    }

    //this section construct

    offsetYear(direction) {
        this.AppData.counter += direction;
        let date = new Date(this.AppData.$date);
        date.setFullYear(date.getFullYear() + this.AppData.counter);
        let { start, end } = this.getStartEnd(date);
        this.getTradingDayBatch(end.format('yyyyMMdd'), start.format('yyyyMMdd'));
        return this;
    }

    getStartEnd(time) {
        let end = new Date(time);
        end.setMonth(11);
        end.setDate(31);
        end.setHours(0);
        end.setMinutes(0);
        end.setSeconds(0);

        let start = new Date(time);
        start.setMonth(0);
        start.setDate(1);
        start.setMinutes(0);
        start.setHours(0);
        start.setSeconds(0);

        return { start, end };
    }

    initializeTradingDay() {
        let { start, end } = this.getStartEnd(this.AppData.$date);

        this.getTradingDayBatch(end.format('yyyyMMdd'), start.format('yyyyMMdd'));

        return this;
    }

    getLineTitle(index) {
        return this.AppData.MonthTitle.find(cdt => cdt.index === index).label;
    }

    isLeapYear(year) {
        return (year % 4 == 0 && year % 100 != 0) || year % 400 == 0;
    }

    isWeekend(date) {
        let $date = new Date(date);
        return $date.getDay() === 0 || $date.getDay() === 6;
    }

    isTimeout(context) {
        let date = new Date(context);
        let now = new Date();
        return now.getTime() - date.getTime() > 3600 * 24 * 1000;
    }

    getMonthOffset(timestamp) {
        let now = this.AppData.$date;

        let nowMonth = now.getMonth();

        let nowYear = now.getFullYear();

        let future = new Date(timestamp);

        let futureYear = future.getFullYear();

        return future.getMonth() + 12 * (futureYear - nowYear) - nowMonth;
    }

    /**
     * return current month days
     */
    getMonthDateCounter(offset) {
        let date = new Date(this.AppData.$date);
        if (typeof offset !== 'undefined') {
            let month = date.getMonth();
            date.setMonth(offset + month);
        }

        let year = date.getFullYear();

        let isLeapYear = this.isLeapYear(year);

        let month = date.getMonth() + 1;
        let counter = 0;

        switch (month) {
            case 1:
            case 3:
            case 5:
            case 7:
            case 8:
            case 10:
            case 12:
                counter = 31;
                break;
            case 4:
            case 6:
            case 9:
            case 11:
                counter = 30;
                break;
            case 2:
                counter = isLeapYear ? 29 : 28;
                break;
            default:
        }

        return counter;
    }

    getDateCell(counter, offset) {
        return counter > this.getMonthDateCounter(offset) ? counter - this.getMonthDateCounter(offset) : counter;
    }

    getMonthStartPosition(offset) {
        let thisDate = new Date(this.AppData.$date);

        if (typeof offset !== 'undefined') {
            let month = thisDate.getMonth() + offset;
            thisDate.setMonth(month);
        }

        thisDate.setDate(1);
        thisDate.setHours(0);
        thisDate.setMinutes(0);
        thisDate.setSeconds(0);
        return thisDate.getDay();
    }

    getCurrentMonth(nextCount) {
        let thisDate = new Date(this.AppData.$date);
        thisDate.setDate(1);
        thisDate.setMinutes(0);
        thisDate.setHours(0);
        thisDate.setSeconds(0);
        if (nextCount) {
            let now = thisDate.getMonth();
            thisDate.setMonth(now + nextCount);
        }

        let month = thisDate.getMonth();
        return month + 1;
    }

    getCurrentYear(offset) {
        let thisDate = new Date(this.AppData.$date);

        if (offset) {
            let now = thisDate.getMonth();
            thisDate.setMonth(now + offset);
        }

        let year = thisDate.getFullYear();

        return year;
    }

    resizeWindow() {
        var winHeight = this.thisWindow.getSize()[1];
        //Title的高度是35 Tab的高度是30 底部状态栏30
        var extraHeight = 35 + 30 + 30;
        var net_height = winHeight - extraHeight;
        var box = this.$container.querySelector('.trading-day-view-root .s-scroll-bar');
        if (box) {
            box.style.height = net_height + 'px';
        }
    }

    build($container) {
        this.$container = $container;
        this.createApp();
        this.initializeTradingDay();
        this.resizeWindow();
    }
}

module.exports = View;
