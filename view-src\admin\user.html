<div class="user-view-root">
    <template>
        <div class="s-scroll-bar" style="overflow: auto">
            
            <div class="s-typical-toolbar">
                
                <el-button type="primary" @click="openUserCreationDialog" size="mini">
                    <i class="iconfont icon-add"></i> 创建用户
                </el-button>
                
                <el-input v-model="searching.value" style="width: 160px;" placeholder="请输入关键词" clearable>
                    <i slot="prefix" class="el-input__icon el-icon-search"></i>
                </el-input>

            </div>

            <data-tables layout="pagination,table" table-name="userManagement" :filters="filters" table-label="用户管理"
                role="superAdmin,orgAdmin" @row-click="handleRowClick" :default-sort="{prop: 'id', order: 'descending'}"
                ref="table" class="s-searchable-table" v-bind:data="userList" v-bind:table-props="tableProps"
                v-bind:pagination-props="paginationDef" v-bind:search-def="searchDef" layout="pagination,table">
                <el-table-column key="userManagementIndex" prop="index" show-overflow-tooltip label="序号" type="index" align="center" width="50"></el-table-column>
                <el-table-column key="userManagementUsername" show-overflow-tooltip label="用户名" prop="username" min-width="100"></el-table-column>
                <el-table-column key="userManagementFullName" show-overflow-tooltip label="真实姓名" prop="fullName"
                    min-width="80"></el-table-column>
                <el-table-column sortable key="userManagementOrgName" show-overflow-tooltip label="机构名称" prop="orgName"
                    min-width="120"></el-table-column>
                <el-table-column key="userManagementRoleType1" label="用户角色" prop="roleType" width="120"
                    show-overflow-tooltip>
                    <template slot-scope="props">
                        <el-tooltip content="修改角色" placement="top" :enterable="false" :open-delay="850">
                            <a class="icon-button iconfont icon-edit s-cp" style="margin-right: 5px"
                                @click="openRoleConfigurationDialog(props.row)"></a>
                        </el-tooltip>
                        <span>{{makeUserRole(props.row)}}</span>
                    </template>
                </el-table-column>
                <el-table-column key="userManagementPhone" label="手机" prop="phoneNo" align="center" width="100">
                </el-table-column>
                <el-table-column key="userManagementStatus" show-overflow-tooltip label="启用/禁用" align="center"
                    prop="status" width="110" sortable="custom">
                    <template slot-scope='props'>
                        <el-switch @change="handleChangeStatus(props.row)" v-model="props.row.status" :active-value="1"
                            :inactive-value="0"></el-switch>
                    </template>
                </el-table-column>

                <el-table-column key="userManagementOnline" label="在线" prop="online" width="60" align="center">
                    <template slot-scope="scope">
                        <span :class="makeOnlineClass(scope.row)"
                            @click="forceOffline(scope.row)">{{makeOnlineStatus(scope.row)}}</span>
                    </template>
                </el-table-column>

                <el-table-column key="userManagementOperation1" label="日志" prop="operation" width="70" class-name="s-col-oper">
                    <template slot-scope='props'>

                        <el-tooltip content="查看用户登录记录" placement="top" :enterable="false" :open-delay="850">
                            <a class="s-cp el-icon-search"
                                @click="viewLoginRecords(props.row)"></a>
                        </el-tooltip>

                        <el-tooltip content="查看操作记录" placement="top" :enterable="false" :open-delay="850">
                            <a class="s-cp el-icon-tickets"
                                @click="viewManagementRecords(props.row)"></a>
                        </el-tooltip>

                    </template>
                </el-table-column>

                <el-table-column key="userManagementOperation2" label="操作" prop="operation" width="80" class-name="s-col-oper">
                    <template slot-scope='props'>

                        <el-tooltip content="修改" placement="top" :enterable="false" :open-delay="850">
                            <a class="s-cp el-icon-edit" @click="handleEdit(props.row)"></a>
                        </el-tooltip>

                        <el-tooltip content="重置密码" placement="top" :enterable="false" :open-delay="850">
                            <a class="s-cp icon-button iconfont icon-zhongzhimima"
                                @click="handleResetPassword(props.row)"></a>
                        </el-tooltip>

                        <el-tooltip content="删除" placement="top" :enterable="false" :open-delay="850">
                            <a class="s-cp icon-button el-icon-delete s-color-red" @click="handleRemove(props.row)"></a>
                        </el-tooltip>

                    </template>
                </el-table-column>
            </data-tables>

            <el-dialog class="user-dialog" width="600px" v-bind:title="dialog.user.title"
                v-bind:visible="dialog.user.visible" :show-close="false" :close-on-click-modal="false"
                :close-on-press-escape="false" v-drag>

                <el-form class="user-form" size="mini" v-bind:model="dialog.user.form" v-bind:rules="dialog.user.rules" ref="userForm" label-width="80px">
                    <el-row>


                        <el-col :span="12">
                            <el-form-item label="用户名" prop="username">
                                <el-input :disabled="dialog.user.disabled" v-model.trim="dialog.user.form.username" :maxlength="25"></el-input>
                            </el-form-item>
                        </el-col>

                        <el-col :span="12">
                            <el-form-item v-if="!dialog.user.form.id" label="密码" prop="password">
                                <el-input v-model.trim="dialog.user.form.password" type="password" :maxlength="20"></el-input>
                            </el-form-item>
                        </el-col>

                        <el-col :span="12">
                            <el-form-item label="真实姓名" prop="fullName">
                                <el-input v-model.trim="dialog.user.form.fullName" :maxlength="25"></el-input>
                            </el-form-item>
                        </el-col>

                        <el-col :span="12" v-if="superAdmin">
                            <el-form-item label="机构名称" prop="orgId">
                                <el-select style="width: 100%" v-model="dialog.user.form.orgId" @change="handleOrgSelect">
                                    <el-option v-for="org in orgList" :key="org.id" :label="org.orgName" :value="org.id"></el-option>
                                </el-select>
                            </el-form-item>
                        </el-col>

                        <el-col :span="12" v-else>
                            <el-form-item label="邮箱" prop="email">
                                <el-input v-model.trim="dialog.user.form.email" :maxlength="50"></el-input>
                            </el-form-item>
                        </el-col>

                        <el-col :span="12">
                            <el-form-item label="用户角色" prop="roleId">
                                <el-select @change="handleSelectRole" style="width: 100%" v-model="dialog.user.form.roleId">
                                    <el-option v-for="role in RoleList" :key="role.id" :label="role.roleName" :value="role.id"></el-option>
                                </el-select>
                            </el-form-item>
                        </el-col>

                        <el-col :span="12">
                            <el-form-item label="手机" prop="phoneNo">
                                <el-input v-model.trim="dialog.user.form.phoneNo" :maxlength="15"></el-input>
                            </el-form-item>
                        </el-col>

                        <el-col :span="12" v-if="superAdmin">
                            <el-form-item label="邮箱" prop="email">
                                <el-input v-model.trim="dialog.user.form.email" :maxlength="50"></el-input>
                            </el-form-item>
                        </el-col>

                        <el-col :span="12">
                            <el-form-item label="Mac地址" prop="mac">
                                <el-input v-model.trim="dialog.user.form.mac" :maxlength="17" placeholder="如A1-B2-C3-D4-E5-F6">
                                    <template slot="suffix">
                                        <el-tooltip class="item" effect="dark" content="点击新增加Mac地址" placement="top">
                                            <i @click="addMultiMacAddress" class="el-input__icon el-icon-plus s-cp"></i>
                                        </el-tooltip>
                                    </template>
                                </el-input>
                            </el-form-item>
                        </el-col>

                        <el-col :span="12" v-for="(row, index) in dialog.user.form.othersMac">

                            <el-form-item :label="'Mac地址' + (index+1)"
                                          :key="row.key"
                                          :prop="'othersMac.' + index + '.val'"
                                          :rules="dialog.user.rules.multiMac">

                                <el-input v-model.trim="row.val" :maxlength="17" placeholder="如A1-B2-C3-D4-E5-F6">
                                    <template slot="suffix">
                                        <el-tooltip class="item" effect="dark" content="点击移除" placement="top">
                                            <i @click="dropMultiMacAddress(row)" class="el-input__icon el-icon-delete s-cp"></i>
                                        </el-tooltip>
                                    </template>
                                </el-input>
                            </el-form-item>
                        </el-col>

                    </el-row>
                    
                </el-form>

                <div slot="footer">
                    <el-button type="primary" @click="saveUser" size="small">保存</el-button>
                    <el-button @click="doClose" size="small">取消</el-button>
                </div>
                
            </el-dialog>

            <el-dialog class="user-dialog" v-drag width="400px" :title="dialog.role.title"
                :visible="dialog.role.visible" :show-close="false" :close-on-click-modal="false"
                :close-on-press-escape="false">
                <el-form class="user-form" size="mini" :model="dialog.role.form" :rules="dialog.role.rules"
                    ref="roleForm" label-width="80px">
                    <el-form-item label="选择角色" prop="roleId">
                        <el-select style="width: 100%" v-model="dialog.role.form.roleId" placeholder="请选择一个角色">
                            <el-option v-for="role in roleList" :key="role.id" :value="role.id" :label="role.roleName">
                            </el-option>
                        </el-select>
                    </el-form-item>
                </el-form>
                <div class="s-center" slot="footer">
                    <el-button type="primary" @click="saveRole" size="small">保存</el-button>
                    <el-button @click="() => dialog.role.visible = false" size="small">取消</el-button>
                </div>
            </el-dialog>

            <el-dialog class="user-dialog" v-drag width="400px" title="重置密码" :visible="dialog.reset.visible"
                :show-close="false" :before-close="resetPasswordState">
                <el-form class="user-form" size="mini" v-bind:model="dialog.reset.form"
                    v-bind:rules="dialog.reset.rules" ref="resetForm" label-width="80px">
                    <el-form-item label="输入密码" prop="password">
                        <el-input v-model="dialog.reset.form.password" :maxlength="20"
                            :type=" dialog.reset.isShowPassword ? 'password' : 'input' " palceholder="请输入密码">
                            <i title="查看密码" slot="suffix" class="el-input__icon s-cp iconfont size-16"
                                :class="{'icon-yincang': !dialog.reset.isShowPassword, 'icon-xianshi': dialog.reset.isShowPassword }"
                                @click="changeInputMode"></i>
                        </el-input>
                    </el-form-item>

                    <el-form-item label="重复密码" prop="repeat">
                        <el-input v-model="dialog.reset.form.repeat" :maxlength="20" type="password" palceholder="请再次输入密码"></el-input>
                    </el-form-item>
                </el-form>
                <div slot="footer">
                    <el-button type="primary" @click="savePassword" size="small">保存</el-button>
                    <el-button @click="resetPasswordState" size="small">取消</el-button>
                </div>
            </el-dialog>

            <el-dialog v-drag class="user-dialog" title="用户登录历史记录查询" :visible="dialog.logRecords.visible"
                :close-on-click-modal="false" :close-on-press-escape="false" :before-close="handleHideRecords">
                <data-tables-server configurable-column="false" :data="dialog.logRecords.data"
                    :total="dialog.logRecords.pagination.total" :page-size="dialog.logRecords.pagination.pageSize"
                    :search-def="dialog.logRecords.meta.searchDef" :table-props="tableProps"
                    :pagination-props="dialog.logRecords.meta.paginationDef" @query-change="handleQueryChange"
                    class="s-searchable-table">
                    <el-table-column type="index" label="序号" width="50"></el-table-column>
                    <el-table-column label="时间" width="150" prop="updateTime" :formatter="formatTime"
                        show-overflow-tooltip></el-table-column>
                    <el-table-column label="操作" width="80" prop="actionName" show-overflow-tooltip></el-table-column>
                    <el-table-column label="IP" prop="ip" show-overflow-tooltip min-width="150">
                        <template slot-scope="scope">
                            <span>{{scope.row.ip || '暂未记录'}}</span>
                        </template>
                    </el-table-column>
                    <el-table-column label="Mac地址" prop="parameter" :formatter="getMac" show-overflow-tooltip width="150"></el-table-column>
                    <el-table-column label="OS" prop="parameter" :formatter="getOs" show-overflow-tooltip></el-table-column>
                </data-tables-server>
            </el-dialog>

            <el-dialog v-drag class="user-dialog" title="用户操作记录" :visible="dialog.managementRecords.visible"
                :before-close="handleHideManagement">
                <data-tables-server configurable-column="false" :data="dialog.managementRecords.data"
                    :total="dialog.managementRecords.pagination.total" :table-props="tableProps"
                    :page-size="dialog.managementRecords.pagination.pageSize"
                    :pagination-props="dialog.managementRecords.meta.paginationDef"
                    :search-def="dialog.managementRecords.meta.searchDef" @query-change="handleQueryManagementLog"
                    class="s-searchable-table">
                    <el-table-column label="用户名" prop="userName" show-overflow-tooltip></el-table-column>
                    <el-table-column label="操作" prop="actionName" show-overflow-tooltip></el-table-column>
                    <el-table-column label="内容" v-if="superAdmin" prop="result" width="300" show-overflow-tooltip>
                        <template slot-scope="scope">
                            <div v-html="parseJson(scope.row.result)"></div>
                        </template>
                    </el-table-column>
                    <el-table-column label="参数" v-if="superAdmin" prop="parameter" width="300" show-overflow-tooltip>
                        <template slot-scope="scope">
                            <div v-html="parseJson(scope.row.parameter)"></div>
                        </template>
                    </el-table-column>
                    <el-table-column label="操作时间" prop="updateTime" width="120" :formatter="formatTime"
                        show-overflow-tooltip></el-table-column>
                </data-tables-server>
            </el-dialog>

        </div>
    </template>
</div>