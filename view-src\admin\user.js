
const BaseAdminView = require('./baseAdminView').BaseAdminView;
const DataTables = require('../../libs/3rd/vue-data-tables.min.3.4.2');
const drag = require('../../directives/drag');
const { app } = require('@electron/remote');
const encryptPasscodeAllowed = app.encryptionOptions.encryptPasscode;

class View extends BaseAdminView {

    get repoOrg() {
        return require('../../repository/org').repoOrg;
    }

    get repoUser() {
        return require('../../repository/user').repoUser;
    }

    get repoRole() {
        return require('../../repository/role').repoRole;
    }

    constructor(view_name) {

        super(view_name, '用户管理');
        this.$container = null;
        this.superAdmin = this.userInfo.isSuperAdmin;

        this.userStatuses = [

            { val: 1, label: '启用', status: true },
            { val: 0, label: '禁用', status: false },
        ];

        this.userList = [];
        this.userListHash = {};
        this.roleList = [];
        this.roleListHash = {};
        this.orgList = [];
        this.orgListHash = {};

        this.originalUserForm = {

            id: null,
            username: null,
            password: null,
            fullName: null,
            orgId: this.superAdmin ? null : this.userInfo.orgId,
            orgName: this.superAdmin ? null : this.userInfo.orgName,
            roleId: null,
            roleName: null,
            phoneNo: null,
            email: null,
            status: 1,
            mac: null,
            othersMac: [],
        };

        this.$defaultPageSize = 15;
        this.currentUser = null;
        this.searching = {

            prop: ['username', 'fullName', 'orgName', 'phoneNo'],
            value: '',
        };

        this.logRecords = {

            visible: false,
            current: null,
            data: [],
            meta: {
                tableProps: null,
                searchDef: {
                    show: false,
                },
                paginationDef: {
                    pageSize: this.$defaultPageSize,
                    pageSizes: [this.$defaultPageSize, 30, 50, 100],
                    currentPage: 1,
                    layout: 'prev,pager,next,sizes,total',
                },
            },
            pagination: {
                pageSize: this.$defaultPageSize,
                pageNo: 1,
                total: 0,
            },
        };

        this.managementRecords = {

            visible: false,
            current: null,
            data: [],
            meta: {
                tableProps: null,
                searchDef: {
                    show: false,
                },
                paginationDef: {
                    pageSize: this.$defaultPageSize,
                    pageSizes: [this.$defaultPageSize, 30, 50, 100],
                    currentPage: 1,
                    layout: 'prev, pager, next, total, sizes',
                },
            },
            pagination: {
                pageSize: this.$defaultPageSize,
                pageNo: 1,
                total: 0,
            },
        };

        this.macRegx = /^[A-Fa-f0-9]{2}((-[A-Fa-f0-9]{2}){5})|((:[A-Fa-f0-9]{2}){5})$/;
    }

    createApp() {

        let controller = this;
        this.tableProps = { maxHeight: 500, ...this.systemSetting.tableProps };
        this.vueApp = new Vue({
            el: this.$container.querySelector('.user-view-root'),
            components: {
                DataTables: DataTables.DataTables,
                DataTablesServer: DataTables.DataTablesServer,
            },
            filters: {
                renderOrgName(orgId) {
                    let matched = controller.orgList.find(cdt => cdt.id === orgId);
                    return matched ? matched.orgName : '未知';
                },
                renderRole(roleId) {
                    let matched = controller.roleList.find(cdt => cdt.id === roleId);
                    return matched ? matched.roleName : '未知';
                },
            },
            directives: {
                drag,
            },
            data: {
                searching: this.searching,
                filters: [this.searching],
                tableProps: this.tableProps,
                paginationDef: this.systemSetting.tablePagination,
                searchDef: {
                    inputProps: {
                        placeholder: '输入关键字筛选',
                        prefixIcon: 'iconfont icon-search',
                    },
                },
                userList: this.userList,
                roleList: this.roleList,
                orgList: this.orgList,
                superAdmin: this.superAdmin,
                dialog: {
                    user: {
                        mode: 'insert',
                        disabled: false,
                        title: null,
                        visible: false,
                        form: this.helper.deepClone(this.originalUserForm),
                        rules: {
                            username: [{ type: 'string', required: true, message: '请输入用户名' }],
                            password: [
                                { type: 'string', required: true, message: '请输入密码' },
                                { min: 6, max: 20, message: '长度8位以上' },
                                { pattern: /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)[^]{8,}$/, message: '数字、大小写字母组合' }
                            ],
                            orgId: [{ type: 'number', required: true, message: '请选择机构' }],
                            roleId: [{ type: 'number', required: true, message: '请选择角色' }],
                            phoneNo: [
                                { type: 'string', required: true, message: '请输入手机号码' },
                                { pattern: /^[1][3,4,5,7,8][0-9]{9}$/, message: '请输入正确的手机号', trigger: 'blur' },
                            ],
                            fullName: [{ type: 'string', required: true, message: '请输入真实名称' }],
                            email: [
                                { type: 'string', required: true, message: '请输入邮箱' },
                                {
                                    pattern: /^[A-Za-z0-9\u4e00-\u9fa5]+@[a-zA-Z0-9_-]+(\.[a-zA-Z0-9_-]+)+$/,
                                    message: '邮箱格式不正确',
                                    trigger: 'blur',
                                },
                            ],
                            mac: [
                                { pattern: this.macRegx, message: "请填写正确的mac地址", trigger: 'blur' }
                            ],
							multiMac: [
								{ required: false, pattern: this.macRegx,  message: 'mac地址不合法', trigger: 'blur'},
								{
									validator: (rule, value, callback) => {
										if (value == '') {
											callback(new Error('mac地址不能为空'));
											return;
										}
										let counter = 0;
										this.vueApp.dialog.user.form.othersMac.forEach(row => {
											row.val && row.val.toUpperCase() == value.toUpperCase() && counter++;
										});
										if (value === this.vueApp.dialog.user.form.mac || counter >1) {
											callback(new Error('多个mac地址不能重复'))
										} else {
											callback();
										}
									},
									trigger: 'blur'
								}
							]
                        },
                    },
                    role: {
                        visible: false,
                        title: null,
                        form: {
                            roleId: null,
                        },
                        rules: {
                            roleId: [{ type: 'number', required: true, message: '请选择角色' }],
                        },
                    },
                    reset: {
                        visible: false,
                        context: null,
                        isShowPassword: true,
                        form: {
                            password: '',
                            repeat: '',
                        },
                        rules: {
                            password: [
                                { type: 'string', required: true, message: '请输入密码' },
                                { min: 6, max: 20, message: '长度8位以上' },
                                { pattern: /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)[^]{8,}$/, message: '数字、大小写字母组合' },
                            ],
                            repeat: [
                                {
                                    validator(rule, value, callback) {
                                        if (controller.vueApp.dialog.reset.form.password !== value) {
                                            callback('两次输入的密码不一致，请确认密码!');
                                        } else {
                                            callback();
                                        }
                                    },
                                },
                                { required: true, message: '请再次密码' },
                            ],
                        },
                    },
                    logRecords: this.logRecords,
                    managementRecords: this.managementRecords,
                },
                userStatuses: this.userStatuses,
            },
            mixins: [],
            computed: {
                currentUserInfo: function () {
                    return controller.userInfo;
                },
                RoleList: function () {
                    if (controller.userInfo.isSuperAdmin) {
                        // return this.roleList.filter(cdt => cdt.id === controller.systemUserEnum.userRole.orgAdmin.code);
                        return this.roleList;
                    } else {
                        return this.roleList;
                    }
                },
            },
            mounted() {
                controller.getRoleList();
            },
            methods: {
                addMultiMacAddress() {
                	if (!controller.macRegx.test(this.dialog.user.form.mac)) {
                		controller.interaction.showError("请先填写一个Mac地址!");
                		return;
					}
                	let newInput = {
                		val: null,
						id: new Date().getTime(),
					};
					this.dialog.user.form.othersMac.push(newInput);
				},
				dropMultiMacAddress(row) {
					this.dialog.user.form.othersMac.remove(x => x.id == row.id);
				},
                formatTime: (row, column) => {
                    return controller.helper.time2String(row[column.property], 'yyyy-MM-dd hh:mm:ss');
                },
                parseJson: json => {
                    let result = '';
                    try {
                        let parsedJson = this.aggregateParseJson(JSON.parse(json)) || {};
                        Object.keys(parsedJson).forEach(key => {
                            result += `<div>
                                        <span>${key}:</span>
                                        <span>${parsedJson[key]}</span>
                                       </div>`;
                        });
                    } catch (e) {
                        result = '暂无数据';
                    }
                    return result;
                },
                getOs(row, column) {
                    try {
                        let json = JSON.parse(row[column.property]);
                        return this.formatOs(json, { property: 'os' });
                    } catch (e) {
                        return '暂无数据';
                    }
                },
                getMac: (row, column) => {
                    try {
                        let json = JSON.parse(row[column.property]);
                        return json.mac || '暂无数据';
                    } catch (e) {
                        return '暂无数据';
                    }
                },
                formatOs: (row, column) => {
                    let val = row[column.property] ? row[column.property].toUpperCase() : '';
                    let result = '';
                    switch (val) {
                        case 'win32':
                            result = 'Windows';
                            break;
                        case 'darwin':
                            result = 'Mac OS';
                            break;
                        case 'linux':
                            result = 'Linux';
                            break;
                        default:
                            result = '未知';
                            break;
                    }
                    return result;
                },
                handleQueryChange: payload => {
                    if (payload && payload.type !== 'init') {
                        this.logRecords.pagination.pageNo = payload.page || 1;
                        this.logRecords.pagination.pageSize = payload.pageSize || this.$defaultPageSize;
                        this.fetchLoginRecords(this.logRecords.pagination);
                    }
                },
                handleQueryManagementLog: payload => {
                    if (payload && payload.type !== 'init') {
                        this.managementRecords.pagination.pageNo = payload.page || 1;
                        this.managementRecords.pagination.pageSize = payload.pageSize || this.$defaultPageSize;
                        this.fetchManagementRecords(this.managementRecords.pagination);
                    }
                },
                viewManagementRecords: current_user => {
                    if (this.managementRecords && this.managementRecords.current && this.managementRecords.current.id !== current_user.id) {
                        this.managementRecords.pagination.total = 0;
                        this.managementRecords.pagination.pageNo = 1;
                        this.managementRecords.pagination.pageSize = this.$defaultPageSize;
                    }
                    this.managementRecords.current = current_user;
                    this.fetchManagementRecords(this.managementRecords.pagination).then(result => {
                        result
                            ? this.vueApp.$nextTick(() => {
                                this.managementRecords.visible = true;
                            })
                            : null;
                    });
                },
                viewLoginRecords: current_user => {
                    if (this.logRecords && this.logRecords.current && this.logRecords.current.id !== current_user.id) {
                        this.logRecords.pagination.total = 0;
                        this.logRecords.pagination.pageNo = 1;
                        this.logRecords.pagination.pageSize = this.$defaultPageSize;
                    }
                    this.logRecords.current = current_user;
                    this.fetchLoginRecords(this.logRecords.pagination).then(result => {
                        result
                            ? this.vueApp.$nextTick(() => {
                                this.logRecords.visible = true;
                            })
                            : null;
                    });
                },
                handleHideRecords: () => {
                    this.logRecords.visible = false;
                },
                handleHideManagement: () => {
                    this.managementRecords.visible = false;
                },
                makeUserRole: user => {
                    return this.getUserRole(user);
                },
                makeOnlineClass: user => {
                    return this.getOnlineClass(user);
                },
                makeOnlineStatus: user => {
                    return this.getOnlineStatusHtml(user);
                },
                forceOffline: user => {
                    //如果不在线
                    if (this.checkForceOffline(user)) {
                        this.interaction.showConfirm({
                            title: '警告',
                            message: '危险操作，确定将当前用户强制下线？',
                            confirmed: () => {
                                this.doForceOffline(user);
                            },
                            canceled: () => {
                                console.log('user cancel');
                            },
                        });
                    }
                },
                handleRowClick: row => {
                    this.currentUser = row;
                },
                handleOrgSelect(org_id) {
                    let matched = controller.orgList.find(cdt => cdt.id === org_id);
                    this.dialog.user.form.orgName = matched ? matched.orgName : null;
                },
                async openUserCreationDialog() {

                    this.dialog.user.title = '创建用户';
                    this.dialog.user.mode = 'insert';
                    this.dialog.user.form = controller.helper.deepClone(controller.originalUserForm);
                    let results = await Promise.all([controller.getOrgList(), controller.getRoleList()]);

                    if (results.every(x => x.flag)) {

                        this.dialog.user.visible = true;
                        this.$nextTick(() => { this.$refs.userForm.clearValidate(); });
                    }
                },
                async openRoleConfigurationDialog(row) {

                    await controller.getRoleList();
                    this.dialog.role.form.roleId = row.roleId;
                    this.dialog.role.title = `修改用户 ${row.username} 角色`;
                    this.dialog.role.visible = true;
                },
                saveRole() {
                    this.$refs.roleForm.validate(async valid => {
                        if (valid) {
                            let thisRoleId = this.dialog.role.form.roleId;
                            let matched = controller.roleList.find(cdt => cdt.id == thisRoleId);
                            let item = Object.assign({}, controller.currentUser, {
                                roleId: matched.id,
                                roleName: matched.roleName,
                                userType: matched.userType,
                            });

                            let result = await controller.saveUser(item);

                            if (result.flag) {
                                this.dialog.role.visible = false;
                                this.updateTable(result.data);
                            }
                        }
                    });
                },
                saveUser() {
                    this.$refs.userForm.validate(async valid => {
                        if (valid) {
                            let roleId = this.dialog.user.form.roleId;
                            let role = controller.roleList.find(cdt => cdt.id == roleId);
                            let sendNode = Object.assign(this.dialog.user.form, {
                                roleName: role.roleName,
                                userType: role.userType,
                            });

                            if (controller.currentUser && controller.currentUser.id && this.dialog.user.mode !== 'insert') {
                                sendNode.id = controller.currentUser.id;
                            }
                            //设置默认密码
                            if (typeof this.dialog.user.form.password !== 'number' && !this.dialog.user.form.password) {
                                sendNode.password = controller.systemEnum.defaultAccountPassword;
                            }
                            let result = await controller.saveUser(sendNode);

                            if (result.flag) {
                                this.dialog.user.visible = false;

                                if (typeof result.data.deleteFlag !== 'undefined') {
                                    delete result.data.deleteFlag;
                                }

                                if (typeof result.data.roleName !== 'undefined') {
                                    delete result.data.roleName;
                                }

                                let updateData = Object.assign({}, result.data, {
                                    onlineStatus: result.data.roleId === controller.systemUserEnum.userRole.tradingMan.code ? false : 'anonymous',
                                });

                                this.updateTable(updateData);
                            }
                        }
                    });
                },
                async handleEdit(row) {

                    this.renderForm(row);
                    this.dialog.user.mode = 'update';
                    this.dialog.user.title = `修改用户 ${row.username}`;
                    this.dialog.user.disabled = true;
                    let results = await Promise.all([controller.getOrgList(), controller.getRoleList()]);
                    if (results.every(x => x.flag)) {
                        this.dialog.user.visible = true;
                    }
                },
                doClose() {

                    this.$refs.userForm.resetFields();
                    this.$nextTick(() => {

						this.dialog.user.form.mac = '';
						this.$refs.userForm.clearValidate();
						this.dialog.user.form.othersMac.clear();
						this.dialog.user.visible = false;
						this.dialog.user.disabled = false;
                    });
                },
                renderForm(row) {

                    let form = row;
					this.dialog.user.form.othersMac.clear();
                    for (const key in form) {
                        if (row.hasOwnProperty(key) && key !='othersMac') {
                            this.dialog.user.form[key] = row[key];
                        }
                    }
					this.dialog.user.form.othersMac.merge(row.othersMac);
                },
                handleChangeStatus: async user => {

                    await this.getRoleList();
                    let matched = this.roleList.find(cdt => cdt.id == user.roleId);
                    if (matched) {
                        user.roleName = matched.roleName;
                        user.userType = matched.userType;
                    }

                    let flag = await controller.saveUser(user, true);
                    if (flag) {
                        this.interaction.showSuccess('切换状态成功!');
                    }
                },
                handleResetPassword(row) {
                    this.dialog.reset.visible = true;
                    this.dialog.reset.context = row;
                },
                changeInputMode() {
                    this.dialog.reset.isShowPassword = !this.dialog.reset.isShowPassword;
                },
                resetPasswordState() {
                    this.dialog.reset.form.password = '';
                    this.dialog.reset.form.repeat = '';
                    this.$refs.resetForm.clearValidate();
                    this.$refs.resetForm.resetFields();
                    this.dialog.reset.visible = false;
                    this.dialog.reset.context = null;
                },
                savePassword() {

                    let row = this.dialog.reset.context;
                    let password = this.dialog.reset.form.password;
                    let username = this.dialog.reset.context.username;

                    this.$refs.resetForm.validate(valid => {

                        if (valid) {

                            controller.interaction.showConfirm({
                                message: `确定重置用户 ${row.username} 密码?`,
                                confirmed: async () => {
                                    let flag = await controller.resetUserPassword(username, password);
                                    if (flag) {
                                        controller.interaction.showSuccess('重置密码成功');
                                        this.resetPasswordState();
                                    }
                                },
                            });
                        }
                    });
                },
                handleRoleFilterVisibleChange(visible) {
                    if (visible) {
                        controller.getRoleList();
                    }
                },
                handleSelectRole(role_id) {
                    let matched = controller.roleList.find(cdt => cdt.id == role_id);
                    if (matched) {
                        this.dialog.user.form.roleName = matched.roleName;
                    }
                },
                updateTable(result) {
                    if (typeof controller.userListHash[result.id] === 'undefined') {
                        controller.userList.unshift(result);
                    } else {
                        var index = controller.userList.findIndex(cdt => cdt.id == result.id);
                        this.$set(this.userList, index, result);
                    }

                    controller.userListHash[result.id] = result;
                },
                handleRemove(row) {
                    controller.interaction.showConfirm({
                        title: '警告',
                        message: '确定要删除当前用户吗?',
                        confirmed: () => {
                            let userId = row.id;
                            controller.removeFromUserList(userId).then(() => {
                                delete controller.userListHash[userId];
                                controller.userList.remove(cdt => cdt.id == userId);
                            });
                        },
                        canceled: () => {
                            console.log("the user's operation has canceled");
                        },
                    });
                },
            },
        });
        this.vueApp.$nextTick(() => {
            this.resizeWindow();
        });
    }

    //把一个JSON抹平
    aggregateParseJson(obj, diffKey) {
        let result = {};
        if (!obj) {
            return result;
        }
        Object.keys(obj).forEach(key => {
            let computedKey = diffKey !== undefined ? diffKey + '.' + key : key;
            Object.prototype.toString.call(obj[key]) === '[object Object]' ? Object.assign(result, this.aggregateParseJson(obj[key], computedKey)) : (result[computedKey] = obj[key]);
        });
        return result;
    }

    getUserRole(user) {
        if (user.roleName) {
            return user.roleName;
        }

        let matched = this.roleList.find(role => role.id == user.roleId);
        return matched ? matched.roleName : '--';
    }

    getOnlineClass(user) {
        return typeof user.onlineStatus !== 'undefined' && user.onlineStatus !== 'anonymous' ? (user.onlineStatus ? 's-color-green s-cp' : 's-color-red') : '';
    }

    getOnlineStatusHtml(user) {
        return typeof user.onlineStatus !== 'undefined' && user.onlineStatus !== 'anonymous' ? (user.onlineStatus ? '下线' : '不在线') : '---';
    }

    checkForceOffline(user) {
        return user.onlineStatus && this.systemUserEnum.userRole.tradingMan.code === user.roleId;
    }

    async doForceOffline(user) {
        let result = await this.sendForceOfflineReq(user);
        if (result) {
            this.interaction.showSuccess('已成功将当前用户强制下线!');
            user.onlineStatus = false;
        }
    }

    async sendForceOfflineReq(user) {
        let result = false;

        let loading = this.interaction.showLoading({
            text: '加载中...',
        });

        try {
            let resp = await this.repoUser.forceOfflineInstance(user.id);
            if (resp.errorCode === 0) {
                result = true;
            } else {
                this.interaction.showHttpError(`强制下线失败，详细信息：${resp.errorCode}/${resp.errorMsg}`);
            }
        } catch (exp) {
            this.interaction.showHttpError('操作失败 !');
        } finally {
            loading.close();
        }

        return Promise.resolve(result);
    }

    async saveUser(user_info, silence) {

        let output = {
            flag: false,
            data: null,
        };

        try {

            let cloned = this.helper.deepClone(user_info);
            let resp = null;

            if (cloned.mac) {

				//处理多个Mac地址
				let macAddresses = [cloned.mac.toUpperCase() , ...cloned.othersMac.map(x => x.val.toUpperCase())];
				delete cloned.othersMac;
				cloned.mac = macAddresses.join(",");
			}

            if (encryptPasscodeAllowed && !this.helper.isNone(cloned.password)) {
                cloned.password = this.helper.aesEncrypt(cloned.password);
            }

            if (cloned.id) {
                resp = await this.repoUser.updateUser(cloned);
            } 
            else {
                delete cloned.id;
                resp = await this.repoUser.createUser(cloned);
            }

            if (resp.errorCode == 0) {

                if (!silence) {
                    this.interaction.showSuccess('保存用户成功');
                }

                let resp_data = resp.data;

                if (resp_data.mac) {

                	let macs = resp_data.mac.split(',');
					if (macs.length > 1) {

						resp_data.mac = macs[0];
						resp_data.othersMac = macs.slice(1).map(x => {
							 return {
								val: x,
								id: Date.now()+ (Math.random().toFixed(3) * 1000)
							 };
						})
                    } 
                    else {
						macs[0] && (resp_data.mac = macs[0]);
						resp_data.othersMac = []
					}
                } 
                else {
					resp_data.othersMac = [];
                }
                
                output.flag = true;
                output.data = resp_data;
            }
            else {
                console.log(resp);
                this.interaction.showHttpError('保存用户失败,详细信息："' + resp.errorMsg + '"');
            }
        } 
        catch (error) {
            console.log(error);
            this.interaction.showError('保存用户失败!');
        }

        return Promise.resolve(output);
    }

    async getUserList() {

        let output = { flag: false, data: null };
        let loading = this.interaction.showLoading({ text: '请求用户列表...' });

        try {
            const resp = await this.repoUser.getAll();
            if (resp.errorCode == 0) {
                
                let list = resp.data;
                list = list.filter(cdt => cdt.id !== this.userInfo.userId);
                this.userList.clear();
                this.userList.merge(list);
                output.flag = true;
                list.forEach(user => {

                    //tips: 由于搜索列表里面需要完全字段，不能有些数据没有onlineStatus有些数据没有onlineStatus，因此手动mock一个数据
                    let userId = user.id;
                    let user_mac = user.mac;

                    if (typeof user.onlineStatus === 'undefined') {
                        user.onlineStatus = 'anonymous';
                    }

                    if (user_mac) {

                    	let macs = user_mac.split(',');
                    	if(macs.length > 1) {

							user.mac = macs[0];
							user.othersMac = macs.slice(1).map(x => ({ val: x, id: Date.now()+(Math.random().toFixed(3)) * 1000 }));
                        } 
                        else {
							macs[0] && (user.mac = macs[0]);
							user.othersMac = [];
						}
                    } 
                    else {
						user.othersMac = [];
                    }
                    
                    this.userListHash[userId] = user;
                });
                output.data = list;
                this.userList = this.userList.orderByDesc(cdt => cdt.id);
            } else {
                console.log(resp);
                this.interaction.showHttpError('查询用户列表出错，详细信息："' + resp.errorMsg + '"');
            }
        } catch (error) {
            console.log(error);
            this.interaction.showError('查询用户列表出错');
        }

        loading.close();

        return Promise.resolve(output);
    }

    formatLoginRecord(element) {
        return element;
    }

    async fetchManagementRecords({ pageSize, pageNo }) {
        let flag = false;
        let loading = this.interaction.showLoading({ text: '正在获取操作记录...' });
        try {
            let userId = this.managementRecords.current.id;
            this.managementRecords.data.clear();
            let resp = await this.repoUser.getLogHistory(userId, this.systemBackupEnum.logType.management.code, {
                pageSize,
                pageNo,
            });
            loading.close();
            if (resp.errorCode === 0) {
                resp.data && !resp.data.data ? (resp.data.data = {}) : null;
                let result = (resp.data.data || []).map(x => this.formatLoginRecord(x)).orderByDesc(x => x.id);
                this.managementRecords.data.merge(result);
                this.managementRecords.pagination.total = resp.data.total;
                this.managementRecords.pagination.pageNo = resp.data.pageNo;
                flag = true;
            } else {
                this.interaction.showError(`获取操作记录失败，详细信息:${resp.errorCode}/${resp.errorMsg}`);
            }
        } catch (e) {
            loading.close();
            this.interaction.showError('获取操作记录失败!');
        }
        return flag;
    }

    async fetchLoginRecords({ pageNo, pageSize }) {

        let flag = false;
        let loading = this.interaction.showLoading({ text: '正在获取用户登录历史记录...' });
        try {
            let userId = this.logRecords.current.id;
            this.logRecords.data.clear();
            let resp = await this.repoUser.getLogHistory(userId, this.systemBackupEnum.logType.login.code, {
                pageSize,
                pageNo,
            });
            loading.close();
            if (resp.errorCode === 0) {
                resp.data && !resp.data.data ? (resp.data.data = {}) : null;
                let result = (resp.data.data || []).map(x => this.formatLoginRecord(x)).orderByDesc(x => x.id);
                this.logRecords.data.merge(result);
                this.logRecords.pagination.total = resp.data.total;
                this.logRecords.pagination.pageNo = resp.data.pageNo;
                flag = true;
            } else {
                this.interaction.showError(`获取当前用户登录历史记录失败,详细信息：${resp.errorCode}/${resp.errorMsg}`);
            }
        } catch (e) {
            loading.close();
            this.interaction.showError('获取用户登录历史记录失败!');
        }
        return flag;
    }

    async getOrgList() {

        let output = { flag: false, data: null };
        let loading = this.interaction.showLoading({ text: '请求机构列表...' });

        try {

            const resp = await this.repoOrg.getAll();
            if (resp.errorCode == 0) {

                output.flag = true;
                let list = resp.data.orderByDesc(cdt => cdt.id);
                this.orgList.clear();
                this.orgList.merge(list);

                list.forEach(org => {

                    let orgId = org.id;
                    this.orgListHash[orgId] = org;
                });

                output.data = list;
            } 
            else {
                this.interaction.showHttpError(`查询机构列表失败,详细信息：${resp.errorMsg}`);
            }
        } 
        catch (error) {
            this.interaction.showError('查询机构列表失败');
        } 
        finally {
            loading.close();
        }

        return Promise.resolve(output);
    }

    async getRoleList() {

        let output = { flag: false, data: null};
        let loading = this.interaction.showLoading({ text: '请求角色列表...' });

        try {
            const resp = await this.repoRole.getAll();
            if (resp.errorCode == 0) {

                output.flag = true;
                let list = resp.data;
                if (!this.userInfo.isSuperAdmin) {

                    let userRole = this.systemUserEnum.userRole;
                    list = list.filter(cdt => !(cdt.id === userRole.superAdmin.code || cdt.id === userRole.orgAdmin.code));
                }

                this.roleList.clear();
                this.roleList.merge(list);

                list.forEach(role => {

                    let roleId = role.id;
                    this.roleListHash[roleId] = role;
                });

                output.data = list;
            } 
            else {
                console.log(resp);
                this.interaction.showHttpError('查询角色列表出错，详细信息："' + resp.errorMsg + '"');
            }
        } 
        catch (error) {
            console.log(error);
            this.interaction.showError('查询角色列表出错');
        } 
        finally {
            loading.close();
        }

        return Promise.resolve(output);
    }

    async changeUserRole(user_id, role_id) {

        let flag = false;
        try {
            const resp = await this.repoUser.changeUserRole(user_id, role_id);
            if (resp.errorCode == 0) {
                this.interaction.showSuccess('修改角色成功');
                flag = true;
            } else {
                console.log(resp);
                this.interaction.showHttpError('修改角色失败,详细信息："' + resp.errorMsg + '"');
            }
        } catch (error) {
            console.log(error);
            this.interaction.showError('修改角色失败');
        }
        return flag;
    }

    async resetUserPassword(username, password) {

        let flag = false;

        if (encryptPasscodeAllowed && !this.helper.isNone(password)) {
            password = this.helper.aesEncrypt(password);
        }

        try {
            const resp = await this.repoUser.resetPassword(username, password);
            if (resp.errorCode == 0) {
                flag = true;
            } 
            else {
                console.log(resp);
                this.interaction.showHttpError('重置密码失败');
            }
        } 
        catch (error) {
            console.log(error);
            this.interaction.showError('重置密码失败');
        }

        return Promise.resolve(flag);
    }

    async removeFromUserList(userId) {

        let loading = this.interaction.showLoading({
            text: '操作进行中...',
        });

        let output = false;

        try {
            const resp = await this.repoUser.deleteUser(userId);
            if (resp.errorCode === 0) {
                this.interaction.showSuccess('删除用户成功!');
                output = true;
            } else {
                this.interaction.showHttpError('删除用户失败，详细信息:"' + resp.errorMsg + '"');
            }
        } catch (exp) {
            this.interaction.showHttpError('删除用户失败!');
        } finally {
            loading.close();
        }

        return Promise.resolve(output);
    }

    refresh() {

        this.searching.value = null;
        this.getUserList();
    }

    resizeWindow() {

        var winHeight = this.thisWindow.getSize()[1];
        this.tableProps.maxHeight = winHeight - 140;
    }

    build($container) {
        this.$container = $container;
        this.createApp();
        this.getUserList();
    }
}

module.exports = View;
