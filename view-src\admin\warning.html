<div class="warning-management">
    
    <div class="user-toolbar themed-box">

		<span>算法母单</span>

		<el-input placeholder="输入关键字搜索" 
				  prefix-icon="el-icon-search" 
				  class="keywords s-mgl-10"
				  v-model="states.keywords"
				  @change="filter" clearable></el-input>

	</div>

	<div class="data-list">

		<table>
			<tr>

				<th label="ID" 
					min-width="60" 
					prop="id" overflowt filterable sortable searchable></th>

				<th label="配置ID" 
					min-width="70" 
					prop="configurationId" overflowt filterable sortable searchable></th>

				<th label="identity" 
					min-width="70" 
					prop="identity" overflowt filterable sortable searchable></th>

				<th label="identityType" 
					min-width="70" 
					prop="identityType" overflowt filterable sortable searchable></th>

				<th label="identityName" 
					min-width="70" 
					prop="identityName" overflowt filterable sortable searchable></th>

				<th label="warningType" 
					min-width="70" 
					prop="warningType" overflowt filterable sortable searchable></th>

				<th label="content" 
					min-width="70" 
					prop="content" overflowt filterable sortable searchable></th>

				<th label="createTime" 
					min-width="70" 
					prop="createTime" overflowt filterable sortable searchable></th>
					
			</tr>
		</table>

	</div>

	<div class="user-footer themed-box">
		<el-pagination class="s-pull-right"
					   :page-sizes="paging.pageSizes"
					   :page-size.sync="paging.pageSize" 
					   :total="paging.total"
					   :current-page.sync="paging.page" 
					   :layout="paging.layout" 
					   @size-change="handlePageSizeChange"
					   @current-change="handlePageChange"></el-pagination>
	</div>

</div>