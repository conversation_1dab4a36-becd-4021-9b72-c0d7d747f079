const { IView } = require('../../component/iview');
const { WarningMsg } = require('../../model/warning');
const { SmartTable } = require('../../libs/table/smart-table');
const { ColumnCommonFunc } = require('../../libs/table/column-common-func');
const { repoRisk } = require('../../repository/risk');

class View extends IView {

    constructor(view_name) {

        super(view_name, false, '交易风险提示管理');
        this.warnings = [new WarningMsg({})];
        this.warnings.pop();

        this.states = {

            /** 关键字 */
            keywords: null,
        };

        var paging = this.systemSetting.tablePagination;
        this.paging = {

            pageSizes: paging.pageSizes,
            pageSize: paging.pageSize,
            layout: paging.layout,
            total: 0,
            page: 0,
        };
    }

    /**
     * @param {WarningMsg} record
     */
    identifyRecord(record) {
        return record.id;
    }

    createToolbarApp() {

        new Vue({

            el: this.$container.querySelector('.user-toolbar'),
            data: {
                states: this.states,
            },
            methods: this.helper.fakeVueInsMethod(this, [this.filter]),
        });
    }

    filter() {

        var tableObj = this.tableObj;
        var keywords = this.states.keywords;

        /**
         * @param {WarningMsg} record 
         */
        function testRecords(record) {
            return tableObj.matchKeywords(record);
        }

        tableObj.setPageIndex(1, false);
        tableObj.setKeywords(keywords, false);
        tableObj.customFilter(testRecords);
    }

    createFooterRowApp() {
        
        new Vue({

            el: this.$container.querySelector('.user-footer'),
            data: {
                paging: this.paging,
            },

            methods: this.helper.fakeVueInsMethod(this, [

                this.handlePageSizeChange,
                this.handlePageChange,
            ]),
        });
    }

    handlePageChange() {
        this.tableObj.setPageIndex(this.paging.page);
    }

    handlePageSizeChange() {

        this.tableObj.setPageSize(this.paging.pageSize, true);
        this.tableObj.setPageIndex(1);
    }

    createTable() {

        this.helper.extend(this, ColumnCommonFunc);
        var $table = this.$container.querySelector('.data-list');
        var tableObj = new SmartTable($table, this.identifyRecord, this, {

            tableName: 'smt-gs2u',
            displayName: this.title,
            defaultSorting: { prop: 'id', direction: 'desc' },
            recordsFiltered: this.handleTableFiltered.bind(this),
        });

        tableObj.setPageSize(this.paging.pageSize);
        tableObj.setMaxHeight(500);
        this.tableObj = tableObj;
    }

    handleTableFiltered(total) {
        this.paging.total = total;
    }

    refresh() {
        this.requestWarningMsgs(true);
    }

    async requestWarningMsgs() {

        var resp = await repoRisk.getRiskMessage();
        if (resp.errorCode != 0) {
            return this.interaction.showError(`风险提示消息，加载失败：${resp.errorCode}/${resp.errorMsg}`);
        }

        var records = resp.data;
        var messages = records instanceof Array ? records.map(item => new WarningMsg(item)) : [];
        this.tableObj.refill(messages);
        this.interaction.showSuccess('刷新动作已完成');
    }

    build($container) {
        
        super.build($container);
        this.createToolbarApp();
        this.createFooterRowApp();
        this.createTable();
        this.requestWarningMsgs();
    }
}

module.exports = View;