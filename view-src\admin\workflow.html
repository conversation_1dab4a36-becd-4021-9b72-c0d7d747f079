<div class="workflow-view-root s-border-box">
    <template>
        <div class="s-scroll-bar" style="overflow: auto">
            <div class="s-typical-toolbar">
                <el-button type="primary" @click="createWorkflow" size="mini">
                    <i class="iconfont icon-add"></i> 创建流程
                </el-button>
                <el-button type="primary" @click="want2saveWorkflow" size="mini">
                    <i class="iconfont icon-save"></i> 保存流程
                </el-button>
            </div>
            <el-tabs type="border-card" v-model="tab" @tab-click="tabChange">
                <el-tab-pane label="流程管理" name="workflow">
                    <data-tables layout="table,pagination" :data="workflowList"
                        :pagination-props="{ show: false, layout: 'prev,pager,next,sizes,total' }">
                        <el-table-column label="序号" width="80" type="index"></el-table-column>
                        <el-table-column label="流程名称" prop="workFlowName"></el-table-column>
                        <el-table-column label="操作" width="160">
                            <template slot-scope="scope">
                                <div class="opt-wrapper-5">
                                    <el-button @click="editWorkflow(scope.row)" type="success" size="mini">编辑
                                    </el-button>
                                    <el-button @click="removeWorkflow(scope.row)" type="danger" size="mini">删除
                                    </el-button>
                                </div>
                            </template>
                        </el-table-column>
                    </data-tables>
                </el-tab-pane>
                <el-tab-pane label="编辑流程" name="edit">
                    <div class="opt-wrapper">
                        <el-button size="small" type="success" @click="addRole">新增角色</el-button>
                    </div>
                    <el-table :data="workflow" stripe>
                        <el-table-column label="审核顺序" align="center" width="100" type="index"></el-table-column>
                        <el-table-column label="选择角色" align="left">
                            <template slot-scope="scope">
                                <el-select v-model="scope.row.role" @change="validRoleUnique(scope.row)">
                                    <el-option v-for="(role, idx) in roleList" :label="role.label" :key="idx"
                                        :value="role.value"></el-option>
                                </el-select>
                            </template>
                        </el-table-column>
                        <el-table-column label="状态" prop="status">
                            <template slot-scope="scope">
                                <el-select v-model="scope.row.status">
                                    <el-option v-for="(status, idx) in statusEnums" :key="idx" :label="status.label"
                                        :value="status.value"></el-option>
                                </el-select>
                            </template>
                        </el-table-column>
                        <el-table-column label="操作" width="100" align="center">
                            <template slot-scope="scope">
                                <div class="opt-wrapper">
                                    <el-button type="danger" size="mini" @click="removeRole(scope.row)">移除角色</el-button>
                                </div>
                            </template>
                        </el-table-column>
                    </el-table>
                </el-tab-pane>
            </el-tabs>
            <el-dialog :visible="workflowDialog.visible" :close-on-click-modal="false" title="保存工作流程"
                :show-close="false">
                <el-form ref="workflow" :model="workflowDialog.data" :rules="workflowDialog.rules">
                    <el-form-item prop="workFlowName">
                        <el-input size="medium" placeholder="请输入流程名称" v-model="workflowDialog.data.workFlowName">
                        </el-input>
                    </el-form-item>
                </el-form>
                <div slot="footer">
                    <el-button type="primary" size="small" @click="saveWorkflow">确定</el-button>
                    <el-button size="small" @click="cancelWorkflow">取消</el-button>
                </div>
            </el-dialog>

        </div>

    </template>
</div>