
const DataTables = require('../../libs/3rd/vue-data-tables.min.3.4.2');
const BaseAdminView = require('./baseAdminView').BaseAdminView;
const drag = require('../../directives/drag');


class View extends BaseAdminView {

    get systemEnum() {
        return require('../../config/system-enum').systemEnum;
    }

    get workflowRepo() {
        return require('../../repository/procedure').repoProcedure;
    }

    constructor(view_name) {

        super(view_name, '流程管理');
        this.$container = null;
        this.vueApp = null;
        this.workflow = [];
        this.workflowList = [];
        this.roleListHash = {};
        this.roleList = [];
        this.statusEnums = this.helper
            .dict2Array(this.systemEnum.examineStatuses)
            .filter(x => {
                return x.mean !== '驳回';
            })
            .map(x => {
                return {
                    value: x.code,
                    label: x.mean,
                };
            });
        this.workflowDialog = {
            visible: false,
            data: {
                id: null,
                workFlowName: '',
            },
            rules: {
                workFlowName: [{ required: true, message: '工作流程名称不能为空' }],
            },
        };
    }

    createApp() {
        const self = this;
        this.vueApp = new Vue({
            el: this.$container.querySelector('.workflow-view-root'),
            components: {
                DataTables: DataTables.DataTables,
            },
            data: {
                roleList: this.roleList,
                workflowList: this.workflowList,
                tab: 'workflow',
                workflow: this.workflow,
                statusEnums: this.statusEnums,
                workflowDialog: this.workflowDialog,
            },
            directives: {
                drag,
            },
            methods: {
                tabChange() {},
                createWorkflow() {
                    if (this.workflow.length > 0) {
                        this.tab = 'edit';
                        self.interaction.showConfirm({
                            title: '提示',
                            message: '当前流程的编辑尚未完成，确定要清除数据重新编辑吗?',
                            confirmed: () => {
                                this.workflow.clear();
                                this.workflowDialog.data.workFlowName = '';
                            },
                        });
                    } else {
                        this.tab = 'edit';
                    }
                },
                editWorkflow(workflow) {
                    this.tab = 'edit';
                    self.buildWorkflow(workflow);
                },
                removeWorkflow(flow) {
                    self.interaction.showConfirm({
                        title: '提示',
                        message: '确定要移除工作流吗?',
                        confirmed: () => {
                            self.removeWorkflow(flow.id).then(resp => {
                                if (resp && resp.errorCode == 0) {
                                    this.workflowList.remove(x => x.id == flow.id);
                                }
                            });
                        },
                    });
                },
                want2saveWorkflow() {

                    if (this.workflow.length <= 0) {
                        self.interaction.showError('流程尚未绑定角色，无法保存流程!');
                        return;
                    }

                    if (this.workflow.find(x => x.role == null)) {
                        self.interaction.showError('流程节点中尚未绑定角色，无法保存流程!');
                        return;
                    }

                    let roles = this.workflow.map(x => x.role);
                    roles = [...new Set(roles)];
                    if (roles.length < this.workflow.length) {
                        self.interaction.showError('流程中存在重复角色，无法保存流程!');
                        return;
                    }

                    if (this.workflow.some(x => x.status == null || x.status === '')) {
                        self.interaction.showError('流程中有选择的角色尚未选择状态，无法保存流程!');
                        return;
                    }

                    this.workflowDialog.visible = true;
                },
                saveWorkflow() {
                    this.$refs.workflow.validate(valid => {
                        if (valid) {
                            self.interaction.showConfirm({
                                title: '提示',
                                message: '确定要保存当前流程吗?',
                                confirmed: () => {
                                    self.saveWorkflow(
                                        self.createWorkflowEntity({
                                            id: this.workflowDialog.data.id || null,
                                            orgId: 0,
                                            workFlowName: this.workflowDialog.data.workFlowName,
                                            content: this.workflow,
                                        }),
                                    ).then(resp => {
                                        if (resp.errorCode == 0) {
                                            this.updateWorkflow(self.standardWorkflow(resp.data || {}));
                                            this.workflow.clear();
                                            this.cancelWorkflow();
                                            this.tab = 'workflow';
                                        }
                                    });
                                },
                            });
                        }
                    });
                },
                updateWorkflow(workflow) {
                    let viewWorkflow = this.workflowList.find(x => x.id == workflow.id);
                    viewWorkflow ? self.helper.extend(viewWorkflow, workflow) : this.workflowList.unshift(workflow);
                },
                cancelWorkflow() {
                    this.$refs.workflow.clearValidate();
                    this.$refs.workflow.resetFields();
                    this.workflowDialog.visible = false;
                },
                addRole() {
                    if (this.workflow.length > 0) {
                        let last = this.workflow[this.workflow.length - 1];
                        if (!last.role) {
                            self.interaction.showError('请先选择角色再继续添加!');
                            return;
                        }
                        if (last.status == null || last.status === '') {
                            self.interaction.showError('请先选择角色状态再继续添加!');
                            return;
                        }
                    }
                    this.workflow.push(self.generateAuditNode());
                },
                removeRole(role) {
                    self.interaction.showConfirm({
                        title: '提示',
                        message: '确定要移除当前角色吗?',
                        confirmed: () => {
                            this.workflow.remove(x => x.id == role.id);
                        },
                    });
                },
                validRoleUnique(this_role) {
                    if (!this_role) {
                        return;
                    }
                    let roleEntities = this.workflow.filter(x => x.role == this_role.role);
                    if (roleEntities.length > 1) {
                        self.interaction.showError('不能重复选择相同角色!');
                        this.$nextTick(() => {
                            this_role.role = null;
                        });
                    }
                },
            },
        });
    }

    generateAuditNode() {
        return {
            id: this.getNodeId(),
            role: null,
            status: null,
        };
    }

    resizeWindow() {
        var winHeight = this.thisWindow.getSize()[1];
        //Title的高度是35 Tab的高度是30 底部状态栏30
        var extraHeight = 35 + 30 + 30;
        var net_height = winHeight - extraHeight;
        var box = this.$container.querySelector('.workflow-view-root .s-scroll-bar');
        if (box) {
            box.style.height = net_height + 'px';
        }
    }

    getRoleList() {
        
        this.roleList = this.helper.dict2Array(this.systemUserEnum.userRole)
                                   .map(role => {
                                        let transRole = { value: role.code, label: role.mean };
                                        this.roleListHash[role.code] = transRole;
                                        return transRole;
                                    });
    }

    /**
     * 生成合理的工作流程的数据
     * @param data
     */
    createWorkflowEntity(workflow) {
        
        let workflowEntity = Object.assign(
            {
                id: null,
                orgId: 0,
                workFlowName: '系统自定义流程',
                content: [],
            },
            workflow,
        );

        if (!Array.isArray(workflow.content)) {
            return workflowEntity;
        }

        workflowEntity.content = workflow.content.map(x => {
            let roleEntity = this.roleListHash[x.role] || {};
            return {
                roleType: x.role,
                roleName: roleEntity.label,
                defaultOffLineSetting: x.status,
            };
        });

        return workflowEntity;
    }

    async saveWorkflow(workflowEntity) {
        let loading = this.interaction.showLoading({ text: '操作进行中...' });
        let resp = null;
        //对于新增和更新
        let action = !workflowEntity.id ? 'saveProcedure' : 'updateProcedure';
        try {
            resp = await this.workflowRepo[action](workflowEntity);
            loading.close();
            if (resp.errorCode == 0) {
                this.interaction.showSuccess('保存工作流程成功!');
            } else {
                this.interaction.showError('保存工作流程失败,详细信息:' + resp.errorMsg);
            }
        } catch (e) {
            loading.close();
            this.interaction.showError('保存工作流程失败！');
        }
        return resp;
    }

    async getAllWorkflow() {
        let loading = this.interaction.showLoading({ text: '正在获取工作流程列表...' });
        try {
            this.workflowList.clear();
            let resp = await this.workflowRepo.getProcedure();
            loading.close();
            if (resp.errorCode == 0) {
                let workflowList = resp.data || [];
                workflowList = workflowList.orderByDesc(x => x.id);
                console.log(workflowList);
                this.workflowList.merge(workflowList);
            } else {
                this.interaction.showError('获取工作流程列表失败,详细信息:' + resp.errorMsg);
            }
        } catch (e) {
            loading.close();
            this.interaction.showError('获取工作流程列表失败！');
        }
    }

    async removeWorkflow(pId) {
        let loading = this.interaction.showLoading({ text: '操作进行中...' });
        let resp = null;
        try {
            resp = await this.workflowRepo.removeProcedure(pId);
            loading.close();
            if (resp.errorCode == 0) {
                this.interaction.showSuccess('删除工作流程成功!');
            } else {
                this.interaction.showError('删除工作流程失败,详细信息:' + resp.errorMsg);
            }
        } catch (e) {
            loading.close();
            this.interaction.showError('删除工作流程失败！');
        }
        return resp;
    }

    buildWorkflow(workflow) {
        if (!workflow) {
            return;
        }
        this.workflow.clear();
        (this.workflowDialog.data.id = workflow.id), (this.workflowDialog.data.workFlowName = workflow.workFlowName);
        workflow.content.forEach(x => {
            this.workflow.push({
                id: this.getNodeId(),
                role: x.roleType,
                status: x.defaultOffLineSetting,
            });
        });
    }

    getNodeId() {
        return (
            new Date().getTime() +
            Math.random()
                .toFixed(10)
                .replace(/\./, '')
        );
    }

    build($container) {
        this.$container = $container;
        this.getRoleList();
        this.getAllWorkflow();
        this.createApp();
        this.resizeWindow();
    }
}

module.exports = View;
