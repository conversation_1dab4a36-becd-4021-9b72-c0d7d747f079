const { IView } = require('../component/iview');
const { repoUser } = require('../repository/user');

module.exports = class ModifyPasswordView extends IView {

    constructor() {
        super('@modify-password', false, '修改密码');
    }

    createApp() {

        this.pattern = /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)[^]{8,}$/;
        this.weakNotice = '密码必须由：数字、大小写字母组合';
        this.dialog = {

            title: this.title,
            visible: true,
            password: null,
            newPassword: null,
            confirmPassword: null,
        };

        new Vue({

            el: this.$container.lastElementChild,
            data: this.dialog,
            methods: this.helper.fakeVueInsMethod(this, [this.checkAndSave, this.reset]),
        });
    }

    async checkAndSave() {

        var oldp = this.dialog.password;
        var newp = this.dialog.newPassword;
        var cnfp = this.dialog.confirmPassword;

        if (!oldp) {
            return this.interaction.showError('原始密码，请输入');
        }
        else if (this.userInfo.password != oldp && this.userInfo.password != this.helper.aesEncrypt(oldp)) {
            return this.interaction.showError('原始密码，输入不正确');
        }
        else if (!newp) {
            return this.interaction.showError('新设密码，请输入');
        }
        else if (!this.pattern.test(newp)) {
            return this.interaction.showError(`新设密码复杂度过低（${this.weakNotice}）`);
        }
        else if (cnfp != newp) {
            return this.interaction.showError('确认密码，与新密码不一致');
        }

        const { app } = require('@electron/remote');
        const encryptPasscodeRequired = app.encryptionOptions.encryptPasscode;
        if (encryptPasscodeRequired) {
            newp = this.helper.aesEncrypt(newp);
        }
        var resp = await repoUser.resetPassword(this.userInfo.userName, newp);
        if (resp.errorCode == 0) {

            this.userInfo.password = newp;
            this.interaction.showSuccess('密码已修改');
            this.reset();
        }
        else {
            this.interaction.showError(`密码修改失败：${resp.errorCode}/${resp.errorMsg}`);
        }
    }

    reset() {

        this.dialog.visible = false;
        this.dialog.password = null;
        this.dialog.newPassword = null;
        this.dialog.confirmPassword = null;
    }

    build($container) {

        super.build($container);
        this.createApp();
    }
};