
const SmartTable = require('../../libs/table/smart-table').SmartTable;
const IView = require('../../component/iview').IView;
const { ContextObjectInfo } = require('../../model/context-object-info');

class BaseList extends IView {

    /**
     * 上下文
     */
    get context() {
        return this._context;
    }

    /**
     * 当前上下文，产品|策略|账号ID
     */
    get identityId() {
        return this._context.identityId;
    }

    get allow2Trade() {
        return this.userInfo.isObserver !== true && !this.blackPermits.some(item => item.functionCode == this.systemPermit.ordering);
    }

    constructor(view_name, is_standalone_window, title) {
        
        super(view_name, is_standalone_window, title);

        this.paging = {

            pageSizes: this.systemSetting.tablePagination.pageSizes,
            pageSize: this.systemSetting.tablePagination.pageSize,
            layout: this.systemSetting.tablePagination.layout,
            total: 0,
            page: 0,
        };
    }

    /**
     * 获取行数据主键
     * @param {*} record 
     */
    identifyRecord(record) {
        return record.id;
    }

    refresh() {
        this.handleRefresh();
    }

    config() {
        this.tableObj.showColumnConfigPanel();
    }

    exportSome() {

        var now = new Date().format('yyyyMMdd-hhmmss');
        this.tableObj.exportAllRecords(`${now}-${this.title}`);
    }

    setHeight(height) {

        var net_height = height - 105;
        if (this.tableObj) {
            this.tableObj.setMaxHeight(net_height);
        }
        else {
            this._properTableHeight = net_height;
        }
    }




    /**
     * 创建工具栏VUE APP
     */
    createToolbarApp() {
        throw new Error('not implemented');
    }

    /**
     * @returns {SmartTable}
     */
    createTableComponent() {
        throw new Error('not implemented');
    }

    /**
     * 处理上下文变更
     * @param { ContextObjectInfo } previous_context 
     */
    handleContextChanged(previous_context) {
        throw new Error('not implemented');
    }

    /**
     * 处理刷新事件
     */
    handleRefresh() {
        throw new Error('not implemented');
    }

    /**
     * 处理交易服务器连接重新恢复事件
     */
    handleConnectionRecovered() {
        //
    }

    /**
     * 处理上下文变更
     * @param {*} context_info 上下文信息（产品 | 策略 | 账号）
     * @param {Boolean} is_focused 当前tab单元是否在焦点状态
     */
    _handleContextChange(context_info, is_focused) {

        var cti = new ContextObjectInfo(context_info);
        if (is_focused) {

            var previous_context = this._context;
            this._context = cti;
            this._showLoading();
            this.handleContextChanged(previous_context);
        }
        else {
            /** 当前tab组件pending状态的上下文对象 */
            this._pendingContext = cti;
        }
    }

    /**
     * 处理tab获得焦点事件
     */
    _handleActivation() {
        
        // 获得焦点的tab，调整位于其中的表格组件宽度，适配窗口实际尺寸
        this.tableObj.fitColumnWidth();

        if (this._pendingContext) {

            var previous_context = this._context;
            this._context = this._pendingContext;
            delete this._pendingContext;
            this._showLoading();
            this.handleContextChanged(previous_context);
        }
    }

    /**
     * 处理tab失去焦点事件
     */
    _handleInactivation() {
        // console.log(`tab view ${this.title} becomes inactive`);
    }

    /**
     * 处理交易服务器连接重新恢复事件
     */
    _handleConnectionRecovered() {
        this.handleConnectionRecovered();
    }

    /**
     * 展示loading效果
     */
    _showLoading() {

        this._hideLoading();
        this._dataLoading = this.interaction.showLoading({ text: '正在加载，订单/持仓/成交数据' });
        this._closeLoadingTimer = setTimeout(() => { this._hideLoading(); }, 1000 * 20);
    }

    /**
     * 销毁loading效果
     */
    _hideLoading() {

        if (this._dataLoading !== undefined) {

            this._dataLoading.close();
            delete this._dataLoading;
        }

        if (this._closeLoadingTimer !== undefined) {

            clearTimeout(this._closeLoadingTimer);
            delete this._closeLoadingTimer;
        }
    }

    _setupTable() {

        const tableObj = this.createTableComponent();
        const theight = this._properTableHeight;

        if (typeof theight == 'number' && theight >= 0) {

            tableObj.setMaxHeight(theight);
            delete this._properTableHeight;
        }
        else {
            tableObj.setMaxHeight(300);
        }

        this.tableObj = tableObj;
    }

    _listen2CoreEvents() {

        this.registerEvent(this.systemEvent.viewContextChange, this._handleContextChange.bind(this));
        this.registerEvent(this.systemEvent.tabActivated, this._handleActivation.bind(this));
        this.registerEvent(this.systemEvent.tabInactivated, this._handleInactivation.bind(this));
        this.renderProcess.on(this.systemEvent.tradingServerReestablished, this._handleConnectionRecovered.bind(this));
    }

    build($container) {

        super.build($container);
        this.createToolbarApp();
        this._setupTable();
        this._listen2CoreEvents();
    }
}

module.exports = { BaseList };
