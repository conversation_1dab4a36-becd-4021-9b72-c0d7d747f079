const IView = require('../../component/iview').IView;
const repoAccount = require('../../repository/account').repoAccount;
const repoPosition = require('../../repository/position').repoPosition;
const repoOrder = require('../../repository/order').repoOrder;
const repoTradeRecord = require('../../repository/traderecord').repoTradeRecord;
const systemEnum = require('../../config/system-enum').systemEnum;

class EditTodayRecordView extends IView {
    constructor(view_name, is_standalone_window, title) {
        super(view_name, is_standalone_window, title);

        this.params = {};
    }

    get record() {
        return this.params.record;
    }

    get recordTypeName() {
        if (this.isOrder) {
            return '订单';
        }
        if (this.isPosition) {
            return '持仓';
        }
        if (this.isTradeRecord) {
            return '成交';
        }
        return '';
    }

    get isOrder() {
        return this.params.isOrder;
    }

    get isPosition() {
        return this.params.isPosition;
    }

    get isTradeRecord() {
        return this.params.isTradeRecord;
    }

    get account() {
        return this.params.account;
    }

    get title() {
        return this.record ? `修改${this.recordTypeName}` : `新增${this.recordTypeName}`;
    }

    async getNotify(params) {
        console.log(params);
        this.params = params;
        this.dialogApp.title = this.title;
        await this.dialogApp.getAccounts();
        this.dialogApp.visible = true;
    }

    initApp() {
        const self = this;
        this.dialogApp = new Vue({
            el: this.$container.querySelector('.edit-today-record-view'),
            data: {
                visible: false,
                title: '',
                form: {},
                rules: {},
                rows: [],
                accounts: [],
                labelWidth: '100px',
                stringKeys: ['yesterdayPosition', 'todayPosition', 'avgPrice', 'volumeOriginal', 'orderPrice', 'tradedVolume', 'tradedPrice', 'volume', 'exchangeOrderId', 'tradeId', 'orderId'],
            },
            watch: {
                visible(val) {
                    if (val) {
                        this.renderForm();
                        this.renderRules();
                        this.renderRows();
                        this.setLabelWidth();
                        if (self.record) {
                            this.setFormData(self.record);
                        }
                    }
                },
            },
            methods: {
                setLabelWidth() {
                    if (self.isPosition) {
                        this.labelWidth = '60px';
                    }
                },
                haschange(col) {
                    return col.handleChange ? 'change' : null;
                },
                hasselect(col) {
                    return col.handleSelect ? 'select' : null;
                },
                async getAccounts() {
                    if (this.accounts.length > 0) {
                        return;
                    }
                    let loading = self.interaction.showLoading({
                        text: '请求账号列表...',
                    });
                    let resp = await repoAccount.getSimpleAccountList(false, self.userInfo.orgId);
                    if (resp.errorCode == 0 && Array.isArray(resp.data)) {
                        this.accounts = resp.data.map((account) => ({ label: account.accountName, value: account.accountId }));
                    }
                    loading.close();
                },
                setFormData(record) {
                    for (const key in this.form) {
                        if (record.hasOwnProperty(key)) {
                            if (this.stringKeys.includes(key)) {
                                this.form[key] = String(record[key]);
                            } else {
                                this.form[key] = record[key];
                            }
                        }
                    }
                },
                save() {
                    this.$refs.form.validate((valid) => {
                        if (valid) {
                            console.log('save', this.form);
                            let repo = null;
                            if (self.isPosition) {
                                repo = repoPosition;
                            } else if (self.isOrder) {
                                repo = repoOrder;
                            } else if (self.isTradeRecord) {
                                repo = repoTradeRecord;
                            }
                            if (repo) {
                                this.saveData(repo);
                            }
                        }
                    });
                },
                async saveData(repo) {
                    let data = this.buildData(this.form);
                    let resp = !!self.record ? await repo.update([data]) : await repo.add([data]);
                    if (resp.errorCode == 0) {
                        self.interaction.showSuccess('保存成功');
                        self.params.callback();
                        this.cancel();
                    } else {
                        self.interaction.showHttpError(`保存${self.recordTypeName}失败: ${resp.errorMsg}`);
                    }
                },
                buildData(form) {
                    let data = { ...form };
                    for (const key in data) {
                        const val = data[key];
                        if (val === '') {
                            delete data[key];
                        }
                    }
                    return data;
                },
                cancel() {
                    this.visible = false;
                    this.clearForm();
                },
                renderForm() {
                    if (self.isPosition) {
                        this.$set(this.$data, 'form', {
                            accountId: self.account.accountId,
                            yesterdayPosition: '',
                            todayPosition: '',
                            avgPrice: '',
                            instrument: '',
                            direction: '',
                        });
                    } else if (self.isOrder) {
                        this.$set(this.$data, 'form', {
                            accountId: self.account.accountId,
                            volumeOriginal: '',
                            orderPrice: '',
                            tradedVolume: '',
                            tradedPrice: '',
                            orderStatus: '',
                            exchangeOrderId: '',
                            instrument: '',
                            direction: '',
                            positionEffect: '',
                        });
                    } else if (self.isTradeRecord) {
                        this.$set(this.$data, 'form', {
                            id: '',
                            accountId: self.account.accountId,
                            instrument: '',
                            direction: '',
                            volume: '',
                            tradedPrice: '',
                            exchangeOrderId: '',
                            tradeId: '',
                            tradeTime: '09:30:00',
                            orderId: '',
                        });
                    }
                },
                renderRules() {
                    if (self.isPosition) {
                        this.$set(this.$data, 'rules', {
                            accountId: [{ type: 'number', required: true, message: '请选择账号' }],
                            yesterdayPosition: [
                                { type: 'string', required: true, message: '请输入昨仓' },
                                {
                                    validator: (rule, value, callback) => {
                                        if (isNaN(value)) {
                                            callback('请输入数字');
                                        } else {
                                            callback();
                                        }
                                    },
                                },
                            ],
                            todayPosition: [
                                { type: 'string', required: true, message: '请输入今仓' },
                                {
                                    validator: (rule, value, callback) => {
                                        if (isNaN(value)) {
                                            callback('请输入数字');
                                        } else {
                                            callback();
                                        }
                                    },
                                },
                            ],
                            avgPrice: [
                                { type: 'string', required: true, message: '请输入持仓均价' },
                                {
                                    validator: (rule, value, callback) => {
                                        if (isNaN(value)) {
                                            callback('请输入数字');
                                        } else {
                                            callback();
                                        }
                                    },
                                },
                            ],
                            instrument: [
                                { type: 'string', required: true, message: '请输入合约' },
                            ],
                            direction: [{ type: 'number', required: true, message: '请选择方向' }],
                        });
                    } else if (self.isOrder) {
                        this.$set(this.$data, 'rules', {
                            accountId: [{ type: 'number', required: true, message: '请选择账号' }],
                            volumeOriginal: [
                                { type: 'string', required: true, message: '请输入委托数量' },
                                {
                                    validator: (rule, value, callback) => {
                                        if (isNaN(value) || value < 0) {
                                            callback('请输入正确的数字');
                                        } else {
                                            callback();
                                        }
                                    },
                                },
                            ],
                            orderPrice: [
                                { type: 'string', required: true, message: '请输入委托价格' },
                                {
                                    validator: (rule, value, callback) => {
                                        if (isNaN(value) || value < 0) {
                                            callback('请输入正确的数字');
                                        } else {
                                            callback();
                                        }
                                    },
                                },
                            ],
                            tradedVolume: [
                                { type: 'string', required: true, message: '请输入成交数量' },
                                {
                                    validator: (rule, value, callback) => {
                                        if (isNaN(value) || value < 0) {
                                            callback('请输入正确的数字');
                                        } else {
                                            callback();
                                        }
                                    },
                                },
                            ],
                            tradedPrice: [
                                { type: 'string', required: true, message: '请输入成交价格' },
                                {
                                    validator: (rule, value, callback) => {
                                        if (isNaN(value) || value < 0) {
                                            callback('请输入正确的数字');
                                        } else {
                                            callback();
                                        }
                                    },
                                },
                            ],
                            orderStatus: [{ type: 'number', required: true, message: '请选择委托状态' }],
                            exchangeOrderId: [{ type: 'string', required: true, message: '请输入委托编号' }],
                            instrument: [
                                { type: 'string', required: true, message: '请输入合约' },
                            ],
                            direction: [{ type: 'number', required: true, message: '请选择方向' }],
                        });
                    } else if (self.isTradeRecord) {
                        this.$set(this.$data, 'rules', {
                            accountId: [{ type: 'number', required: true, message: '请选择账号' }],
                            instrument: [
                                { type: 'string', required: true, message: '请输入合约' },
                            ],
                            direction: [{ type: 'number', required: true, message: '请选择方向' }],
                            volume: [
                                { type: 'string', required: true, message: '请输入成交数量' },
                                {
                                    validator: (rule, value, callback) => {
                                        if (isNaN(value) || value < 0) {
                                            callback('请输入正确的数字');
                                        } else {
                                            callback();
                                        }
                                    },
                                },
                            ],
                            tradedPrice: [
                                { type: 'string', required: true, message: '请输入成交价格' },
                                {
                                    validator: (rule, value, callback) => {
                                        if (isNaN(value) || value < 0) {
                                            callback('请输入正确的数字');
                                        } else {
                                            callback();
                                        }
                                    },
                                },
                            ],
                            exchangeOrderId: [{ type: 'string', required: true, message: '请输入委托编号' }],
                            tradeId: [{ type: 'string', required: true, message: '请输入成交编号' }],
                            tradeTime: [{ type: 'string', required: true, message: '请输入成交时间' }],
                        });
                    }
                },
                renderRows() {
                    if (self.isPosition) {
                        this.$set(this.$data, 'rows', [
                            {
                                cols: [
                                    {
                                        label: '账号',
                                        prop: 'accountId',
                                        type: 'select',
                                        options: this.accounts,
                                        filterable: true,
                                        disabled: true,
                                    },
                                    {
                                        label: '合约',
                                        prop: 'instrument',
                                        disabled: !!self.record,
                                    },
                                ],
                            },
                            {
                                cols: [
                                    {
                                        label: '昨仓',
                                        prop: 'yesterdayPosition',
                                    },
                                    {
                                        label: '今仓',
                                        prop: 'todayPosition',
                                    },
                                ],
                            },
                            {
                                cols: [
                                    {
                                        label: '均价',
                                        prop: 'avgPrice',
                                    },
                                    {
                                        label: '方向',
                                        prop: 'direction',
                                        type: 'select',
                                        disabled: !!self.record,
                                        options: self.helper
                                            .dict2Array(self.account.assetType == systemEnum.assetsType.future.code ? self.systemTrdEnum.futureTradingDirection : self.systemTrdEnum.tradingDirection)
                                            .map((item) => ({
                                                value: item.code,
                                                label: item.mean,
                                            })),
                                    },
                                ],
                            },
                        ]);
                    } else if (self.isOrder) {
                        this.$set(this.$data, 'rows', [
                            {
                                cols: [
                                    {
                                        label: '账号',
                                        prop: 'accountId',
                                        type: 'select',
                                        options: this.accounts,
                                        filterable: true,
                                        disabled: true,
                                    },
                                    {
                                        label: '合约',
                                        prop: 'instrument',
                                        disabled: !!self.record,
                                    },
                                ],
                            },
                            {
                                cols: [
                                    {
                                        label: '委托状态',
                                        prop: 'orderStatus',
                                        type: 'select',
                                        options: self.helper.dict2Array(systemEnum.orderStatus).map((item) => ({
                                            value: item.code,
                                            label: item.mean,
                                        })),
                                    },
                                    {
                                        label: '委托编号',
                                        prop: 'exchangeOrderId',
                                    },
                                ],
                            },
                            {
                                cols: [
                                    {
                                        label: '委托数量',
                                        prop: 'volumeOriginal',
                                    },
                                    {
                                        label: '委托价格',
                                        prop: 'orderPrice',
                                        disabled: !!self.record,
                                    },
                                ],
                            },
                            {
                                cols: [
                                    {
                                        label: '成交数量',
                                        prop: 'tradedVolume',
                                    },
                                    {
                                        label: '成交价格',
                                        prop: 'tradedPrice',
                                    },
                                ],
                            },
                            {
                                cols: [
                                    {
                                        label: '买卖方向',
                                        prop: 'direction',
                                        type: 'select',
                                        disabled: !!self.record,
                                        options: self.helper
                                            .dict2Array(self.account.assetType == systemEnum.assetsType.future.code ? self.systemTrdEnum.futureTradingDirection : self.systemTrdEnum.tradingDirection)
                                            .map((item) => ({
                                                value: item.code,
                                                label: item.mean,
                                            })),
                                    },
                                    {
                                        label: '开平仓方向',
                                        prop: 'positionEffect',
                                        type: 'select',
                                        disabled: !!self.record,
                                        clearable: true,
                                        options: self.helper.dict2Array(self.systemTrdEnum.positionEffect).map((item) => ({
                                            value: item.code,
                                            label: item.mean,
                                        })),
                                    },
                                ],
                            },
                        ]);
                    } else if (self.isTradeRecord) {
                        this.$set(this.$data, 'rows', [
                            {
                                cols: [
                                    {
                                        label: '账号',
                                        prop: 'accountId',
                                        type: 'select',
                                        options: this.accounts,
                                        filterable: true,
                                        disabled: true,
                                    },
                                    {
                                        label: '合约',
                                        prop: 'instrument',
                                        disabled: !!self.record,
                                    },
                                ],
                            },
                            {
                                cols: [
                                    {
                                        label: '成交编号',
                                        prop: 'tradeId',
                                    },
                                    {
                                        label: '成交时间',
                                        prop: 'tradeTime',
                                    },
                                ],
                            },
                            {
                                cols: [
                                    {
                                        label: '委托编号',
                                        prop: 'exchangeOrderId',
                                    },
                                    {
                                        label: '订单ID',
                                        prop: 'orderId',
                                    },
                                ],
                            },
                            {
                                cols: [
                                    {
                                        label: '成交数量',
                                        prop: 'volume',
                                    },
                                    {
                                        label: '成交价格',
                                        prop: 'tradedPrice',
                                    },
                                ],
                            },
                            {
                                cols: [
                                    {
                                        label: '方向',
                                        prop: 'direction',
                                        type: 'select',
                                        disabled: !!self.record,
                                        options: self.helper
                                            .dict2Array(self.account.assetType == systemEnum.assetsType.future.code ? self.systemTrdEnum.futureTradingDirection : self.systemTrdEnum.tradingDirection)
                                            .map((item) => ({
                                                value: item.code,
                                                label: item.mean,
                                            })),
                                    },
                                ],
                            },
                        ]);
                    }
                },
                clearForm() {
                    for (const key in this.form) {
                        if (key == 'tradeTime') {
                            this.form[key] == '09:30:00';
                        } else if (key == 'accountId') {
                            this.form[key] = self.account.accountId;
                        } else {
                            this.form[key] = '';
                        }
                    }
                },
            },
        });
    }

    build($container) {
        super.build($container);
        this.initApp();
    }
}

module.exports = { EditTodayRecordView };
