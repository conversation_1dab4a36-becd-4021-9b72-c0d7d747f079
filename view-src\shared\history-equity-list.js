const { BaseList } = require('./baselist');
const repoTrading = require('../../repository/trading').repoTrading;
const SmartTable = require('../../libs/table/smart-table').SmartTable;
const ColumnCommonFunc = require('../../libs/table/column-common-func').ColumnCommonFunc;
const NumberMixin = require('../../mixin/number').NumberMixin;

class View extends BaseList {

    constructor(view_name, is_standalone_window) {

        super(view_name, is_standalone_window, '历史权益');

        this.condition = {
            dateRange: [new Date().addDays(-3), new Date().addDays(-1)],
        };

        this.paging = {

            pageSizes: this.systemSetting.tablePagination.pageSizes,
            pageSize: this.systemSetting.tablePagination.pageSize,
            layout: this.systemSetting.tablePagination.layout,
            total: 0,
            page: 0,
        };
    }

    handleContextChanged() {
        this.turn2Request();
    }

    handlePageSizeChange() {
        this.tableObj.setPageSize(this.paging.pageSize);
    }

    handlePageChange() {
        this.tableObj.setPageIndex(this.paging.page);
    }

    search() {
        this.turn2Request();
    }

    handleRefresh() {

        this.paging.page = 1;
        this.turn2Request();
    }

    async turn2Request() {

        if (!this.context) {

            this.paging.total = 0;
            this.paging.page = 0;
            this.tableObj.refill([]);
            return;
        }

        var $loading = this.interaction.showLoading({ text: `正在请求${this.title}...` });
        try {

            $loading.close();
            var equities = await this.requestHistoryEquities();
            this.paging.total = equities.length;
            this.tableObj.refill(equities);
        } 
        catch (ex) {

            $loading.close();
            console.error(ex);
            this.interaction.showError(ex.message);
        }
        finally {
            this.handleDataRequestCompleted();
        }
    }

    /**
     * @returns {Array}
     */
    async requestHistoryEquities() {

        let condition = this.condition;
        let context = this.context;
        let identity_id = this.identityId;

        if (!(condition.dateRange instanceof Array) || condition.dateRange.length != 2) {
            throw new Error('请选择查询日期区间');
        }

        let criteria = {

            beginDay: condition.dateRange[0].format('yyyyMMdd'),
            endDay: condition.dateRange[1].format('yyyyMMdd'),
            identityId: identity_id,
            identityType: context.isAboutFund ? 3 : context.isAboutStrategy ? 2 : 1,
            parentOrderId: context.isAboutParent ? identity_id : undefined,
        };

        let resp = this.context.isAboutAccount ? await repoTrading.getHistoryAccountBalances(criteria)
                                               : await repoTrading.getHistoryBalances(criteria);
        if (resp.errorCode !== 0) {
            throw new Error(`历史权益查询错误，返回代码 = ${resp.errorCode}/${resp.errorMsg}`);
        }

        return resp.data;
    }

    createToolbarApp() {
        this.toolbarApp = new Vue({
            el: this.$container.querySelector('.user-toolbar'),
            data: {
                condition: this.condition,
                paging: this.paging,
            },

            mixins: [NumberMixin],
            methods: this.helper.fakeVueInsMethod(this, [this.search, this.handlePageSizeChange, this.handlePageChange]),
        });
    }

    createTableComponent() {
        this.helper.extend(this, ColumnCommonFunc);
        var $table = this.$container.querySelector('.table-control');
        var tableObj = new SmartTable($table, this.identifyRecord, this, {
            tableName: 'smt-heql',
            displayName: this.title,
            enableConfigToolkit: true,
            defaultSorting: { prop: 'createTime', direction: 'desc' },
            recordsFiltered: this.handleTableFiltered.bind(this),
        });

        tableObj.setPageSize(this.paging.pageSize);
        return tableObj;
    }

    handleTableFiltered(filtered_count) {
        this.paging.total = filtered_count;
    }

    build($container) {
        super.build($container);
    }
}

module.exports = View;
