
const { BaseList } = require('./baselist');
const repoTrading = require('../../repository/trading').repoTrading;
const SmartTable = require('../../libs/table/smart-table').SmartTable;
const ColumnCommonFunc = require('../../libs/table/column-common-func').ColumnCommonFunc;
const NumberMixin = require('../../mixin/number').NumberMixin;
const { ServerBatchData } = require('../../model/server-batch-data');
const Exchange = require('../../model/exchange').Exchange;
const BizHelper = require('../../libs/helper-biz').BizHelper;

class View extends BaseList {

    constructor(view_name, is_standalone_window) {

        super(view_name, is_standalone_window, '历史成交');

        this.condition = {

            dateRange: [new Date().addDays(-3), new Date().addDays(-1)],
            accountId: null,
            keywords: null,
            accounts: [],
        };
    }

    handleContextChanged() {

        const condition = this.condition;
        condition.accountId = null;
        condition.keywords = null;
        condition.accounts.clear();
        condition.accounts.merge(this.helper.deepClone(this.context.accounts));

        this.turn2Request();
    }

    handlePageSizeChange() {

        this.tableObj.setPageSize(this.paging.pageSize);
        this.turn2Request();
    }

    handlePageChange() {
        this.turn2Request();
    }

    handleColumnSorting(sorting) {
        this.turn2Request();
    }

    search() {
        this.turn2Request();
    }

    handleRefresh() {

        this.condition.keywords = null;
        this.paging.page = 1;
        this.turn2Request();
    }

    async exportSome() {

        var now = new Date().format('yyyyMMdd-hhmmss');
        try {
            var svrd = await this.requestHistoryExchanges(true);
            var exchanges = svrd.contents;
            this.tableObj.exportRecords(exchanges, `${now}-${this.title}`);
        }
        catch(ex) {
            this.interaction.showError(ex.message);
        }
    }

    async turn2Request() {

        if (!this.context) {

            this.paging.total = 0;
            this.paging.page = 0;
            this.tableObj.refill([]);
            return;
        }

        var $loading = this.interaction.showLoading({ text: `正在请求${this.title}...` });
        try {

            $loading.close();
            var svrd = await this.requestHistoryExchanges();
            var exchanges = svrd.contents;

            this.paging.total = svrd.total;
            this.paging.pageSize = svrd.pageSize;
            this.tableObj.refill(exchanges);
        } 
        catch (ex) {

            $loading.close();
            console.error(ex);
            this.interaction.showError(ex.message);
        }
        finally {
            
            /** 隐藏数据加载效果 */
            this._hideLoading();
        }
    }

    /**
     * @param {Boolean} is_exporting
     * @returns {ServerBatchData}
     */
    async requestHistoryExchanges(is_exporting) {

        let condition = this.condition;
        let context = this.context;
        let identity_id = this.identityId;

        if (!(condition.dateRange instanceof Array) || condition.dateRange.length != 2) {
            throw new Error('请选择查询日期区间');
        }

        var resp = null;
        let criteria = {

            beginDay: condition.dateRange[0].format('yyyyMMdd'),
            endDay: condition.dateRange[1].format('yyyyMMdd'),
            accountId: condition.accountId || null,
            parentOrderId: context.isAboutParent ? identity_id : undefined,
            instrument: (condition.keywords || '').trim() || null,
            pageNo: is_exporting ? 1: this.paging.page,
            pageSize: is_exporting ? 9999999: this.paging.pageSize,
            sort: this.tableObj.sortingExpress,
        };

        if (context.isAboutFund) {

            criteria.fundId = identity_id;
            resp = await repoTrading.getProductHistoryExchanges(criteria);
        }
        else if (context.isAboutStrategy) {

            criteria.strategyId = identity_id;
            resp = await repoTrading.getStrategyHistoryExchanges(criteria);
        }
        else {

            if (this.helper.isNone(condition.accountId)) {
                criteria.accountId = identity_id;
            }

            resp = await repoTrading.getAccountHistoryExchanges(criteria);
        }
        
        if (resp.errorCode !== 0) {
            throw new Error(`历史成交查询错误，返回代码 = ${ resp.errorCode }/${ resp.errorMsg }`);
        }

        const dto = this.helper.extend({ identityId: identity_id }, resp.data);
        return new ServerBatchData(dto);
    }

    createToolbarApp() {

        this.toolbarApp = new Vue({

            el: this.$container.querySelector('.user-toolbar'),
            data: {
                condition: this.condition,
                paging: this.paging,
            },

            mixins: [NumberMixin],
            methods: this.helper.fakeVueInsMethod(this, [this.search, this.handlePageSizeChange, this.handlePageChange]),
        });
    }

    createTableComponent() {
        
        this.helper.extend(this, ColumnCommonFunc);
        var $table = this.$container.querySelector('.table-control');
        var tableObj = new SmartTable($table, this.identifyRecord, this, {

            tableName: 'smt-hexl',
            displayName: this.title,
            enableConfigToolkit: true,
            defaultSorting: { prop: 'createTime', direction: 'desc' },
            recordsFiltered: this.handleTableFiltered.bind(this),
            serverDataRequester: this.handleColumnSorting.bind(this),
        });

        tableObj.setPageSize(this.paging.pageSize);
        return tableObj;
    }

    handleTableFiltered(filtered_count) {
        this.paging.total = filtered_count;
    }

    build($container) {
        super.build($container);
    }
}

module.exports = View;
