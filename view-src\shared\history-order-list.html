<div class="summary-history-order">

	<div class="user-toolbar themed-box">

		<el-date-picker size="mini" type="daterange" v-model="condition.dateRange" start-placeholder="开始日期" range-separator="至" end-placeholder="结束日期"></el-date-picker>
        <el-select size="mini" placeholder="账号" v-model="condition.accountId" class="s-mgl-10 s-w-150" filterable clearable>
            <el-option v-for="(item, item_idx) in condition.accounts" :key="item_idx" :value="item.accountId" :label="item.accountName"></el-option>
        </el-select>
        <el-input size="mini" placeholder="完整合约代码" v-model.trim="condition.keywords" class="s-mgl-10 s-w-120" clearable></el-input>
        <el-button size="mini" type="primary" size="small" @click="search" class="s-mgl-10">查询</el-button>
		
		<el-pagination :page-sizes="paging.pageSizes" 
					   :page-size.sync="paging.pageSize" 
					   :total="paging.total" 
					   :current-page.sync="paging.page"
					   :layout="paging.layout" 
					   @size-change="handlePageSizeChange" 
					   @current-change="handlePageChange"></el-pagination>

	</div>

	<div class="table-control">
		<table>
            <tr>
                <th label="账号" min-width="150" prop="accountName" overflowt sortable searchable></th>
                <th label="交易员" min-width="90" prop="userName" overflowt sortable searchable></th>
                <th label="代码" fixed-width="100" prop="instrument" overflowt sortable searchable></th>
                <th label="名称" fixed-width="80" prop="instrumentName" overflowt sortable searchable></th>
                <th label="交易日" fixed-width="80" prop="tradingDay" sortable></th>

                <th type="program" 
                    label="订单状态" 
                    fixed-width="100" 
                    prop="orderStatus" 
                    watch="orderStatus, errorMsg" 
                    formatter="formatOrderStatus" 
                    export-formatter="formatOrderStatusText" overflowt sortable></th>

                <th type="program" 
                    label="方向" 
                    fixed-width="70" 
                    prop="direction" 
                    watch="direction" 
                    formatter="formatDirection" 
                    export-formatter="formatDirectionText" sortable></th>

                <th type="program" 
                    label="交易方式" 
                    fixed-width="100" 
                    prop="businessFlag" 
                    watch="businessFlag" 
                    formatter="formatBusinessFlag" 
                    export-formatter="formatBusinessFlag" sortable></th>

                <th label="委托量" 
                    fixed-width="80" 
                    prop="volumeOriginal" 
                    align="right" summarizable thousands-int></th>

                <th label="委托价" 
                    fixed-width="60" 
                    prop="orderPrice" 
                    align="right" 
                    formatter="formatPrice"></th>

                <th label="成交量" 
                    fixed-width="80" 
                    prop="tradedVolume" 
                    align="right" summarizable thousands-int></th>

                <th label="成交价" 
                    fixed-width="60" 
                    prop="tradedPrice" 
                    align="right" 
                    formatter="formatPrice"></th>

                <th type="program" 
                    label="报单时间" 
                    fixed-width="100" 
                    prop="orderTime" 
                    watch="orderTime" 
                    formatter="formatTime" sortable></th>

                <th type="program" 
                    label="创建时间" 
                    fixed-width="100" 
                    prop="createTime" 
                    watch="createTime" 
                    formatter="formatTime" sortable></th>

                <th type="program" 
                    label="成交时间"
                    fixed-width="100" 
                    prop="tradeTime" 
                    watch="tradeTime" 
                    formatter="formatTime" sortable></th>

                <th label="报单编号" min-width="80" prop="exchangeOrderId" overflowt></th>
                <th label="冻结资金" fixed-width="80" prop="frozenMargin" align="right" thousands-int></th>
                <th label="策略" min-width="150" prop="strategyName" overflowt searchable sortable></th> <!-- strategy-remove -->
                <th label="产品" min-width="150" prop="fundName" sortable overflowt searchable></th> <!-- product-remove -->

                <th type="program" 
                    label="资产类型" 
                    fixed-width="100" 
                    prop="assetType" 
                    watch="assetType" 
                    formatter="formatAssetType" sortable></th>

                <th type="program" 
                    label="外来单" 
                    fixed-width="90" 
                    prop="foreign" 
                    watch="foreign" 
                    formatter="formatYesNo" 
                    export-formatter="formatYesNoText" sortable></th>

                <th type="program" 
                    label="强平" 
                    fixed-width="80" 
                    prop="forceClose" 
                    watch="forceClose" 
                    formatter="formatYesNo" 
                    export-formatter="formatYesNoText" sortable></th>
                    
                <th label="备注" min-width="150" prop="remark" overflowt sortable></th>
            </tr>
        </table>
	</div>

</div>