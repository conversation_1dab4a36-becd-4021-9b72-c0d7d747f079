<div class="summary-order">

	<div class="user-toolbar themed-box">

		<el-input placeholder="关键字搜索" prefix-icon="el-icon-search" class="searching-keywords"
			v-model="condition.keywords" @change="filterRecords" clearable></el-input>

		<span class="option-box">
			<el-select clearable v-model="condition.orderType" placeholder="订单状态过滤" @change="filterRecords">
				<el-option v-for="(val, key) in orderTypes" :key="key" :label="val.mean" :value="val.code">
				</el-option>
			</el-select>
		</span>

		<span class="operation-box" v-show="allow2Trade">
			<el-button type="primary" size="small" @click="batchCancel(cancelTypes.all)">全撤</el-button>
			<el-button type="primary" size="small" @click="batchCancel(cancelTypes.buy)">撤买单</el-button>
			<el-button type="primary" size="small" @click="batchCancel(cancelTypes.sell)">撤卖单</el-button>
			<el-button type="primary" size="small" @click="batchCancel(cancelTypes.selected)">撤勾选</el-button>
		</span>

		<el-pagination :page-sizes="paging.pageSizes" :page-size.sync="paging.pageSize" :total="paging.total"
			:current-page.sync="paging.page" :layout="paging.layout" @size-change="handlePageSizeChange"
			@current-change="handlePageChange"></el-pagination>


		<span class="user-toolkit">

			<el-button size="small" v-show="sharedCondition.showPause"
				:type="sharedCondition.isPaused ? 'success' : 'danger'" @click="toggleRealtimePush">
				{{ sharedCondition.isPaused ? '恢复订单刷新' : '暂停订单刷新' }}</el-button>

			<el-tooltip content="添加订单记录" v-if="sharedCondition.canChangeRecord">
				<el-button size="small" type="primary" @click="openAddRecordDialog">
					<i class="el-icon-plus"></i> 订单</el-button>
			</el-tooltip>

		</span>

	</div>

	<div class="table-control">
		<table>
			<tr>
				<th type="check" fixed-width="40" fixed></th>
				<th label="账号" min-width="150" prop="accountName" filterable overflowt sortable searchable></th>
				<th label="交易员" min-width="90" prop="userName" filterable overflowt sortable searchable></th>
				<th label="代码" fixed-width="100" prop="instrument" filterable overflowt sortable searchable></th>
				<th label="名称" fixed-width="80" prop="instrumentName" filterable overflowt sortable searchable></th>

				<th type="program" 
					label="订单状态" 
					fixed-width="100" 
					prop="orderStatus" 
					watch="orderStatus, errorMsg"
					formatter="formatOrderStatus" 
					export-formatter="formatOrderStatusText" 
					filter-data-provider="rebindOrderStatus" overflowt sortable></th>

				<th type="program" 
					label="方向" 
					fixed-width="70" 
					prop="direction" 
					watch="direction"
					formatter="formatDirection" 
					export-formatter="formatDirectionText" 
					filter-data-provider="rebindDirection" sortable></th>

				<th type="program" 
					label="交易方式" 
					fixed-width="100" 
					prop="businessFlag" 
					watch="businessFlag"
					formatter="formatBusinessFlag" 
					export-formatter="formatBusinessFlag" 
					filter-data-provider="rebindBusinessFlag" sortable></th>

				<th label="委托量" 
					fixed-width="80" 
					prop="volumeOriginal" 
					align="right" filterable sortable summarizable thousands-int></th>

				<th label="委托价" 
					fixed-width="60" 
					prop="orderPrice" 
					align="right" 
					formatter="formatPrice"></th>

				<th label="成交量" 
					fixed-width="80" 
					prop="tradedVolume" 
					align="right" filterable sortable summarizable thousands-int></th>

				<th label="成交价" 
					fixed-width="60" 
					prop="tradedPrice" 
					align="right" 
					formatter="formatPrice"></th>

				<th type="program" 
					label="创建时间" 
					fixed-width="100" 
					prop="createTime" 
					watch="createTime" 
					formatter="formatTime" 
					sortable></th>

				<th type="program" 
					label="报单时间" 
					fixed-width="100" 
					prop="orderTime" 
					watch="orderTime" 
					formatter="formatTime" sortable></th>

				<th type="program" 
					label="成交时间" 
					fixed-width="100" 
					prop="tradeTime" 
					watch="tradeTime" 
					formatter="formatTime" filterable sortable></th>

				<th label="报单编号" 
					min-width="80" 
					prop="exchangeOrderId" overflowt></th>

				<th label="冻结资金" 
					fixed-width="80" 
					prop="frozenMargin" 
					align="right" thousands-int></th>

				<th label="策略" 
					min-width="150" 
					prop="strategyName" filterable overflowt searchable sortable></th>

				<!-- strategy-remove -->
				<th label="产品" 
					min-width="150" 
					prop="fundName" filterable sortable overflowt searchable></th>

				<!-- product-remove -->
				<th type="program" 
					label="资产类型" 
					fixed-width="100" 
					prop="assetType" 
					watch="assetType"
					formatter="formatAssetType" 
					filter-data-provider="rebindAssetType" sortable></th>

				<th type="program" 
					label="外来单" 
					fixed-width="90" 
					prop="foreign" watch="foreign" 
					formatter="formatYesNo"
					export-formatter="formatYesNoText" 
					filter-data-provider="rebindYesNo" sortable></th>

				<th type="program" 
					label="强平"
					fixed-width="80" 
					prop="forceClose" 
					watch="forceClose"
					formatter="formatYesNo" 
					export-formatter="formatYesNoText" 
					filter-data-provider="rebindYesNo" sortable></th>

				<th label="备注" min-width="150" prop="remark" overflowt sortable></th>

				<th type="program" 
					fixed="right" 
					watch="isCompleted" 
					label="操作" 
					fixed-width="130"
					formatter="formatActions" 
					class-maker="getCompletementCellClass" 
					exportable="false">

				</th>
			</tr>
		</table>
	</div>

</div>