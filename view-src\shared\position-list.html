<div class="summary-position">

	<div class="user-toolbar themed-box">

		<el-input placeholder="关键字搜索" 
				  prefix-icon="el-icon-search" 
				  class="searching-keywords"
				  v-model="condition.keywords" 
				  @change="filterRecords" clearable></el-input>
		
		<el-pagination :page-sizes="paging.pageSizes" 
					   :page-size.sync="paging.pageSize" 
					   :total="paging.total" 
					   :current-page.sync="paging.page"
					   :layout="paging.layout" 
					   @size-change="handlePageSizeChange" 
					   @current-change="handlePageChange"></el-pagination>

		<span class="user-toolkit">

			<el-button size="small" 
					   v-show="sharedCondition.showPause"
					   :type="sharedCondition.isPaused ? 'success' : 'danger'"
					   @click="toggleRealtimePush">{{ sharedCondition.isPaused ? '恢复持仓刷新' : '暂停持仓刷新' }}</el-button>

			<el-tooltip content="添加持仓记录" v-if="sharedCondition.canChangeRecord">
				<el-button size="small" type="primary" @click="openAddRecordDialog">
					<i class="el-icon-plus"></i> 持仓</el-button>
			</el-tooltip>

			<el-tooltip content="添加调仓记录">
				<el-button size="small" type="primary" v-show="condition.showAdjustPosBtn" @click="openAdjustPosWin">
					<i class="el-icon-plus"></i> 调仓</el-button>
			</el-tooltip>

		</span>

		<div class="dialog-adjust-pos">

			<el-dialog :title="dialog.title" width="300px" :visible="dialog.visible" :close-on-click-modal="false" :show-close="false">
				<div class="content-holder"></div>
			</el-dialog>

		</div>

	</div>

	<div class="table-control">
		<table>
			<tr>
				<th label="代码" fixed-width="100" prop="instrument" overflowt filterable sortable searchable></th>
				<th label="名称" fixed-width="80" prop="instrumentName" overflowt filterable sortable searchable></th>
				<th label="账号" min-width="150" prop="accountName" overflowt filterable sortable searchable></th>

				<th type="program" 
					label="资产类型" 
					fixed-width="100" 
					prop="assetType" 
					watch="assetType" 
					formatter="formatAssetType" 
					filter-data-provider="rebindAssetType" sortable></th>
				
				<th type="program" 
					label="方向" 
					fixed-width="70" 
					prop="direction" 
					watch="direction" 
					formatter="formatDirection" 
					export-formatter="formatDirectionText" 
					filter-data-provider="rebindDirection" sortable></th>
				
				<th label="总仓" 
					fixed-width="80" 
					prop="totalPosition" 
					align="right" filterable sortable thousands-int></th>
				
				<th label="昨仓" 
					fixed-width="80" 
					prop="yesterdayPosition" 
					align="right" filterable sortable thousands-int></th>
				
				<th label="今仓" 
					fixed-width="80" 
					prop="todayPosition" 
					align="right" filterable sortable thousands-int></th>
				
				<th label="冻结仓数" 
					fixed-width="80" 
					prop="frozenVolume" 
					align="right" filterable sortable thousands-int></th>
				
				<th label="可平数" 
					fixed-width="80" 
					prop="closableVolume" 
					align="right" filterable sortable thousands-int></th>
				
				<th label="持仓均价" 
					fixed-width="80" 
					prop="avgPrice" 
					align="right" 
					formatter="formatPrice"></th>
				
				<th label="最新价" 
					fixed-width="80" 
					prop="lastPrice" 
					align="right" 
					formatter="formatPrice"></th>
				
				<th label="持仓市值" 
					min-width="80" 
					prop="marketValue" 
					align="right" sortable summarizable thousands-int></th>
				
				<th label="盈亏" 
					min-width="80" 
					prop="profit" 
					align="right" 
					class-maker="makeBenefitClass" 
					footer-class-maker="makeBenefitClass" overflowt sortable summarizable thousands-int></th>
				
				<th label="浮动盈亏" 
					min-width="80" 
					prop="floatProfit" 
					align="right" 
					class-maker="makeBenefitClass" 
					footer-class-maker="makeBenefitClass" overflowt sortable summarizable thousands-int></th>
				
				<th label="平仓盈亏" 
					min-width="80" 
					prop="closeProfit" 
					align="right" 
					class-maker="makeBenefitClass" 
					footer-class-maker="makeBenefitClass" overflowt sortable summarizable thousands-int></th>

				<th label="手续费" 
					min-width="80" 
					prop="usedCommission" 
					align="right" overflowt sortable summarizable thousands-int></th>

				<th label="保证金" 
					min-width="80" 
					prop="usedMargin" 
					align="right" overflowt sortable summarizable thousands-int></th>

				<th type="program"
					label="更新时间" 
					fixed-width="100" 
					prop="updateTime" 
					watch="updateTime" 
					formatter="formatTime" sortable></th>

				<th type="program" 
					fixed="right" label="操作" 
					fixed-width="130" 
					formatter="formatActions" 
					exportable="false"></th>
			</tr>
		</table>
	</div>

</div>