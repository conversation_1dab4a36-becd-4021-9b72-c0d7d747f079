
const { TodayBaseList } = require('./baselist-today');
const { ContextObjectInfo } = require('../../model/context-object-info');
const SmartTable = require('../../libs/table/smart-table').SmartTable;
const ColumnCommonFunc = require('../../libs/table/column-common-func').ColumnCommonFunc;
const NumberMixin = require('../../mixin/number').NumberMixin;
const ModelConverter = require('../../model/model-converter').ModelConverter;
const PositionRecord = require('../../model/position').Position;
const repoTrading = require('../../repository/trading').repoTrading;
const BizHelper = require('../../libs/helper-biz').BizHelper;

class View extends TodayBaseList {

    constructor(view_name, is_standalone_window) {

        super(view_name, is_standalone_window, TodayBaseList.ViewTitles.position);

        this.setDataFunCodeType(this.serverFunction.requestTodayPosition, 'position');
        this.condition = {

            keywords: null,
            showAdjustPosBtn: false,
        };

        this.dialog = {

            title: '调仓',
            visible: false,
        };

        this.setTitle([

            'id',
            'accountId',
            'financeAccount',
            'accountName',
            'fundId',
            'fundName',
            'strategyId',
            'strategyName',
            'tradingDay',
            'direction',
            'instrument',
            'instrumentName',
            'yesterdayPosition',
            'todayPosition',
            'closeProfit',
            'floatProfit',
            'avgPrice',
            'lastSettlePrice',
            'usedCommission',
            'usedMargin',
            'marketValue',
            'assetType',
            'frozenVolume',
            'frozenTodayVolume',
            'positionCost',
            'settlementPrice',
            'marginRateByMoney',
            'marginRateByVolume',
            'updateTime',
            'identityid',
        ]);
    }

    /**
     * 退订，之前的，上下文对象的实时数据
     * @param {ContextObjectInfo} previous_context 
     */
    unsubChange(previous_context) {
        this.standardSend(this.systemEvent.unsubscribeAccountChange, previous_context.identityId, [this.serverEvent.positionChanged]);
    }

    /**
     * 订阅，当前的，上下文对象的实时数据
     */
    resubChange() {
        this.standardSend(this.systemEvent.subscribeAccountChange, this.identityId, [this.serverEvent.positionChanged]);
    }

    resetControls() {

        super.resetControls();
        this.condition.keywords = null;
        this.condition.showAdjustPosBtn = this.userInfo.isOrgAdmin && this.context && this.context.isAboutStrategy;
    }

    /**
     * @param {Array<Array>} locals 
     * @param {Array<Array>} newers 
     * @returns {Array<Array>}
     */
    mergeAll(locals, newers) {
        
        let id_idx = this.RecordIdIdx;
        let ut_idx = this.RecordUpdateTimeIdx;
        let map_loc = {};
        let map_new = {};

        locals.forEach(values => { map_loc[values[id_idx]] = values; });
        newers.forEach(values => { map_new[values[id_idx]] = values; });

        for (let ord_id in map_new) {
            
            let item_new = map_new[ord_id];
            let item_loc = map_loc[ord_id];

            if (item_loc === undefined) {
                locals.push(item_new);
            }
            else {

                let utime_new = item_new[ut_idx];
                let utime_loc = item_loc[ut_idx];
                if (utime_new >= utime_loc) {
                    this.helper.extend(item_loc, item_new);
                }
            }
        }

        return locals;
    }

    /**
     * @param {Array<Array>} contents 
     */
    consumeBatchPush(contents) {

        super.consumeBatchPush(contents);
        var records = ModelConverter.formalizePositions(this.titles, contents);
        this.tableObj.refill(records);
    }

    /**
     * @param {*} struc
     */
    consumeRealtimePush(struc) {
        
        var position = new PositionRecord(struc);
        this.tableObj.putRow(position);
    }

    createToolbarApp() {

        this.toolbarApp = new Vue({

            el: this.$container.querySelector('.user-toolbar'),
            data: {

                condition: this.condition,
                dialog: this.dialog,
                sharedCondition: this.sharedCondition,
                paging: this.paging,
            },

            mixins: [NumberMixin],
            methods: this.helper.fakeVueInsMethod(this, [

                this.toggleRealtimePush, 
                this.openAddRecordDialog,
                this.openAdjustPosWin,
                this.filterRecords,
                this.handlePageSizeChange, 
                this.handlePageChange
            ]),
        });
    }

    openAdjustPosWin() {

        this.dialog.visible = true;
        this.toolbarApp.$nextTick(() => {

            if (this.$dialogAdjust === undefined) {
                this.$dialogAdjust = this.toolbarApp.$el.querySelector('.content-holder');
            }

            this.renderAdjustPos();
        });
    }

    renderAdjustPos() {

        let thisObj = this;

        function notifyChange() {
            thisObj.moduleAdjustPos.trigger(thisObj.systemEvent.viewContextChange, thisObj.identityId);
        }

        if (this.moduleAdjustPos) {

            this._loadedAdjustPosModule ? notifyChange() : this.interaction.showMessage('调仓模块正在加载...');
            return;
        }

        let AdjustPosView = require('../admin/shared/adjust-pos');
        let viewIns = this.moduleAdjustPos = new AdjustPosView('@admin/shared/adjust-pos', false);
		viewIns.registerEvent('adjust-pos-done', this.handleAdjustWorkDone.bind(this));
        viewIns.loadBuild(this.$dialogAdjust, undefined, () => {

            this._loadedAdjustPosModule = true;
            notifyChange();
        });
    }

    handleAdjustWorkDone() {
        this.dialog.visible = false;
    }

    filterRecords() {

        let thisObj = this;
        let keywords = this.condition.keywords;

        /**
         * @param {PositionRecord} record 
         */
        function filterByPinyin(record) {
            return thisObj.testKeywords(record.instrumentName, keywords);
        }
        
        this.tableObj.setPageIndex(1, false);
        this.tableObj.setKeywords(keywords, false);
        this.tableObj.mixedFilter(filterByPinyin, 'or');
    }

    handlePageSizeChange() {
        this.tableObj.setPageSize(this.paging.pageSize);
    }

    handlePageChange() {

        if (!this.isDataPushReceived) {

            this.interaction.showMessage('数据未准备完整，请稍后翻页');
            return;
        }

        this.tableObj.setPageIndex(this.paging.page);
    }

    createTableComponent() {
        
        this.helper.extend(this, ColumnCommonFunc);
        var $table = this.$container.querySelector('.table-control');
        var tableObj = new SmartTable($table, this.identifyRecord, this, {

            tableName: 'smt-pl',
            displayName: this.title,
            enableConfigToolkit: true,
            defaultSorting: { prop: 'updateTime', direction: 'desc' },
            recordsFiltered: this.handleTableFiltered.bind(this),
        });

        tableObj.setPageSize(this.paging.pageSize);
        return tableObj;
    }

    handleTableFiltered(filtered_count) {
        this.paging.total = filtered_count;
    }

    /**
     * @param {PositionRecord} row_data 
     */
    formatActions(row_data) {
        
        if (this.sharedCondition.canChangeRecord) {

            return `<button event.onclick="updateRecord"><i class="el-icon-edit"></i> 更改</button>
                    <button event.onclick="deleteRecord" class="danger"><i class="el-icon-delete"></i> 删除</button>`;
        }
        else {
            return '';
        }
    }

    listen2Events() {

        this.standardListen(this.serverEvent.todayPositionPush, this.handleBatchPush.bind(this));
        this.standardListen(this.serverEvent.positionChanged, this.handleRealtimeChange.bind(this));
    }

    async keepPriceUpdated() {

        if (!this.context) {
            return;
        }
        else if (this._isUpdatingPrice === true || this.tableObj.rowCount == 0) {
			return;
		}

        this._isUpdatingPrice = true;

		try {

            let price_map = {};
            let { containsStockAccounts, containsFutureAccounts, containsOptionAccounts } = this.context;

			if (containsStockAccounts) {
                
                let resp = await repoTrading.getMarketLatestPrice(this.systemEnum.assetsType.stock.code);
                let map = resp.data;
                for (let key in map) {
                    price_map[key] = map[key];
                }
            }

            if (containsFutureAccounts) {
                
                let resp = await repoTrading.getMarketLatestPrice(this.systemEnum.assetsType.future.code);
                let map = resp.data;
                for (let key in map) {
                    price_map[key] = map[key];
                }
            }

            if (containsOptionAccounts) {
                
                let resp = await repoTrading.getMarketLatestPrice(this.systemEnum.assetsType.option.code);
                let map = resp.data;
                for (let key in map) {
                    price_map[key] = map[key];
                }
            }

			this.tableObj.allRows.forEach(row => {

                let pos = row.rowData;                
                if (!(pos instanceof PositionRecord)) {

                    /**
                     * will never run in
                    */
                    return;
                }

                let instrument = pos.instrument;
                let latest_price = price_map[instrument];

                if (typeof latest_price != 'number' || latest_price === pos.lastPrice) {
                    return;
                }
                
                let revised_pos = {

                    id: pos.id,
                    lastPrice: latest_price,
                    marketValue: latest_price * pos.totalPosition * pos.direction * pos.volumeMultiple,
                    floatProfit: (latest_price - pos.avgPrice) * pos.totalPosition * pos.direction * pos.volumeMultiple,
                };

                revised_pos.profit = pos.closeProfit + revised_pos.floatProfit - pos.usedCommission;
                this.tableObj.updateRow(revised_pos);
			});
		}
		catch(ex) {
			console.error(ex);
		}
		finally {
			this._isUpdatingPrice = false;
		}
    }

    dispose() {

        super.dispose();
        clearInterval(this.updateRealtimePriceJobId);
        delete this.updateRealtimePriceJobId;
        this.moduleAdjustPos && this.moduleAdjustPos.dispose();
        console.log('stopped to request simple price');
    }

    build($container) {

        super.build($container);
        this.listen2Events();
        this.updateRealtimePriceJobId = setInterval(() => { this.keepPriceUpdated(); }, 1000 * 10);
    }
}

module.exports = View;