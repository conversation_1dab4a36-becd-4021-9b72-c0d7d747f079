<template>

	<div class="template-root s-full-size">

        <div id="top-drag-handler" class="s-dragable">
            <a id="btn-close" class="s-no-drag" @click="closeWindow">
                <i class="el-icon-close"></i>
            </a>
        </div>

        <div id="brand-name" class="s-center">
            <span id="sub-introduction" class="s-fs-16 s-unselectable">君弘高御</span>
        </div>

        <div class="sign-in-input-box">

            <el-checkbox class="credit-option" v-model="uidata.credit">信用交易</el-checkbox>

            <div v-for="(ctr, ctr_idx) in ctrs" :key="ctr_idx" class="input-item" :class="ctr.clsname">

                <label class="item-label">{{ ctr.label }}</label>
                
                <template v-if="ctr.isBranch">
                    
                    <el-select class="s-full-width"
                            v-model="ctr.value"
                            v-bind:disabled="uidata.isSigningIn"
                            @change="checkInput(ctr)"
                            :placeholder="'请选择' + ctr.label"
                            filterable clearable>
                            
                            <el-option v-for="(item, item_idx) in branchs" 
                                        v-bind:key="item_idx"
                                        v-bind:value="item.id" 
                                        v-bind:label="item.name"></el-option>
                    </el-select>

                </template>

                <template v-else-if="ctr.isServer">
                    
                    <el-select class="s-full-width"
                            v-model="ctr.value"
                            v-bind:disabled="uidata.isSigningIn"
                            @change="checkServer"
                            :placeholder="'请选择' + ctr.label"
                            filterable clearable>
                            
                            <el-option v-for="(item, item_idx) in servers" 
                                        v-bind:key="item_idx"
                                        v-bind:value="item.id" 
                                        v-bind:label="item.name"></el-option>
                    </el-select>

                </template>

                <template v-else>
                    
                    <el-input :id="ctr.id || undefined"
                                :type="ctr.ctype || 'text'"
                                :placeholder="'请输入' + ctr.label"
                                v-model.trim="ctr.value"
                                v-bind:disabled="uidata.isSigningIn"
                                @blur="checkInput(ctr)"
                                @keydown.native="finishInput"
                                clearable></el-input>

                    <a v-if="ctr.isCaptcha" id="captcha-img" v-show="!uidata.isSigningIn" @click="refreshCaptcha"></a>

                </template>

                <div class="input-error s-color-red" 
                     :style="{ visibility: typeof ctr.error == 'string' && ctr.error.trim().length > 0 ? 'visible': 'hidden' }">{{ ctr.error }}</div>
                
            </div>

            <div class="input-item" style="height: 40px; margin-top: 10px;">
                <label class="item-label" style="margin-top: 3px;">登录选项</label>
                <el-checkbox v-model="uidata.rememberUser" 
                             v-bind:disabled="uidata.isSigningIn"
                             @change="handleRembChange"
                             style="margin-right: 0px" >记住用户名</el-checkbox>
            </div>

            <div class="button-row">
                <el-button type="primary" id="btn-to-sign-in" v-bind:disabled="uidata.isSigningIn" @click="check2SignIn">
                    <span v-if="uidata.isSigningIn">正在登录 <i class="el-icon-loading" v-show="uidata.isSigningIn"></i></span>
                    <span v-else>立即登录</span>
                </el-button>
            </div>

        </div>

    </div>
    
</template>
