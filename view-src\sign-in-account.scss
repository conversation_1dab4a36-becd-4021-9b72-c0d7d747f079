html,
body {

	height: 100%;
	width: 100%;
}

.template-root {

	position: absolute;
	z-index: 99;
}

#top-drag-handler {
	height: 110px;
}

#brand-name {
	margin-top: -70px;
}

#sub-introduction {
	letter-spacing: 3px;
}

#btn-close {

	display: block;
    float: right;
    margin-top: 5px;
    margin-right: 15px;
    text-decoration: none;
    outline: none;
	cursor: pointer;
	
	.iconfont {
	
		position: relative;
		left: 3.5px;
		font-size: 8px;
	}
}

.sign-in-input-box {

    margin-top: 40px;
    padding-left: 30px;
    padding-right: 30px;
	
	.input-item {

		margin-top: 7px;
		box-sizing: border-box;
		height: 50px;
		padding-left: 70px;

		.item-label {

			display: block;
			float: left;
			margin-left: -70px;
			margin-top: 10px;
			opacity: 0.6;
		}

		.el-input,
		.el-input input {
			height: 36px !important;
		}

		.el-input input {

			border: none;
			padding-left: 12px;
			padding-right: 12px;
		}

		.input-error {

			padding-left: 12px;
			padding-top: 2px;
		}

	}

	.credit-option {

		position: absolute;
		right: 30px;
		top: 165px;
	}

	.input-sec-account {
		.el-input {
			width: 150px;
		}
	}
}

/*
    rewrite ele-checkbox size
*/

.el-checkbox {
	
	opacity: 0.6;
	
	&.is-checked {
		opacity: 1;
	}

	.el-checkbox__label {
		font-size: 12px;
	}
}

.el-checkbox__input.is-checked .el-checkbox__inner,
.el-checkbox__input.is-indeterminate .el-checkbox__inner {

	background-color: white;
	border-color: white;
}

.el-checkbox__input.is-checked+.el-checkbox__label {
	color: white;
}

.el-checkbox__inner {

	width: 14px;
	height: 14px;

	&::after {

		border-right: 2px solid #2C79F2;
		border-bottom: 2px solid #2C79F2;
	}
}

.el-checkbox__label {

	position: relative;
	top: 1px;
}

body > .el-select-dropdown {

	max-height: 150px;
	overflow-y: auto;
}

.input-item-captcha {
	padding-right: 120px;
}

.input-item-captcha .el-input {
	width: 110px;
}

#captcha-img {

	display: block;
	float: right;
	height: 32px;
	margin-top: -7px;
	margin-right: -120px;

	svg {

		width: 120px;
		height: 50px;
	}
}

.button-row {

	padding-left: 70px;

	button {

		height: 36px;
		width: 100%;
	}
}