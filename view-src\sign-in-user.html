<template>
	<div class="template-root s-full-size">
        <div id="top-drag-handler" class="s-dragable">
            <a id="btn-close" class="s-no-drag" @click="closeWindow">
                <i class="el-icon-close"></i>
            </a>
        </div>
        <div id="brand-name" class="s-center s-fs-20">
            <img src="../asset/image/logo-name.png" width="150" height="40" />
            <br/>
            <span id="sub-introduction" class="s-opacity-6 s-fs-14 s-unselectable">专业PB管理及交易平台</span>
        </div>
        <div class="sign-in-input-box">
            <div class="input-item">
                <el-input placeholder="请输入用户名" 
                            v-model.trim="userName" 
                            v-bind:disabled="isSigningIn" 
                            @blur="checkUserNameInput"
                            @keydown.native="move2Next($event)"></el-input>
                <div class="input-error s-color-red" :style="{ visibility: hasUserNameError ? 'visible': 'hidden' }">{{userNameError}}</div>
            </div>
            <div class="input-item">
                <el-input id="input-user-passcode"
                            placeholder="请输入密码" 
                            type="password" 
                            v-model.trim="passcode"
                            v-bind:disabled="isSigningIn" 
                            @blur="checkPasscodeInput"
                            @keydown.native="finishInput($event)"></el-input>
                <div class="input-error s-color-red" :style="{ visibility: hasPasscodeError ? 'visible': 'hidden' }">{{passcodeError}}</div>
            </div>
            <div class="input-item input-item-captcha">
                <el-input
                        id="input-captcha"
                        placeholder="请输入验证码"
                        v-model.trim="captcha"
                        v-bind:disabled="isSigningIn"
                        @blur="checkCaptchaInput"
                        @keydown.native="finishInput($event)"></el-input>
                <a id="captcha-img" v-show="!isSigningIn" @click="refreshCaptcha"></a>
                <div class="input-error s-color-red" :style="{ visibility: hasCaptchaError ? 'visible': 'hidden' }">
                    <template>
                        {{ captchaError }}
                    </template>
                </div>
            </div>
            <div class="input-item last-input-item">
                <el-checkbox style="margin-right: 0px" v-model="rememberUserName" v-bind:disabled="isSigningIn">记住用户名</el-checkbox>
                <el-select class="select-server-list s-pull-right" v-model="selectedServer" v-bind:disabled="isSigningIn">
                    <el-option v-for="(svr, svr_idx) in servers" v-bind:key="svr_idx" v-bind:value="svr.id" v-bind:label="svr.name"></el-option>
                </el-select>
            </div>
            <div class="input-item">
                <el-button class="s-full-width"
                            id="btn-to-sign-in"
                            v-bind:disabled="isSigningIn" 
                            @click="toSignIn">
                    <span v-if="isSigningIn">
                        正在登录
                        <i class="el-icon-loading" v-show="isSigningIn"></i>
                    </span>
                    <span v-else>
                        立即登录
                    </span>
                </el-button>
            </div>
        </div>
    </div>
</template>