html,
body {

	height: 100%;
	width: 100%;
}

.template-root {

	position: absolute;
	z-index: 99;
}

#top-drag-handler {
	height: 110px;
}

#brand-name {
	margin-top: -57px;
}

#btn-close {

	display: block;
    float: right;
    margin-top: 5px;
    margin-right: 15px;
    text-decoration: none;
    outline: none;
	cursor: pointer;
	
	.iconfont {
	
		position: relative;
		left: 3.5px;
		font-size: 8px;
	}
}

#sub-introduction {
	letter-spacing: 1.3px;
}

.select-server-list {

	width: 160px;
	height: 32px;
	margin-top: -8px;

	.el-select .el-input,
	.el-select .el-input input {
		height: 26px !important;
	}
}

#btn-to-sign-in {

	height: 36px;
	margin-top: 10px;
	border: none;
	background-image: linear-gradient(-180deg, #FFFFFF 0%, #E9EFF4 100%);

	* {

		font-size: 14px;
		color: #1A65DC;
		line-height: 14px;
	}
}

.sign-in-input-box {

	width: 255px;
    margin-top: 26px;
    margin-left: auto;
	margin-right: auto;
	
	.input-item {

		height: 50px;
		margin-top: 7px;

		.el-input,
		.el-input input {
			height: 36px !important;
		}

		.el-input input {

			border: none;
			padding-left: 12px;
			padding-right: 12px;
		}

		.input-error {

			padding-left: 12px;
			padding-top: 2px;
		}

	}
}

.input-item-captcha .el-input {
	width: 120px;
}

/*
    rewrite ele-checkbox size
*/

.el-checkbox {
	
	opacity: 0.6;
	
	&.is-checked {
		opacity: 1;
	}

	.el-checkbox__label {
		font-size: 12px;
	}
}

.el-checkbox__input.is-checked .el-checkbox__inner,
.el-checkbox__input.is-indeterminate .el-checkbox__inner {

	background-color: white;
	border-color: white;
}

.el-checkbox__input.is-checked+.el-checkbox__label {
	color: white;
}

.el-checkbox__inner {

	width: 14px;
	height: 14px;

	&::after {

		border-right: 2px solid #2C79F2;
		border-bottom: 2px solid #2C79F2;
	}
}

.el-checkbox__label {

	position: relative;
	top: 1px;
}

body > .el-select-dropdown {

	max-height: 150px;
	overflow-y: auto;
}

#captcha-img {

	display: block;
	float: right;
	height: 32px;
	margin-top: -7px;

	svg {

		width: 120px;
		height: 50px;
	}
}

.last-input-item {

	margin-top: 15px;
	height: 40px;
}