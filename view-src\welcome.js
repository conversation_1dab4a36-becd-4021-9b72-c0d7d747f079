const IView = require('../component/iview').IView;
const InstrumentCampus = require('../toolset/instrument-campus').InstrumentCampus;
const repoRole = require('../repository/role').repoRole;
const repoApp = require('../repository/app').repoApp;
const Menu = require('../model/menu').Menu;

class View extends IView {

    constructor(view_name) {

        super(view_name, true, '欢迎使用，系统启动中...');
        this.getContextDataItem

        this.markData = {

            title: '市场数据',
            description: '正在加载市场合约数据...',
            process: async () => { await this.requestInstruments(); },
        };

        this.userMenu = {

            title: '用户数据',
            description: '正在加载用户数据...',
            process: async () => { await this.requestUserMenu(); },
        };

        this.uidata = {

            stepIndex: 0,
            stepDescription: '准备数据加载...',
            steps: [this.markData, this.userMenu],
        };
    }

    requestInstruments() {

        return new Promise((resolve, reject) => {

            var success_callback = (stock_list, future_list, option_list) => { resolve(); };
            var error_callback = (reason) => { reject(reason); };
            new InstrumentCampus(success_callback, error_callback).loadMarketInstruments();
        });
    }

    requestUserMenu() {

        return new Promise(async (resolve, reject) => {

            var resp_role_menu = await repoRole.getRoleRights(this.userInfo.roleId);

            if (resp_role_menu.errorCode !== 0) {

                reject(`用户权限数据获取错误：${resp_role_menu.errorMsg}`);
                return;
            }

            var resp_sys_menu = await repoRole.getAllMenus();
            if (resp_sys_menu.errorCode !== 0) {

                reject(`系统菜单数据获取错误：${resp_role_menu.errorMsg}`);
                return;
            }

            var role_menus = resp_role_menu.data;
            var sys_menus = resp_sys_menu.data;
            var login_input = this.getContextDataItem(this.dataKey.logInInput);
            var config_str = login_input.configStr;
            if (typeof config_str != 'string' || config_str.trim().length == 0) {
                this.removeSpecialMenus(role_menus, sys_menus);
            }
            var user_menus = Menu.ConstructTree(sys_menus, role_menus);

            /**
             * referenced app menus
             */
            let resp_app = await repoApp.getMyApplications();
            let app_menus = resp_app.data;

            if (resp_app.errorCode == 0 && app_menus instanceof Array && app_menus.length > 0) {
                user_menus.merge(Menu.ConstructAppMenus(app_menus));
            }

            this.setContextDataItem(this.dataKey.userMenus, user_menus);
            resolve();
        });
    }

    /**
     * 移除指定的菜单项
     * @param {Array} role_menus 
     * @param {Array} sys_menus 
     */
    removeSpecialMenus(role_menus, sys_menus) {
        
        let menu_id = 9901;
        let menu_name = '打板';
        role_menus.remove(x => x.menuId == menu_id || x.menuName == menu_name);
        sys_menus.remove(x => x.id == menu_id || x.menuName == menu_name || x.parentMenuId == menu_id);
    }

    handleStepChange() {

        if (this.uidata.stepIndex <= this.uidata.steps.length) {
            this.uidata.stepDescription = this.uidata.steps[this.uidata.stepIndex - 1].description;
        } 
        else {
            this.renderProcess.send(this.systemEvent.sysLoadingCompleted);
        }
    }

    createApp() {
        this.vueApp = new Vue({ el: this.$container, data: this.uidata });
    }

    async startChainedJob() {

        this.uidata.stepIndex = 1;
        this.handleStepChange();

        try {

            // execute all jobs one by one
            for (let step of this.uidata.steps) {

                await step.process();
                this.uidata.stepIndex++;
                this.handleStepChange();
            }
        } 
        catch (error_msg) {

            console.log(error_msg);
            this.interaction.showAlert(`${error_msg}，系统将退出。`, () => {
                this.renderProcess.send(this.systemEvent.exitApp);
            });
        }
    }

    build ($container) {

        super.build($container);
        this.createApp();
        this.startChainedJob();
    }
}

module.exports = View;
